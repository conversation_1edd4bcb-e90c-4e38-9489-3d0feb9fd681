# frozen_string_literal: true

class AddOrganizationToStatementLineItems < ActiveRecord::Migration[7.2]
  def up
    add_reference :statement_line_items, :organization, foreign_key: true, null: true

    StatementLineItem.where(organization_id: nil).find_each do |line_item|
      org = line_item.identity&.organization || line_item.statement.pay_period.branch.organization
      line_item.organization_id = org.id
      line_item.save!(validate: false)
    end

    change_column_null :statement_line_items, :organization_id, false
  end

  def down
    change_column_null :statement_line_items, :organization_id, true

    remove_reference :statement_line_items, :organization, foreign_key: true
  end
end
