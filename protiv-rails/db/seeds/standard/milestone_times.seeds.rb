# frozen_string_literal: true

after "standard:milestones" do
  return if MilestoneTime.any?

  milestone_times = []

  Organization.includes(:identities, branches: :routes).all.each do |org|
    org.branches.each do |branch|
      routes = branch.routes
      crew = org.identities.limit(4).all
      route_count = routes.count

      branch.milestones.all.each_with_index do |milestone, idx|
        route = routes[idx % route_count]

        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[0], start_time: "2024-12-13 08:00:00.000000", end_time: "2024-12-13 16:00:00.000000", labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[1], start_time: "2024-12-13 08:00:00.000000", end_time: "2024-12-13 16:00:00.000000", labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[2], start_time: "2024-12-13 08:00:00.000000", end_time: "2024-12-13 16:00:00.000000", labor_cost: Money.from_amount(144, "USD"), base_hourly_rate: Money.from_amount(18, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[3], start_time: "2024-12-13 08:00:00.000000", end_time: "2024-12-13 16:00:00.000000", labor_cost: Money.from_amount(120, "USD"), base_hourly_rate: Money.from_amount(15, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[0], start_time: "2024-12-14 08:00:00.000000", end_time: "2024-12-14 16:00:00.000000", labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[1], start_time: "2024-12-14 08:00:00.000000", end_time: "2024-12-14 16:00:00.000000", labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[2], start_time: "2024-12-14 08:00:00.000000", end_time: "2024-12-14 16:00:00.000000", labor_cost: Money.from_amount(144, "USD"), base_hourly_rate: Money.from_amount(18, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[3], start_time: "2024-12-14 08:00:00.000000", end_time: "2024-12-14 15:00:00.000000", labor_cost: Money.from_amount(105, "USD"), base_hourly_rate: Money.from_amount(15, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[0], start_time: "2024-12-16 08:00:00.000000", end_time: "2024-12-16 16:00:00.000000", labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[1], start_time: "2024-12-16 08:00:00.000000", end_time: "2024-12-16 16:00:00.000000", labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[2], start_time: "2024-12-16 08:00:00.000000", end_time: "2024-12-16 16:00:00.000000", labor_cost: Money.from_amount(144, "USD"), base_hourly_rate: Money.from_amount(18, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[0], start_time: "2024-12-17 08:00:00.000000", end_time: "2024-12-17 16:00:00.000000", labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[1], start_time: "2024-12-17 08:00:00.000000", end_time: "2024-12-17 16:00:00.000000", labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[2], start_time: "2024-12-17 08:00:00.000000", end_time: "2024-12-17 14:00:00.000000", labor_cost: Money.from_amount(108, "USD"), base_hourly_rate: Money.from_amount(18, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[0], start_time: "2024-12-18 08:00:00.000000", end_time: "2024-12-18 16:00:00.000000", labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[1], start_time: "2024-12-18 08:00:00.000000", end_time: "2024-12-18 16:00:00.000000", labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD"))
        milestone_times << MilestoneTime.new(milestone: milestone, route: route, identity: crew[0], start_time: "2024-12-19 08:00:00.000000", end_time: "2024-12-19 13:00:00.000000", labor_cost: Money.from_amount(150, "USD"), base_hourly_rate: Money.from_amount(30, "USD"))
      end
    end

    if milestone_times.size > 1000
      MilestoneTime.import(milestone_times)
      milestone_times = []
    end
  end
  MilestoneTime.import(milestone_times)

  # Update each milestone's seconds_cost to the sum of all its milestone_times duration_seconds
  Milestone.includes(:milestone_times).find_each do |milestone|
    total_duration_seconds = milestone.milestone_times.sum(&:duration_seconds)
    milestone.update_column(:seconds_cost, total_duration_seconds)
  end
end
