# frozen_string_literal: true

Organization.find_each do |org|
  # Check if an active benchmark already exists for this organization
  existing_benchmark = LaborEfficiencyBenchmark.find_by(
    organization: org,
    level: "organization",
    level_record_id: nil,
    active: true
  )

  if existing_benchmark
    # Update existing benchmark to seed target percentage
    existing_benchmark.update!(target_percentage: 80.0)
  else
    # Create new benchmark if none exists
    LaborEfficiencyBenchmark.create!(
      organization: org,
      level: "organization",
      level_record_id: nil,
      target_percentage: 80.0,
      active: true
    )
  end
end
