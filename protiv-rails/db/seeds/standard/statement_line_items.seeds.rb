# frozen_string_literal: true

after "standard:pay_periods", "standard:identities" do
  Identity.all.each do |identity|
    next if identity.statement_line_items.any?

    (1..5).each do |i|
      StatementLineItem.create!(
        organization: identity.organization,
        identity: identity,
        status: if i == 5 then "void" else "in_process" end,
        amount: Money.from_amount(if i % 2 == 0 then 10 else -5 end)
      )
    end
  end
end
