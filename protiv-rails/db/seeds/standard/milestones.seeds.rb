# frozen_string_literal: true

after "standard:jobs", "standard:invoice_types" do
  return if Milestone.any?

  milestones = []
  milestone_counter = 0 # Global counter to help cycle through all invoice types

  Job.all.each_with_index do |job, idx|
    organization = job.organization
    invoice_type_ids = InvoiceType.where(organization: organization).pluck(:id)
    num_invoice_types = invoice_type_ids.length

    # Create 1 or 2 milestones per job
    (1..((idx % 2) + 1)).each do |i|
      milestones << Milestone.new(status: "pending", job: job,
                                  seconds_budget: 1519200,
                                  labor_budget_cents: 717400,
                                  currency: "USD",
                                  name: "Milestone #{milestone_counter + 1}")
      current_invoice_type_id = nil
      if num_invoice_types > 0
        # Use global counter to better distribute invoice types
        current_invoice_type_id = invoice_type_ids[milestone_counter % num_invoice_types]
        milestone_counter += 1
      end

      milestones << Milestone.new(
        status: "pending",
        job: job,
        invoice_type_id: current_invoice_type_id,
        seconds_budget: 1_519_200,
        labor_budget_cents: 717_400,
        currency: "USD",
        name: "Milestone #{milestone_counter + 1}"
      )
    end
  end

  Milestone.import(milestones) if milestones.any?
end
