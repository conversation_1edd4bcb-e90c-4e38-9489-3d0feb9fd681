SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: attendance_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.attendance_status AS ENUM (
    'pending',
    'paid'
);


--
-- Name: attendance_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.attendance_type AS ENUM (
    'regular',
    'manual',
    'unpaid_break',
    'performance_bonus'
);


--
-- Name: bonus_source; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.bonus_source AS ENUM (
    'company',
    'rounding',
    'crew_lead',
    'manager',
    'other',
    'crew',
    'crew_retention'
);


--
-- Name: budget_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.budget_type AS ENUM (
    'hours',
    'labor_budget',
    'contract_price'
);


--
-- Name: integration_source; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.integration_source AS ENUM (
    'abstract',
    'aspire'
);


--
-- Name: integration_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.integration_status AS ENUM (
    'pending',
    'active',
    'failed'
);


--
-- Name: job_budget_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.job_budget_status AS ENUM (
    'over',
    'under'
);


--
-- Name: job_progress_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.job_progress_status AS ENUM (
    'on_schedule',
    'behind_schedule'
);


--
-- Name: job_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.job_status AS ENUM (
    'pending',
    'in_progress',
    'completed',
    'canceled'
);


--
-- Name: job_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.job_type AS ENUM (
    'unknown',
    'non_recurring',
    'recurring'
);


--
-- Name: milestone_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.milestone_status AS ENUM (
    'pending',
    'scheduled',
    'in_review',
    'in_progress',
    'completed',
    'canceled'
);


--
-- Name: partial_pro_pay_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.partial_pro_pay_status AS ENUM (
    'draft',
    'materialized',
    'finalized'
);


--
-- Name: period_schedule; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.period_schedule AS ENUM (
    'weekly',
    'biweekly',
    'semimonthly',
    'monthly',
    'bimonthly'
);


--
-- Name: period_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.period_type AS ENUM (
    'pay',
    'bonus',
    'route'
);


--
-- Name: pro_pay_budget_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.pro_pay_budget_type AS ENUM (
    'hours',
    'amount',
    'rate'
);


--
-- Name: pro_pay_distribution_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.pro_pay_distribution_type AS ENUM (
    'equal_rate',
    'equal_weighted',
    'crew_lead_weighted',
    'manual_distribution'
);


--
-- Name: pro_pay_payout_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.pro_pay_payout_type AS ENUM (
    'milestone',
    'job',
    'phase',
    'route'
);


--
-- Name: pro_pay_source_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.pro_pay_source_type AS ENUM (
    'route_recurring_jobs',
    'route_non_recurring_jobs',
    'job_with_phases',
    'manual'
);


--
-- Name: pro_pay_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.pro_pay_status AS ENUM (
    'draft',
    'finalized',
    'journalized',
    'materialized',
    'active',
    'completed',
    'approved',
    'deleted',
    'paid'
);


--
-- Name: rate_class; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.rate_class AS ENUM (
    'manager',
    'crew_lead',
    'crew',
    'company',
    'other'
);


--
-- Name: role_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.role_type AS ENUM (
    'owner',
    'admin',
    'manager',
    'crew_lead',
    'employee'
);


--
-- Name: schedule_period; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.schedule_period AS ENUM (
    'weekly',
    'biweekly',
    'semimonthly',
    'monthly',
    'bimonthly'
);


--
-- Name: statement_line_item_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.statement_line_item_status AS ENUM (
    'paid',
    'held',
    'in_process',
    'void'
);


--
-- Name: statement_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.statement_status AS ENUM (
    'paid',
    'open',
    'void'
);


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ar_internal_metadata; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ar_internal_metadata (
    key character varying NOT NULL,
    value character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: attendances; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.attendances (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    milestone_id bigint,
    identity_id bigint NOT NULL,
    started_at timestamp(6) without time zone NOT NULL,
    ended_at timestamp(6) without time zone,
    created_by_user_id bigint NOT NULL,
    break_time_seconds integer DEFAULT 0,
    organization_id bigint,
    started_longitude double precision,
    started_latitude double precision,
    ended_longitude double precision,
    ended_latitude double precision,
    manager_id bigint,
    route_id bigint
);


--
-- Name: attendances_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.attendances_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: attendances_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.attendances_id_seq OWNED BY public.attendances.id;


--
-- Name: bonus_line_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.bonus_line_items (
    id bigint NOT NULL,
    partial_sub_pro_pay_id bigint NOT NULL,
    payable_type character varying NOT NULL,
    payable_id bigint NOT NULL,
    bonus_source public.bonus_source,
    bonus_pool_percent_hundredths integer DEFAULT 0 NOT NULL,
    duration_seconds integer DEFAULT 0 NOT NULL,
    labor_cost_cents integer DEFAULT 0 NOT NULL,
    labor_cost_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    bonus_cents integer DEFAULT 0 NOT NULL,
    bonus_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    date_payable date
);


--
-- Name: bonus_line_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.bonus_line_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: bonus_line_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.bonus_line_items_id_seq OWNED BY public.bonus_line_items.id;


--
-- Name: bonus_pool_payables; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.bonus_pool_payables (
    id bigint NOT NULL,
    bonus_pool_id bigint,
    payable_type character varying NOT NULL,
    payable_id bigint NOT NULL,
    rate_class public.rate_class NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: bonus_pool_payables_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.bonus_pool_payables_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: bonus_pool_payables_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.bonus_pool_payables_id_seq OWNED BY public.bonus_pool_payables.id;


--
-- Name: bonus_pools; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.bonus_pools (
    id bigint NOT NULL,
    organization_id bigint,
    name character varying,
    company_percent_hundredths integer DEFAULT 0 NOT NULL,
    crew_lead_percent_hundredths integer DEFAULT 0 NOT NULL,
    manager_percent_hundredths integer DEFAULT 0 NOT NULL,
    other_percent_hundredths integer DEFAULT 0 NOT NULL,
    crew_percent_hundredths integer DEFAULT 10000 NOT NULL,
    crew_retention_percent_hundredths integer DEFAULT 0 NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    crew_retention_days integer DEFAULT 0,
    manager_retention_days integer DEFAULT 0,
    crew_lead_retention_days integer DEFAULT 0,
    other_retention_days integer DEFAULT 0
);


--
-- Name: bonus_pools_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.bonus_pools_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: bonus_pools_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.bonus_pools_id_seq OWNED BY public.bonus_pools.id;


--
-- Name: branches; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.branches (
    id bigint NOT NULL,
    name character varying,
    organization_id bigint,
    address_line_1 character varying,
    address_line_2 character varying,
    address_city character varying,
    address_state_province character varying,
    address_zip_code character varying,
    legal_name character varying,
    region_name character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    payroll_schedule_id bigint,
    bonus_schedule_id bigint,
    time_zone character varying DEFAULT 'America/New_York'::character varying,
    currency character varying DEFAULT 'USD'::character varying,
    new_routes_create_grouped_pro_pays boolean,
    allow_grouped_pro_pay_creation boolean,
    route_schedule_id bigint
);


--
-- Name: branches_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.branches_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: branches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.branches_id_seq OWNED BY public.branches.id;


--
-- Name: catalog_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.catalog_items (
    id bigint NOT NULL,
    organization_id bigint,
    item_type character varying,
    name character varying,
    description character varying,
    purchase_unit character varying,
    allocation_unit character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: catalog_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.catalog_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: catalog_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.catalog_items_id_seq OWNED BY public.catalog_items.id;


--
-- Name: changesets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.changesets (
    id bigint NOT NULL,
    data jsonb,
    record_was_deleted boolean DEFAULT false,
    record_type character varying,
    record_id bigint,
    sync_uuid character varying,
    ignored boolean DEFAULT false,
    integration_id bigint,
    remote_slug character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    remote_updated_at timestamp(6) without time zone
);


--
-- Name: changesets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.changesets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: changesets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.changesets_id_seq OWNED BY public.changesets.id;


--
-- Name: clock_times; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.clock_times (
    id bigint NOT NULL,
    identity_id bigint,
    start_at timestamp(6) without time zone,
    end_at timestamp(6) without time zone,
    accepted_at timestamp(6) without time zone,
    break_time_seconds integer DEFAULT 0,
    used_breaks boolean,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: clock_times_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.clock_times_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: clock_times_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.clock_times_id_seq OWNED BY public.clock_times.id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.companies (
    id bigint NOT NULL,
    name character varying,
    contact_email character varying,
    contact_phone character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: companies_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.companies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.companies_id_seq OWNED BY public.companies.id;


--
-- Name: company_defaults; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_defaults (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    company_id bigint,
    budget_type public.budget_type DEFAULT 'hours'::public.budget_type,
    currency_code character varying,
    contract_price_percent numeric,
    increase_amount_cents integer DEFAULT 0 NOT NULL,
    increase_amount_currency character varying DEFAULT 'USD'::character varying NOT NULL
);


--
-- Name: company_defaults_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.company_defaults_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: company_defaults_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.company_defaults_id_seq OWNED BY public.company_defaults.id;


--
-- Name: cost_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cost_codes (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    name character varying,
    description text,
    company_id bigint NOT NULL
);


--
-- Name: cost_codes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cost_codes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cost_codes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cost_codes_id_seq OWNED BY public.cost_codes.id;


--
-- Name: credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.credentials (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    integration_id bigint NOT NULL,
    bearer_token character varying,
    refresh_token character varying,
    expires_at timestamp(6) without time zone
);


--
-- Name: credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.credentials_id_seq OWNED BY public.credentials.id;


--
-- Name: jobs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.jobs (
    id bigint NOT NULL,
    name character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    organization_id bigint,
    job_type public.job_type DEFAULT 'unknown'::public.job_type,
    status public.job_status DEFAULT 'pending'::public.job_status,
    progress_status public.job_progress_status,
    budget_status public.job_budget_status,
    progress_percent double precision DEFAULT 0.0,
    budget_used_percent double precision DEFAULT 0.0,
    manager_id bigint,
    initial_sync_done boolean DEFAULT false NOT NULL,
    branch_id bigint,
    last_activity_at timestamp(6) without time zone NOT NULL,
    remote_reference character varying,
    property_id bigint,
    created_date timestamp(6) without time zone,
    division_name character varying,
    milestone_progress_percent_hundredths integer,
    invoice_type_id bigint
);


--
-- Name: milestone_times; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.milestone_times (
    id bigint NOT NULL,
    identity_id bigint,
    milestone_id bigint,
    start_time timestamp(6) without time zone,
    end_time timestamp(6) without time zone,
    duration_seconds integer,
    route_id bigint,
    base_hourly_rate_cents integer DEFAULT 0 NOT NULL,
    base_hourly_rate_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    labor_cost_cents integer DEFAULT 0 NOT NULL,
    labor_cost_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    accepted_by_name character varying,
    accepted_at timestamp(6) without time zone,
    approved_by_name character varying,
    approved_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: milestones; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.milestones (
    id bigint NOT NULL,
    job_id bigint,
    status public.milestone_status,
    seconds_budget integer,
    currency character varying DEFAULT 'USD'::character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    labor_budget_cents integer DEFAULT 0 NOT NULL,
    seconds_cost integer,
    material_budget_cents integer DEFAULT 0 NOT NULL,
    material_cost_cents integer DEFAULT 0 NOT NULL,
    equipment_budget_cents integer DEFAULT 0 NOT NULL,
    equipment_cost_cents integer DEFAULT 0 NOT NULL,
    labor_cost_cents integer DEFAULT 0 NOT NULL,
    contract_price_cents integer DEFAULT 0 NOT NULL,
    percent_complete_hundredths integer,
    name character varying,
    description character varying,
    invoice_type_id bigint,
    service_id bigint,
    tracked_milestone_item_id bigint,
    percent_complete_cached_hundredths integer DEFAULT 0,
    remote_reference character varying
);


--
-- Name: employee_job_times; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.employee_job_times AS
 SELECT concat(milestone_times.identity_id, '-', jobs.id) AS id,
    milestone_times.identity_id,
    jobs.id AS job_id,
    sum(milestone_times.duration_seconds) AS duration_seconds,
    min(milestone_times.start_time) AS start_time,
    max(milestone_times.end_time) AS end_time,
    avg(milestone_times.base_hourly_rate_cents) AS base_hourly_rate_cents,
    sum(milestone_times.labor_cost_cents) AS labor_cost_cents
   FROM ((public.milestone_times
     JOIN public.milestones ON ((milestone_times.milestone_id = milestones.id)))
     JOIN public.jobs ON ((milestones.job_id = jobs.id)))
  GROUP BY milestone_times.identity_id, jobs.id;


--
-- Name: employee_milestone_times; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.employee_milestone_times AS
 SELECT concat(identity_id, '-', milestone_id) AS id,
    identity_id,
    milestone_id,
    sum(duration_seconds) AS duration_seconds,
    min(start_time) AS start_time,
    max(end_time) AS end_time,
    avg(base_hourly_rate_cents) AS base_hourly_rate_cents,
    sum(labor_cost_cents) AS labor_cost_cents
   FROM public.milestone_times
  GROUP BY identity_id, milestone_id;


--
-- Name: events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.events (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    action character varying NOT NULL,
    user_agent character varying,
    ip_address character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.events_id_seq OWNED BY public.events.id;


--
-- Name: identities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.identities (
    id bigint NOT NULL,
    user_id bigint,
    organization_id bigint,
    "primary" boolean,
    email character varying,
    phone_number character varying,
    name character varying,
    employee_id character varying,
    other_id character varying,
    other_id_description character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    crew_lead boolean DEFAULT false NOT NULL,
    route_manager boolean DEFAULT false NOT NULL
);


--
-- Name: identities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.identities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: identities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.identities_id_seq OWNED BY public.identities.id;


--
-- Name: integration_records; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.integration_records (
    id bigint NOT NULL,
    integration_id bigint,
    remote_slug character varying NOT NULL,
    record_type character varying,
    record_id bigint,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: integration_records_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.integration_records_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: integration_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.integration_records_id_seq OWNED BY public.integration_records.id;


--
-- Name: integrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.integrations (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    source character varying DEFAULT 'abstract'::character varying,
    last_synced_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    client_id character varying,
    secret character varying,
    initial_sync_done boolean DEFAULT false NOT NULL,
    sync_interval interval DEFAULT '08:00:00'::interval,
    auto_sync boolean DEFAULT true,
    last_sync_started_at timestamp(6) without time zone,
    status public.integration_status DEFAULT 'pending'::public.integration_status,
    CONSTRAINT check_sync_interval CHECK (((sync_interval >= '01:00:00'::interval) AND (sync_interval <= '24:00:00'::interval)))
);


--
-- Name: integrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.integrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: integrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.integrations_id_seq OWNED BY public.integrations.id;


--
-- Name: invitations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invitations (
    id bigint NOT NULL,
    user_id bigint,
    expires_at timestamp(6) without time zone NOT NULL,
    role_type public.role_type NOT NULL,
    deleted_at timestamp(6) without time zone,
    invited_by_id bigint,
    organization_id bigint,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    email character varying NOT NULL
);


--
-- Name: invitations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.invitations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: invitations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.invitations_id_seq OWNED BY public.invitations.id;


--
-- Name: invoice_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoice_types (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    name character varying NOT NULL,
    organization_id bigint NOT NULL
);


--
-- Name: invoice_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.invoice_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: invoice_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.invoice_types_id_seq OWNED BY public.invoice_types.id;


--
-- Name: item_allocations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.item_allocations (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    organization_id bigint,
    catalog_item_id bigint,
    milestone_id bigint,
    item_quantity double precision,
    unit_cost_cents integer,
    total_cost_cents integer,
    currency_code character varying DEFAULT 'USD'::character varying
);


--
-- Name: item_allocations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.item_allocations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: item_allocations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.item_allocations_id_seq OWNED BY public.item_allocations.id;


--
-- Name: job_budgets; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.job_budgets AS
 SELECT milestones.job_id,
    sum(milestones.labor_cost_cents) AS labor_cost_cents,
    sum(milestones.labor_budget_cents) AS labor_budget_cents,
    sum(milestones.equipment_cost_cents) AS equipment_cost_cents,
    sum(milestones.equipment_budget_cents) AS equipment_budget_cents,
    sum(milestones.material_cost_cents) AS material_cost_cents,
    sum(milestones.material_budget_cents) AS material_budget_cents,
    sum(milestones.contract_price_cents) AS contract_price_cents,
    sum(milestones.seconds_budget) AS seconds_budget,
    sum(milestones.seconds_cost) AS seconds_cost,
    milestones.currency
   FROM (public.milestones
     JOIN public.jobs ON ((jobs.id = milestones.job_id)))
  GROUP BY milestones.job_id, milestones.currency;


--
-- Name: jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.jobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.jobs_id_seq OWNED BY public.jobs.id;


--
-- Name: labor_efficiency_benchmarks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.labor_efficiency_benchmarks (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    level character varying NOT NULL,
    level_record_id integer,
    target_percentage numeric(5,2) NOT NULL,
    active boolean DEFAULT true NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: labor_efficiency_benchmarks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.labor_efficiency_benchmarks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: labor_efficiency_benchmarks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.labor_efficiency_benchmarks_id_seq OWNED BY public.labor_efficiency_benchmarks.id;


--
-- Name: labor_metrics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.labor_metrics (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    organization_id bigint NOT NULL,
    identity_id bigint NOT NULL,
    milestone_id bigint NOT NULL,
    date date NOT NULL,
    total_clocked_seconds integer DEFAULT 0,
    total_billable_seconds integer DEFAULT 0,
    non_billable_seconds integer DEFAULT 0,
    efficiency_percentage numeric(5,2) DEFAULT 0.0,
    has_time_discrepancy boolean DEFAULT false,
    labor_cost_cents integer DEFAULT 0,
    labor_cost_currency character varying DEFAULT 'USD'::character varying,
    non_billable_labor_cost_cents integer DEFAULT 0 NOT NULL,
    benchmark_percentage numeric(5,2),
    variance_from_benchmark numeric(5,2)
);


--
-- Name: labor_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.labor_metrics_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: labor_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.labor_metrics_id_seq OWNED BY public.labor_metrics.id;


--
-- Name: line_item_categories; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.line_item_categories (
    id bigint NOT NULL,
    name character varying NOT NULL,
    category_type character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: line_item_categories_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.line_item_categories_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: line_item_categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.line_item_categories_id_seq OWNED BY public.line_item_categories.id;


--
-- Name: milestone_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.milestone_items (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    catalog_item_id bigint,
    milestone_id bigint,
    organization_id bigint,
    allocation_unit character varying,
    quantity double precision,
    cost_cents integer,
    cost_currency character varying DEFAULT 'USD'::character varying NOT NULL
);


--
-- Name: milestone_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.milestone_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: milestone_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.milestone_items_id_seq OWNED BY public.milestone_items.id;


--
-- Name: milestone_times_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.milestone_times_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: milestone_times_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.milestone_times_id_seq OWNED BY public.milestone_times.id;


--
-- Name: milestones_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.milestones_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: milestones_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.milestones_id_seq OWNED BY public.milestones.id;


--
-- Name: organizations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.organizations (
    id bigint NOT NULL,
    name character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    default_progress_tracking_type character varying DEFAULT 'percentage'::character varying NOT NULL,
    default_tracking_material character varying,
    default_bonus_pool_id bigint,
    default_payroll_schedule_id bigint,
    default_bonus_schedule_id bigint,
    default_route_schedule_id bigint,
    phases_hour_threshold integer DEFAULT 1500 NOT NULL
);


--
-- Name: organizations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.organizations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: organizations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.organizations_id_seq OWNED BY public.organizations.id;


--
-- Name: partial_sub_pro_pays; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.partial_sub_pro_pays (
    id bigint NOT NULL,
    sub_pro_pay_id bigint NOT NULL,
    estimated_percent_complete integer,
    finalized_at timestamp(6) without time zone,
    bonus_cents integer,
    bonus_currency character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    materialized_at timestamp without time zone,
    status public.partial_pro_pay_status DEFAULT 'draft'::public.partial_pro_pay_status NOT NULL
);


--
-- Name: partial_sub_pro_pays_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.partial_sub_pro_pays_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: partial_sub_pro_pays_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.partial_sub_pro_pays_id_seq OWNED BY public.partial_sub_pro_pays.id;


--
-- Name: pay_periods; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pay_periods (
    id bigint NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone NOT NULL,
    open boolean DEFAULT true,
    payroll_schedule_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    branch_id bigint NOT NULL,
    period_type public.period_type NOT NULL
);


--
-- Name: pay_periods_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pay_periods_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pay_periods_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pay_periods_id_seq OWNED BY public.pay_periods.id;


--
-- Name: payroll_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payroll_schedules (
    id bigint NOT NULL,
    period public.schedule_period,
    name character varying DEFAULT 'default'::character varying NOT NULL,
    week_day smallint,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    organization_id bigint
);


--
-- Name: payroll_schedules_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.payroll_schedules_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payroll_schedules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.payroll_schedules_id_seq OWNED BY public.payroll_schedules.id;


--
-- Name: payrolls; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payrolls (
    id bigint NOT NULL,
    pay_period_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: payrolls_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.payrolls_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payrolls_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.payrolls_id_seq OWNED BY public.payrolls.id;


--
-- Name: pro_pay_payables; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pro_pay_payables (
    id bigint NOT NULL,
    pro_pay_id bigint,
    payable_type character varying NOT NULL,
    payable_id bigint NOT NULL,
    rate_class public.rate_class NOT NULL,
    participating boolean DEFAULT true NOT NULL,
    crew_lead_override boolean,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: pro_pay_payables_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pro_pay_payables_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pro_pay_payables_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pro_pay_payables_id_seq OWNED BY public.pro_pay_payables.id;


--
-- Name: pro_pays; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pro_pays (
    id bigint NOT NULL,
    reference_bonus_pool_id bigint,
    manager_id bigint,
    company_percent_hundredths integer DEFAULT 0 NOT NULL,
    crew_lead_percent_hundredths integer DEFAULT 0 NOT NULL,
    manager_percent_hundredths integer DEFAULT 0 NOT NULL,
    other_percent_hundredths integer DEFAULT 0 NOT NULL,
    crew_percent_hundredths integer DEFAULT 10000 NOT NULL,
    crew_retention_percent_hundredths integer DEFAULT 0 NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    source_route_id bigint,
    organization_id bigint NOT NULL,
    source_type public.pro_pay_source_type,
    source_route_pay_period_id bigint,
    source_job_id bigint,
    crew_retention_days integer DEFAULT 0,
    manager_retention_days integer DEFAULT 0,
    crew_lead_retention_days integer DEFAULT 0,
    other_retention_days integer DEFAULT 0,
    status public.pro_pay_status DEFAULT 'active'::public.pro_pay_status,
    payout_type public.pro_pay_payout_type,
    code integer
);


--
-- Name: pro_pays_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pro_pays_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pro_pays_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pro_pays_id_seq OWNED BY public.pro_pays.id;


--
-- Name: properties; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.properties (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    name character varying,
    address_line_1 character varying,
    address_line_2 character varying,
    address_city character varying,
    address_state_province character varying,
    address_zip_code character varying,
    primary_contact_id bigint,
    organization_id bigint,
    geolocation_latitude double precision,
    geolocation_longitude double precision
);


--
-- Name: properties_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.properties_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: properties_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.properties_id_seq OWNED BY public.properties.id;


--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    user_id bigint NOT NULL,
    active boolean DEFAULT true,
    active_at timestamp(6) without time zone NOT NULL,
    inactive_at timestamp(6) without time zone,
    role_type public.role_type NOT NULL,
    employee_id character varying,
    organization_id bigint NOT NULL
);


--
-- Name: roles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.roles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.roles_id_seq OWNED BY public.roles.id;


--
-- Name: routes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.routes (
    id bigint NOT NULL,
    name character varying,
    crew_lead_id bigint,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    branch_id bigint,
    create_grouped_pro_pays boolean,
    organization_id bigint,
    manager_id bigint
);


--
-- Name: routes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.routes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: routes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.routes_id_seq OWNED BY public.routes.id;


--
-- Name: schedule_visits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schedule_visits (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    route_id bigint NOT NULL,
    milestone_id bigint NOT NULL,
    hours double precision,
    scheduled_date timestamp(6) without time zone,
    remote_id integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: schedule_visits_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.schedule_visits_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: schedule_visits_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.schedule_visits_id_seq OWNED BY public.schedule_visits.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: service_materials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.service_materials (
    id bigint NOT NULL,
    service_id bigint NOT NULL,
    catalog_item_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: service_materials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.service_materials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: service_materials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.service_materials_id_seq OWNED BY public.service_materials.id;


--
-- Name: services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.services (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    organization_id bigint,
    name character varying,
    display_name character varying,
    service_type character varying,
    progress_type character varying DEFAULT 'percentage'::character varying NOT NULL
);


--
-- Name: services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.services_id_seq OWNED BY public.services.id;


--
-- Name: sign_in_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sign_in_tokens (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    organization_id bigint
);


--
-- Name: sign_in_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sign_in_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sign_in_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sign_in_tokens_id_seq OWNED BY public.sign_in_tokens.id;


--
-- Name: statement_line_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.statement_line_items (
    id bigint NOT NULL,
    identity_id bigint NOT NULL,
    statement_id bigint,
    line_item_category_id bigint,
    pro_pay_id bigint,
    amount_cents integer DEFAULT 0 NOT NULL,
    amount_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    held_days integer,
    held_indefinitely boolean DEFAULT false,
    source_note character varying,
    category_note character varying,
    status public.statement_line_item_status DEFAULT 'in_process'::public.statement_line_item_status NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    job_id bigint,
    organization_id bigint NOT NULL,
    code integer NOT NULL
);


--
-- Name: statement_line_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.statement_line_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: statement_line_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.statement_line_items_id_seq OWNED BY public.statement_line_items.id;


--
-- Name: statements; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.statements (
    id bigint NOT NULL,
    pay_period_id bigint NOT NULL,
    net_amount_cents integer DEFAULT 0 NOT NULL,
    net_amount_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    bonuses_cents integer DEFAULT 0 NOT NULL,
    bonuses_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    deductions_cents integer DEFAULT 0 NOT NULL,
    deductions_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    previous_balance_cents integer DEFAULT 0 NOT NULL,
    previous_balance_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    overtime_cents integer DEFAULT 0 NOT NULL,
    overtime_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    omit_previous_balance boolean DEFAULT false,
    status public.statement_status DEFAULT 'open'::public.statement_status NOT NULL,
    payment_date timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    identity_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    code integer NOT NULL
);


--
-- Name: statements_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.statements_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: statements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.statements_id_seq OWNED BY public.statements.id;


--
-- Name: sub_pro_pay_line_item_details; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sub_pro_pay_line_item_details (
    identity_id bigint,
    sub_pro_pay_id bigint,
    crew_lead boolean,
    duration_adjustment_seconds integer DEFAULT 0 NOT NULL,
    bonus_percent_hundredths integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    labor_cost_adjustment_cents integer DEFAULT 0 NOT NULL,
    labor_cost_adjustment_currency character varying DEFAULT 'USD'::character varying NOT NULL
);


--
-- Name: sub_pro_pay_pro_payables; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sub_pro_pay_pro_payables (
    id bigint NOT NULL,
    sub_pro_pay_id bigint NOT NULL,
    pro_payable_type character varying NOT NULL,
    pro_payable_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: sub_pro_pays; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sub_pro_pays (
    id bigint NOT NULL,
    overtime boolean,
    overtime_difference boolean,
    budget_currency_code character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    budget_type public.pro_pay_budget_type,
    distribution_type public.pro_pay_distribution_type,
    status public.pro_pay_status DEFAULT 'draft'::public.pro_pay_status,
    budget_minutes integer DEFAULT 0 NOT NULL,
    quantity_budgeted numeric DEFAULT 0.0 NOT NULL,
    crew_lead_bonus_rate numeric(5,4),
    budget_cents integer DEFAULT 0 NOT NULL,
    budget_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    budget_rate_cents integer DEFAULT 0 NOT NULL,
    budget_rate_currency character varying DEFAULT 'USD'::character varying NOT NULL,
    quantity_complete numeric DEFAULT 0.0 NOT NULL,
    reference_bonus_pool_id bigint,
    company_percent_hundredths integer DEFAULT 0 NOT NULL,
    crew_lead_percent_hundredths integer DEFAULT 0 NOT NULL,
    manager_percent_hundredths integer DEFAULT 0 NOT NULL,
    other_percent_hundredths integer DEFAULT 0 NOT NULL,
    crew_percent_hundredths integer DEFAULT 10000 NOT NULL,
    crew_retention_percent_hundredths integer DEFAULT 0 NOT NULL,
    pro_pay_id bigint
);


--
-- Name: sub_pro_pay_work_totals; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.sub_pro_pay_work_totals AS
 SELECT sub_pro_pays.id AS sub_pro_pay_id,
    identities.crew_lead,
    identities.id AS identity_id,
    sum(milestone_times.duration_seconds) AS duration_seconds,
    sum(milestone_times.labor_cost_cents) AS labor_cost_cents,
    milestone_times.labor_cost_currency
   FROM (((((public.milestone_times
     JOIN public.milestones ON ((milestone_times.milestone_id = milestones.id)))
     JOIN public.jobs ON ((jobs.id = milestones.job_id)))
     JOIN public.sub_pro_pay_pro_payables ON ((((milestones.id = sub_pro_pay_pro_payables.pro_payable_id) AND ((sub_pro_pay_pro_payables.pro_payable_type)::text = 'Milestone'::text)) OR ((jobs.id = sub_pro_pay_pro_payables.pro_payable_id) AND ((sub_pro_pay_pro_payables.pro_payable_type)::text = 'Job'::text)))))
     JOIN public.sub_pro_pays ON ((sub_pro_pays.id = sub_pro_pay_pro_payables.sub_pro_pay_id)))
     JOIN public.identities ON ((identities.id = milestone_times.identity_id)))
  GROUP BY identities.crew_lead, identities.id, sub_pro_pays.id, milestone_times.labor_cost_currency;


--
-- Name: sub_pro_pay_line_items; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.sub_pro_pay_line_items AS
 SELECT spplid.identity_id,
        CASE
            WHEN (spplid.crew_lead IS NOT NULL) THEN spplid.crew_lead
            ELSE sppwt.crew_lead
        END AS crew_lead,
    spplid.sub_pro_pay_id,
    (COALESCE(sppwt.duration_seconds, (0)::bigint) + spplid.duration_adjustment_seconds) AS duration_seconds,
    spplid.duration_adjustment_seconds,
    sppwt.duration_seconds AS duration_seconds_entered,
    (COALESCE(sppwt.labor_cost_cents, (0)::bigint) + spplid.labor_cost_adjustment_cents) AS labor_cost_cents,
    sppwt.labor_cost_currency,
    spplid.labor_cost_adjustment_cents,
    sppwt.labor_cost_cents AS labor_cost_entered_cents,
    sppwt.labor_cost_currency AS labor_cost_entered_currency,
    spplid.bonus_percent_hundredths,
    spplid.created_at,
    spplid.updated_at
   FROM (public.sub_pro_pay_line_item_details spplid
     LEFT JOIN public.sub_pro_pay_work_totals sppwt ON (((sppwt.identity_id = spplid.identity_id) AND (sppwt.sub_pro_pay_id = spplid.sub_pro_pay_id) AND ((sppwt.labor_cost_currency)::text = (spplid.labor_cost_adjustment_currency)::text))))
UNION
 SELECT sppwt.identity_id,
        CASE
            WHEN (spplid.crew_lead IS NOT NULL) THEN spplid.crew_lead
            ELSE sppwt.crew_lead
        END AS crew_lead,
    sppwt.sub_pro_pay_id,
    (COALESCE(sppwt.duration_seconds, (0)::bigint) + COALESCE(spplid.duration_adjustment_seconds, 0)) AS duration_seconds,
    spplid.duration_adjustment_seconds,
    sppwt.duration_seconds AS duration_seconds_entered,
    (COALESCE(sppwt.labor_cost_cents, (0)::bigint) + COALESCE(spplid.labor_cost_adjustment_cents, 0)) AS labor_cost_cents,
    sppwt.labor_cost_currency,
    spplid.labor_cost_adjustment_cents,
    sppwt.labor_cost_cents AS labor_cost_entered_cents,
    sppwt.labor_cost_currency AS labor_cost_entered_currency,
    spplid.bonus_percent_hundredths,
    spplid.created_at,
    spplid.updated_at
   FROM (public.sub_pro_pay_work_totals sppwt
     LEFT JOIN public.sub_pro_pay_line_item_details spplid ON (((sppwt.identity_id = spplid.identity_id) AND (sppwt.sub_pro_pay_id = spplid.sub_pro_pay_id) AND ((sppwt.labor_cost_currency)::text = (spplid.labor_cost_adjustment_currency)::text))));


--
-- Name: sub_pro_pay_calculated_totals; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.sub_pro_pay_calculated_totals AS
 SELECT sub_pro_pay_id,
    (sum(labor_cost_cents))::integer AS total_labor_cost_cents,
    labor_cost_currency AS total_labor_cost_currency,
    sum(
        CASE
            WHEN crew_lead THEN labor_cost_cents
            ELSE (0)::bigint
        END) AS total_crew_lead_labor_cost_cents,
    labor_cost_currency AS total_crew_lead_labor_cost_currency,
    sum((duration_seconds)::integer) AS total_duration_seconds,
    sum(
        CASE
            WHEN crew_lead THEN (duration_seconds)::integer
            ELSE 0
        END) AS total_crew_lead_duration_seconds
   FROM public.sub_pro_pay_line_items sppli
  GROUP BY sub_pro_pay_id, labor_cost_currency;


--
-- Name: sub_pro_pay_finalized_bonus_totals; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.sub_pro_pay_finalized_bonus_totals AS
 SELECT pspp.sub_pro_pay_id,
    pspp.status,
    bli.payable_id,
    bli.payable_type,
    (sum(bli.bonus_cents))::integer AS total_bonus_cents,
    bli.bonus_currency AS total_bonus_currency,
    bli.bonus_source
   FROM (public.bonus_line_items bli
     JOIN public.partial_sub_pro_pays pspp ON ((pspp.id = bli.partial_sub_pro_pay_id)))
  WHERE (pspp.status = 'finalized'::public.partial_pro_pay_status)
  GROUP BY pspp.sub_pro_pay_id, pspp.status, bli.payable_id, bli.payable_type, bli.bonus_currency, bli.bonus_source;


--
-- Name: sub_pro_pay_pro_payables_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sub_pro_pay_pro_payables_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sub_pro_pay_pro_payables_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sub_pro_pay_pro_payables_id_seq OWNED BY public.sub_pro_pay_pro_payables.id;


--
-- Name: sub_pro_pays_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sub_pro_pays_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sub_pro_pays_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sub_pro_pays_id_seq OWNED BY public.sub_pro_pays.id;


--
-- Name: sync_caches; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sync_caches (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    integration_id bigint NOT NULL,
    integration_record_id bigint,
    source character varying,
    remote_resource character varying,
    remote_primary_id character varying NOT NULL,
    remote_secondary_id character varying,
    raw_data jsonb
);


--
-- Name: sync_caches_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sync_caches_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sync_caches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sync_caches_id_seq OWNED BY public.sync_caches.id;


--
-- Name: sync_data; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sync_data (
    id bigint NOT NULL,
    record_type character varying,
    record_id bigint,
    integration_id bigint,
    synced_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    source character varying,
    remote_resource character varying,
    remote_id character varying
);


--
-- Name: sync_data_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sync_data_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sync_data_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sync_data_id_seq OWNED BY public.sync_data.id;


--
-- Name: sync_status_ranges; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sync_status_ranges (
    id bigint NOT NULL,
    integration_id bigint,
    resource character varying,
    start_timestamp timestamp(6) without time zone,
    end_timestamp timestamp(6) without time zone,
    start_remote_id integer,
    end_remote_id integer,
    last_sync_started_at timestamp(6) without time zone,
    last_synced_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: sync_status_ranges_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sync_status_ranges_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sync_status_ranges_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sync_status_ranges_id_seq OWNED BY public.sync_status_ranges.id;


--
-- Name: sync_statuses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sync_statuses (
    id bigint NOT NULL,
    integration_id bigint NOT NULL,
    resource character varying NOT NULL,
    last_synced_at timestamp(6) without time zone,
    last_sync_started_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    helper_model boolean DEFAULT false
);


--
-- Name: sync_statuses_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sync_statuses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sync_statuses_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sync_statuses_id_seq OWNED BY public.sync_statuses.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    email character varying NOT NULL,
    password_digest character varying NOT NULL,
    verified boolean DEFAULT false NOT NULL,
    admin boolean DEFAULT false,
    phone_country_code character varying,
    phone character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    name character varying NOT NULL,
    password_change_pending_verification boolean DEFAULT false NOT NULL,
    token_version integer DEFAULT 1 NOT NULL
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: wages; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.wages (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    job_id bigint NOT NULL,
    description text,
    period_seconds integer DEFAULT 3600 NOT NULL,
    amount_cents integer DEFAULT 0 NOT NULL,
    amount_currency character varying DEFAULT 'USD'::character varying NOT NULL
);


--
-- Name: wages_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.wages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: wages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.wages_id_seq OWNED BY public.wages.id;


--
-- Name: attendances id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendances ALTER COLUMN id SET DEFAULT nextval('public.attendances_id_seq'::regclass);


--
-- Name: bonus_line_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_line_items ALTER COLUMN id SET DEFAULT nextval('public.bonus_line_items_id_seq'::regclass);


--
-- Name: bonus_pool_payables id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_pool_payables ALTER COLUMN id SET DEFAULT nextval('public.bonus_pool_payables_id_seq'::regclass);


--
-- Name: bonus_pools id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_pools ALTER COLUMN id SET DEFAULT nextval('public.bonus_pools_id_seq'::regclass);


--
-- Name: branches id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.branches ALTER COLUMN id SET DEFAULT nextval('public.branches_id_seq'::regclass);


--
-- Name: catalog_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.catalog_items ALTER COLUMN id SET DEFAULT nextval('public.catalog_items_id_seq'::regclass);


--
-- Name: changesets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.changesets ALTER COLUMN id SET DEFAULT nextval('public.changesets_id_seq'::regclass);


--
-- Name: clock_times id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clock_times ALTER COLUMN id SET DEFAULT nextval('public.clock_times_id_seq'::regclass);


--
-- Name: companies id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies ALTER COLUMN id SET DEFAULT nextval('public.companies_id_seq'::regclass);


--
-- Name: company_defaults id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_defaults ALTER COLUMN id SET DEFAULT nextval('public.company_defaults_id_seq'::regclass);


--
-- Name: cost_codes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cost_codes ALTER COLUMN id SET DEFAULT nextval('public.cost_codes_id_seq'::regclass);


--
-- Name: credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credentials ALTER COLUMN id SET DEFAULT nextval('public.credentials_id_seq'::regclass);


--
-- Name: events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events ALTER COLUMN id SET DEFAULT nextval('public.events_id_seq'::regclass);


--
-- Name: identities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identities ALTER COLUMN id SET DEFAULT nextval('public.identities_id_seq'::regclass);


--
-- Name: integration_records id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integration_records ALTER COLUMN id SET DEFAULT nextval('public.integration_records_id_seq'::regclass);


--
-- Name: integrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations ALTER COLUMN id SET DEFAULT nextval('public.integrations_id_seq'::regclass);


--
-- Name: invitations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invitations ALTER COLUMN id SET DEFAULT nextval('public.invitations_id_seq'::regclass);


--
-- Name: invoice_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_types ALTER COLUMN id SET DEFAULT nextval('public.invoice_types_id_seq'::regclass);


--
-- Name: item_allocations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.item_allocations ALTER COLUMN id SET DEFAULT nextval('public.item_allocations_id_seq'::regclass);


--
-- Name: jobs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jobs ALTER COLUMN id SET DEFAULT nextval('public.jobs_id_seq'::regclass);


--
-- Name: labor_efficiency_benchmarks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_efficiency_benchmarks ALTER COLUMN id SET DEFAULT nextval('public.labor_efficiency_benchmarks_id_seq'::regclass);


--
-- Name: labor_metrics id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_metrics ALTER COLUMN id SET DEFAULT nextval('public.labor_metrics_id_seq'::regclass);


--
-- Name: line_item_categories id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_categories ALTER COLUMN id SET DEFAULT nextval('public.line_item_categories_id_seq'::regclass);


--
-- Name: milestone_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_items ALTER COLUMN id SET DEFAULT nextval('public.milestone_items_id_seq'::regclass);


--
-- Name: milestone_times id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_times ALTER COLUMN id SET DEFAULT nextval('public.milestone_times_id_seq'::regclass);


--
-- Name: milestones id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestones ALTER COLUMN id SET DEFAULT nextval('public.milestones_id_seq'::regclass);


--
-- Name: organizations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations ALTER COLUMN id SET DEFAULT nextval('public.organizations_id_seq'::regclass);


--
-- Name: partial_sub_pro_pays id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partial_sub_pro_pays ALTER COLUMN id SET DEFAULT nextval('public.partial_sub_pro_pays_id_seq'::regclass);


--
-- Name: pay_periods id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pay_periods ALTER COLUMN id SET DEFAULT nextval('public.pay_periods_id_seq'::regclass);


--
-- Name: payroll_schedules id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payroll_schedules ALTER COLUMN id SET DEFAULT nextval('public.payroll_schedules_id_seq'::regclass);


--
-- Name: payrolls id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payrolls ALTER COLUMN id SET DEFAULT nextval('public.payrolls_id_seq'::regclass);


--
-- Name: pro_pay_payables id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pay_payables ALTER COLUMN id SET DEFAULT nextval('public.pro_pay_payables_id_seq'::regclass);


--
-- Name: pro_pays id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays ALTER COLUMN id SET DEFAULT nextval('public.pro_pays_id_seq'::regclass);


--
-- Name: properties id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.properties ALTER COLUMN id SET DEFAULT nextval('public.properties_id_seq'::regclass);


--
-- Name: roles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles ALTER COLUMN id SET DEFAULT nextval('public.roles_id_seq'::regclass);


--
-- Name: routes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.routes ALTER COLUMN id SET DEFAULT nextval('public.routes_id_seq'::regclass);


--
-- Name: schedule_visits id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedule_visits ALTER COLUMN id SET DEFAULT nextval('public.schedule_visits_id_seq'::regclass);


--
-- Name: service_materials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_materials ALTER COLUMN id SET DEFAULT nextval('public.service_materials_id_seq'::regclass);


--
-- Name: services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.services ALTER COLUMN id SET DEFAULT nextval('public.services_id_seq'::regclass);


--
-- Name: sign_in_tokens id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sign_in_tokens ALTER COLUMN id SET DEFAULT nextval('public.sign_in_tokens_id_seq'::regclass);


--
-- Name: statement_line_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statement_line_items ALTER COLUMN id SET DEFAULT nextval('public.statement_line_items_id_seq'::regclass);


--
-- Name: statements id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statements ALTER COLUMN id SET DEFAULT nextval('public.statements_id_seq'::regclass);


--
-- Name: sub_pro_pay_pro_payables id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pay_pro_payables ALTER COLUMN id SET DEFAULT nextval('public.sub_pro_pay_pro_payables_id_seq'::regclass);


--
-- Name: sub_pro_pays id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pays ALTER COLUMN id SET DEFAULT nextval('public.sub_pro_pays_id_seq'::regclass);


--
-- Name: sync_caches id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_caches ALTER COLUMN id SET DEFAULT nextval('public.sync_caches_id_seq'::regclass);


--
-- Name: sync_data id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_data ALTER COLUMN id SET DEFAULT nextval('public.sync_data_id_seq'::regclass);


--
-- Name: sync_status_ranges id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_status_ranges ALTER COLUMN id SET DEFAULT nextval('public.sync_status_ranges_id_seq'::regclass);


--
-- Name: sync_statuses id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_statuses ALTER COLUMN id SET DEFAULT nextval('public.sync_statuses_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: wages id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.wages ALTER COLUMN id SET DEFAULT nextval('public.wages_id_seq'::regclass);


--
-- Name: ar_internal_metadata ar_internal_metadata_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ar_internal_metadata
    ADD CONSTRAINT ar_internal_metadata_pkey PRIMARY KEY (key);


--
-- Name: attendances attendances_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendances
    ADD CONSTRAINT attendances_pkey PRIMARY KEY (id);


--
-- Name: bonus_line_items bonus_line_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_line_items
    ADD CONSTRAINT bonus_line_items_pkey PRIMARY KEY (id);


--
-- Name: bonus_pool_payables bonus_pool_payables_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_pool_payables
    ADD CONSTRAINT bonus_pool_payables_pkey PRIMARY KEY (id);


--
-- Name: bonus_pools bonus_pools_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_pools
    ADD CONSTRAINT bonus_pools_pkey PRIMARY KEY (id);


--
-- Name: branches branches_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT branches_pkey PRIMARY KEY (id);


--
-- Name: catalog_items catalog_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.catalog_items
    ADD CONSTRAINT catalog_items_pkey PRIMARY KEY (id);


--
-- Name: changesets changesets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.changesets
    ADD CONSTRAINT changesets_pkey PRIMARY KEY (id);


--
-- Name: clock_times clock_times_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clock_times
    ADD CONSTRAINT clock_times_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: company_defaults company_defaults_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_defaults
    ADD CONSTRAINT company_defaults_pkey PRIMARY KEY (id);


--
-- Name: cost_codes cost_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cost_codes
    ADD CONSTRAINT cost_codes_pkey PRIMARY KEY (id);


--
-- Name: credentials credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credentials
    ADD CONSTRAINT credentials_pkey PRIMARY KEY (id);


--
-- Name: events events_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: integration_records integration_records_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integration_records
    ADD CONSTRAINT integration_records_pkey PRIMARY KEY (id);


--
-- Name: integrations integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT integrations_pkey PRIMARY KEY (id);


--
-- Name: invitations invitations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invitations
    ADD CONSTRAINT invitations_pkey PRIMARY KEY (id);


--
-- Name: invoice_types invoice_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_types
    ADD CONSTRAINT invoice_types_pkey PRIMARY KEY (id);


--
-- Name: item_allocations item_allocations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.item_allocations
    ADD CONSTRAINT item_allocations_pkey PRIMARY KEY (id);


--
-- Name: jobs jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT jobs_pkey PRIMARY KEY (id);


--
-- Name: labor_efficiency_benchmarks labor_efficiency_benchmarks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_efficiency_benchmarks
    ADD CONSTRAINT labor_efficiency_benchmarks_pkey PRIMARY KEY (id);


--
-- Name: labor_metrics labor_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_metrics
    ADD CONSTRAINT labor_metrics_pkey PRIMARY KEY (id);


--
-- Name: line_item_categories line_item_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_categories
    ADD CONSTRAINT line_item_categories_pkey PRIMARY KEY (id);


--
-- Name: milestone_items milestone_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_items
    ADD CONSTRAINT milestone_items_pkey PRIMARY KEY (id);


--
-- Name: milestone_times milestone_times_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_times
    ADD CONSTRAINT milestone_times_pkey PRIMARY KEY (id);


--
-- Name: milestones milestones_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestones
    ADD CONSTRAINT milestones_pkey PRIMARY KEY (id);


--
-- Name: organizations organizations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);


--
-- Name: partial_sub_pro_pays partial_sub_pro_pays_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partial_sub_pro_pays
    ADD CONSTRAINT partial_sub_pro_pays_pkey PRIMARY KEY (id);


--
-- Name: pay_periods pay_periods_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pay_periods
    ADD CONSTRAINT pay_periods_pkey PRIMARY KEY (id);


--
-- Name: payroll_schedules payroll_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payroll_schedules
    ADD CONSTRAINT payroll_schedules_pkey PRIMARY KEY (id);


--
-- Name: payrolls payrolls_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payrolls
    ADD CONSTRAINT payrolls_pkey PRIMARY KEY (id);


--
-- Name: pro_pay_payables pro_pay_payables_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pay_payables
    ADD CONSTRAINT pro_pay_payables_pkey PRIMARY KEY (id);


--
-- Name: pro_pays pro_pays_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT pro_pays_pkey PRIMARY KEY (id);


--
-- Name: properties properties_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.properties
    ADD CONSTRAINT properties_pkey PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: routes routes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.routes
    ADD CONSTRAINT routes_pkey PRIMARY KEY (id);


--
-- Name: schedule_visits schedule_visits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedule_visits
    ADD CONSTRAINT schedule_visits_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: service_materials service_materials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_materials
    ADD CONSTRAINT service_materials_pkey PRIMARY KEY (id);


--
-- Name: services services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_pkey PRIMARY KEY (id);


--
-- Name: sign_in_tokens sign_in_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sign_in_tokens
    ADD CONSTRAINT sign_in_tokens_pkey PRIMARY KEY (id);


--
-- Name: statement_line_items statement_line_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statement_line_items
    ADD CONSTRAINT statement_line_items_pkey PRIMARY KEY (id);


--
-- Name: statements statements_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statements
    ADD CONSTRAINT statements_pkey PRIMARY KEY (id);


--
-- Name: sub_pro_pay_pro_payables sub_pro_pay_pro_payables_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pay_pro_payables
    ADD CONSTRAINT sub_pro_pay_pro_payables_pkey PRIMARY KEY (id);


--
-- Name: sub_pro_pays sub_pro_pays_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pays
    ADD CONSTRAINT sub_pro_pays_pkey PRIMARY KEY (id);


--
-- Name: sync_caches sync_caches_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_caches
    ADD CONSTRAINT sync_caches_pkey PRIMARY KEY (id);


--
-- Name: sync_data sync_data_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_data
    ADD CONSTRAINT sync_data_pkey PRIMARY KEY (id);


--
-- Name: sync_status_ranges sync_status_ranges_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_status_ranges
    ADD CONSTRAINT sync_status_ranges_pkey PRIMARY KEY (id);


--
-- Name: sync_statuses sync_statuses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_statuses
    ADD CONSTRAINT sync_statuses_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: wages wages_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.wages
    ADD CONSTRAINT wages_pkey PRIMARY KEY (id);


--
-- Name: idx_on_identity_id_sub_pro_pay_id_2277e0b32b; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_identity_id_sub_pro_pay_id_2277e0b32b ON public.sub_pro_pay_line_item_details USING btree (identity_id, sub_pro_pay_id);


--
-- Name: idx_on_pro_pay_id_payable_id_payable_type_rate_clas_84ddfe4243; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_pro_pay_id_payable_id_payable_type_rate_clas_84ddfe4243 ON public.pro_pay_payables USING btree (pro_pay_id, payable_id, payable_type, rate_class);


--
-- Name: idx_on_user_id_organization_id_role_type_active_11c915c830; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_user_id_organization_id_role_type_active_11c915c830 ON public.roles USING btree (user_id, organization_id, role_type, active);


--
-- Name: index_attendances_on_created_by_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_attendances_on_created_by_user_id ON public.attendances USING btree (created_by_user_id);


--
-- Name: index_attendances_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_attendances_on_identity_id ON public.attendances USING btree (identity_id);


--
-- Name: index_attendances_on_manager_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_attendances_on_manager_id ON public.attendances USING btree (manager_id);


--
-- Name: index_attendances_on_milestone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_attendances_on_milestone_id ON public.attendances USING btree (milestone_id);


--
-- Name: index_attendances_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_attendances_on_organization_id ON public.attendances USING btree (organization_id);


--
-- Name: index_attendances_on_route_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_attendances_on_route_id ON public.attendances USING btree (route_id);


--
-- Name: index_bonus_line_items_on_partial_sub_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_bonus_line_items_on_partial_sub_pro_pay_id ON public.bonus_line_items USING btree (partial_sub_pro_pay_id);


--
-- Name: index_bonus_line_items_on_payable; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_bonus_line_items_on_payable ON public.bonus_line_items USING btree (payable_type, payable_id);


--
-- Name: index_bonus_pool_payables_on_bonus_pool_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_bonus_pool_payables_on_bonus_pool_id ON public.bonus_pool_payables USING btree (bonus_pool_id);


--
-- Name: index_bonus_pool_payables_on_payable; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_bonus_pool_payables_on_payable ON public.bonus_pool_payables USING btree (payable_type, payable_id);


--
-- Name: index_bonus_pools_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_bonus_pools_on_organization_id ON public.bonus_pools USING btree (organization_id);


--
-- Name: index_branches_on_allow_grouped_pro_pay_creation; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_branches_on_allow_grouped_pro_pay_creation ON public.branches USING btree (allow_grouped_pro_pay_creation);


--
-- Name: index_branches_on_bonus_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_branches_on_bonus_schedule_id ON public.branches USING btree (bonus_schedule_id);


--
-- Name: index_branches_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_branches_on_organization_id ON public.branches USING btree (organization_id);


--
-- Name: index_branches_on_payroll_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_branches_on_payroll_schedule_id ON public.branches USING btree (payroll_schedule_id);


--
-- Name: index_branches_on_route_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_branches_on_route_schedule_id ON public.branches USING btree (route_schedule_id);


--
-- Name: index_catalog_items_on_item_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_catalog_items_on_item_type ON public.catalog_items USING btree (item_type);


--
-- Name: index_catalog_items_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_catalog_items_on_organization_id ON public.catalog_items USING btree (organization_id);


--
-- Name: index_changesets_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_changesets_on_integration_id ON public.changesets USING btree (integration_id);


--
-- Name: index_changesets_on_integration_id_and_remote_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_changesets_on_integration_id_and_remote_slug ON public.changesets USING btree (integration_id, remote_slug);


--
-- Name: index_changesets_on_record; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_changesets_on_record ON public.changesets USING btree (record_type, record_id);


--
-- Name: index_clock_times_on_accepted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_clock_times_on_accepted_at ON public.clock_times USING btree (accepted_at);


--
-- Name: index_clock_times_on_end_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_clock_times_on_end_at ON public.clock_times USING btree (end_at);


--
-- Name: index_clock_times_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_clock_times_on_identity_id ON public.clock_times USING btree (identity_id);


--
-- Name: index_clock_times_on_start_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_clock_times_on_start_at ON public.clock_times USING btree (start_at);


--
-- Name: index_company_defaults_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_company_defaults_on_company_id ON public.company_defaults USING btree (company_id);


--
-- Name: index_cost_codes_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_cost_codes_on_company_id ON public.cost_codes USING btree (company_id);


--
-- Name: index_credentials_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_credentials_on_integration_id ON public.credentials USING btree (integration_id);


--
-- Name: index_events_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_events_on_user_id ON public.events USING btree (user_id);


--
-- Name: index_identities_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_identities_on_email ON public.identities USING btree (email);


--
-- Name: index_identities_on_employee_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_identities_on_employee_id ON public.identities USING btree (employee_id);


--
-- Name: index_identities_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_identities_on_organization_id ON public.identities USING btree (organization_id);


--
-- Name: index_identities_on_other_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_identities_on_other_id ON public.identities USING btree (other_id);


--
-- Name: index_identities_on_phone_number; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_identities_on_phone_number ON public.identities USING btree (phone_number);


--
-- Name: index_identities_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_identities_on_user_id ON public.identities USING btree (user_id);


--
-- Name: index_identities_on_user_id_and_primary; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_identities_on_user_id_and_primary ON public.identities USING btree (user_id, "primary");


--
-- Name: index_integration_records_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integration_records_on_integration_id ON public.integration_records USING btree (integration_id);


--
-- Name: index_integration_records_on_integration_id_and_remote_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_integration_records_on_integration_id_and_remote_slug ON public.integration_records USING btree (integration_id, remote_slug);


--
-- Name: index_integration_records_on_record; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integration_records_on_record ON public.integration_records USING btree (record_type, record_id);


--
-- Name: index_integrations_on_auto_sync; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_auto_sync ON public.integrations USING btree (auto_sync);


--
-- Name: index_integrations_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_organization_id ON public.integrations USING btree (organization_id);


--
-- Name: index_integrations_on_organization_id_and_source; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_integrations_on_organization_id_and_source ON public.integrations USING btree (organization_id, source);


--
-- Name: index_integrations_on_sync_interval; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_sync_interval ON public.integrations USING btree (sync_interval);


--
-- Name: index_invitations_on_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invitations_on_deleted_at ON public.invitations USING btree (deleted_at);


--
-- Name: index_invitations_on_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invitations_on_expires_at ON public.invitations USING btree (expires_at);


--
-- Name: index_invitations_on_invited_by_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invitations_on_invited_by_id ON public.invitations USING btree (invited_by_id);


--
-- Name: index_invitations_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invitations_on_organization_id ON public.invitations USING btree (organization_id);


--
-- Name: index_invitations_on_organization_id_and_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invitations_on_organization_id_and_email ON public.invitations USING btree (organization_id, email) WHERE (deleted_at IS NULL);


--
-- Name: index_invitations_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invitations_on_user_id ON public.invitations USING btree (user_id);


--
-- Name: index_invitations_on_user_id_and_organization_id_active; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_invitations_on_user_id_and_organization_id_active ON public.invitations USING btree (user_id, organization_id) WHERE (deleted_at IS NULL);


--
-- Name: index_invoice_types_on_name_and_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_invoice_types_on_name_and_organization_id ON public.invoice_types USING btree (name, organization_id);


--
-- Name: index_invoice_types_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_invoice_types_on_organization_id ON public.invoice_types USING btree (organization_id);


--
-- Name: index_item_allocations_on_catalog_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_item_allocations_on_catalog_item_id ON public.item_allocations USING btree (catalog_item_id);


--
-- Name: index_item_allocations_on_milestone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_item_allocations_on_milestone_id ON public.item_allocations USING btree (milestone_id);


--
-- Name: index_item_allocations_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_item_allocations_on_organization_id ON public.item_allocations USING btree (organization_id);


--
-- Name: index_jobs_on_branch_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_branch_id ON public.jobs USING btree (branch_id);


--
-- Name: index_jobs_on_invoice_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_invoice_type_id ON public.jobs USING btree (invoice_type_id);


--
-- Name: index_jobs_on_last_activity_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_last_activity_at ON public.jobs USING btree (last_activity_at);


--
-- Name: index_jobs_on_manager_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_manager_id ON public.jobs USING btree (manager_id);


--
-- Name: index_jobs_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_organization_id ON public.jobs USING btree (organization_id);


--
-- Name: index_jobs_on_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_property_id ON public.jobs USING btree (property_id);


--
-- Name: index_jobs_on_remote_reference; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_jobs_on_remote_reference ON public.jobs USING btree (remote_reference);


--
-- Name: index_labor_efficiency_benchmarks_on_level; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_efficiency_benchmarks_on_level ON public.labor_efficiency_benchmarks USING btree (level);


--
-- Name: index_labor_efficiency_benchmarks_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_efficiency_benchmarks_on_organization_id ON public.labor_efficiency_benchmarks USING btree (organization_id);


--
-- Name: index_labor_efficiency_benchmarks_uniqueness; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_labor_efficiency_benchmarks_uniqueness ON public.labor_efficiency_benchmarks USING btree (organization_id, level, level_record_id, active);


--
-- Name: index_labor_metrics_on_has_time_discrepancy; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_metrics_on_has_time_discrepancy ON public.labor_metrics USING btree (has_time_discrepancy);


--
-- Name: index_labor_metrics_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_metrics_on_identity_id ON public.labor_metrics USING btree (identity_id);


--
-- Name: index_labor_metrics_on_milestone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_metrics_on_milestone_id ON public.labor_metrics USING btree (milestone_id);


--
-- Name: index_labor_metrics_on_org_identity_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_metrics_on_org_identity_date ON public.labor_metrics USING btree (organization_id, identity_id, date);


--
-- Name: index_labor_metrics_on_org_identity_milestone_date; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_labor_metrics_on_org_identity_milestone_date ON public.labor_metrics USING btree (organization_id, identity_id, milestone_id, date);


--
-- Name: index_labor_metrics_on_org_milestone_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_metrics_on_org_milestone_date ON public.labor_metrics USING btree (organization_id, milestone_id, date);


--
-- Name: index_labor_metrics_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_labor_metrics_on_organization_id ON public.labor_metrics USING btree (organization_id);


--
-- Name: index_milestone_items_on_catalog_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_items_on_catalog_item_id ON public.milestone_items USING btree (catalog_item_id);


--
-- Name: index_milestone_items_on_milestone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_items_on_milestone_id ON public.milestone_items USING btree (milestone_id);


--
-- Name: index_milestone_items_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_items_on_organization_id ON public.milestone_items USING btree (organization_id);


--
-- Name: index_milestone_times_on_accepted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_times_on_accepted_at ON public.milestone_times USING btree (accepted_at);


--
-- Name: index_milestone_times_on_approved_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_times_on_approved_at ON public.milestone_times USING btree (approved_at);


--
-- Name: index_milestone_times_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_times_on_identity_id ON public.milestone_times USING btree (identity_id);


--
-- Name: index_milestone_times_on_labor_cost_currency; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_times_on_labor_cost_currency ON public.milestone_times USING btree (labor_cost_currency);


--
-- Name: index_milestone_times_on_milestone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_times_on_milestone_id ON public.milestone_times USING btree (milestone_id);


--
-- Name: index_milestone_times_on_route_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestone_times_on_route_id ON public.milestone_times USING btree (route_id);


--
-- Name: index_milestones_on_invoice_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestones_on_invoice_type_id ON public.milestones USING btree (invoice_type_id);


--
-- Name: index_milestones_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestones_on_job_id ON public.milestones USING btree (job_id);


--
-- Name: index_milestones_on_remote_reference; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestones_on_remote_reference ON public.milestones USING btree (remote_reference);


--
-- Name: index_milestones_on_service_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestones_on_service_id ON public.milestones USING btree (service_id);


--
-- Name: index_milestones_on_tracked_milestone_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_milestones_on_tracked_milestone_item_id ON public.milestones USING btree (tracked_milestone_item_id);


--
-- Name: index_organizations_on_default_bonus_pool_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_organizations_on_default_bonus_pool_id ON public.organizations USING btree (default_bonus_pool_id);


--
-- Name: index_organizations_on_default_bonus_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_organizations_on_default_bonus_schedule_id ON public.organizations USING btree (default_bonus_schedule_id);


--
-- Name: index_organizations_on_default_payroll_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_organizations_on_default_payroll_schedule_id ON public.organizations USING btree (default_payroll_schedule_id);


--
-- Name: index_organizations_on_default_route_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_organizations_on_default_route_schedule_id ON public.organizations USING btree (default_route_schedule_id);


--
-- Name: index_partial_sub_pro_pays_on_sub_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_partial_sub_pro_pays_on_sub_pro_pay_id ON public.partial_sub_pro_pays USING btree (sub_pro_pay_id);


--
-- Name: index_pay_periods_on_branch_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pay_periods_on_branch_id ON public.pay_periods USING btree (branch_id);


--
-- Name: index_pay_periods_on_end_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pay_periods_on_end_time ON public.pay_periods USING btree (end_time);


--
-- Name: index_pay_periods_on_open; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pay_periods_on_open ON public.pay_periods USING btree (open);


--
-- Name: index_pay_periods_on_payroll_schedule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pay_periods_on_payroll_schedule_id ON public.pay_periods USING btree (payroll_schedule_id);


--
-- Name: index_pay_periods_on_start_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pay_periods_on_start_time ON public.pay_periods USING btree (start_time);


--
-- Name: index_payroll_schedules_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_payroll_schedules_on_organization_id ON public.payroll_schedules USING btree (organization_id);


--
-- Name: index_payrolls_on_pay_period_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_payrolls_on_pay_period_id ON public.payrolls USING btree (pay_period_id);


--
-- Name: index_pro_pay_payables_on_payable; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pay_payables_on_payable ON public.pro_pay_payables USING btree (payable_type, payable_id);


--
-- Name: index_pro_pay_payables_on_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pay_payables_on_pro_pay_id ON public.pro_pay_payables USING btree (pro_pay_id);


--
-- Name: index_pro_pays_on_manager_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pays_on_manager_id ON public.pro_pays USING btree (manager_id);


--
-- Name: index_pro_pays_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pays_on_organization_id ON public.pro_pays USING btree (organization_id);


--
-- Name: index_pro_pays_on_organization_id_and_code; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_pro_pays_on_organization_id_and_code ON public.pro_pays USING btree (organization_id, code);


--
-- Name: index_pro_pays_on_reference_bonus_pool_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pays_on_reference_bonus_pool_id ON public.pro_pays USING btree (reference_bonus_pool_id);


--
-- Name: index_pro_pays_on_source_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pays_on_source_job_id ON public.pro_pays USING btree (source_job_id);


--
-- Name: index_pro_pays_on_source_route_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pays_on_source_route_id ON public.pro_pays USING btree (source_route_id);


--
-- Name: index_pro_pays_on_source_route_pay_period_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_pro_pays_on_source_route_pay_period_id ON public.pro_pays USING btree (source_route_pay_period_id);


--
-- Name: index_properties_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_properties_on_organization_id ON public.properties USING btree (organization_id);


--
-- Name: index_properties_on_primary_contact_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_properties_on_primary_contact_id ON public.properties USING btree (primary_contact_id);


--
-- Name: index_roles_on_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_roles_on_active ON public.roles USING btree (active);


--
-- Name: index_roles_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_roles_on_organization_id ON public.roles USING btree (organization_id);


--
-- Name: index_roles_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_roles_on_user_id ON public.roles USING btree (user_id);


--
-- Name: index_routes_on_branch_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_routes_on_branch_id ON public.routes USING btree (branch_id);


--
-- Name: index_routes_on_create_grouped_pro_pays; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_routes_on_create_grouped_pro_pays ON public.routes USING btree (create_grouped_pro_pays);


--
-- Name: index_routes_on_crew_lead_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_routes_on_crew_lead_id ON public.routes USING btree (crew_lead_id);


--
-- Name: index_routes_on_manager_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_routes_on_manager_id ON public.routes USING btree (manager_id);


--
-- Name: index_routes_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_routes_on_organization_id ON public.routes USING btree (organization_id);


--
-- Name: index_schedule_visits_on_milestone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_schedule_visits_on_milestone_id ON public.schedule_visits USING btree (milestone_id);


--
-- Name: index_schedule_visits_on_org_route_milestone; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_schedule_visits_on_org_route_milestone ON public.schedule_visits USING btree (organization_id, route_id, milestone_id);


--
-- Name: index_schedule_visits_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_schedule_visits_on_organization_id ON public.schedule_visits USING btree (organization_id);


--
-- Name: index_schedule_visits_on_remote_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_schedule_visits_on_remote_id ON public.schedule_visits USING btree (remote_id);


--
-- Name: index_schedule_visits_on_route_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_schedule_visits_on_route_id ON public.schedule_visits USING btree (route_id);


--
-- Name: index_service_materials_on_catalog_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_service_materials_on_catalog_item_id ON public.service_materials USING btree (catalog_item_id);


--
-- Name: index_service_materials_on_service_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_service_materials_on_service_id ON public.service_materials USING btree (service_id);


--
-- Name: index_service_materials_on_service_id_and_catalog_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_service_materials_on_service_id_and_catalog_item_id ON public.service_materials USING btree (service_id, catalog_item_id);


--
-- Name: index_services_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_services_on_organization_id ON public.services USING btree (organization_id);


--
-- Name: index_sign_in_tokens_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sign_in_tokens_on_organization_id ON public.sign_in_tokens USING btree (organization_id);


--
-- Name: index_sign_in_tokens_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sign_in_tokens_on_user_id ON public.sign_in_tokens USING btree (user_id);


--
-- Name: index_statement_line_items_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statement_line_items_on_identity_id ON public.statement_line_items USING btree (identity_id);


--
-- Name: index_statement_line_items_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statement_line_items_on_job_id ON public.statement_line_items USING btree (job_id);


--
-- Name: index_statement_line_items_on_line_item_category_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statement_line_items_on_line_item_category_id ON public.statement_line_items USING btree (line_item_category_id);


--
-- Name: index_statement_line_items_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statement_line_items_on_organization_id ON public.statement_line_items USING btree (organization_id);


--
-- Name: index_statement_line_items_on_organization_id_and_code; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_statement_line_items_on_organization_id_and_code ON public.statement_line_items USING btree (organization_id, code);


--
-- Name: index_statement_line_items_on_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statement_line_items_on_pro_pay_id ON public.statement_line_items USING btree (pro_pay_id);


--
-- Name: index_statement_line_items_on_statement_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statement_line_items_on_statement_id ON public.statement_line_items USING btree (statement_id);


--
-- Name: index_statements_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statements_on_identity_id ON public.statements USING btree (identity_id);


--
-- Name: index_statements_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statements_on_organization_id ON public.statements USING btree (organization_id);


--
-- Name: index_statements_on_organization_id_and_code; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_statements_on_organization_id_and_code ON public.statements USING btree (organization_id, code);


--
-- Name: index_statements_on_pay_period_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_statements_on_pay_period_id ON public.statements USING btree (pay_period_id);


--
-- Name: index_sub_pro_pay_line_item_details_on_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sub_pro_pay_line_item_details_on_identity_id ON public.sub_pro_pay_line_item_details USING btree (identity_id);


--
-- Name: index_sub_pro_pay_line_item_details_on_sub_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sub_pro_pay_line_item_details_on_sub_pro_pay_id ON public.sub_pro_pay_line_item_details USING btree (sub_pro_pay_id);


--
-- Name: index_sub_pro_pay_pro_payables_on_pro_payable; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sub_pro_pay_pro_payables_on_pro_payable ON public.sub_pro_pay_pro_payables USING btree (pro_payable_type, pro_payable_id);


--
-- Name: index_sub_pro_pay_pro_payables_on_sub_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sub_pro_pay_pro_payables_on_sub_pro_pay_id ON public.sub_pro_pay_pro_payables USING btree (sub_pro_pay_id);


--
-- Name: index_sub_pro_pays_on_pro_pay_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sub_pro_pays_on_pro_pay_id ON public.sub_pro_pays USING btree (pro_pay_id);


--
-- Name: index_sub_pro_pays_on_reference_bonus_pool_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sub_pro_pays_on_reference_bonus_pool_id ON public.sub_pro_pays USING btree (reference_bonus_pool_id);


--
-- Name: index_sync_caches_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_integration_id ON public.sync_caches USING btree (integration_id);


--
-- Name: index_sync_caches_on_integration_record_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_integration_record_id ON public.sync_caches USING btree (integration_record_id);


--
-- Name: index_sync_caches_on_master_opportunity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_master_opportunity_id ON public.sync_caches USING btree (((raw_data ->> 'MasterOpportunityID'::text)));


--
-- Name: index_sync_caches_on_opportunity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_opportunity_id ON public.sync_caches USING btree (((raw_data ->> 'OpportunityID'::text)));


--
-- Name: index_sync_caches_on_opportunity_number; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_opportunity_number ON public.sync_caches USING btree (((raw_data ->> 'OpportunityNumber'::text)));


--
-- Name: index_sync_caches_on_remote_resource_and_remote_primary_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_remote_resource_and_remote_primary_id ON public.sync_caches USING btree (remote_resource, remote_primary_id);


--
-- Name: index_sync_caches_on_remote_resource_and_remote_secondary_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_caches_on_remote_resource_and_remote_secondary_id ON public.sync_caches USING btree (remote_resource, remote_secondary_id);


--
-- Name: index_sync_data_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_data_on_integration_id ON public.sync_data USING btree (integration_id);


--
-- Name: index_sync_data_on_record; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_data_on_record ON public.sync_data USING btree (record_type, record_id);


--
-- Name: index_sync_data_on_source_and_remote_resource_and_remote_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_data_on_source_and_remote_resource_and_remote_id ON public.sync_data USING btree (source, remote_resource, remote_id);


--
-- Name: index_sync_status_ranges_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_status_ranges_on_integration_id ON public.sync_status_ranges USING btree (integration_id);


--
-- Name: index_sync_status_ranges_on_resource; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_status_ranges_on_resource ON public.sync_status_ranges USING btree (resource);


--
-- Name: index_sync_statuses_on_integration_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_statuses_on_integration_id ON public.sync_statuses USING btree (integration_id);


--
-- Name: index_sync_statuses_on_integration_id_and_resource; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_sync_statuses_on_integration_id_and_resource ON public.sync_statuses USING btree (integration_id, resource);


--
-- Name: index_users_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_email ON public.users USING btree (email);


--
-- Name: index_users_on_phone_country_code_and_phone; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_phone_country_code_and_phone ON public.users USING btree (phone_country_code, phone);


--
-- Name: index_wages_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_wages_on_job_id ON public.wages USING btree (job_id);


--
-- Name: organizations fk_rails_0c17ad1468; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT fk_rails_0c17ad1468 FOREIGN KEY (default_bonus_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: events fk_rails_0cb5590091; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT fk_rails_0cb5590091 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: branches fk_rails_0df96a2c11; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT fk_rails_0df96a2c11 FOREIGN KEY (route_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: milestone_times fk_rails_217e20df5b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_times
    ADD CONSTRAINT fk_rails_217e20df5b FOREIGN KEY (identity_id) REFERENCES public.identities(id);


--
-- Name: bonus_pool_payables fk_rails_21fdaae785; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_pool_payables
    ADD CONSTRAINT fk_rails_21fdaae785 FOREIGN KEY (bonus_pool_id) REFERENCES public.bonus_pools(id);


--
-- Name: identities fk_rails_2a1c1b4c10; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identities
    ADD CONSTRAINT fk_rails_2a1c1b4c10 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: attendances fk_rails_2db9a5417d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendances
    ADD CONSTRAINT fk_rails_2db9a5417d FOREIGN KEY (created_by_user_id) REFERENCES public.users(id);


--
-- Name: roles fk_rails_2f99738edd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT fk_rails_2f99738edd FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: branches fk_rails_310d94efd7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT fk_rails_310d94efd7 FOREIGN KEY (bonus_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: pro_pays fk_rails_33f2e90f7e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT fk_rails_33f2e90f7e FOREIGN KEY (source_route_pay_period_id) REFERENCES public.pay_periods(id);


--
-- Name: company_defaults fk_rails_351a4ceb40; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_defaults
    ADD CONSTRAINT fk_rails_351a4ceb40 FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: pro_pays fk_rails_35339e0be1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT fk_rails_35339e0be1 FOREIGN KEY (manager_id) REFERENCES public.identities(id);


--
-- Name: pay_periods fk_rails_35718676fc; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pay_periods
    ADD CONSTRAINT fk_rails_35718676fc FOREIGN KEY (branch_id) REFERENCES public.branches(id);


--
-- Name: labor_metrics fk_rails_383d375f73; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_metrics
    ADD CONSTRAINT fk_rails_383d375f73 FOREIGN KEY (identity_id) REFERENCES public.identities(id);


--
-- Name: branches fk_rails_3ea825b0e3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT fk_rails_3ea825b0e3 FOREIGN KEY (payroll_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: organizations fk_rails_480281c440; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT fk_rails_480281c440 FOREIGN KEY (default_route_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: sub_pro_pay_line_item_details fk_rails_4d2c2c195c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pay_line_item_details
    ADD CONSTRAINT fk_rails_4d2c2c195c FOREIGN KEY (identity_id) REFERENCES public.identities(id);


--
-- Name: bonus_line_items fk_rails_4e62cb1bf6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bonus_line_items
    ADD CONSTRAINT fk_rails_4e62cb1bf6 FOREIGN KEY (partial_sub_pro_pay_id) REFERENCES public.partial_sub_pro_pays(id);


--
-- Name: sub_pro_pays fk_rails_4e75ef78aa; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pays
    ADD CONSTRAINT fk_rails_4e75ef78aa FOREIGN KEY (reference_bonus_pool_id) REFERENCES public.bonus_pools(id);


--
-- Name: wages fk_rails_4e9cb8d409; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.wages
    ADD CONSTRAINT fk_rails_4e9cb8d409 FOREIGN KEY (job_id) REFERENCES public.jobs(id);


--
-- Name: sign_in_tokens fk_rails_4fe6cab6ce; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sign_in_tokens
    ADD CONSTRAINT fk_rails_4fe6cab6ce FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: identities fk_rails_5373344100; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identities
    ADD CONSTRAINT fk_rails_5373344100 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: credentials fk_rails_552666c571; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credentials
    ADD CONSTRAINT fk_rails_552666c571 FOREIGN KEY (integration_id) REFERENCES public.integrations(id);


--
-- Name: properties fk_rails_56bef12604; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.properties
    ADD CONSTRAINT fk_rails_56bef12604 FOREIGN KEY (primary_contact_id) REFERENCES public.identities(id);


--
-- Name: schedule_visits fk_rails_57c5f3f6c1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedule_visits
    ADD CONSTRAINT fk_rails_57c5f3f6c1 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: statements fk_rails_6223e62d67; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statements
    ADD CONSTRAINT fk_rails_6223e62d67 FOREIGN KEY (identity_id) REFERENCES public.identities(id);


--
-- Name: labor_metrics fk_rails_623f355b4a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_metrics
    ADD CONSTRAINT fk_rails_623f355b4a FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: pro_pays fk_rails_644aa3a6c1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT fk_rails_644aa3a6c1 FOREIGN KEY (source_job_id) REFERENCES public.jobs(id);


--
-- Name: payroll_schedules fk_rails_6786308254; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payroll_schedules
    ADD CONSTRAINT fk_rails_6786308254 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: labor_metrics fk_rails_6962293f00; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_metrics
    ADD CONSTRAINT fk_rails_6962293f00 FOREIGN KEY (milestone_id) REFERENCES public.milestones(id);


--
-- Name: schedule_visits fk_rails_6a509d35cb; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedule_visits
    ADD CONSTRAINT fk_rails_6a509d35cb FOREIGN KEY (route_id) REFERENCES public.routes(id);


--
-- Name: routes fk_rails_7283be9bcd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.routes
    ADD CONSTRAINT fk_rails_7283be9bcd FOREIGN KEY (crew_lead_id) REFERENCES public.identities(id);


--
-- Name: milestone_times fk_rails_73c5182c47; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_times
    ADD CONSTRAINT fk_rails_73c5182c47 FOREIGN KEY (milestone_id) REFERENCES public.milestones(id);


--
-- Name: integrations fk_rails_755d734f25; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT fk_rails_755d734f25 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: sub_pro_pay_pro_payables fk_rails_7c110f3f0a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pay_pro_payables
    ADD CONSTRAINT fk_rails_7c110f3f0a FOREIGN KEY (sub_pro_pay_id) REFERENCES public.sub_pro_pays(id);


--
-- Name: jobs fk_rails_82ba4b4cf5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT fk_rails_82ba4b4cf5 FOREIGN KEY (manager_id) REFERENCES public.identities(id);


--
-- Name: attendances fk_rails_8998b2baaf; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendances
    ADD CONSTRAINT fk_rails_8998b2baaf FOREIGN KEY (manager_id) REFERENCES public.identities(id);


--
-- Name: organizations fk_rails_8bc2705454; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT fk_rails_8bc2705454 FOREIGN KEY (default_payroll_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: pay_periods fk_rails_9365d867e6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pay_periods
    ADD CONSTRAINT fk_rails_9365d867e6 FOREIGN KEY (payroll_schedule_id) REFERENCES public.payroll_schedules(id);


--
-- Name: routes fk_rails_949ba94c0f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.routes
    ADD CONSTRAINT fk_rails_949ba94c0f FOREIGN KEY (manager_id) REFERENCES public.identities(id);


--
-- Name: statements fk_rails_97c6f283b6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statements
    ADD CONSTRAINT fk_rails_97c6f283b6 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: milestones fk_rails_a42fc736e6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestones
    ADD CONSTRAINT fk_rails_a42fc736e6 FOREIGN KEY (job_id) REFERENCES public.jobs(id);


--
-- Name: sign_in_tokens fk_rails_a9860dd74e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sign_in_tokens
    ADD CONSTRAINT fk_rails_a9860dd74e FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: roles fk_rails_ab35d699f0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT fk_rails_ab35d699f0 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: sub_pro_pay_line_item_details fk_rails_b9ebe6b683; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sub_pro_pay_line_item_details
    ADD CONSTRAINT fk_rails_b9ebe6b683 FOREIGN KEY (sub_pro_pay_id) REFERENCES public.sub_pro_pays(id);


--
-- Name: schedule_visits fk_rails_bbb5f47d0c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedule_visits
    ADD CONSTRAINT fk_rails_bbb5f47d0c FOREIGN KEY (milestone_id) REFERENCES public.milestones(id);


--
-- Name: routes fk_rails_bd97323ec0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.routes
    ADD CONSTRAINT fk_rails_bd97323ec0 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: pro_pays fk_rails_bed56ddc39; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT fk_rails_bed56ddc39 FOREIGN KEY (source_route_id) REFERENCES public.routes(id);


--
-- Name: service_materials fk_rails_c0f0908225; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_materials
    ADD CONSTRAINT fk_rails_c0f0908225 FOREIGN KEY (catalog_item_id) REFERENCES public.catalog_items(id);


--
-- Name: organizations fk_rails_c24b7dfd4b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT fk_rails_c24b7dfd4b FOREIGN KEY (default_bonus_pool_id) REFERENCES public.bonus_pools(id);


--
-- Name: labor_efficiency_benchmarks fk_rails_cec7d6c9c5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.labor_efficiency_benchmarks
    ADD CONSTRAINT fk_rails_cec7d6c9c5 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: jobs fk_rails_d0e6c661ce; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT fk_rails_d0e6c661ce FOREIGN KEY (invoice_type_id) REFERENCES public.invoice_types(id);


--
-- Name: invoice_types fk_rails_d5c6dbc951; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_types
    ADD CONSTRAINT fk_rails_d5c6dbc951 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: invitations fk_rails_d799c974a1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invitations
    ADD CONSTRAINT fk_rails_d799c974a1 FOREIGN KEY (invited_by_id) REFERENCES public.users(id);


--
-- Name: milestones fk_rails_df4eb455c6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestones
    ADD CONSTRAINT fk_rails_df4eb455c6 FOREIGN KEY (invoice_type_id) REFERENCES public.invoice_types(id);


--
-- Name: milestone_times fk_rails_ec4330b536; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestone_times
    ADD CONSTRAINT fk_rails_ec4330b536 FOREIGN KEY (route_id) REFERENCES public.routes(id);


--
-- Name: service_materials fk_rails_ed2ceca347; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_materials
    ADD CONSTRAINT fk_rails_ed2ceca347 FOREIGN KEY (service_id) REFERENCES public.services(id);


--
-- Name: pro_pays fk_rails_ed45708073; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT fk_rails_ed45708073 FOREIGN KEY (reference_bonus_pool_id) REFERENCES public.bonus_pools(id);


--
-- Name: statement_line_items fk_rails_ee16e48fda; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statement_line_items
    ADD CONSTRAINT fk_rails_ee16e48fda FOREIGN KEY (job_id) REFERENCES public.jobs(id);


--
-- Name: payrolls fk_rails_f0dc0a3173; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payrolls
    ADD CONSTRAINT fk_rails_f0dc0a3173 FOREIGN KEY (pay_period_id) REFERENCES public.pay_periods(id);


--
-- Name: milestones fk_rails_f12a4ecd78; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.milestones
    ADD CONSTRAINT fk_rails_f12a4ecd78 FOREIGN KEY (tracked_milestone_item_id) REFERENCES public.milestone_items(id);


--
-- Name: pro_pays fk_rails_f5d049d668; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pro_pays
    ADD CONSTRAINT fk_rails_f5d049d668 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: partial_sub_pro_pays fk_rails_fb26ec09f0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partial_sub_pro_pays
    ADD CONSTRAINT fk_rails_fb26ec09f0 FOREIGN KEY (sub_pro_pay_id) REFERENCES public.sub_pro_pays(id);


--
-- Name: statement_line_items fk_rails_fe0c5bedf3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.statement_line_items
    ADD CONSTRAINT fk_rails_fe0c5bedf3 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- PostgreSQL database dump complete
--

SET search_path TO "$user", public;

INSERT INTO "schema_migrations" (version) VALUES
('9'),
('8'),
('7'),
('6'),
('5'),
('4'),
('3'),
('20250605162214'),
('20250605162042'),
('20250605152206'),
('20250605144628'),
('20250602205148'),
('20250602144832'),
('20250527131643'),
('20250527044231'),
('20250523045905'),
('20250523040739'),
('20250523040701'),
('20250520163013'),
('20250518235757'),
('20250518231522'),
('20250512112258'),
('20250507171500'),
('20250507152639'),
('20250507060946'),
('20250507054806'),
('20250507014743'),
('20250505050949'),
('20250502160545'),
('20250502130517'),
('20250502103333'),
('20250502091439'),
('20250501194935'),
('20250501182109'),
('20250501180230'),
('20250501000000'),
('20250430173226'),
('20250430172617'),
('20250430170347'),
('20250429192627'),
('20250428164317'),
('20250428163630'),
('20250428141707'),
('20250425182352'),
('20250425171903'),
('20250425020000'),
('20250425010000'),
('20250424225428'),
('20250424220452'),
('20250424151525'),
('20250424151335'),
('20250423230649'),
('20250423210738'),
('20250423142442'),
('20250422164210'),
('20250422150857'),
('20250422142706'),
('20250421175504'),
('20250421165341'),
('20250421164136'),
('20250418173725'),
('20250418154057'),
('20250418150004'),
('20250417050305'),
('20250416175347'),
('20250416174140'),
('20250416152651'),
('20250416141211'),
('20250416022457'),
('20250414202453'),
('20250411133844'),
('20250410193222'),
('20250408133850'),
('20250408124710'),
('20250407163423'),
('20250403160129'),
('20250321163619'),
('20250321125851'),
('20250320154423'),
('20250313171537'),
('20250313135216'),
('20250312173026'),
('20250311185850'),
('20250310154855'),
('20250310153426'),
('20250310143406'),
('20250307171639'),
('20250307152300'),
('20250306162716'),
('20250305194627'),
('20250305192029'),
('20250305190838'),
('20250305190728'),
('20250305135634'),
('20250304164813'),
('20250304151646'),
('20250304134919'),
('20250303154157'),
('20250226183002'),
('20250226150119'),
('20250226124427'),
('20250225190426'),
('20250225190152'),
('20250225150445'),
('20250220192824'),
('20250218163748'),
('20250214183213'),
('20250213205702'),
('20250213184912'),
('20250211201700'),
('20250211175230'),
('20250210215024'),
('20250210203350'),
('20250210124131'),
('20250206172420'),
('20250131172155'),
('20250130180021'),
('20250123214137'),
('20250123144121'),
('20250122220626'),
('20250121141741'),
('20250117133129'),
('20250115000000'),
('20250114173951'),
('20250108132145'),
('20250107155812'),
('20250101000000'),
('20241219204055'),
('20241219174356'),
('20241219130622'),
('20241218155530'),
('20241216173729'),
('20241208023105'),
('20241206165650'),
('20241205203441'),
('20241204091860'),
('20241204091859'),
('20241204091858'),
('20241204091857'),
('20241203195925'),
('20241202163849'),
('20241202160350'),
('20241202154929'),
('20241202154928'),
('20241115201021'),
('20241112174444'),
('20241112174443'),
('20241112174442'),
('20241105190216'),
('20241105190215'),
('20241101143234'),
('2'),
('12'),
('11'),
('10'),
('1');

