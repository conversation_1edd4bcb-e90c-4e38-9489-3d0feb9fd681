# frozen_string_literal: true

require "sidekiq/web"
require "sidekiq/cron/web"

Rails.application.routes.draw do
  if Rails.env.development?
    mount GraphiQL::Rails::Engine, at: "/graphiql", graphql_path: "/graphql"
  end
  post "/graphql", to: "graphql#execute"
  namespace :authentications do
    resources :events, only: :index
  end

  resource :password, only: [:edit, :update]
  # FIXME: remove
  resource :email, only: [:edit, :update]
  # FIXME: remove
  resource :email_verification, only: [:show, :create]

  ########## Onboarding Routes: ###############
  get "onboarding/step/2", to: "onboarding#step_2", as: :onboarding_step_2
  get "onboarding/step/3", to: "onboarding#step_3", as: :onboarding_step_3

  ########## Ember Routes: ###############
  get "onboarding(/*path)", to: "ember#onboarding", as: :onboarding
  get "signup(/*path)", to: "ember#signup", as: :signup
  get "dashboard(/*path)", to: "ember#dashboard", as: :dashboard
  get "sign-in", to: "ember#sign_in", as: :sign_in
  get "sign-out", to: "ember#sign_out", as: :sign_out
  get "password-reset(/:token)", to: "ember#password_reset", as: :password_reset
  get "passwordless", to: "ember#passwordless", as: :passwordless
  get "about", to: "ember#marketing", as: :marketing_about
  get "features", to: "ember#marketing", as: :marketing_features
  get "contact", to: "ember#marketing", as: :marketing_contact

  if Rails.application.enable_dev_portal?
    get "dev(/*path)", to: "ember#dev", as: :dev
  end

  # In development, we use the Ember development to work with the frontend app.
  # Therefore, the Rails server won't get to inject the bootstrap payload into
  # the index.html.
  #
  # Instead, the development index.html has a <script src="/bootstrap.js"> tag
  # which gets routed here. In production, we replace that <script> tag with an
  # inline <script> tag that directly contains the bootstrap payload.
  if Rails.env.development?
    constraints(format: :js) do
      get "bootstrap", to: "ember#bootstrap", as: :ember_bootstrap
    end
  end

  root "ember#marketing"

  # devise_for :users
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/*
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest

  ########## Admin Routes: ###############
  # Defines the root path route ("/")
  # root "posts#index"
  if Rails.env.development?
    mount Sidekiq::Web => "/admin/sidekiq"
    mount LetterOpenerWeb::Engine, at: "/letter_opener"
    mount VandalUi::Engine, at: "/vandal"
  else
    constraints(AdminConstraint) do
      mount Sidekiq::Web => "/admin/sidekiq"
    end
  end

  ########## API Routes: ###############
  namespace :api, defaults: { format: :jsonapi } do
    scope :v2 do
      get "healthcheck" => "base#healthcheck"

      resources :password_resets, path: "password-resets", only: [:create, :show, :update]
      # FIXME: may sessions#show?
      # get "current_user" => "current_user#show"
      resources :users, only: [:show, :index, :update] do
        member do
          patch "password", to: "users#change_password"
          get "verify_password_change", to: "users#verify_password_change"
        end
      end
      resources :accounts, only: [:show, :create]
      resource :session, only: [:create, :destroy, :update]

      # FIXME: will graphiti allow this to be nested?
      resources :organizations, only: [:index, :show, :create, :update]
      resources :integrations, only: [:index, :show, :create, :update]

      resources :statements, only: [:index, :show, :create, :update, :destroy] do
        member do
          post :pay, to: "statements#pay"
          post :void, to: "statements#void"
          post :open, to: "statements#open"
        end
      end
      resources :statement_line_items, only: [:index, :show, :create, :update, :destroy] do
        member do
          post :extend, to: "statement_line_items#extend"
          post :release, to: "statement_line_items#release"
          post :void, to: "statement_line_items#void"
        end
      end
      resources :line_item_categories, only: [:index, :show]

      resources :pro_pays, only: [:index, :show, :create] do
        member do
          post :approve
        end
      end
      resources :sub_pro_pay_line_items, only: [:index, :show]
      namespace :integrations do
        post "aspire", to: "aspire#create"

        # Jobber OAuth routes
        namespace :jobber do
          namespace :oauth do
            get "authorize", controller: "jobber_oauth", action: "authorize"
            get "callback", to: "jobber_oauth#callback"
            post "refresh_token", to: "jobber_oauth#refresh_token"
            # Temporary debug route
            get "debug_credentials", to: "jobber_oauth#debug_credentials"
          end
        end
      end

      resources :branches, only: [:index, :show]
      resources :routes, only: [:index, :show, :update]
      resources :jobs, only: [:index, :show]
      resources :job_details, only: [:show]
      resources :milestones, only: [:index, :show, :update]
      resources :milestone_times, only: [:index, :show]
      resources :employee_milestone_times, only: [:index]
      resources :employee_job_times, only: [:index]
      resources :attendances, only: [:create, :index, :update, :show]
      resources :schedule_visits, only: [:index, :show]
      resources :services, only: [:index, :show, :update] do
        collection do
          patch :bulk_update_progress_types
        end
        member do
          post :tracking_materials
          patch :tracking_materials
        end
      end
      resources :catalog_items, only: [:index, :show] do
        collection do
          post :fetch_for_signup
        end
      end
      resources :labor_efficiency_benchmarks, only: [:index, :show, :update] do
        collection do
          post :reset
        end
      end
    end
  end

  # route all ember stuff to the ember controller action
  # These routes are already defined above
  # get "/sign_in", to: "ember#index", as: :sign_in
  # get "/sign_out", to: "ember#index", as: :sign_out
  get "/reset_password", to: "ember#index", as: :reset_password
  get "/verify_email", to: "ember#index", as: :verify_email
  # get "/passwordless", to: "ember#passwordless", as: :passwordless

  get "/docs", to: "api_docs#index"
  get "/docs/*file", to: "api_docs#show"

  # for handling CORS pre-flight requests
  match "*unmatched_route", to: "cors#preflight", via: :options

  resources :webhooks, only: [:create]
  post "/webhooks/github", to: "github_webhooks#create"

  namespace :internal, path: "/__internal" do
    get "/feature-flags", to: "feature_flags#index"
    post "/authenticate", to: "authenticate#create"
    post "/publish", to: "publish#create"
    post "/preview", to: "preview#create"
    post "/feature-flags", to: "feature_flags#create"
    resource :session, only: [:create, :destroy]
  end

  scope module: "graphiti" do
    scope path: "/graphiti" do
      get "/schema.json", to: "schema#show"
      # render error if no endpoint specified
      match "/", to: "rails#render_404", via: :all
      match "/:endpoint", to: "rails#render_404", via: :all
    end
    resources :schema,
              only: [:show],
              controller: "schema",
              defaults: {
                format: "application/vnd.api+json"
              }
  end

  namespace :api do
    namespace :admin do
      resources :users
      resources :organizations
      resources :job_histories, only: [:index, :show]
      resources :jobs, only: [:index, :show, :update] do
        get "history", on: :member
        put "reject", on: :member
        put "cancel", on: :member
        put "assign", on: :member
      end
      resources :job_details, only: [:show]

      resources :branches
      resources :milestones
      resources :milestone_times
      resources :milestone_time_templates
      resources :employees

      post "run/:id", to: "run#create"
      post "skip/:id", to: "skip#create"
      post "reset/:id", to: "reset#create"
    end

    namespace :employee, constraints: lambda { |req| req.session[:employee_id].present? } do
      get :current, to: "base#current"
      get :jobs, to: "jobs#index"
      get "jobs/:id", to: "jobs#show"
      get "jobs/:job_id/milestone/:milestone_id", to: "milestone_steps#show"
      post "jobs/:job_id/milestone/:milestone_id", to: "milestone_steps#update"
    end

    get "/config", to: "config#show"

    resources :password_resets, path: "password-resets", only: [:create, :show, :update]
    # FIXME: may sessions#show?
    # get "current_user" => "current_user#show"
    resources :users, only: [:show, :index]
    resources :accounts, only: [:show, :create]
    resource :session, only: [:create, :destroy, :update]

    # FIXME: will graphiti allow this to be nested?
    resources :organizations, only: [:index, :show, :create]
    resources :integrations, only: [:index, :show, :create, :update]

    resources :branches, only: [:index, :show]
    resources :jobs, only: [:index, :show]
    resources :job_details, only: [:show]
    resources :milestones, only: [:index, :show]
    resources :milestone_times, only: [:index, :show]
    resources :employee_milestone_times, only: [:index]
    resources :employee_job_times, only: [:index]
    resources :schedule_visits, only: [:index, :show]
  end

  namespace :api do
    resources :invitations, only: [:create]
  end

  # Add this route for accepting invitations
  get "/invitations/:token/accept", to: "invitations#accept", as: :accept_invitation
end
