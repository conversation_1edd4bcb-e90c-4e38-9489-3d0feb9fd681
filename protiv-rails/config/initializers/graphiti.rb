# frozen_string_literal: true

ActionDispatch::Request.parameter_parsers[:jsonapi] = ActionDispatch::Request.parameter_parsers[:json]

# This is sest by default in Graphiti's Railtie with:
# Graphiti.config.concurrency = !::Rails.env.test? && ::Rails.application.config.cache_classes
# This behavior should not implicitly change depending on `cache_classes`.
#
# In any case it doesn't play nicely with the `Current` object we use for `current_user`
# and `current_organization` - these variables aren't available outside the request thread.
Graphiti.config.concurrency = false
