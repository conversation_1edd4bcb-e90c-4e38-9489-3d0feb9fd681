# frozen_string_literal: true

module SyncTracking
  SYNC_UUID_KEY = "_sync_uuid"
  SYNC_INTEGRATION_ID_KEY = "_sync_integration_id"
  class SidekiqClientMiddleware
    include Sidekiq::ClientMiddleware

    def call(job_class_or_string, job, queue, redis_pool)
      if Sync::Current.tracking?
        job[SYNC_UUID_KEY] = Sync::Current.uuid
        job[SYNC_INTEGRATION_ID_KEY] = Sync::Current.integration_id
      end

      yield
    end
  end

  class SidekiqServerMiddleware
    def call(job_instance, job_payload, queue)
      if (integration_id = job_payload[SYNC_INTEGRATION_ID_KEY])
        uuid = job_payload[SYNC_UUID_KEY]
        Sync::ErrorTracking.clear!
        Sync::Current.track(integration_id: integration_id, with_uuid: uuid) { yield }
      else
        yield
      end
    end
  end
end

Sidekiq.configure_client do |config|
  config.client_middleware do |chain|
    chain.add SyncTracking::SidekiqClientMiddleware
  end
end

Sidekiq.configure_server do |config|
  config.client_middleware do |chain|
    chain.add SyncTracking::SidekiqClientMiddleware
  end

  config.server_middleware do |chain|
    chain.add SyncTracking::SidekiqServerMiddleware
  end
end
