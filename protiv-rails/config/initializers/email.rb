# frozen_string_literal: true

# Validate required email configuration in production
if Rails.env.production?
  # Always required environment variables
  raise "DOMAIN_NAME is missing!" if <PERSON>NV["DOMAIN_NAME"].blank?
  raise "MAILER_FROM is missing!" if <PERSON>NV["MAILER_FROM"].blank?

  # Check which email service is configured
  if <PERSON>N<PERSON>["MANDRILL_API_KEY"].present?
    # Mandrill is the preferred email service
    Rails.logger.info "Using Mandrill for email delivery"
  else
    # Standard SMTP environment variables
    raise "SMTP_ADDRESS is missing!" if <PERSON>N<PERSON>["SMTP_ADDRESS"].blank?
    raise "SMTP_PORT is missing!" if <PERSON><PERSON><PERSON>["SMTP_PORT"].blank?
    raise "SMTP_USERNAME is missing!" if <PERSON><PERSON><PERSON>["SMTP_USERNAME"].blank?
    raise "SMTP_PASSWORD is missing!" if <PERSON>N<PERSON>["SMTP_PASSWORD"].blank?
    Rails.logger.info "Using standard SMTP for email delivery"
  end
end
