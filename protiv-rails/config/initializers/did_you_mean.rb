# frozen_string_literal: true

module DisableDidYouMeanCorrections
  def corrections
    []
  end
end

if defined?(DidYouMean::KeyError<PERSON>he<PERSON>)
  # We have certain KeyErrors where all the keys have a very similar stem, which
  # balloons the error message to include every single key in the hash.
  #
  # I don't understand why this was ever added to the default Ruby gems in the first place,
  # but it's particularly pathological here.
  #
  # The gem can also be disabled entirely via RUBYOPT, but that seems prone to error
  DidYouMean::KeyErrorChecker.prepend(DisableDidYouMeanCorrections)
end
