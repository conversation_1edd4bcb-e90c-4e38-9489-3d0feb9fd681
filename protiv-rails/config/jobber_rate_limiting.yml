# Jobber GraphQL API Rate Limiting Configuration
#
# This configuration file defines settings for <PERSON>ber's GraphQL API rate limiting
# implementation. The settings control cost-based throttling, batch sizing,
# retry behavior, and circuit breaker functionality.

default: &default
  # Rate Limiting Configuration
  rate_limiting:
    # Percentage of maximumAvailable cost to use (1-100)
    max_query_cost_percentage: 80

    # Minimum cost threshold to maintain before throttling
    minimum_cost_threshold: 500

    # Safety margin for cost estimation (0.0-1.0)
    safety_margin: 0.2

  # Batch Processing Configuration
  batch_processing:
    # Default batch size for paginated queries
    default_batch_size: 50

    # Minimum batch size (cannot go below this)
    minimum_batch_size: 10

    # Maximum batch size (cannot exceed this)
    maximum_batch_size: 100

    # Enable automatic batch size optimization
    auto_batch_sizing: true

  # Retry Configuration
  retry_settings:
    # Maximum number of retry attempts
    maximum_retry_attempts: 3

    # Initial backoff time in seconds
    initial_backoff: 1.0

    # Exponential backoff multiplier
    backoff_multiplier: 2.0

    # Maximum backoff time in seconds
    maximum_backoff: 60.0

    # Jitter factor for randomizing backoff (0.0-1.0)
    jitter_factor: 0.1

  # Circuit Breaker Configuration
  circuit_breaker:
    # Number of failures before circuit opens
    failure_threshold: 5

    # Timeout in seconds before circuit reset attempt
    timeout: 300

    # Enable circuit breaker functionality
    enabled: true

  # Query Optimization Settings
  query_optimization:
    # Field limits for nested connections to reduce costs
    field_limits:
      jobs:
        line_items: 20
        time_sheet_entries: 50
        visits: 10
      visits:
        line_items: 50
        time_sheet_entries: 100
      users:
        # No specific limits for users
      properties:
        # No specific limits for properties

  # Monitoring and Logging
  monitoring:
    # Enable detailed cost metrics logging
    log_cost_metrics: true

    # Enable throttling event logging
    log_throttling_events: true

    # Enable pagination progress logging
    log_pagination_progress: false

    # Log level for rate limiting events (debug, info, warn, error)
    log_level: info

  # Cache Configuration
  caching:
    # TTL for throttle status cache in seconds
    throttle_status_ttl: 60

    # TTL for cost estimation cache in seconds
    cost_estimation_ttl: 300

    # Enable Redis caching for rate limiting data
    enabled: true

development:
  <<: *default
  rate_limiting:
    # More conservative in development
    max_query_cost_percentage: 80
    minimum_cost_threshold: 200
    safety_margin: 0.3

  batch_processing:
    default_batch_size: 25
    minimum_batch_size: 5
    maximum_batch_size: 50

  monitoring:
    log_cost_metrics: true
    log_throttling_events: true
    log_pagination_progress: true
    log_level: debug

test:
  <<: *default
  rate_limiting:
    # Very conservative in test environment
    max_query_cost_percentage: 30
    minimum_cost_threshold: 100
    safety_margin: 0.5

  batch_processing:
    default_batch_size: 10
    minimum_batch_size: 1
    maximum_batch_size: 20

  retry_settings:
    maximum_retry_attempts: 1
    initial_backoff: 0.1
    maximum_backoff: 1.0

  circuit_breaker:
    failure_threshold: 2
    timeout: 30

  monitoring:
    log_cost_metrics: false
    log_throttling_events: false
    log_pagination_progress: false
    log_level: error

  caching:
    enabled: false

staging:
  <<: *default
  rate_limiting:
    max_query_cost_percentage: 70
    minimum_cost_threshold: 400
    safety_margin: 0.25

  batch_processing:
    default_batch_size: 40
    minimum_batch_size: 8
    maximum_batch_size: 80

  monitoring:
    log_cost_metrics: true
    log_throttling_events: true
    log_pagination_progress: false
    log_level: info

production:
  <<: *default
  # Production uses default settings optimized for performance
  monitoring:
    log_cost_metrics: true
    log_throttling_events: true
    log_pagination_progress: false
    log_level: warn
