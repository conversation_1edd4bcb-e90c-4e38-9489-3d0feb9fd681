# frozen_string_literal: true

require 'spec_helper'
require_relative '../../../lib/jobber_client/query_cost_calculator'

RSpec.describe JobberClient::QueryCostCalculator do
  subject(:calculator) { described_class.new }

  describe '#estimate_cost' do
    context 'with a simple query' do
      let(:query) do
        <<~GRAPHQL
          query GetUsers($first: Int) {
            users(first: $first) {
              nodes {
                id
                name {
                  first
                  last
                }
                email {
                  raw
                }
              }
            }
          }
        GRAPHQL
      end

      let(:variables) { { first: 10 } }

      it 'estimates cost correctly' do
        result = calculator.estimate_cost(query, variables)

        expect(result).to include(
          :estimated_cost,
          :base_cost,
          :field_count,
          :connection_multiplier,
          :safety_margin
        )

        expect(result[:estimated_cost]).to be > result[:base_cost]
        expect(result[:field_count]).to be > 0
        expect(result[:connection_multiplier]).to eq(10)
        expect(result[:safety_margin]).to eq(0.2)
      end
    end

    context 'with a complex nested query' do
      let(:query) do
        <<~GRAPHQL
          query GetJobs($first: Int) {
            jobs(first: $first) {
              edges {
                node {
                  id
                  title
                  lineItems {
                    nodes {
                      id
                      name
                      quantity
                    }
                  }
                  timeSheetEntries {
                    nodes {
                      id
                      finalDuration
                    }
                  }
                }
              }
              pageInfo {
                hasNextPage
                endCursor
              }
            }
          }
        GRAPHQL
      end

      let(:variables) { { first: 25 } }

      it 'handles nested connections correctly' do
        result = calculator.estimate_cost(query, variables)

        expect(result[:connection_multiplier]).to eq(25)
        expect(result[:field_count]).to be > 10
        expect(result[:estimated_cost]).to be > 100
      end
    end

    context 'with invalid GraphQL' do
      let(:query) { 'invalid graphql query' }
      let(:variables) { {} }

      it 'returns conservative estimate' do
        result = calculator.estimate_cost(query, variables)

        expect(result[:estimated_cost]).to eq(1000)
        expect(result[:error]).to be_present
      end
    end
  end

  describe '#calculate_optimal_batch_size' do
    let(:query_template) do
      <<~GRAPHQL
        query GetUsers($first: Int) {
          users(first: $first) {
            nodes {
              id
              name {
                first
                last
              }
            }
          }
        }
      GRAPHQL
    end

    it 'calculates optimal batch size based on available cost' do
      available_cost = 1000
      batch_size = calculator.calculate_optimal_batch_size(available_cost, query_template)

      expect(batch_size).to be >= described_class::MIN_BATCH_SIZE
      expect(batch_size).to be_a(Integer)
    end

    it 'respects minimum batch size' do
      available_cost = 10 # Very low cost
      batch_size = calculator.calculate_optimal_batch_size(available_cost, query_template)

      expect(batch_size).to eq(described_class::MIN_BATCH_SIZE)
    end
  end
end
