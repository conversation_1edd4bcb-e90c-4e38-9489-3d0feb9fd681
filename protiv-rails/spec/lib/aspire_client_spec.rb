# frozen_string_literal: true

require "rails_helper"
require "aspire_client"

RSpec.describe AspireClient do
  AspireClient::Models.constants.each do |const|
    specify "#{const} round trips from default" do
      klass = AspireClient::Models.const_get(const)
      next unless klass.ancestors.include?(AspireClient::Models::Base)
      next if klass == AspireClient::Models::Base

      t0 = klass.new.serializable_hash

      t1 = klass.new(t0).serializable_hash
      t2 = klass.new(t1).serializable_hash

      expect(t1).to eq(t2)
    end
  end

  AspireClient::Resources::RESOURCE_MAP.each do |key, const|
    specify "method ##{key} maps to #{const}" do
      klass = AspireClient::Client.new.public_send(key)
      expect(klass).to be_instance_of(const)
    end
  end
end
