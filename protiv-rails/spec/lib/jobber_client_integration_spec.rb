# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'JobberClient Integration with Rate Limiting' do
  let(:access_token) { 'test_access_token' }
  let(:integration_id) { 'test_integration_123' }
  let(:mock_redis) { MockRedis.new }

  before do
    allow(Protiv::Application).to receive(:redis).and_return(mock_redis)
  end

  describe 'JobberClient::Client with rate limiting enabled' do
    subject(:client) do
      JobberClient::Client.new(
        access_token,
        integration_id: integration_id,
        enable_rate_limiting: true
      )
    end

    it 'initializes with rate limiting components' do
      expect(client.rate_limiting_enabled?).to be true
      expect(client.rate_limiter).to be_a(JobberRateLimiter)
      expect(client.pagination_manager).to be_a(JobberClient::PaginationManager)
      expect(client.retry_handler).to be_a(JobberClient::RetryHandler)
    end

    it 'provides rate limiting status' do
      status = client.rate_limiting_status

      expect(status[:enabled]).to be true
      expect(status[:integration_id]).to eq(integration_id)
      expect(status[:max_cost_percentage]).to eq(80)
      expect(status[:circuit_breaker_open]).to be false
    end

    it 'can estimate query costs' do
      query = client.users_query
      variables = { first: 10 }

      cost_estimate = client.estimate_query_cost(query, variables)

      expect(cost_estimate).to include(
        :estimated_cost,
        :base_cost,
        :field_count,
        :connection_multiplier
      )
      expect(cost_estimate[:estimated_cost]).to be > 0
    end

    it 'can configure rate limiting settings' do
      client.configure_rate_limiting(
        max_cost_percentage: 60,
        enable_auto_pagination: true,
        enable_circuit_breaker: true
      )

      status = client.rate_limiting_status
      expect(status[:max_cost_percentage]).to eq(60)
    end

    it 'can reset rate limiting state' do
      # Open circuit breaker
      client.rate_limiter.open_circuit_breaker!
      expect(client.rate_limiting_status[:circuit_breaker_open]).to be true

      # Reset state
      client.reset_rate_limiting!
      expect(client.rate_limiting_status[:circuit_breaker_open]).to be false
    end
  end

  describe 'JobberClient::Client without rate limiting' do
    subject(:client) do
      JobberClient::Client.new(
        access_token,
        enable_rate_limiting: false
      )
    end

    it 'initializes without rate limiting components' do
      expect(client.rate_limiting_enabled?).to be false
      expect(client.rate_limiter).to be_nil
    end

    it 'provides disabled rate limiting status' do
      status = client.rate_limiting_status
      expect(status[:enabled]).to be false
    end
  end

  describe 'Configuration loading' do
    it 'loads configuration from YAML file' do
      config = JobberClient::Configuration.new

      expect(config.rate_limiting.max_query_cost_percentage).to eq(80)
      expect(config.batch_processing.default_batch_size).to eq(50)
      expect(config.retry_settings.maximum_retry_attempts).to eq(3)
      expect(config.circuit_breaker.failure_threshold).to eq(5)
    end

    it 'validates configuration' do
      config = JobberClient::Configuration.new

      errors = config.validate
      expect(errors).to be_empty
    end

    it 'provides feature flags' do
      config = JobberClient::Configuration.new

      expect(config.feature_enabled?(:circuit_breaker)).to be true
      expect(config.feature_enabled?(:auto_batch_sizing)).to be true
      expect(config.feature_enabled?(:caching)).to be true
    end
  end

  describe 'Query Cost Calculator' do
    let(:calculator) { JobberClient::QueryCostCalculator.new }

    it 'estimates costs for different query types' do
      # Simple query
      simple_query = 'query { users { nodes { id name } } }'
      simple_cost = calculator.estimate_cost(simple_query)
      
      # Complex query with pagination
      complex_query = <<~GRAPHQL
        query GetJobs($first: Int) {
          jobs(first: $first) {
            edges {
              node {
                id
                title
                lineItems { nodes { id name } }
                timeSheetEntries { nodes { id duration } }
              }
            }
          }
        }
      GRAPHQL
      complex_cost = calculator.estimate_cost(complex_query, { first: 50 })

      expect(complex_cost[:estimated_cost]).to be > simple_cost[:estimated_cost]
    end

    it 'calculates optimal batch sizes' do
      query = 'query GetUsers($first: Int) { users(first: $first) { nodes { id } } }'
      
      # High available cost should allow larger batches
      large_batch = calculator.calculate_optimal_batch_size(5000, query)
      
      # Low available cost should result in smaller batches
      small_batch = calculator.calculate_optimal_batch_size(100, query)

      expect(large_batch).to be > small_batch
      expect(small_batch).to be >= JobberClient::QueryCostCalculator::MIN_BATCH_SIZE
    end
  end

  describe 'Throttle Status Tracker' do
    let(:tracker) { JobberClient::ThrottleStatusTracker.new(integration_id: integration_id) }

    it 'tracks throttle status and provides recommendations' do
      # Simulate good throttle status
      good_status = {
        'maximumAvailable' => 10000,
        'currentlyAvailable' => 8000,
        'restoreRate' => 500
      }

      level = tracker.update_status(good_status)
      expect(level).to eq(:ok)

      decision = tracker.should_allow_query?(estimated_cost: 100)
      expect(decision[:allowed]).to be true

      # Simulate critical throttle status
      critical_status = {
        'maximumAvailable' => 10000,
        'currentlyAvailable' => 500,
        'restoreRate' => 500
      }

      level = tracker.update_status(critical_status)
      expect(level).to eq(:critical)

      decision = tracker.should_allow_query?(estimated_cost: 1000)
      expect(decision[:allowed]).to be false
      expect(decision[:wait_time]).to be > 0
    end

    it 'recommends appropriate batch sizes based on throttle level' do
      # Critical throttle status
      critical_status = {
        'maximumAvailable' => 10000,
        'currentlyAvailable' => 800,
        'restoreRate' => 500
      }

      tracker.update_status(critical_status)
      
      recommendation = tracker.recommend_batch_size(
        base_batch_size: 100,
        cost_per_item: 10
      )

      expect(recommendation[:batch_size]).to be < 100
      expect(recommendation[:throttle_level]).to eq(:critical)
    end
  end
end
