# frozen_string_literal: true

require "rails_helper"

RSpec.describe MoneyType do
  describe "graphiti type" do
    let(:type) { Graphiti::Types[:money] }

    let(:one_dollar) { Money.from_amount(1, "USD") }
    let(:one_dollar_cad) { Money.from_amount(1, "CAD") }
    let(:json_string) { %({ "cents": 100 }) }
    let(:json_string_cad) { %({ "cents": 100, "currency_iso": "CAD" }) }
    let(:json) { JSON.parse(json_string) }
    let(:json_cad) { JSON.parse(json_string_cad) }

    specify do
      expect(type[:kind]).to eq("record")
    end

    specify do
      expect { type[:params].call({}) }.to raise_error(Dry::Types::CoercionError)
    end

    specify do
      expect(type[:params].call({ "cents" => 100 }.with_indifferent_access)).to eq(one_dollar)
    end

    specify do
      expect(type[:params].call({ cents: 100 }.with_indifferent_access)).to eq(one_dollar)
    end

    specify do
      expect(type[:params].call({ cents: 100 })).to eq(one_dollar)
    end

    specify do
      expect(type[:read].call(one_dollar)).to eq({ "cents" => 100, "currency_iso" => "USD" })
    end

    specify do
      expect(type[:read].call(%({ "cents": 100 }))).to eq({ "cents" => 100, "currency_iso" => "USD" })
    end

    specify do
      expect(type[:write].call(json_string)).to eq(one_dollar)
    end

    specify do
      expect(type[:write].call(json_string_cad)).to eq(one_dollar_cad)
    end

    specify do
      expect(type[:write].call(one_dollar)).to eq(one_dollar)
    end

    specify do
      expect(type[:write].call(json)).to eq(one_dollar)
    end

    specify do
      expect(type[:write].call(json_cad)).to eq(one_dollar_cad)
    end
  end
end
