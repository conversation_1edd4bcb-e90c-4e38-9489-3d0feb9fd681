# frozen_string_literal: true

require "rails_helper"
require "range_utils"

RSpec.describe RangeUtils do
  include RangeUtils

  specify do
    ranges = [
      (1..10),
      (2..8),
      (20..24),
      (5..12),
      (25..30),
      (26..40)
    ].shuffle

    expected_ranges = [
      (1..12),
      (20..24),
      (25..40)
    ]

    compressed = compress_ranges(ranges)

    expect(compressed).to eq(expected_ranges)
  end

  specify do
    freeze_time

    ranges = [
      30.days.ago..20.days.ago,
      50.days.ago..25.days.ago,
      10.days.ago..5.days.ago,
      18.days.ago..9.days.ago,
      3.days.ago..2.days.ago,
      2.days.ago..1.day.ago
    ].shuffle

    compressed = compress_ranges(ranges)

    expected = [
      50.days.ago..20.days.ago,
      18.days.ago..5.days.ago,
      3.days.ago..1.day.ago
    ]

    expect(compressed).to eq(expected)
  end

  specify do
    target_range = (1..20)
    targets = diff_ranges(target_range, [15..30])
    expect(targets).to eq([1..15])

    target_range = (1..100)
    targets = diff_ranges(target_range, [10..20, 25..30, 45..55, 75..110].shuffle)

    expect(targets).to eq([1..10, 20..25, 30..45, 55..75])
  end
end
