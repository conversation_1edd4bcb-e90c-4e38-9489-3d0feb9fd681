# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Onboarding Flow", type: :feature do
  let(:user) { fixture(:user, :john_appleseed) }

  describe "Step 2: Connect to Aspire" do
    it "displays the Connect to Aspire page" do
      # Verify the view template content
      expect(File.read(Rails.root.join("app/views/onboarding/step_2.html.erb"))).to include("Let's Hook You Up with Aspire")
      expect(File.read(Rails.root.join("app/views/onboarding/step_2.html.erb"))).to include("Watch how Protiv connects with Aspire")
      expect(File.read(Rails.root.join("app/views/onboarding/step_2.html.erb"))).to include("Step 1: Connect")
      expect(File.read(Rails.root.join("app/views/onboarding/step_2.html.erb"))).to include("Step 2: Preferences")
      expect(File.read(Rails.root.join("app/views/onboarding/step_2.html.erb"))).to include("Connect Aspire and Get Started")
    end
  end

  describe "Step 3: Preferences" do
    it "displays the Preferences page" do
      # Verify the view template content
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Set Your Preferences")
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Great! You're now connected to Aspire.")
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Connection Successful!")
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Preference Options")
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Enable automatic data synchronization")
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Receive notifications for updates")
      expect(File.read(Rails.root.join("app/views/onboarding/step_3.html.erb"))).to include("Complete Setup and Go to Dashboard")
    end
  end
end
