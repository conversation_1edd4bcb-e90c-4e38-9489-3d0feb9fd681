# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Statement, type: :model do
  subject { build(:statement) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:organization) }
    it { should belong_to(:pay_period) }
    it { should belong_to(:identity) }
    it { should have_many(:statement_line_items) }
  end

  describe "enums" do
    it do
      should define_enum_for(:status).
        backed_by_column_of_type(:enum).
        with_values(open: "open",
                    paid: "paid",
                    void: "void")
    end
    it { should allow_values(:open, :paid, :void).for(:status) }
  end

  describe "for a new statement" do
    subject(:statement) { create(:statement) }
    it { should be_valid }

    it "should have no line_items" do
      expect(statement.statement_line_items.count).to eq(0)
    end
  end

  describe "for a statement with line items" do
    subject(:statement) { create(:statement_with_line_items_unpaid, :with_skip_update_statement) }
    it "should be in open status" do
      expect(statement.status).to eq("open")
    end

    it "can calculate the net_total by touching" do
      statement.reload
      expect(statement.net_amount).to eq(Money.from_amount(0))
      expect(statement.bonuses).to eq(Money.from_amount(0))
      expect(statement.deductions).to eq(Money.from_amount(0))

      statement.touch
      expect(statement.net_amount).to eq(Money.from_amount(44.50))
      expect(statement.bonuses).to eq(Money.from_amount(50.0))
      expect(statement.deductions).to eq(Money.from_amount(-5.50))
    end

    it "will update the net_total when a new line_item is added" do
      statement.touch
      expect(statement.net_amount).to eq(Money.from_amount(44.50))
      expect(statement.bonuses).to eq(Money.from_amount(50.0))
      expect(statement.deductions).to eq(Money.from_amount(-5.50))

      create(:deduction_line_item, statement: statement)
      expect(statement.reload.net_amount).to eq(Money.from_amount(39.0))
      expect(statement.bonuses).to eq(Money.from_amount(50.0))
      expect(statement.deductions).to eq(Money.from_amount(-11.0))
    end

    it "can be moved to open with mark_open if the net_amount is correct" do
      statement.touch
      statement.status = "paid"
      statement.mark_open
      expect(statement.status).to eq("open")

      statement.status = "void"
      statement.mark_open
      expect(statement.status).to eq("open")
    end

    it "will not be moved to open with mark_open if the net_amount is incorrect" do
      statement.reload
      statement.status = "paid"
      expect { statement.mark_open! }.to raise_exception(AASM::InvalidTransition)
      expect(statement.status).to eq("paid")

      statement.status = "void"
      expect { statement.mark_open! }.to raise_exception(AASM::InvalidTransition)
      expect(statement.status).to eq("void")
    end

    it "has payment_date when paid" do
      statement.touch
      expect(statement.payment_date).to be_nil

      statement.pay!
      expect(statement.payment_date).not_to be_nil
    end

    it "resets payment_date when unmarked as paid" do
      statement.touch
      statement.pay!
      expect(statement.payment_date).not_to be_nil

      statement.open!
      expect(statement.payment_date).to be_nil
    end
  end
  describe "voiding a statement" do
    subject(:statement) { create(:statement_with_line_items_unpaid, :with_skip_update_statement) }

    it "voids all line items when the statement is voided" do
      statement.touch
      expect(statement.statement_line_items.pluck(:status)).to all(eq("in_process"))

      statement.void!
      statement.reload

      expect(statement.status).to eq("void")
      expect(statement.statement_line_items.pluck(:status)).to all(eq("void"))
    end

    it "rolls back the transaction if voiding a line item fails" do
      # Simulate a failure in voiding a line item
      allow_any_instance_of(StatementLineItem).to receive(:void!).and_raise(ActiveRecord::RecordInvalid)

      statement.touch
      expect { statement.void! }.to raise_error(ActiveRecord::RecordInvalid)
      expect(statement.reload.status).not_to eq("void")
      expect(statement.statement_line_items.pluck(:status)).not_to include("void")
    end
  end
end
