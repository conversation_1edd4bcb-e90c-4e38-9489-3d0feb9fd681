# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SubProPayProPayable, type: :model do
  let(:job_sub_pro_pay) { create(:job_sub_pro_pay) }
  subject { create(:sub_pro_pay_pro_payable, pro_payable: build(:milestone), sub_pro_pay: job_sub_pro_pay) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:sub_pro_pay) }
  end

  describe "validations" do
    context "when existing sub_pro_pay_pro_payable for pro_payable exists" do
      before do
        @job_sub_pro_pay = create(:job_sub_pro_pay)
        @job_sub_pro_pay2 = create(:job_sub_pro_pay)
      end

      describe "when job has not been pro_payed" do
        let(:new_job) { build(:job, organization: @job_sub_pro_pay.pro_pay.organization) }
        subject { build(:sub_pro_pay_pro_payable, pro_payable: new_job, sub_pro_pay: @job_sub_pro_pay) }

        it { should be_valid }
      end

      describe "when job has already been pro_payed" do
        let(:existing_job) { @job_sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable }
        subject { build(:sub_pro_pay_pro_payable, pro_payable: existing_job, sub_pro_pay: @job_sub_pro_pay2) }

        it { should_not be_valid }
      end

      describe "when job has already been pro_payed through a milestone" do
        let(:sub_pro_pay) { create(:single_milestone_sub_pro_pay) }
        subject { build(:sub_pro_pay_pro_payable,
                        pro_payable: sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable.job, sub_pro_pay: @job_sub_pro_pay2) }

        it { should_not be_valid }
      end

      describe "when milestone has not been pro_payed" do
        let(:new_milestone) { build(:milestone, job: create(:job, organization: @job_sub_pro_pay.pro_pay.organization)) }
        subject { build(:sub_pro_pay_pro_payable, pro_payable: new_milestone, sub_pro_pay: @job_sub_pro_pay) }

        it { should be_valid }
      end

      describe "when milestone has been pro_payed through its job" do
        let(:new_milestone) { build(:milestone, job: @job_sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable) }
        subject { build(:sub_pro_pay_pro_payable, pro_payable: new_milestone, sub_pro_pay: @job_sub_pro_pay) }

        it { should_not be_valid }
      end

      describe "when milestone has already been pro_payed through job" do
        subject do
          build(:sub_pro_pay_pro_payable,
                pro_payable: @job_sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable.milestones.first,
                sub_pro_pay: @job_sub_pro_pay2)
        end
        it { should_not be_valid }
      end
    end
  end
end
