# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Route, type: :model do
  subject { build(:route) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:organization).optional }
    it { should belong_to(:branch).optional }
    it { should belong_to(:crew_lead) }
    it { should belong_to(:manager).optional }
    it { should have_many(:schedule_visits) }
  end
end
