# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Job, type: :model do
  subject { fixture(:job, :lawn_q1) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:manager).optional }
    it { should belong_to(:branch).optional }
    it { should belong_to(:organization) }
    it { should have_one(:sub_pro_pay_pro_payable) }
    it { should have_many(:milestones) }
    it { should have_many(:job_budgets) }
  end

  describe "budget calculations" do
    # Use a job with no milestones from the fixture set to test the model more clearly
    # Maybe it would be ok to not use a fixture for tests like this?
    let(:job) { fixture(:job, :lawn_q3) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }

    describe "#total_costs" do
      before do
        create(:milestone, job: job,
          equipment_cost_cents: 300,
          labor_cost_cents: 300,
          material_cost_cents: 400,
          currency: job.currency)

        create(:milestone, job: job,
          equipment_cost_cents: 600,
          labor_cost_cents: 600,
          material_cost_cents: 800,
          currency: job.currency)
      end


      it "aggregates total costs from job budgets" do
        expect(job.total_costs).to eq(Money.new(3000, job.currency))
      end
    end

    describe "#contract_price" do
      before do
        create(:milestone, job: job, contract_price_cents: 3000)
        create(:milestone, job: job, contract_price_cents: 5000)
      end

      it "aggregates contract prices from job budgets" do
        expect(job.contract_price).to eq(Money.new(8000, job.currency))
      end
    end

    describe "with no job budgets" do
      let(:job) { fixture(:job, :lawn_q3) }

      it "returns zero money for total_budget" do
        expect(job.total_budget).to be_a(Money)
        expect(job.total_budget).to eq(Money.zero(job.currency))
      end

      it "returns zero money for total_costs" do
        expect(job.total_costs).to be_a(Money)
        expect(job.total_costs).to eq(Money.zero(job.currency))
      end

      it "returns zero money for contract_price" do
        expect(job.contract_price).to be_a(Money)
        expect(job.contract_price).to eq(Money.zero(job.currency))
      end
    end
  end
end
