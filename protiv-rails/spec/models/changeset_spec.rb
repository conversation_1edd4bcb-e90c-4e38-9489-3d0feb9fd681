# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Changeset, type: :model do
  let(:user) { create(:user) }

  describe "automated tracking in a sync block" do
    it "allows nested tracking" do
      expect(Sync::Current.uuid).to be_nil

      Sync::Current.track do
        expect(Sync::Current.uuid).to be_present
        expect(Sync::Current).to be_tracking
        u1 = Sync::Current.uuid

        Sync::Current.track(requires_new: false) do
          expect(Sync::Current.uuid).to eq(u1)
        end

        Sync::Current.track(requires_new: true) do
          expect(Sync::Current.uuid).not_to eq(u1)
        end

        expect(Sync::Current.uuid).to eq(u1)
      end

      expect(Sync::Current.uuid).to eq(nil)
    end

    specify do
      company = Sync::Current.track do
        company = create(:company)

        company.remote_slug = "remote:company:1"
        company.update(name: "company 1")
        company.update(name: "company 2")
        company.update(name: "company 3")
        company
      end

      # should it be allowed to track changes outside a sync block?
      company.update(name: "company 4")
    end
  end
end
