# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Organization, type: :model do
  describe "creation" do
    let(:organization) { create(:organization) }

    it "should be valid" do
      expect(organization).to be_valid
    end

    it "should have a default bonus pool" do
      expect(organization.default_bonus_pool).to_not be_nil
    end
  end

  describe "associations" do
    it { should have_many(:schedule_visits) }
  end

  describe "schedule_visits" do
    let(:organization) { create(:organization) }
    let(:route) { create(:route, organization: organization) }
    let(:milestone) { create(:milestone, organization: organization) }

    it "can have schedule visits" do
      schedule_visit = create(:schedule_visit,
        organization: organization,
        route: route,
        milestone: milestone,
        hours: 4.5,
        scheduled_date: Time.zone.now
      )

      expect(organization.schedule_visits).to include(schedule_visit)
      expect(route.schedule_visits).to include(schedule_visit)
      expect(milestone.schedule_visits).to include(schedule_visit)
    end

    it "validates schedule_visit hours" do
      schedule_visit = build(:schedule_visit,
        organization: organization,
        route: route,
        milestone: milestone,
        hours: -1
      )

      expect(schedule_visit).not_to be_valid
      expect(schedule_visit.errors[:hours]).to include("must be greater than or equal to 0")
    end
  end
end
