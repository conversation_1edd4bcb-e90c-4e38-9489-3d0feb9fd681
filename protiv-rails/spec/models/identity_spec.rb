# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Identity, type: :model do
  describe "associations" do
    it { should belong_to(:user).optional }
    it { should belong_to(:organization).optional }
    it { should have_many(:milestone_times) }
    it { should have_many(:sub_pro_pay_line_items) }
    it { should have_many(:sub_pro_pays) }
  end

  context "before a user is created" do
    let(:email) { generate(:user_factory_email) }
    let(:identity) { create(:identity, email: email) }

    it "claims the identity when the user is verified" do
      expect(identity.user).not_to be_present

      user = create(:user, email: email)
      expect(identity.reload.user).not_to be_present

      VerifyUser.new(user: user).execute!

      expect(identity.reload.user).to eq(user)
    end
  end

  context "with an existing user" do
    let(:user) { create(:user, verified: true) }

    specify do
      identity = Identity.create(email: user.email)

      expect(identity.user).to eq(user)
    end
  end
end
