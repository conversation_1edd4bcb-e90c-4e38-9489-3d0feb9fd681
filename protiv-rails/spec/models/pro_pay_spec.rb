# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ProPay, type: :model do
  subject { build(:pro_pay) }
  it { should be_valid }

  describe "associations" do
    it { should have_many(:sub_pro_pays) }
    it { should belong_to(:reference_bonus_pool).optional }
  end

  describe "bonus_pool management" do
    context "for a new pro_pay" do
      context "without a reference_bonus_pool set" do
        subject(:pro_pay) { build(:pro_pay) }

        it "uses the default values" do
          expect(pro_pay.crew_percent_hundredths).to eq(100_00)
          expect(pro_pay.company_percent_hundredths).to eq(0)
          expect(pro_pay.crew_lead_percent_hundredths).to eq(0)
          expect(pro_pay.manager_percent_hundredths).to eq(0)
          expect(pro_pay.other_percent_hundredths).to eq(0)
          expect(pro_pay.crew_retention_percent_hundredths).to eq(0)
        end

        it "should have no payables" do
          expect(pro_pay.pro_pay_payables.count).to eq(0)
        end
      end

      context "with a reference_bonus_pool set" do
        let(:bonus_pool_simple) { create(:bonus_pool_with_others, :division_1) }
        subject(:pro_pay) { create(:pro_pay, reference_bonus_pool: bonus_pool_simple) }

        it "uses the reference values" do
          expect(pro_pay.crew_percent_hundredths).to eq(50_00)
          expect(pro_pay.company_percent_hundredths).to eq(25_00)
          expect(pro_pay.crew_lead_percent_hundredths).to eq(0)
          expect(pro_pay.manager_percent_hundredths).to eq(25_00)
          expect(pro_pay.other_percent_hundredths).to eq(0)
          expect(pro_pay.crew_retention_percent_hundredths).to eq(50_00)
        end

        it "should have payable others from the bonus pool" do
          expect(pro_pay.pro_pay_payables.count).to eq(1)
          expect(pro_pay.pro_pay_payables.first.payable).to eq(bonus_pool_simple.bonus_pool_payables.first.payable)
        end
      end
    end

    context "for an existing pro_pay" do
      context "with a reference_bonus_pool set" do
        let(:bonus_pool) { create(:bonus_pool_with_others, :division_1, name: "old pool") }
        subject(:pro_pay) { create(:pro_pay, reference_bonus_pool: bonus_pool) }

        let(:bonus_pool_2) { create(:bonus_pool_with_two_others, :division_2, name: "New Pool") }

        it "changes the values and payables when a reference_bonus_pool_is changed" do
          expect(pro_pay.crew_percent_hundredths).to eq(50_00)
          expect(pro_pay.company_percent_hundredths).to eq(25_00)
          expect(pro_pay.crew_lead_percent_hundredths).to eq(0)
          expect(pro_pay.manager_percent_hundredths).to eq(25_00)
          expect(pro_pay.other_percent_hundredths).to eq(0)
          expect(pro_pay.crew_retention_percent_hundredths).to eq(50_00)
          expect(pro_pay.pro_pay_payables.count).to eq(1)
          prior_payable = pro_pay.pro_pay_payables.first

          pro_pay.update!(reference_bonus_pool: bonus_pool_2)
          expect(pro_pay.crew_percent_hundredths).to eq(60_00)
          expect(pro_pay.company_percent_hundredths).to eq(15_00)
          expect(pro_pay.crew_lead_percent_hundredths).to eq(5_00)
          expect(pro_pay.manager_percent_hundredths).to eq(8_00)
          expect(pro_pay.other_percent_hundredths).to eq(12_00)
          expect(pro_pay.crew_retention_percent_hundredths).to eq(27_00)
          expect(pro_pay.pro_pay_payables.count).to eq(3)

          prior_payable.reload
          expect(prior_payable.participating).to be_falsy
        end
      end
    end
  end
end
