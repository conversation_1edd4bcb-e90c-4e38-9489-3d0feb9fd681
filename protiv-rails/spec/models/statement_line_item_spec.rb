# frozen_string_literal: true

require 'rails_helper'

RSpec.describe StatementLineItem, type: :model do
  let(:organization) { create(:organization, :with_branches) }
  let(:user) { create(:user) }
  let(:identity) { create(:identity, user: user, organization: organization) }

  subject { build(:statement_line_item, identity: identity) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:organization) }
    it { should belong_to(:identity) }
    it { should belong_to(:statement).optional }
    it { should belong_to(:line_item_category).optional }
    it { should belong_to(:pro_pay).optional }
  end

  describe "enums" do
    it do
      should define_enum_for(:status).
        backed_by_column_of_type(:enum).
        with_values(paid: "paid",
                    held: "held",
                    in_process: "in_process",
                    void: "void")
    end
    it { should allow_values(:paid, :held, :in_process, :void).for(:status) }
  end

  describe "for a new statement_line_item" do
    let(:pay_period) { create(:pay_period, branch: organization.branches.first) }
    let(:statement) { create(:statement, pay_period: pay_period, identity: identity) }
    subject(:statement_line_item) { build(:statement_line_item, statement: statement,
      amount: Money.from_amount(100), identity: identity) }

    it { should be_valid }

    it "should update net_amount when saved" do
      expect(statement_line_item.statement.net_amount).to eq(Money.from_amount(0))
      statement_line_item.save!
      expect(statement_line_item.statement.net_amount).to eq(Money.from_amount(100))
    end

    it "should update held_days when extend" do
      expect(statement_line_item.status).to eq("in_process")
      expect(statement_line_item.statement).to be_present
      expect(statement_line_item.held_days).to eq(nil)
      expect(statement_line_item.held_indefinitely).to eq(false)

      statement_line_item.extend(5)
      expect(statement_line_item.status).to eq("held")
      expect(statement_line_item.statement).not_to be_present
      expect(statement_line_item.held_days).to eq(5)
      expect(statement_line_item.held_indefinitely).to eq(false)
    end

    it "should use nearest Statement when released" do
      # Stop check_if_empty deletion of a Statement, so extending the LineItem doesn't remove the Statement
      allow_any_instance_of(Statement).to receive(:check_if_empty).and_return(false)
      expect(statement_line_item.status).to eq("in_process")
      expect(statement_line_item.statement).to be_present

      statement_line_item.extend(5)

      expect(statement_line_item.status).to eq("held")
      expect(statement_line_item.statement).not_to be_present

      statement_line_item.release!
      expect(statement_line_item.status).to eq("in_process")
      expect(statement_line_item.statement).to be_present
    end
  end

  describe "voiding a line item" do
    let(:statement) { create(:statement, identity: identity) }
    subject(:line_item) { build(:statement_line_item, statement: statement,
                                amount: Money.from_amount(100), identity: identity) }

    it "updates the status to void" do
      expect(line_item.status).to eq("in_process")
      line_item.void
      expect(line_item.status).to eq("void")
    end

    it "resets held_days and held_indefinitely" do
      line_item.update!(held_days: 5, held_indefinitely: true)
      line_item.void
      expect(line_item.held_days).to be_nil
      expect(line_item.held_indefinitely).to be_falsey
    end

    it "does not update already voided items" do
      line_item.update!(status: "void")
      expect { line_item.void! }.to raise_error(AASM::InvalidTransition)
    end
  end

  describe "voided statement restrictions" do
    let(:statement) { create(:statement, status: "void", identity: identity) }
    subject(:line_item) { create(:statement_line_item, statement: statement, status: "void", identity: identity) }

    it "prevents transitioning from void to in_process" do
      expect { line_item.mark_in_process! }.to raise_error(AASM::InvalidTransition)
      expect(line_item.reload.status).to eq("void")
    end

    it "prevents release when statement is voided" do
      expect { line_item.release! }.to raise_error(AASM::InvalidTransition)
    end
  end
end
