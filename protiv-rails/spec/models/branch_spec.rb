# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Branch, type: :model do
  subject { build(:branch) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:organization) }
    it { should belong_to(:pay_payroll_schedule).optional }
    it { should belong_to(:bonus_payroll_schedule).optional }
    it { should have_many(:jobs) }
    it { should have_many(:pay_periods) }
    it { should have_many(:pay_pay_periods) }
    it { should have_many(:pay_pay_periods) }
    it { should have_many(:bonus_pay_periods) }
    it { should have_one(:current_pay_pay_period) }
    it { should have_one(:future_pay_pay_period) }
    it { should have_one(:current_bonus_pay_period) }
    it { should have_one(:future_bonus_pay_period) }
  end
end
