# frozen_string_literal: true

require "rails_helper"

RSpec.describe LaborEfficiencyBenchmark do
  describe "validations" do
    it "is valid with required attributes" do
      benchmark = build(:labor_efficiency_benchmark)
      expect(benchmark).to be_valid
    end

    it "requires level_record_id for non-organization levels" do
      benchmark = build(:labor_efficiency_benchmark, level: "job", level_record_id: nil)
      expect(benchmark).not_to be_valid
    end

    it "allows nil level_record_id for organization level" do
      benchmark = build(:labor_efficiency_benchmark, level: "organization", level_record_id: nil)
      expect(benchmark).to be_valid
    end

    it "enforces target_percentage range" do
      expect(build(:labor_efficiency_benchmark, target_percentage: -1)).not_to be_valid
      expect(build(:labor_efficiency_benchmark, target_percentage: 0)).to be_valid
      expect(build(:labor_efficiency_benchmark, target_percentage: 100)).to be_valid
      expect(build(:labor_efficiency_benchmark, target_percentage: 101)).not_to be_valid
    end

    it "allows only one active benchmark per organization/level/level_record_id combination" do
      organization = create(:organization)

      # Organization already has a benchmark from after_create callback
      # Try to create second active benchmark for same scope - should fail
      second_benchmark = build(:labor_efficiency_benchmark,
                              organization: organization,
                              level: "organization",
                              active: true)

      expect(second_benchmark).not_to be_valid
      expect(second_benchmark.errors[:active]).to include("already has an active benchmark for this combination")
    end

    it "allows multiple inactive benchmarks for same scope" do
      organization = create(:organization)

      # Create first inactive benchmark
      first_benchmark = create(:labor_efficiency_benchmark,
                              organization: organization,
                              level: "organization",
                              active: false)

      # Create second inactive benchmark for same scope - should succeed
      second_benchmark = build(:labor_efficiency_benchmark,
                              organization: organization,
                              level: "organization",
                              active: false)

      expect(second_benchmark).to be_valid
    end

    it "allows active benchmark after deactivating existing one" do
      organization = create(:organization)

      # Find the existing benchmark created by after_create callback
      existing_benchmark = LaborEfficiencyBenchmark.find_by(
        organization: organization,
        level: "organization",
        active: true
      )

      # Deactivate existing benchmark
      existing_benchmark.update!(active: false)

      # Create new active benchmark for same scope - should succeed
      new_benchmark = build(:labor_efficiency_benchmark,
                           organization: organization,
                           level: "organization",
                           active: true)

      expect(new_benchmark).to be_valid
    end
  end
end
