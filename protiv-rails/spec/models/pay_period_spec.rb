# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PayPeriod, type: :model do
  subject { build(:pay_period) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:payroll_schedule) }
    it { should belong_to(:branch) }
  end

  describe "without existing pay_periods" do
    it { should be_valid }
  end

  describe "with existing pay_periods" do
    # first_period will be created when it's referred to by nested subjects
    let(:first_period) { create(:pay_period) }

    describe "new later periods" do
      describe "with no overlap or gap" do
        subject { build(:pay_period,
                        start_time: first_period.end_time,
                        end_time: first_period.end_time + 14.days,
                        branch: first_period.branch,
                        payroll_schedule: first_period.payroll_schedule,
                        period_type: first_period.period_type) }

        it { should be_valid }
      end

      describe "with overlap" do
        subject { build(:pay_period,
                        start_time: first_period.end_time - 1.second,
                        end_time: first_period.end_time + 14.days,
                        branch: first_period.branch,
                        payroll_schedule: first_period.payroll_schedule,
                        period_type: first_period.period_type) }

        it { should_not be_valid }
      end

      describe "with gap" do
        subject { build(:pay_period,
                        start_time: first_period.end_time + 1.second,
                        end_time: first_period.end_time + 14.days,
                        branch: first_period.branch,
                        payroll_schedule: first_period.payroll_schedule,
                        period_type: first_period.period_type) }

        it { should_not be_valid }
      end
    end

    describe "new earlier periods" do
      describe "with no overlap or gap" do
        subject { build(:pay_period,
                        start_time: first_period.start_time - 14.days,
                        end_time: first_period.start_time,
                        branch: first_period.branch,
                        payroll_schedule: first_period.payroll_schedule,
                        period_type: first_period.period_type) }

        it { should be_valid }
      end

      describe "with overlap" do
        subject { build(:pay_period,
                        start_time: first_period.start_time - 14.days,
                        end_time: first_period.start_time + 1.second,
                        branch: first_period.branch,
                        payroll_schedule: first_period.payroll_schedule,
                        period_type: first_period.period_type) }
        it { should_not be_valid }
      end

      describe "with gap" do
        subject { build(:pay_period,
                        start_time: first_period.start_time - 14.days,
                        end_time: first_period.start_time - 1.second,
                        branch: first_period.branch,
                        payroll_schedule: first_period.payroll_schedule,
                        period_type: first_period.period_type) }

        it { should_not be_valid }
      end
    end

    describe "editing periods" do
      let(:pay_periods) { create(:branch_with_payroll_schedule).pay_periods.order(:start_time).to_a }

      describe "should fail if it introduces gaps or overlaps by changing" do
        describe "first entry" do
          let(:period) { pay_periods.first }

          it "should allow editing start_time" do
            period.start_time = period.start_time - 14.days
            expect(period).to be_valid
          end

          it "should not allow editing end_time" do
            period.end_time = period.end_time - 1.second
            expect(period).to_not be_valid
          end
        end

        describe "middle entry" do
          let(:period) { pay_periods.second }

          it "should not allow editing start_time" do
            period.start_time = period.start_time - 1.second
            expect(period).to_not be_valid
          end

          it "should not allow editing end_time" do
            period.end_time = period.end_time - 1.second
            expect(period).to_not be_valid
          end
        end

        describe "middle entry two" do
          let(:period) { pay_periods.third }

          it "should not allow editing start_time" do
            period.start_time = period.start_time - 1.second
            expect(period).to_not be_valid
          end

          it "should not allow editing end_time" do
            period.end_time = period.end_time - 1.second
            expect(period).to_not be_valid
          end
        end

        describe "last entry" do
          let(:period) { pay_periods.last }

          it "should not allow editing start_time" do
            period.start_time = period.start_time - 14.days
            expect(period).to_not be_valid
          end

          it "should allow editing end_time" do
            period.end_time = period.end_time - 1.second
            expect(period).to be_valid
          end
        end

        describe "branch" do
          let(:period) { pay_periods.second }

          it "should not allow editing branch" do
            period.branch = create(:branch)
            expect(period).to_not be_valid
          end
        end

        describe "period_type" do
          let(:period) { pay_periods.second }

          it "should not allow editing period_type" do
            period.period_type = "pay"
            expect(period).to_not be_valid
          end
        end
      end
    end

    describe "destroying periods" do
      let(:pay_periods) { create(:branch_with_payroll_schedule).pay_periods.order(:start_time).to_a }

      describe "should fail if it introduces gaps or overlaps by destroying" do
        describe "first entry" do
          let(:period) { pay_periods.first }

          it "should allow destroying" do
            expect(period.destroy).to be_truthy
          end
        end

        describe "middle entry" do
          let(:period) { pay_periods.second }

          it "should allow destroying" do
            expect(period.destroy).to be_falsey
          end
        end

        describe "middle entry two" do
          let(:period) { pay_periods.third }

          it "should allow destroying" do
            expect(period.destroy).to be_falsey
          end
        end

        describe "last entry" do
          let(:period) { pay_periods.last }

          it "should allow destroying" do
            expect(period.destroy).to be_truthy
          end
        end
      end
    end
  end
end
