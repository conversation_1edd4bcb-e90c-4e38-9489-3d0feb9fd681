# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance, type: :model do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization:, role_type: Role::MANAGER) }
  let!(:integration) { create(:integration, organization:, source: :test_a) }
  let(:manager) do
    create(:identity, organization:, crew_lead: true)
  end
  let(:identity) do
    create(:identity, organization:, user:).tap do |identity|
      identity.integration_records.create(
        integration:,
        remote_slug: "test:person:12345"
      )
    end
  end

  describe "backsyncing" do
    before do
      freeze_time
    end

    specify do
      attendance = organization.attendances.create(
        identity:,
        started_at: 8.hours.ago,
        created_by_user: user,
        manager:
      )

      expect_any_instance_of(integration.adapter.class).to receive(:create_clock_time).with(attendance)

      expect do
        attendance.update!(ended_at: Time.now)
      end.to change { SyncDatum.count }.from(0).to(1)

      integration.create_credential(bearer_token: "FAKE_ASPIRE_TOKEN")

      expect(Sync::SyncDatumJob.jobs.count).to eq(1)
      Sidekiq::Worker.drain_all
    end
  end

  describe 'overlap validation' do
    let(:milestone) { create(:milestone, organization:) }
    let(:started_at) { Time.new(2025, 3, 21, 8, 0, 0) }

    def create_attendance(**kwargs)
      args = {
        identity:,
        milestone:,
        created_by_user: user,
        started_at:,
        organization:,
        manager:,
        **kwargs
      }

      Attendance.create!(args)
    end

    it 'does not allow additional attendances when one is open' do
      a1 = create_attendance
      expect(a1).to be_valid

      expect {
        create_attendance(started_at: started_at + 1.hour)
      }.to raise_error(ActiveRecord::RecordInvalid, /attendance overlaps/)

      expect {
        create_attendance(started_at: started_at - 1.hour)
      }.to raise_error(ActiveRecord::RecordInvalid, /attendance overlaps/)
    end

    it 'allows the same ended_at as another started_at' do
      a1 = create_attendance(ended_at: started_at + 1.hour)
      a2 = create_attendance(ended_at: started_at, started_at: started_at - 1.hour)
    end

    it 'allows the same started_at as another ended_at' do
      a1 = create_attendance(ended_at: started_at + 1.hour)
      a2 = create_attendance(started_at: started_at + 1.hour)
    end

    it 'does not allow overlapping attendances' do
      a1 = create_attendance
      a1.update(ended_at: started_at + 4.hours, break_time_seconds: 2.hours)
      expect(a1).to be_valid

      # started before last ended_at
      expect {
        create_attendance(started_at: started_at + 3.5.hours)
      }.to raise_error(ActiveRecord::RecordInvalid, /attendance overlaps/)

      # touching, but not overlapping
      a2 = create_attendance(started_at: started_at - 1.hour)
      expect {
        a2.update!(ended_at: started_at)
      }.not_to raise_error, "ending at the same moment as another start should be ok"

      # overlap both start and end
      expect {
        create_attendance(started_at: started_at - 10.hours, ended_at: started_at + 10.hours)
      }.to raise_error(ActiveRecord::RecordInvalid, /attendance overlaps/)

      # ended after last started_at (a2)
      expect {
        create_attendance(started_at: started_at - 10.hours, ended_at: started_at - 20.minutes)
      }.to raise_error(ActiveRecord::RecordInvalid, /attendance overlaps/)
    end

    it 'does not allow break times to exceed the interval' do
      expect {
        create_attendance(ended_at: started_at + 1.hour, break_time_seconds: 2.hours)
      }.to raise_error(ActiveRecord::RecordInvalid, /break_time_seconds must fall within time range/)
    end

    it 'does not allow ended_at to preceed started_at' do
      expect {
        create_attendance(ended_at: started_at - 1.hour)
      }.to raise_error(ActiveRecord::RecordInvalid, /ended_at must exceed started_at/)
    end
  end
end
