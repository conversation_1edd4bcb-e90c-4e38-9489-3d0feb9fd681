# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SubProPayLineItem, type: :model do
  describe "associations" do
    it { should belong_to(:sub_pro_pay) }
    it { should belong_to(:identity) }
  end

  context "with multiple crew time entries" do
    subject(:sub_pro_pay) do
      create(:single_milestone_sub_pro_pay_with_time_data,
             budget_type: :hours,
             budget_minutes: 143 * 60)
    end

    it "will total each crew member's time and cost" do
      line_items = SubProPayLineItem.where(sub_pro_pay: sub_pro_pay).order(:identity_id)
      expect(line_items.count).to eq 4

      expect(line_items[0].labor_cost_cents).to eq 135000
      expect(line_items[1].labor_cost_cents).to eq 88000
      expect(line_items[2].labor_cost_cents).to eq 54000
      expect(line_items[3].labor_cost_cents).to eq 22500

      expect(line_items[0].duration_hours).to eq 45.0
      expect(line_items[1].duration_hours).to eq 40.0
      expect(line_items[2].duration_hours).to eq 30
      expect(line_items[3].duration_hours).to eq 15
    end

    it "will allow overrides with creation of ProPayLineItemDetail" do
      line_items = SubProPayLineItem.where(sub_pro_pay: sub_pro_pay).includes(:identity).order(:identity_id)
      expect(line_items.count).to eq 4
      expect(line_items[0].labor_cost).to eq Money.from_amount(1350)
      expect(line_items[0].duration_hours).to eq 45.0
      expect(line_items[0].crew_lead).to eq true

      create(:sub_pro_pay_line_item_detail, sub_pro_pay: sub_pro_pay, crew_lead: false, identity: line_items[0].identity, labor_cost_adjustment: -1330, duration_adjustment_seconds: -(44 * 3600))
      line_items = SubProPayLineItem.where(sub_pro_pay: sub_pro_pay).includes(:identity).order(:identity_id)
      expect(line_items.count).to eq 4
      expect(line_items[0].labor_cost).to eq Money.from_amount(20)
      expect(line_items[0].duration_hours).to eq 1.0
      expect(line_items[0].crew_lead).to eq false
    end

    it "will allow crew_members to be added to a sub_pro_pay without recorded hours" do
      line_items = SubProPayLineItem.where(sub_pro_pay: sub_pro_pay).includes(:sub_pro_pay).order(:identity_id)
      expect(line_items.count).to eq 4

      create(:sub_pro_pay_line_item_detail, sub_pro_pay: sub_pro_pay, crew_lead: false, identity: create(:identity), labor_cost_adjustment: 60, duration_adjustment_seconds: 7200)
      line_items = SubProPayLineItem.where(sub_pro_pay: sub_pro_pay).order(:identity_id)
      expect(line_items.count).to eq 5
      expect(line_items[4].labor_cost).to eq Money.from_amount(60)
      expect(line_items[4].duration_hours).to eq 2.0
    end
  end
end
