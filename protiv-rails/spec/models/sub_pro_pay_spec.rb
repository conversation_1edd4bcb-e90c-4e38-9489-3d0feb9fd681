# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SubProPay, type: :model do
  subject { create(:job_sub_pro_pay) }
  it { should be_valid }

  describe "associations" do
    it { should belong_to(:pro_pay) }
    it { should have_many(:sub_pro_pay_line_items) }
    it { should belong_to(:reference_bonus_pool).optional }
  end

  describe "enums" do
    it do
      should define_enum_for(:status).
        backed_by_column_of_type(:enum).
        with_values(draft: "draft",
                    finalized: "finalized")
    end
    it { should allow_values(:draft, :finalized).for(:status) }

    it do
      should define_enum_for(:budget_type).
        backed_by_column_of_type(:enum).
        with_values(hours: "hours",
                    amount: "amount",
                    rate: "rate")
    end
    it { should allow_values(:hours, :amount, :rate).for(:budget_type) }

    it do
      should define_enum_for(:distribution_type).
        backed_by_column_of_type(:enum).
        with_values(equal_rate: "equal_rate",
                    equal_weighted: "equal_weighted",
                    crew_lead_weighted: "crew_lead_weighted",
                    manual_distribution: "manual_distribution")
    end
    it { should allow_values(:equal_rate, :equal_weighted, :crew_lead_weighted, :manual_distribution).for(:distribution_type) }
  end

  describe "validations" do
    it { should validate_numericality_of(:budget_minutes).only_integer }
    # it { should validate_numericality_of(:budget_rate).only_integer }
    it { should validate_numericality_of(:quantity_budgeted) }
    it { should validate_numericality_of(:crew_lead_bonus_rate) }

    describe "budget hours" do
      subject { build(:sub_pro_pay, budget_type: :hours) }

      it { should validate_presence_of(:budget_minutes) }
    end

    describe "budget amount" do
      subject { build(:sub_pro_pay, budget_type: :amount) }

      it { should validate_presence_of(:budget) }
    end

    describe "budget rate" do
      subject { build(:sub_pro_pay, budget_type: :rate) }

      it { should validate_presence_of(:budget_rate) }
      it { should validate_presence_of(:quantity_budgeted) }
    end
  end

  describe "calculations" do
    describe "for Budget Hours" do
      subject(:sub_pro_pay) { create(:single_milestone_sub_pro_pay_with_time_data,
                                     budget_type: :hours, budget_minutes: 143 * 60) }

      describe "calculated percent complete" do
        it 'returns the correct value' do
          sub_pro_pay.reload
          expect(sub_pro_pay.calculated_percent_complete).to eq(0.9091)
        end
      end

      describe "budget" do
        it 'returns the correct full budget' do
          sub_pro_pay.reload
          expect(sub_pro_pay.full_budget_amount).to eq(Money.from_amount(3_294.50))
        end

        it 'returns the correct budget with calculated percent complete' do
          expect(sub_pro_pay.relative_budget_amount).to eq(Money.from_amount(2995.03))
        end
      end

      describe "bonus" do
        it 'returns the correct bonus when 100% complete' do
          expect(sub_pro_pay.bonus(percent_complete: 1.0)).to eq(Money.from_amount(299.50))
        end

        it 'returns the correct bonus using calculated percent completed' do
          expect(sub_pro_pay.bonus).to eq(Money.from_amount(0.03))
        end

        it 'returns the correct bonus with overridden calculated percent completed' do
          expect(sub_pro_pay.bonus(percent_complete: 0.50)).to eq(Money.from_amount(-1_347.75))
        end

        it 'returns the correct bonus with a custom percent complete' do
          expect(sub_pro_pay.bonus(percent_complete: 0.91)).to eq(Money.from_amount(3))
        end
      end
    end

    describe "for Budget Amount" do
      subject(:sub_pro_pay) { create(:job_sub_pro_pay_with_time_data, budget_type: :amount, budget: Money.from_amount(3_300)) }

      describe "calculated percent complete" do
        it 'returns the correct value' do
          expect(sub_pro_pay.calculated_percent_complete).to eq(0.9076)
          # FIXME: Test with total_duration_seconds from calculated pro_pay_calculated_totals
        end
      end

      describe "budget" do
        it 'returns the correct full budget' do
          expect(sub_pro_pay.full_budget_amount).to eq(Money.from_amount(3_300))
        end

        it 'returns the correct budget with calculated percent complete' do
          expect(sub_pro_pay.relative_budget_amount).to eq(Money.from_amount(2995.08))
        end
      end

      describe "bonus" do
        it 'returns the correct bonus when 100% complete' do
          expect(sub_pro_pay.bonus(percent_complete: 1.0)).to eq(Money.from_amount(305))
        end

        it 'returns the correct bonus with overridden calculated percent completed' do
          expect(sub_pro_pay.bonus(percent_complete: 0.50)).to eq(Money.from_amount(-1345))
        end

        it 'returns the correct bonus with calculated percent complete' do
          expect(sub_pro_pay.bonus).to eq(Money.from_amount(0.08))
        end
      end
    end

    describe "for Budget Rate" do
      subject(:sub_pro_pay) { create(:job_sub_pro_pay_with_time_data, budget_type: :rate, budget_rate: Money.from_amount(8.50), quantity_budgeted: 450.0) }

      describe "calculated percent complete" do
        it 'returns the correct value' do
          sub_pro_pay.quantity_complete = 400
          expect(sub_pro_pay.calculated_percent_complete).to eq(0.8889)

          sub_pro_pay.quantity_complete = 450
          expect(sub_pro_pay.calculated_percent_complete).to eq(1.0)
        end
      end

      describe "budget" do
        it 'returns the correct full budget' do
          expect(sub_pro_pay.full_budget_amount).to eq(Money.from_amount(3_825))
        end

        it 'returns the correct budget with calculated percent complete' do
          sub_pro_pay.quantity_complete = 400
          expect(sub_pro_pay.relative_budget_amount).to eq(Money.from_amount(3400.04))
        end
      end

      describe "bonus" do
        it 'returns the correct bonus when 100% complete' do
          expect(sub_pro_pay.bonus(percent_complete: 1.0)).to eq(Money.from_amount(830.00))
        end

        it 'returns the correct bonus with overridden calculated percent completed' do
          sub_pro_pay.quantity_complete = 400
          expect(sub_pro_pay.bonus(percent_complete: 0.50)).to eq(Money.from_amount(-1_082.5))
        end

        it 'returns the correct bonus with calculated percent complete' do
          sub_pro_pay.quantity_complete = 400
          expect(sub_pro_pay.bonus).to eq(Money.from_amount(405.04))
        end
      end
    end
  end
end
