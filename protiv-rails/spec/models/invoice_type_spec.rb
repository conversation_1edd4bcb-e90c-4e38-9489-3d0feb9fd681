# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvoiceType, type: :model do
  let(:organization) { create(:organization) }
  let(:invoice_type) { build(:invoice_type, organization: organization) } # build avoids hitting the DB unnecessarily for some tests

  describe 'validations' do
    it 'is valid with valid attributes' do
      expect(invoice_type).to be_valid
    end

    it 'is invalid without a name' do
      invoice_type.name = nil
      expect(invoice_type).not_to be_valid
      expect(invoice_type.errors[:name]).to include("can't be blank")
    end

    it 'is invalid without an organization' do
      invoice_type.organization = nil
      expect(invoice_type).not_to be_valid
      expect(invoice_type.errors[:organization]).to include("must exist")
    end

    describe 'name uniqueness per organization' do
      let!(:existing_invoice_type) { create(:invoice_type, organization: organization, name: 'T&M') }

      it 'is invalid with a duplicate name in the same organization' do
        duplicate_invoice_type = build(:invoice_type, organization: organization, name: 'T&M')
        expect(duplicate_invoice_type).not_to be_valid
        expect(duplicate_invoice_type.errors[:name]).to include("has already been taken")
      end

      it 'is valid with the same name in a different organization' do
        another_organization = create(:organization)
        different_org_invoice_type = build(:invoice_type, organization: another_organization, name: 'T&M')
        expect(different_org_invoice_type).to be_valid
      end
    end
  end

  describe 'associations' do
    it { should belong_to(:organization) }
  end

  describe 'trackable concern' do
    # Basic check to ensure the module is included
    it 'includes Trackable' do
      expect(described_class.ancestors).to include(Trackable)
    end
  end
end
