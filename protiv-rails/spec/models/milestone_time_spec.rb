# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MilestoneTime, type: :model do
  let(:now) { Time.now }
  subject { build(:milestone_time) }

  describe "associations" do
    it { should belong_to(:milestone) }
    it { should belong_to(:identity) }
  end

  describe "validations" do
    it { should validate_numericality_of(:base_hourly_rate) }
  end

  it "calculates duration_seconds as 0 when saved if start_time is missing" do
    subject.start_time = nil
    subject.end_time = now + 1.hour
    expect(subject).to be_valid
    expect(subject.duration_seconds).to eq(0)
  end

  it "calculates duration_seconds as 0 when saved if end_time is missing" do
    subject.start_time = now
    subject.end_time = nil
    expect(subject).to be_valid
    expect(subject.duration_seconds).to eq(0)
  end

  it "calculates duration_seconds as non zero when saved if end_time is present" do
    subject.start_time = now
    subject.end_time = now + 1.hour
    expect(subject).to be_valid
    expect(subject.duration_seconds).to eq(1.hour)
  end

  describe "#update_job_activity" do
    let(:identity) { create(:identity) }
    let(:milestone) { create(:milestone, job: job) }
    subject { build(:milestone_time, identity: identity, milestone: milestone) }

    let!(:existing_job) { create(:job) }

    context "when job is nil" do
      let(:job) { nil }

      it "does not update last_activity_at" do
        expect(subject.job).to be_nil
        subject.start_time = now

        expect { subject.save! }.not_to change {
          existing_job.reload.last_activity_at
        }, "if this fails the update_all is incorrectly scoped"
      end
    end

    context "when job is present" do
      let(:job) { existing_job }
      let!(:other_job) { create(:job) }

      def create_milestone_time(**opts)
        create(
          :milestone_time,
          identity: identity,
          milestone: milestone,
          **opts
        )
      end

      it "updates job#last_activity_at & job#status" do
        expect(existing_job.status).to eq("pending")
        expect(other_job.status).to eq("pending")
        expect(subject.job).not_to be_nil
        subject.start_time = now
        subject.save!

        [existing_job, other_job].each(&:reload)

        expect(existing_job.last_activity_at).to eq(subject.start_time)
        expect(existing_job.status).to eq("in_progress")

        # test that the update_all is correctly scoped
        expect(other_job.last_activity_at).not_to eq(subject.start_time)
        expect(other_job.status).to eq("pending")
      end

      it "only updates job#last_activity_at & job#status for newer times" do
        freeze_time

        m1 = create_milestone_time start_time: 1.hour.ago, end_time: Time.now
        travel 1.minute
        expect(job.reload.last_activity_at).to eq(m1.start_time)
        expect(job.status).to eq("in_progress")

        job.update(status: :completed)

        m2 = create_milestone_time start_time: 2.hours.ago, end_time: Time.now
        expect(job.reload.last_activity_at).to eq(m1.start_time)
        expect(job.status).to eq("completed"), "jobs won't transition out of 'completed'"
      end
    end
  end
end
