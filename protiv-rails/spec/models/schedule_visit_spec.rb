# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ScheduleVisit, type: :model do
  subject { build(:schedule_visit) }

  describe "validations" do
    it { should be_valid }
    it { should validate_numericality_of(:hours).is_greater_than_or_equal_to(0).allow_nil }

    describe "uniqueness validation" do
      subject { create(:schedule_visit) }
      it { should validate_uniqueness_of(:remote_id).allow_nil }
    end
  end

  describe "associations" do
    it { should belong_to(:organization) }
    it { should belong_to(:route) }
    it { should belong_to(:milestone) }
  end

  describe "attributes" do
    it "allows nil for hours" do
      subject.hours = nil
      expect(subject).to be_valid
    end

    it "allows nil for scheduled_date" do
      subject.scheduled_date = nil
      expect(subject).to be_valid
    end

    it "allows nil for remote_id" do
      subject.remote_id = nil
      expect(subject).to be_valid
    end

    it "rejects negative hours" do
      subject.hours = -1
      expect(subject).not_to be_valid
      expect(subject.errors[:hours]).to include("must be greater than or equal to 0")
    end
  end
end
