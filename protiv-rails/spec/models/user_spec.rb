# frozen_string_literal: true

require "rails_helper"

RSpec.describe User, type: :model do
  specify do
    user = create(:user)

    expect(user).to be_valid
    expect(user.organizations).to be_present
  end

  describe '#integrations' do
    let!(:integration) { create(:integration) }
    let!(:organization) { integration.organization }
    let(:user) { create(:user) }

    it 'only reveals integrations to admins' do
      expect(user.integrations).to be_empty

      AddUserToOrganization.new(user: user, organization: organization, role_type: :manager).execute!
      user.reload
      expect(user.organizations).to include(integration.organization)
      expect(user.integrations).to be_empty

      AddUserToOrganization.new(user: user, organization: organization, role_type: :admin).execute!
      user.reload
      expect(user.integrations).to include(integration)
    end
  end

  describe "validations" do
    subject { build(:user) } # Use build to avoid hitting DB unless needed

    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(100) }

    it { should validate_presence_of(:email) }
    it { should validate_uniqueness_of(:email).case_insensitive }
    it { should allow_value("<EMAIL>").for(:email) }
    it { should_not allow_value("user@example").for(:email) }
    it { should_not allow_value("userexample.com").for(:email) }

    it { should validate_length_of(:password).is_at_least(8) }

    context "when phone is present" do
      before { subject.phone = "1234567890" }
      it { should validate_presence_of(:phone_country_code) }
      it { should validate_length_of(:phone_country_code).is_at_most(4) }
    end

    context "when phone_country_code is present" do
      before { subject.phone_country_code = "+1" }
      it { should validate_presence_of(:phone) }
      it { should validate_length_of(:phone).is_at_most(20) }
    end

    context "when phone and country code are absent" do
      it { should be_valid } # This check ensures they are not required
    end
  end

  describe "token generation" do
    let(:user) { create(:user) }

    describe ":password_change_verification" do
      let(:token) { user.generate_token_for(:password_change_verification) }

      it "generates a token" do
        expect(token).to be_a(String)
      end

      it "can find user by token" do
        found_user = User.find_by_token_for(:password_change_verification, token)
        expect(found_user).to eq(user)
      end

      it "generates tokens with expiry info" do
        expect(token).to be_present
      end

      it "generates a different token after password change" do
        old_token = token
        user.update(password: "new_password_123")
        new_token = user.generate_token_for(:password_change_verification)

        expect(new_token).not_to eq(old_token)
      end
    end

    # Add similar tests for :email_verification and :password_reset if missing
    # describe ":email_verification" do ... end
    # describe ":password_reset" do ... end
  end

  describe "#invalidate_all_sessions!" do
    let(:user) { create(:user, token_version: 1) }

    it "increments the token_version" do
      expect { user.invalidate_all_sessions! }.to change { user.reload.token_version }.from(1).to(2)
    end

    it "creates a sessions_invalidated event" do
      expect { user.invalidate_all_sessions! }.to change { user.events.count }.by(1)
      expect(user.events.last.action).to eq("sessions_invalidated")
    end

    it "invalidates existing API tokens" do
      # Generate a token with the current version
      old_token = user.generate_token_for(:api)

      # Invalidate all sessions
      user.invalidate_all_sessions!

      # Token should no longer be valid
      expect(User.find_by_token_for(:api, old_token)).to be_nil

      # New token should work
      new_token = user.generate_token_for(:api)
      expect(User.find_by_token_for(:api, new_token)).to eq(user)
    end

    it "only invalidates tokens for the specific user" do
      other_user = create(:user)
      user_token = user.generate_token_for(:api)
      other_token = other_user.generate_token_for(:api)

      # Invalidate sessions for only one user
      user.invalidate_all_sessions!

      # This user's token should be invalid
      expect(User.find_by_token_for(:api, user_token)).to be_nil

      # The other user's token should still be valid
      expect(User.find_by_token_for(:api, other_token)).to eq(other_user)
    end
  end

  describe "#can_clock_in?" do
    let(:organization) { create(:organization) }
    let(:user) { create(:user) }
    let(:identity) { create(:identity, user: user, organization: organization) }
    let(:other_identity) { create(:identity, organization: organization) }

    before do
      AddUserToOrganization.new(
        user:,
        organization:,
        role_type:,
      ).execute!
    end

    context "when user is an employee" do
      let(:role_type) { Role::EMPLOYEE }

      specify do
        expect(user.can_clock_in?(identity)).to eq(true)
      end

      specify do
        expect(user.can_clock_in?(other_identity)).to eq(false)
      end
    end

    context "when user is a manager" do
      let(:role_type) { Role::MANAGER }

      specify do
        expect(user.can_clock_in?(identity)).to eq(true)
      end

      specify do
        expect(user.can_clock_in?(other_identity)).to eq(true)
      end
    end

    context "when user is an admin" do
      let(:role_type) { Role::ADMIN }

      specify do
        expect(user.can_clock_in?(identity)).to eq(true)
      end

      specify do
        expect(user.can_clock_in?(other_identity)).to eq(true)
      end
    end
  end
end
