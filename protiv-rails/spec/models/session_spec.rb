# frozen_string_literal: true

require "rails_helper"

RSpec.describe Session do
  let(:user) { create(:user) }
  let(:organization) { user.organizations.first }

  specify do
    session = Session.new(user, organization)
    token = session.generate_token_for(:api)
    retrieved = Session.find_by_token_for!(:api, token)
    expect(retrieved.user).to eq(user)
    expect(retrieved.organization).to eq(organization)
  end
end
