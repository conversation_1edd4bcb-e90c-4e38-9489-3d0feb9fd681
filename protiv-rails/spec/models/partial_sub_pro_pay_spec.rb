# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PartialSubProPay, type: :model do
  specify do
    expect(create(:partial_sub_pro_pay, status: 'finalized')).to be_valid
  end

  describe "associations" do
    it { should belong_to(:sub_pro_pay) }
    it { should have_many(:bonus_line_items) }
  end

  describe "enums" do
    it do
      should define_enum_for(:status).
        backed_by_column_of_type(:enum).
        with_values(draft: "draft",
                    materialized: "materialized",
                    finalized: "finalized")
    end
    it { should allow_values(:draft, :materialized, :finalized).for(:status) }
  end

  describe "validations" do
    it { should validate_numericality_of(:estimated_percent_complete).only_integer }
    it { should validate_numericality_of(:bonus) }

    describe "budget hours" do
      subject { build(:sub_pro_pay, budget_type: :hours) }

      it { should validate_presence_of(:budget_minutes) }
    end

    describe "budget amount" do
      subject { build(:sub_pro_pay, budget_type: :amount) }

      it { should validate_presence_of(:budget) }
    end

    describe "budget rate" do
      subject { build(:sub_pro_pay, budget_type: :rate) }

      it { should validate_presence_of(:budget_rate) }
      it { should validate_presence_of(:quantity_budgeted) }
    end
  end

  describe "status" do
    describe "draft" do
      subject(:partial_sub_pro_pay) do
        create(:partial_sub_pro_pay,
               sub_pro_pay: create(:single_milestone_sub_pro_pay_with_time_data,
                                   budget_type: "hours",
                                   distribution_type: "equal_rate",
                                   budget_minutes: 143 * 60))
      end

      it "enters as draft" do
        expect(partial_sub_pro_pay.status).to eq("draft")
      end

      it "can be materialized using materialize" do
        expect(partial_sub_pro_pay.bonus_line_items.count).to be_zero

        expect(partial_sub_pro_pay.materialized_at).to be_nil

        partial_sub_pro_pay.materialize
        expect(partial_sub_pro_pay.status).to eq("materialized")
        expect(partial_sub_pro_pay.materialized_at).not_to be_nil

        expect(partial_sub_pro_pay.bonus_line_items.count).to be_positive
      end
    end
  end
end
