# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Service, type: :model do
  describe 'progress_type' do
    it 'defaults to percentage' do
      service = create(:service)
      expect(service.progress_type).to eq('percentage')
    end

    it 'can be set to units' do
      service = create(:service, progress_type: 'units')
      expect(service.progress_type).to eq('units')
    end

    it 'validates progress_type inclusion' do
      expect {
        build(:service, progress_type: 'invalid')
      }.to raise_error(ArgumentError, "'invalid' is not a valid progress_type")
    end
  end

  describe 'material associations' do
    it 'can have multiple tracking materials' do
      organization = create(:organization)
      service = create(:service, organization: organization)
      material1 = create(:catalog_item, organization: organization, item_type: 'Material')
      material2 = create(:catalog_item, organization: organization, item_type: 'Material')

      service.tracking_materials << [material1, material2]

      expect(service.tracking_materials).to include(material1, material2)
    end

    it 'only allows materials, not labor items' do
      organization = create(:organization)
      service = create(:service, organization: organization)
      labor_item = create(:catalog_item, organization: organization, item_type: 'Labor')

      expect {
        service.service_materials.create!(catalog_item: labor_item)
      }.to raise_error(ActiveRecord::RecordInvalid, /Only materials can be associated/)
    end

    it 'prevents duplicate material associations' do
      organization = create(:organization)
      service = create(:service, organization: organization)
      material = create(:catalog_item, organization: organization, item_type: 'Material')

      service.tracking_materials << material

      expect {
        service.tracking_materials << material
      }.to raise_error(ActiveRecord::RecordNotUnique)
    end
  end

  describe 'tracking materials for units-based services' do
    context 'when progress_type is percentage' do
      it 'tracking_materials are available but not used for progress tracking' do
        organization = create(:organization)
        service = create(:service, organization: organization, progress_type: 'percentage')
        material = create(:catalog_item, organization: organization, item_type: 'Material')
        service.tracking_materials << material

        expect(service.tracking_materials).to include(material)
      end
    end

    context 'when progress_type is units' do
      it 'returns associated tracking materials' do
        organization = create(:organization)
        service = create(:service, organization: organization, progress_type: 'units')
        material1 = create(:catalog_item, organization: organization, item_type: 'Material')
        material2 = create(:catalog_item, organization: organization, item_type: 'Material')
        service.tracking_materials << [material1, material2]

        expect(service.tracking_materials).to match_array([material1, material2])
      end
    end
  end
end
