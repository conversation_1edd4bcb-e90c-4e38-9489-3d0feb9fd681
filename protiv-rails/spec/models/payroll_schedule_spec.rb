# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PayrollSchedule, type: :model do
  specify do
    expect(create(:payroll_schedule)).to be_valid
  end

  # FIXME: Test date computations

  # FIXME: Move this to Branch
  # context "with a current pay_period" do
  #   subject(:payroll_schedule) do
  #     create(:payroll_schedule_with_period)
  #   end
  #
  #   it "can find the current pay_period" do
  #     expect(payroll_schedule.pay_periods.count).to eq 1
  #     expect(payroll_schedule.current_pay_period).to be_present
  #   end
  # end
  #
  # context "without a current pay_period" do
  #   subject(:payroll_schedule) do
  #     create(:payroll_schedule_with_prior_period)
  #   end
  #
  #   it "can find the current pay_period" do
  #     expect(payroll_schedule.pay_periods.count).to eq 1
  #     expect(payroll_schedule.current_pay_period).to_not be_present
  #   end
  # end
  #
  # context "with a current pay_period, but not a future pay_period" do
  #   subject(:payroll_schedule) do
  #     create(:payroll_schedule_with_period)
  #   end
  #
  #   it "can find a PayrollSchedule that does not have a future pay period" do
  #     expect(payroll_schedule.pay_periods.count).to eq 1
  #
  #     expect(PayrollSchedule.with_future_pay_period).to_not be_present
  #     expect(PayrollSchedule.without_future_pay_period).to be_present
  #
  #     create(:pay_period,
  #            payroll_schedule: payroll_schedule,
  #            start_time: payroll_schedule.pay_periods.last.end_time + 1.day,
  #            end_time: payroll_schedule.end_date_from_start_date(payroll_schedule.pay_periods.last.end_time + 1.day))
  #
  #     expect(PayrollSchedule.without_future_pay_period).to_not be_present
  #   end
  # end
end
