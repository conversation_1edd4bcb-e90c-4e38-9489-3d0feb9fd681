# frozen_string_literal: true

require "rails_helper"

RSpec.describe EmailsController do
  let(:user) { create(:user) }

  before do
    sign_in_as(user)
  end

  specify "should get edit" do
    get edit_email_url
    assert_response :success
  end

  specify "should update email" do
    patch email_url, params: { email: "<EMAIL>", password_challenge: "Secret1*3*5*" }
    assert_redirected_to root_url
  end


  specify "should not update email with wrong password challenge" do
    patch email_url, params: { email: "<EMAIL>", password_challenge: "SecretWrong1*3" }

    assert_response :unprocessable_entity
    assert_select "li", /Password challenge is invalid/
  end
end
