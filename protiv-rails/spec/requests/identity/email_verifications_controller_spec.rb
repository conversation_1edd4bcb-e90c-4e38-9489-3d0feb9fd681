# frozen_string_literal: true

require "rails_helper"

RSpec.describe EmailVerificationsController, type: :request do
  let(:user) { create(:user) }

  before do
    sign_in_as(user)
    user.update! verified: false
  end

  specify "should send a verification email" do
    assert_enqueued_email_with <PERSON>r<PERSON><PERSON><PERSON>, :email_verification, params: { user: user } do
      post email_verification_url
    end

    assert_redirected_to root_url
  end

  specify "should verify email" do
    sid = user.generate_token_for(:email_verification)

    get email_verification_url(sid: sid, email: user.email)
    assert_redirected_to root_url
  end

  specify "should not verify email with expired token" do
    sid = user.generate_token_for(:email_verification)

    travel 3.days

    get email_verification_url(sid: sid, email: user.email)

    assert_redirected_to edit_email_url
    assert_equal "That email verification link is invalid", flash[:alert]
  end
end
