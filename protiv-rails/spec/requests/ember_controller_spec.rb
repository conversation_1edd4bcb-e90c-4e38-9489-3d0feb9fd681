# frozen_string_literal: true

require "rails_helper"

RSpec.describe EmberController do
  include SpecHelpers::Ember

  let(:user) { create(:user) }

  context "unauthenticated" do
    before do
      expect(@request&.session).to be_nil # This is a bit weird
    end

    specify do
      get "/dashboard/arbitrary/path?foo"

      expect(response).to redirect_to(sign_in_path(redirect: '/dashboard/arbitrary/path?foo'))
    end

    context "with a magic token" do
      let!(:token) { user.sign_in_token }

      it "sets the current session" do
        expect do
          get "/dashboard/arbitrary/path", params: { sid: token }
        end.to change { SignInToken.count }.from(1).to(0)

        expect(session[:user_id]).to eq(user.id)
        index_html = parse_index_html(response.body)
        session = Session.find_by_token_for(:api, index_html.authentication_token)
        expect(session.user).to eq(user)
      end
    end

    context "with an invalid token" do
      let(:token) { user.sign_in_token(expires_in: 0) }

      specify do
        get "/dashboard/arbitrary/path", params: { sid: token }

        # FIXME: flash message?
        expect(response).to redirect_to(sign_in_path)
        expect(session[:user_id]).to be_nil
      end
    end
  end

  context "with current session" do
    before do
      sign_in_as(user)
    end

    specify do
      get "/dashboard/arbitrary/path"

      expect(response.status).to eq(200)
      index_html = parse_index_html(response.body)
      session = Session.find_by_token_for(:api, index_html.authentication_token)
      expect(session.user).to eq(user)
    end

    context "with a magic token" do
      let(:new_user) { create(:user) }
      let!(:token) { new_user.sign_in_token }

      it "replaces the current session" do
        expect do
          get "/dashboard/arbitrary/path", params: { sid: token }
        end.to change { SignInToken.count }.from(1).to(0)

        expect(session[:user_id]).to eq(new_user.id)
        index_html = parse_index_html(response.body)
        session = Session.find_by_token_for(:api, index_html.authentication_token)
        expect(session.user).to eq(new_user)
      end

      context "and a redirect" do
        let(:redirect) { "/some/other/path?with=query" }
        it "replaces the current session" do
          expect do
            get "/passwordless", params: { sid: token, redirect: redirect }
          end.to change { SignInToken.count }.from(1).to(0)

          expect(session[:user_id]).to eq(new_user.id)
          expect(response).to redirect_to(redirect)
        end
      end
    end

    context "with an invalid token" do
      let(:token) { user.sign_in_token(expires_in: 0) }

      specify do
        get "/"
        expect(session[:user_id]).to eq(user.id)

        get "/dashboard/arbitrary/path", params: { sid: token }

        # FIXME: flash message?
        expect(response).to redirect_to(sign_in_path)
        expect(session[:user_id]).to be_nil
      end
    end

    describe "#sign_out" do
      before { sign_in_as(user) }

      before do
        get "/"
        expect(session[:user_id]).to eq(user.id)
      end

      it "clears the session with a redirect" do
        get "/sign-out?redirect=/marketing/page"
        expect(session[:user_id]).to eq(nil)
        expect(response).to redirect_to("/marketing/page")
      end

      it "clears the session" do
        get "/sign-out"
        expect(session[:user_id]).to eq(nil)
        expect(response).to redirect_to("/")
      end
    end
  end
end
