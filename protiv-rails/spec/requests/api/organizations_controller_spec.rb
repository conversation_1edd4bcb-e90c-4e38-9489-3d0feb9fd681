# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::OrganizationsController, type: :request do
  let(:user) { fixture(:user, :john_appleseed) }
  let(:headers) { auth_for(user) }

  describe "#index" do
    context "when authorized" do
      specify do
        jsonapi_get api_organizations_path, headers: headers

        expect(response).to be_successful
        data = response.parsed_body

        expect(data.fetch("data").length).to eq(user.organizations.length)
      end
    end
  end

  describe "#create" do
    let(:organization_name) { "New Test Organization" }
    let(:organization_params) do
      {
        data: {
          type: "organizations",
          attributes: {
            name: organization_name
          }
        }
      }
    end

    context "when authenticated" do
      it "creates a new organization" do
        expect {
          jsonapi_post api_organizations_path, organization_params, headers: headers
        }.to change(Organization, :count).by(1)
          .and change(PayrollSchedule, :count).by(3)

        expect(response).to have_http_status(:created)

        data = jsonapi_data
        expect(data.jsonapi_type).to eq("organization")
        expect(data.attributes[:name]).to eq(organization_name)

        # Verify the user is an admin of the new organization
        organization = Organization.find(data.id)
        expect(user.admin_organizations).to include(organization)

        # Verify default schedules were created
        expect(organization.default_payroll_schedule).to be_present
        expect(organization.default_bonus_schedule).to be_present
        expect(organization.default_route_schedule).to be_present

        # Verify labor efficiency benchmark was created
        expect(organization.labor_efficiency_benchmark).to be_present
        expect(organization.labor_efficiency_benchmark.target_percentage).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
        expect(organization.labor_efficiency_benchmark.level).to eq("organization")
        expect(organization.labor_efficiency_benchmark.active).to be true
      end
    end

    context "when not authenticated" do
      it "returns unauthorized" do
        jsonapi_post api_organizations_path, organization_params
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with invalid parameters" do
      before do
        organization_params[:data][:attributes][:name] = ""
      end

      it "returns validation errors" do
        jsonapi_post api_organizations_path, organization_params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)

        # Just verify that the response contains an error
        error_response = JSON.parse(response.body)
        expect(error_response).to have_key("errors")
      end
    end
  end
end
