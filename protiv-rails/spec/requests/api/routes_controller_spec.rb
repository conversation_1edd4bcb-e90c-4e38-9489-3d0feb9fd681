# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::RoutesController do
  describe "#index" do
    let(:user) { fixture(:user, :luigi) }

    context "without routes" do
      let(:organization) { fixture(:organization, :protiv) }
      let(:headers) { auth_for(user, organization) }

      specify do
        jsonapi_get api_routes_path,
                    headers: headers

        expect(response).to be_successful
        data = response.parsed_body

        expect(data["data"]).to eq([])
      end
    end

    context "with routes" do
      let(:organization) { fixture(:organization, :lawn_mow_inc) }
      let(:headers) { auth_for(user, organization) }

      specify do
        jsonapi_get api_routes_path,
                    headers: headers

        expect(response).to be_successful
        data = response.parsed_body

        expect(organization.routes.count).to satisfy("be greater than 0") { |n| n > 0 }
        expect(data["data"].count).to eq(organization.routes.count)
      end
    end

    context "as an unauthorized user" do
      let(:other_user) { fixture(:user, :john_appleseed) }
      let(:headers) { auth_for(other_user) }

      specify do
        jsonapi_get api_routes_path, headers: headers

        expect(response.parsed_body.fetch("data")).to eq([])
      end
    end
  end

  describe "#update" do
    subject(:make_request) do
      jsonapi_patch "/api/v2/routes/#{route.id}", payload, headers: headers
    end

    context "with routes" do
      let(:organization) { fixture(:organization, :lawn_mow_inc) }
      let(:headers) { auth_for(user, organization) }

      let!(:route) { fixture(:route, :lakewood_route_1) }
      let(:payload) do
        {
          data: {
            id: route.id,
            type: "route",
            attributes: {
              create_grouped_pro_pays: true
            }
          }
        }
      end

      let(:instance) do
        RouteResource.find(payload)
      end

      context "as a manager user" do
        let(:user) { fixture(:user, :luigi) }

        it 'updates the resource' do
          expect(RouteResource).to receive(:find).and_call_original
          expect { make_request }.to change { route.reload.create_grouped_pro_pays }.from(false).to(true)
          expect(response.status).to eq(200)
        end
      end

      context "as an employee user" do
        let(:user) { fixture(:user, :louis) }

        it 'updates the resource' do
          expect { make_request }.not_to change { route.reload.create_grouped_pro_pays }.from(false)
          expect(response.status).to eq(401)
        end
      end

      context "as an unauthorized user" do
        let(:user) { fixture(:user, :john_appleseed) }

        it 'updates the resource' do
          expect { make_request }.not_to change { route.reload.create_grouped_pro_pays }.from(false)
          expect(response.status).to eq(401)
        end
      end
    end
  end
end
