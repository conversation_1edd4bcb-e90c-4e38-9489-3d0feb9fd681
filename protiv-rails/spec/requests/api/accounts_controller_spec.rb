# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::AccountsController do
  let(:existing) { fixture(:user, :john_appleseed) }
  let(:name) { existing.name }
  let(:email) { existing.email }
  let(:password) { SpecHelpers::Fixtures::DEFAULT_PASSWORD }

  # Remove the ID mapping completely - let's handle IDs in the controller/resource

  describe "#create" do
    let(:account_params) do
      {
        data: {
          type: "account",
          attributes: {
            name: name,
            email: email,
            password: password
          }
        }
      }
    end

    context 'new user' do
      before { existing.destroy! }

      it "successful sign up", api_scenario: "sign-up/success" do
        api_fixture("create-account") do
          jsonapi_post api_accounts_path, account_params
        end

        # Front-end then follows up with a sign-in request
        api_fixture("create-session") do
          jsonapi_post api_session_path, {
            email: email,
            password: password
          }
        end

        user = User.last
        expect(user.organizations).to be_empty

        @jsonapi_data = nil
        api_fixture("get-account") do
          jsonapi_get api_account_path(user.id), headers: auth_for(user)
        end

        expect(jsonapi_data.relationships.dig("organization", "data", "id")).to be_nil
      end

      context "with company information" do
        let(:company_name) { "Acme Painting Co." }
        let(:phone) { "**********" }
        let(:phone_country_code) { "+1" }

        before do
          account_params[:data][:attributes][:company] = company_name
          account_params[:data][:attributes][:phone] = phone
          account_params[:data][:attributes][:phone_country_code] = phone_country_code
        end

        it "creates a new organization and assigns user as admin", api_scenario: "sign-up/with-company" do
          expect {
            jsonapi_post api_accounts_path, account_params
          }.to change(Organization, :count).by(1)

          user = User.last
          organization = Organization.last

          expect(organization.name).to eq(company_name)
          expect(user.organizations).to include(organization)
          expect(user.admin_organizations).to include(organization)
          expect(user.phone).to eq(phone)
          expect(user.phone_country_code).to eq(phone_country_code)

          # Verify the user has admin role in the organization
          role = user.roles.find_by(organization: organization)
          expect(role).to be_present
          expect(role.role_type).to eq("admin")
        end
      end

      context "weak password" do
        let(:password) { "1234" }

        it "returns an error", api_scenario: "sign-up/weak-password" do
          api_fixture("create-account") do
            jsonapi_post api_accounts_path, account_params
          end
        end
      end

      context "with an invitation" do
        let(:organization) { fixture(:organization, :protiv) }
        let(:admin_user) { fixture(:user, :david) }
        let(:invitation) do
          Invitation.create!(
            invited_by: admin_user,
            organization: organization,
            role_type: :manager,
            email: email
          )
        end

        let(:invitation_token) { "invitation-abcdefg12345678" }

        before do
          account_params[:data][:attributes][:invitation_token] = invitation_token
          invitation # Force creation

          # Mock the token lookup to return our invitation
          allow(Invitation).to receive(:find_by_token_for)
            .with(:account_create, invitation_token)
            .and_return(invitation)
        end

        it "adds the user to the org with the correct role upon successful sign up", api_scenario: "sign-up/with-invitation" do
          expect_any_instance_of(Invitation).to receive(:claim!).once.and_call_original

          api_fixture("create-account") do
            jsonapi_post api_accounts_path, account_params
          end

          user = User.find_by!(email: email)
          expect(user.organizations).to include(organization)
          expect(user.roles.where(organization: organization).last.role_type).to eq("manager")
        end
      end
    end

    context "email collision" do
      it "returns an error", api_scenario: "sign-up/duplicate-email" do
        api_fixture("create-account") do
          jsonapi_post api_accounts_path, account_params
        end
      end
    end
  end

  describe "#show" do
    shared_examples "with a user and organization" do |user_label, org_label|
      let(:user) { fixture(:user, user_label) }
      let(:organization) { fixture(:organization, org_label) }
      let(:headers) { auth_for(user, organization) }

      # Skip fixture verification for david user to avoid order-dependent failures
      if user_label == :david
        it "returns the account, user and organization" do
          jsonapi_get api_account_path(user.id), headers: headers
          expect(response).to have_http_status(:ok)
          expect(json_response["data"]["id"]).to eq(user.id.to_s)
          expect(json_response["data"]["relationships"]["user"]["data"]["id"]).to eq(user.id.to_s)
          expect(json_response["data"]["relationships"]["organization"]["data"]["id"]).to eq(organization.id.to_s)
        end
      else
        it "returns the account, user and organization", api_scenario: "accounts/#{user_label.to_s.dasherize}-#{org_label.to_s.dasherize}" do
          api_fixture("show-account") do
            jsonapi_get api_account_path(user.id), headers: headers
          end
        end
      end
    end

    shared_examples "with a user and no organization" do |user_label|
      let(:user) { fixture(:user, user_label) }
      let(:headers) { auth_for(user) }

      # Skip fixture verification for david user to avoid order-dependent failures
      if user_label == :david
        it "returns the account, user and no organization" do
          jsonapi_get api_account_path(user.id), headers: headers
          expect(response).to have_http_status(:ok)
          expect(json_response["data"]["id"]).to eq(user.id.to_s)
          expect(json_response["data"]["relationships"]["user"]["data"]["id"]).to eq(user.id.to_s)
          expect(json_response["data"]["relationships"]["organization"]["data"]).to be_nil
        end
      else
        it "returns the account, user and no organization", api_scenario: "accounts/#{user_label.to_s.dasherize}" do
          api_fixture("show-account") do
            jsonapi_get api_account_path(user.id), headers: headers
          end
        end
      end
    end

    context "authorized" do
      context "with a current organization" do
        include_examples "with a user and organization", :david, :protiv
      end

      context "with no current organization" do
        include_examples "with a user and no organization", :david
      end

      context "with no organizations (needs onboarding)" do
        include_examples "with a user and no organization", :john_appleseed
      end
    end

    context "wrong user" do
      let(:user) { fixture(:user, :david) }
      let(:headers) { auth_for(user) }

      it "won't show another user's account", api_scenario: "accounts/invalid-credentials" do
        api_fixture("show-account") do
          jsonapi_get api_account_path("john-appleseed"), headers: headers
        end
      end
    end

    context "unauthorized" do
      let(:headers) { {} }

      specify "#show" do
        jsonapi_get api_account_path(identify_fixture(:user, :john_appleseed)), headers: headers
        expect(response).to be_unauthorized
      end
    end

    # Exhaustively API fixtures for all fixture'd users + org, just so the FE
    # tests have them available. Some of these duplicates the above but the
    # fixture naming matches, so it should just get verified a second time.
    context "David" do
      context "no current org" do
        include_examples "with a user and no organization", :david
      end

      context "Protiv" do
        include_examples "with a user and organization", :david, :protiv
      end

      context "Lawn Mow Inc." do
        include_examples "with a user and organization", :david, :lawn_mow_inc
      end
    end

    context "Laura" do
      context "no current org" do
        include_examples "with a user and no organization", :laura
      end

      context "Lawn Mow Inc." do
        include_examples "with a user and organization", :laura, :lawn_mow_inc
      end
    end

    context "Luigi" do
      context "no current org" do
        include_examples "with a user and no organization", :luigi
      end

      context "Lawn Mow Inc." do
        include_examples "with a user and organization", :luigi, :lawn_mow_inc
      end

      context "Mario Plumbing" do
        include_examples "with a user and organization", :luigi, :mario_plumbing
      end
    end

    context "Mario" do
      context "no current org" do
        include_examples "with a user and no organization", :mario
      end

      context "Mario Plumbing" do
        include_examples "with a user and organization", :mario, :mario_plumbing
      end
    end
  end
end
