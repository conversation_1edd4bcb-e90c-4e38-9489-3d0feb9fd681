# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::InvitationsController, type: :request do
  let(:organization) { create(:organization) }
  let(:admin) { create(:user, admin: true) } # Ensure admin flag is true
  let(:target_user) { create(:user) }

  describe "POST /api/invitations" do
    let(:valid_params) do
      {
        data: {
          type: "invitation",
          attributes: {
            email: target_user.email,
            role_type: "employee"
          }
        }
      }
    end

    context "when authenticated as admin" do
      before do
        # Ensure admin has proper role in the organization
        unless admin.roles.exists?(organization: organization, role_type: "admin")
          Role.create!(
            user: admin,
            organization: organization,
            role_type: "admin",
            active_at: Time.current
          )
        end

        @headers = auth_for(admin, organization).to_hash
        allow_any_instance_of(Api::InvitationsController).to receive(:current_organization).and_return(organization)

        # Mock Mandrill API calls
        allow(MandrillMailer).to receive(:send_mail).and_return(true)
      end

      context "when user exists" do
        let!(:target_user) { create(:user, email: "<EMAIL>") }
        let(:valid_params) do
          {
            data: {
              type: "invitation",
              attributes: {
                email: target_user.email,
                role_type: "employee"
              }
            }
          }
        end

        it "creates an invitation and sends email" do
          # Mock the UserMailer to avoid actual email sending
          mail_double = double("Mail::Message", deliver_now: true)
          expect_any_instance_of(UserMailer).to receive(:invitation_instructions).and_return(mail_double)

          expect {
            jsonapi_post "/api/invitations", valid_params, headers: @headers
          }.to change(Invitation, :count).by(1)

          expect(response).to have_http_status(:created)
          expect(json_response["data"]["attributes"]["email"]).to eq(target_user.email)

          # Verify the invitation was created with correct attributes
          invitation = Invitation.last
          expect(invitation.email).to eq(target_user.email)
          expect(invitation.organization).to eq(organization)
          expect(invitation.role_type).to eq("employee")
        end
      end

      context "when user doesn't exist" do
        let(:valid_params) do
          {
            data: {
              type: "invitation",
              attributes: {
                email: "<EMAIL>",
                role_type: "employee"
              }
            }
          }
        end

        it "returns error" do
          jsonapi_post "/api/invitations", valid_params, headers: @headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_response["errors"][0]["code"]).to eq("not_found")
          expect(json_response["errors"][0]["title"]).to eq("User not found")
          expect(json_response["errors"][0]["detail"]).to eq("No registered user found with this email address")
        end
      end

      context "when user is already a member" do
        before do
          AddUserToOrganization.new(
            user: target_user,
            organization: organization,
            role_type: "employee"
          ).execute!
        end

        it "returns error" do
          jsonapi_post "/api/invitations", valid_params, headers: @headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_response["errors"][0]["code"]).to eq("already_member")
          expect(json_response["errors"][0]["title"]).to eq("Already a member")
          expect(json_response["errors"][0]["detail"]).to eq("User is already a member of this organization")
        end
      end

      context "with invalid role_type" do
        let(:valid_params) do
          {
            data: {
              type: "invitation",
              attributes: {
                email: target_user.email,
                role_type: "invalid_role"
              }
            }
          }
        end

        it "returns error" do
          jsonapi_post "/api/invitations", valid_params, headers: @headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_response["errors"][0]["code"]).to eq("invalid_role_type")
          expect(json_response["errors"][0]["title"]).to eq("Invalid role type")
          expect(json_response["errors"][0]["detail"]).to include("role_type")
        end
      end
    end

    context "when not authenticated as admin" do
      let(:regular_user) { create(:user, organization: organization, role_type: "employee") }

      before do
        api_authenticate(regular_user)
        allow_any_instance_of(Api::InvitationsController).to receive(:current_organization).and_return(organization)
      end

      it "returns forbidden error" do
        jsonapi_post "/api/invitations", valid_params, headers: @headers

        expect(response).to have_http_status(:forbidden)
        expect(json_response["errors"][0]["code"]).to eq("unauthorized")
        expect(json_response["errors"][0]["title"]).to eq("Unauthorized")
        expect(json_response["errors"][0]["detail"]).to eq("You don't have permission to invite users to this organization")
      end
    end
  end
end
