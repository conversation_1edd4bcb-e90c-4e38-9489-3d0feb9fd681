# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::MilestonesController do
  context "not authorized" do
    specify do
      jsonapi_get(api_milestones_path)

      expect(controller.current_user).to be_nil
      expect(controller.current_organization).to be_nil
      expect(response).to be_unauthorized
    end
  end

  context "authorized" do
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:user) { fixture(:user, :luigi) }
    let(:identity) { fixture(:identity, :luigi) }
    let(:employee) { fixture(:identity, :louis) }
    let(:job) { fixture(:job, :lawn_q2) }
    let(:milestone) { fixture(:milestone, :lawn_q2_milestone_1) }
    let(:headers) { auth_for(user, organization) }

    describe "GET show" do
      it "returns the requested milestone", api_scenario: "milestone" do
        jsonapi_get(
          api_milestone_path(milestone),
          headers: headers
        )

        expect(response).to be_successful

        data = jsonapi_data
        expect(data.jsonapi_type).to eq("milestone")
        expect(data.id).to eq(milestone.id)
        expect(data.attributes[:status]).to eq("in_progress")
      end

      it "includes employee_count attribute" do
        jsonapi_get(
          api_milestone_path(milestone),
          headers: headers
        )

        expect(response).to be_successful
        expect(jsonapi_data.attributes[:employee_count]).to be_present
      end

      it "includes related job and milestone_times when requested" do
        jsonapi_get(
          api_milestone_path(milestone, include: "job,milestone_times"),
          headers: headers
        )

        expect(response).to be_successful

        data = jsonapi_data
        expect(data.sideload(:job).id).to eq(job.id)
        expect(data.sideload(:milestone_times)).not_to be_empty
      end

      it "includes related employee_milestone_times when requested and paginated", api_scenario: "milestones" do
        api_fixture("get-with-employee-milestone-times") do
          jsonapi_get(
            api_milestone_path(
              milestone,
              include: "employee_milestone_times.identity,job",
              page: {
                "employee_milestone_times.number": 0,
                "employee_milestone_times.size": 5
              }
            ),
            headers: headers
          )
        end

        expect(response).to be_successful
        employee_milestone_times = jsonapi_data.sideload(:employee_milestone_times)
        expect(employee_milestone_times.size).to eq(1)
        expect(employee_milestone_times.first.sideload(:identity).id).to eq(employee.id)
        # TODO: add more milestone_times to the fixture and test that the pagination works
      end

      context "with material selection attributes" do
        let(:service) { create(:service, organization: organization, progress_type: 'units') }
        let(:material1) { create(:catalog_item, organization: organization, item_type: 'Material') }
        let(:material2) { create(:catalog_item, organization: organization, item_type: 'Material') }
        let(:milestone_with_service) { create(:milestone, organization: organization, service: service) }

        before do
          service.tracking_materials << [material1, material2]
        end

        it "includes needs_material_selection when multiple materials available" do
          jsonapi_get(
            api_milestone_path(milestone_with_service),
            headers: headers
          )

          expect(response).to be_successful
          data = jsonapi_data
          expect(data.attributes[:needs_material_selection]).to eq(true)
          # tracking_materials removed - frontend should fetch via service relationship
        end

        it "returns false for needs_material_selection when material already selected" do
          milestone_item = create(:milestone_item, milestone: milestone_with_service, catalog_item: material1)
          milestone_with_service.update!(tracked_milestone_item: milestone_item)

          jsonapi_get(
            api_milestone_path(milestone_with_service),
            headers: headers
          )

          expect(response).to be_successful
          data = jsonapi_data
          expect(data.attributes[:needs_material_selection]).to eq(false)
        end

        it "provides eligible materials via service relationship (JSON:API best practice)" do
          jsonapi_get(
            api_milestone_path(milestone_with_service, include: "service"),
            headers: headers
          )

          expect(response).to be_successful
          data = jsonapi_data
          service_data = data.sideload(:service)

          # Frontend can now fetch service.tracking_materials when needed
          expect(service_data.id).to eq(service.id)
          expect(service_data.attributes[:progress_type]).to eq('units')
        end
      end

      describe "with progress tracking" do
        let(:organization) { milestone.organization }
        let(:milestone_item) { fixture(:milestone_item, :milestone_item_1) }
        let(:catalog_item) { fixture(:catalog_item, :catalog_item_1) }

        before { milestone.update!(tracked_milestone_item: milestone_item) }

        it "returns the requested milestone", api_scenario: "milestone-material-tracking" do
          api_fixture("get-with-progress-tracking-material") do
            jsonapi_get(
              api_milestone_path(milestone, include: "tracking_material"),
              headers: headers,
            )
          end

          expect(response).to be_successful

          data = jsonapi_data
          tracking_material = data.sideloads(:tracking_material)
          expect(data.jsonapi_type).to eq("milestone")
          expect(data.id).to eq(milestone.id)
          expect(data.attributes[:status]).to eq("in_progress")

          expect(tracking_material.material_name).to eq("unknown material")
          expect(tracking_material.budget_quantity).to eq(100.0)
          expect(tracking_material.install_rate_per_hour.round(1)).to eq(1.7)
          expect(tracking_material.install_rate).to eq({ "cents" => 2864, "currency_iso" => "USD" })
          expect(tracking_material.actual_quantity).to eq(0.0)
          expect(tracking_material.percent_complete).to eq(0.0)
        end

        context "with item allocations" do
          before do
            milestone.update!(tracked_milestone_item: milestone_item)
            milestone.item_allocations.create!(
              catalog_item:,
              item_quantity: milestone_item.quantity / 2.0,
              organization:,
              unit_cost_cents: 200,
              total_cost_cents: milestone_item.quantity * 200 / 2.0
            )
            Sidekiq::Worker.drain_all
          end

          it "returns the requested milestone", api_scenario: "milestone-material-tracking-allocations" do
            api_fixture("get-with-progress-tracking-material-allocations") do
              jsonapi_get(
                api_milestone_path(milestone, include: "tracking_material"),
                headers: headers,
              )
            end

            expect(response).to be_successful

            data = jsonapi_data
            tracking_material = data.sideloads(:tracking_material)
            expect(data.jsonapi_type).to eq("milestone")
            expect(data.id).to eq(milestone.id)
            expect(data.attributes[:status]).to eq("in_progress")

            expect(tracking_material.material_name).to eq("unknown material")
            expect(tracking_material.budget_quantity).to eq(100.0)
            expect(tracking_material.install_rate_per_hour.round(1)).to eq(1.7)
            expect(tracking_material.install_rate).to eq({ "cents" => 2864, "currency_iso" => "USD" })
            expect(tracking_material.actual_quantity).to eq(50.0)
            expect(tracking_material.percent_complete).to eq(50.0)
          end
        end
      end
    end

    describe "PATCH update" do
      let(:service) { create(:service, organization: organization, progress_type: 'units') }
      let(:material1) { create(:catalog_item, organization: organization, item_type: 'Material') }
      let(:material2) { create(:catalog_item, organization: organization, item_type: 'Material') }
      let(:milestone_with_service) { create(:milestone, organization: organization, service: service) }
      let(:milestone_item1) { create(:milestone_item, milestone: milestone_with_service, catalog_item: material1) }
      let(:milestone_item2) { create(:milestone_item, milestone: milestone_with_service, catalog_item: material2) }

      before do
        service.tracking_materials << [material1, material2]
        # Create milestone items for both materials
        milestone_item1
        milestone_item2
      end

      it "successfully updates tracked_milestone_item_id" do
        patch_data = {
          data: {
            type: "milestone",
            id: milestone_with_service.id.to_s,
            attributes: {
              tracked_milestone_item_id: milestone_item1.id
            }
          }
        }

        jsonapi_patch(
          api_milestone_path(milestone_with_service),
          patch_data,
          headers: headers
        )

        expect(response).to be_successful
        milestone_with_service.reload
        expect(milestone_with_service.tracked_milestone_item_id).to eq(milestone_item1.id)
      end

      it "validates that selected material belongs to service" do
        other_service = create(:service, organization: organization, progress_type: 'units')
        other_material = create(:catalog_item, organization: organization, item_type: 'Material')
        other_service.tracking_materials << other_material
        other_milestone_item = create(:milestone_item, milestone: milestone_with_service, catalog_item: other_material)

        patch_data = {
          data: {
            type: "milestone",
            id: milestone_with_service.id.to_s,
            attributes: {
              tracked_milestone_item_id: other_milestone_item.id
            }
          }
        }

        jsonapi_patch(
          api_milestone_path(milestone_with_service),
          patch_data,
          headers: headers
        )

        expect(response).to have_http_status(:unprocessable_entity)
        errors = JSON.parse(response.body)['errors']
        expect(errors.first['detail']).to include("must be one of the service's tracking materials")
      end

      context "auto-assignment logic" do
        let(:single_material_service) { create(:service, organization: organization, progress_type: 'units') }
        let(:single_milestone) { create(:milestone, organization: organization, service: single_material_service) }
        let(:single_material) { create(:catalog_item, organization: organization, item_type: 'Material') }
        let(:single_milestone_item) { create(:milestone_item, milestone: single_milestone, catalog_item: single_material) }

        before do
          single_material_service.tracking_materials << single_material
          single_milestone_item # Create the milestone item
        end

        it "auto-assigns material when service has exactly one tracking material" do
          # Update something else to trigger the auto-assignment logic
          patch_data = {
            data: {
              type: "milestone",
              id: single_milestone.id.to_s,
              attributes: {
                description: "Updated description"
              }
            }
          }

          expect(single_milestone.tracked_milestone_item).to be_nil

          jsonapi_patch(
            api_milestone_path(single_milestone),
            patch_data,
            headers: headers
          )

          expect(response).to be_successful
          single_milestone.reload
          expect(single_milestone.tracked_milestone_item).to eq(single_milestone_item)
        end

        it "does not auto-assign when material is already selected" do
          single_milestone.update!(tracked_milestone_item: single_milestone_item)
          original_selection = single_milestone.tracked_milestone_item

          patch_data = {
            data: {
              type: "milestone",
              id: single_milestone.id.to_s,
              attributes: {
                description: "Updated description"
              }
            }
          }

          jsonapi_patch(
            api_milestone_path(single_milestone),
            patch_data,
            headers: headers
          )

          expect(response).to be_successful
          single_milestone.reload
          expect(single_milestone.tracked_milestone_item).to eq(original_selection)
        end
      end

      context 'when service has one tracking material' do
        let!(:material) { create(:catalog_item, organization: organization, item_type: 'Material') }
        let!(:test_service) { create(:service, organization: organization, progress_type: 'units') }
        let!(:milestone_with_one_material) { create(:milestone, organization: organization, service: test_service) }

        before do
          test_service.tracking_materials << material
          create(:milestone_item, milestone: milestone_with_one_material, catalog_item: material)
        end

        it 'auto-assigns the material' do
          params = {
            data: {
              type: 'milestone',
              id: milestone_with_one_material.id,
              attributes: {}
            }
          }

          jsonapi_patch "/api/v2/milestones/#{milestone_with_one_material.id}", params, headers: headers

          expect(response).to have_http_status(:ok)
          expect(milestone_with_one_material.reload.tracked_milestone_item.catalog_item).to eq(material)
        end
      end

      context 'when service has multiple tracking materials' do
        let!(:material1) { create(:catalog_item, organization: organization, item_type: 'Material') }
        let!(:material2) { create(:catalog_item, organization: organization, item_type: 'Material') }
        let!(:test_service_multi) { create(:service, organization: organization, progress_type: 'units') }
        let!(:milestone_with_multi_materials) { create(:milestone, organization: organization, service: test_service_multi) }

        before do
          test_service_multi.tracking_materials << [material1, material2]
          @milestone_item1 = create(:milestone_item, milestone: milestone_with_multi_materials, catalog_item: material1)
          @milestone_item2 = create(:milestone_item, milestone: milestone_with_multi_materials, catalog_item: material2)
        end

        it 'allows manual selection of tracked material' do
          params = {
            data: {
              type: 'milestone',
              id: milestone_with_multi_materials.id,
              attributes: { tracked_milestone_item_id: @milestone_item2.id }
            }
          }

          jsonapi_patch "/api/v2/milestones/#{milestone_with_multi_materials.id}", params, headers: headers

          expect(response).to have_http_status(:ok)
          expect(milestone_with_multi_materials.reload.tracked_milestone_item).to eq(@milestone_item2)
        end

        it 'validates material belongs to service tracking materials' do
          other_material = create(:catalog_item, organization: organization, item_type: 'Material')
          other_milestone_item = create(:milestone_item, milestone: milestone_with_multi_materials, catalog_item: other_material)

          params = {
            data: {
              type: 'milestone',
              id: milestone_with_multi_materials.id,
              attributes: { tracked_milestone_item_id: other_milestone_item.id }
            }
          }

          jsonapi_patch "/api/v2/milestones/#{milestone_with_multi_materials.id}", params, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_response['errors']).to be_present
        end
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
end
