# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::ProPaysController, api_scenario: 'pro_pays' do
  let(:headers) { {} }
  let(:params) { {} }

  context 'when not logged in', api_scenario: 'unauthorized' do
    specify do
      api_fixture('get-pro-pays') do
        jsonapi_get('/api/v2/pro_pays', headers: headers, params: params)
        expect(response).to be_unauthorized
      end
    end
  end

  describe 'when logged in', api_scenario: 'authorized' do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }
    let(:job) { fixture(:job, :lawn_q2) }

    describe '#index' do
      context 'filtered by job_id' do
        let(:params) { { filter: { job_id: { eq: job.id } }, include: 'jobs,milestones,source_route', stats: { deleted: 'count' } } }

        specify api_scenario: 'index-job-filter' do
          api_fixture('get') do
            jsonapi_get('/api/v2/pro_pays', headers: headers, params: params)
          end

          expect(response).to be_successful
          expect(jsonapi_data.length).to eq(1)

          pro_pay = jsonapi_data.first
          milestones = pro_pay.sideload(:milestones)
          expect(milestones.size).to eq(2)
          expect(milestones.map(&:id)).to contain_exactly(
            fixture(:milestone, :lawn_q2_milestone_1).id,
            fixture(:milestone, :lawn_q2_milestone_2).id
          )
        end
      end
    end

    describe '#show' do
      let(:pro_pay) { fixture(:pro_pay, :pro_pay) }

      specify 'show', api_scenario: 'show' do
        params[:include] = 'milestones,jobs'
        api_fixture('get') do
          jsonapi_get("/api/v2/pro_pays/#{pro_pay.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(pro_pay.id)

        # Verify included relationships
        milestones = jsonapi_data.sideload(:milestones)
        expect(milestones.size).to eq(2)
        expect(milestones.map(&:id)).to contain_exactly(
          fixture(:milestone, :lawn_q2_milestone_1).id,
          fixture(:milestone, :lawn_q2_milestone_2).id
        )
      end
    end
  end
end
