# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::OrganizationsController, type: :request do
  let(:user) { fixture(:user, :david) }

  describe "#index" do
    context "when authorized" do
      let(:headers) { auth_for(user) }

      specify do
        jsonapi_get api_organizations_path, headers: headers

        expect(response).to be_successful
        data = response.parsed_body

        expect(data.fetch("data").length).to eq(user.organizations.length)
      end
    end
  end
end
