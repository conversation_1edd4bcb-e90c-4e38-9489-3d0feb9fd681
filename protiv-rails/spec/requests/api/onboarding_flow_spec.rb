# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Onboarding Flow" do
  describe "Account creation with company information" do
    let(:name) { "<PERSON> Do<PERSON>" }
    let(:email) { "<EMAIL>" }
    let(:password) { "secure1234" }
    let(:phone) { "**********" }
    let(:phone_country_code) { "+1" }
    let(:company) { "Acme Painting Co." }

    let(:account_params) do
      {
        data: {
          type: "account",
          attributes: {
            name: name,
            email: email,
            password: password,
            phone: phone,
            phone_country_code: phone_country_code,
            company: company
          }
        }
      }
    end

    it "creates a new user, organization, and assigns admin role" do
      expect {
        jsonapi_post api_accounts_path, account_params
      }.to change(User, :count).by(1)
        .and change(Organization, :count).by(1)
        .and change(Role, :count).by(1)

      expect(response).to have_http_status(:created)

      # Verify user was created with correct attributes
      user = User.last
      expect(user.name).to eq(name)
      expect(user.email).to eq(email)
      expect(user.phone).to eq(phone)
      expect(user.phone_country_code).to eq(phone_country_code)

      # Verify organization was created with correct name
      organization = Organization.last
      expect(organization.name).to eq(company)

      # Verify user has admin role in the organization
      role = user.roles.find_by(organization: organization)
      expect(role).to be_present
      expect(role.role_type).to eq("admin")
      expect(role.active).to be true

      # Verify user belongs to the organization
      expect(user.organizations).to include(organization)
      expect(user.admin_organizations).to include(organization)
      expect(organization.admin_users).to include(user)
    end

    context "when company name is not provided" do
      let(:company) { nil }

      it "creates a user without an organization" do
        expect {
          jsonapi_post api_accounts_path, account_params
        }.to change(User, :count).by(1)
          .and change(Organization, :count).by(0)
          .and change(Role, :count).by(0)

        expect(response).to have_http_status(:created)

        # Verify user was created with correct attributes
        user = User.last
        expect(user.name).to eq(name)
        expect(user.email).to eq(email)
        expect(user.phone).to eq(phone)
        expect(user.phone_country_code).to eq(phone_country_code)

        # Verify user has no organizations
        expect(user.organizations).to be_empty
      end
    end

    context "with invalid data" do
      let(:password) { "123" } # Too short

      it "returns validation errors" do
        expect {
          jsonapi_post api_accounts_path, account_params
        }.to change(User, :count).by(0)
          .and change(Organization, :count).by(0)
          .and change(Role, :count).by(0)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(jsonapi_errors).to be_present
      end
    end
  end
end
