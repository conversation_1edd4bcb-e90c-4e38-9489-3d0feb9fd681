# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::EmployeeJobTimesController do
  context "when user is not authenticated" do
    specify do
      jsonapi_get(api_employee_job_times_path)

      expect(controller.current_user).to be_nil
      expect(controller.current_organization).to be_nil
      expect(response).to be_unauthorized
    end
  end

  describe "authorized" do
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:user) { fixture(:user, :luigi) }
    let(:identity) { fixture(:identity, :luigi) }
    let(:employee) { fixture(:identity, :louis) }
    let(:job) { fixture(:job, :lawn_q2) }

    let(:headers) { auth_for(user, organization) }

    it "filters by job_id" do
      jsonapi_get(
        api_employee_job_times_path(
          filter: { job_id: job.id },
          include: "identity,job"
        ),
        headers: headers
      )

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to be >= 0
      if data.size > 0
        expect(data[0].jsonapi_type).to eq("employee-job-time")
        expect(data.first.sideload(:job).id).to eq(job.id)
      end
    end

    it "filters by job_id and identity_id" do
      jsonapi_get(
        api_employee_job_times_path(
          filter: { job_id: job.id, identity_id: employee.id },
          include: "job,identity"
        ),
        headers: headers
      )

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to be >= 0
      if data.size > 0
        expect(data[0].jsonapi_type).to eq("employee-job-time")
        expect(data.first.sideload(:job).id).to eq(job.id)
        expect(data.first.sideload(:identity).id).to eq(employee.id)
      end
    end
  end
end
