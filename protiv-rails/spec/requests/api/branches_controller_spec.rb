# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::BranchesController do
  describe "#index" do
    let(:user) { fixture(:user, :david) }

    context "without branches" do
      let(:organization) { fixture(:organization, :protiv) }
      let(:headers) { auth_for(user, organization) }

      specify do
        jsonapi_get api_branches_path,
          headers: headers

        expect(response).to be_successful
        data = response.parsed_body

        expect(data["data"]).to eq([])
      end
    end

    context "with branches" do
      let(:organization) { fixture(:organization, :lawn_mow_inc) }
      let(:headers) { auth_for(user, organization) }

      specify do
        jsonapi_get api_branches_path,
          headers: headers

        expect(response).to be_successful
        data = response.parsed_body

        expect(organization.branches.count).to satisfy("be greater than 0") { |n| n > 0 }
        expect(data["data"].count).to eq(organization.branches.count)
      end
    end

    context "as an unauthorized user" do
      let(:other_user) { fixture(:user, :john_appleseed) }
      let(:headers) { auth_for(other_user) }

      specify do
        jsonapi_get api_branches_path, headers: headers

        expect(response.parsed_body.fetch("data")).to eq([])
      end
    end
  end
end
