# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::PasswordResetsController do
  let(:user) { fixture(:user, :david, password: current_password) }
  let(:email) { user.email }
  let(:current_password) { 'forgotten' }
  let(:new_password) { SpecHelpers::Fixtures::DEFAULT_PASSWORD }

  before { user.update!(password: current_password) }

  let (:post_sign_in_redirect_url) { "/dashboard" }

  let(:bad_redirect) do
    "http://www.external.com/off/site/redirect"
  end

  def expect_password_changed
    expect(User.authenticate_by(email: email, password: current_password)).to be_nil
    expect(User.authenticate_by(email: email, password: new_password)).to eq(user)
  end

  def expect_password_not_changed
    expect(User.authenticate_by(email: email, password: current_password)).to eq(user)
    expect(User.authenticate_by(email: email, password: new_password)).to be_nil
  end

  describe "requesting a password reset" do
    it "accepts a valid email address matching an existing user", api_scenario: "password-resets/request/valid-email" do
      assert_enqueued_email_with <PERSON><PERSON><PERSON><PERSON><PERSON>, :password_reset, params: { user: user } do
        api_fixture("request") do
          jsonapi_post(api_password_resets_path, { email: user.email })
        end
      end

      expect(response).to have_http_status(:accepted)
      expect_password_not_changed
    end

    it "accepts an invalid email address not matching any users", api_scenario: "password-resets/request/invalid-email" do
      assert_no_enqueued_emails do
        api_fixture("request") do
          jsonapi_post(api_password_resets_path, { email: "<EMAIL>" })
        end
      end

      expect(response).to have_http_status(:accepted)
      expect_password_not_changed
    end
  end

  describe "resetting password" do
    let(:token) { user.generate_token_for(:password_reset) }
    let(:path) { api_password_reset_path(token) }

    describe "valid token" do
      let(:api_fixture_overrides) do
        {
          "request.path" => ->(path) { path.sub(token, 'valid-password-reset-token') }
        }
      end

      def expect_url_with_redirect_path(url:, redirect_path:)
        uri = URI(url)
        expect(uri.query).not_to be_nil

        query = URI.decode_www_form(uri.query).to_h
        expect(query).to have_key("redirect")
        expect(query["redirect"]).to eq(redirect_path)
      end

      def expect_url_without_redirect_path(url:)
        uri = URI(url)

        if uri.query && (query = URI.decode_www_form(uri.query).to_h)
          expect(query).not_to have_key("redirect")
        end
      end

      describe "without redirect path" do
        it "changes the user's password", api_scenario: "password-resets/reset/valid-token" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:ok)
          expect_password_not_changed

          api_fixture("update") do
            jsonapi_put(path, { password: new_password })
          end

          expect(response).to have_http_status(:ok)
          expect_password_changed

          body = response.parsed_body
          redirect_url = body.dig("data", "attributes", "redirect")

          expect_url_without_redirect_path(url: redirect_url)

          expect {
            get redirect_url
          }.to change(SignInToken, :count).by(-1)

          expect(session[:user_id]).to eq(user.id)
        end
      end

      describe "with redirect path" do
        it "changes the user's password", api_scenario: "password-resets/reset/valid-token-with-redirect" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:ok)
          expect_password_not_changed

          api_fixture("update") do
            jsonapi_put(path, {
              password: new_password,
              redirect: post_sign_in_redirect_url
            })
          end

          expect(response).to have_http_status(:ok)
          expect_password_changed

          expect(User.authenticate_by(email: email, password: current_password)).to be_nil
          expect(User.authenticate_by(email: email, password: new_password)).to eq(user)

          body = response.parsed_body
          redirect_url = body.dig("data", "attributes", "redirect")

          expect_url_with_redirect_path(url: redirect_url, redirect_path: post_sign_in_redirect_url)

          expect {
            get redirect_url
          }.to change(SignInToken, :count).by(-1)

          expect(session[:user_id]).to eq(user.id)
        end
      end
    end

    describe "invalid operations" do
      describe "malformed token" do
        let(:token) { "invalid-password-reset-token" }

        it "refuses to change the user's password", api_scenario: "password-resets/reset/invalid-token" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed

          api_fixture("update") do
            jsonapi_put(path, {
              password: new_password,
              redirect: post_sign_in_redirect_url
            })
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed
        end
      end

      describe "outdated token" do
        let(:api_fixture_overrides) do
          {
            "request.path" => ->(path) { path.sub(token, 'outdated-password-reset-token') }
          }
        end

        before do
          # Generate the token based on the current password
          token

          # Changing the password (by whatever means) invalidates the token.
          user.update!(password: "temporary", password_confirmation: "temporary")

          # Change it back for the expect_password_not_changed helper. Even though the
          # password is changed back, the salt will be different, this still invalidates
          # the previously-generated token (note the `let!`).
          user.update!(password: current_password, password_confirmation: current_password)
        end

        it "refuses to change the user's password", api_scenario: "password-resets/reset/outdated-token" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed

          api_fixture("update") do
            jsonapi_put(path, {
              password: new_password,
              redirect: post_sign_in_redirect_url
            })
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed
        end
      end

      describe "expired token" do
        let(:api_fixture_overrides) do
          {
            "request.path" => ->(path) { path.sub(token, 'expired-password-reset-token') }
          }
        end

        before do
          # Generate the token based on the current time
          token

          # Tokens are valid for a limited time only
          travel_to 30.minutes.from_now
        end

        it "refuses to change the user's password", api_scenario: "password-resets/reset/expired-token" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed

          api_fixture("update") do
            jsonapi_put(path, {
              password: new_password,
              redirect: post_sign_in_redirect_url
            })
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed
        end
      end

      describe "token expired during reset" do
        let(:api_fixture_overrides) do
          {
            "request.path" => ->(path) { path.sub(token, 'valid-password-reset-token') }
          }
        end

        it "refuses to change the user's password", api_scenario: "password-resets/reset/expired-during-reset" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:ok)
          expect_password_not_changed

          travel_to 30.minutes.from_now

          api_fixture("update") do
            jsonapi_put(path, {
              password: new_password,
              redirect: post_sign_in_redirect_url
            })
          end

          expect(response).to have_http_status(:unauthorized)
          expect_password_not_changed
        end
      end

      describe "bad redirect url" do
        let(:api_fixture_overrides) do
          {
            "request.path" => ->(path) { path.sub(token, 'valid-password-reset-token') }
          }
        end

        it "refuses to change the user's password", api_scenario: "password-resets/reset/bad-redirect" do
          api_fixture("show") do
            jsonapi_get path
          end

          expect(response).to have_http_status(:ok)
          expect_password_not_changed

          api_fixture("update") do
            jsonapi_put(path, {
              password: new_password,
              redirect: bad_redirect
            })
          end

          expect(response).to have_http_status(:bad_request)
          expect_password_not_changed
        end
      end
    end
  end
end
