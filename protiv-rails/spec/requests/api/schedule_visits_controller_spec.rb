# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::ScheduleVisitsController, api_scenario: 'schedule_visits' do
  let(:headers) { {} }
  let(:params) { {} }

  context 'when not logged in', api_scenario: 'unauthorized' do
    specify do
      api_fixture('get-schedule-visits') do
        jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
        expect(response).to be_unauthorized
      end
    end
  end

  describe 'when logged in', api_scenario: 'authorized' do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }

    describe '#index' do
      context 'unfiltered' do
        specify api_scenario: 'index-unfiltered' do
          api_fixture('get') do
            jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
          end

          expect(response).to be_successful
          expect(jsonapi_data).not_to be_empty
        end
      end

      context 'filtered by route' do
        let(:route) { fixture(:route, :lakewood_route_1) }
        let(:params) { { filter: { route_id: route.id } } }

        specify api_scenario: 'index-route-filter' do
          api_fixture('get') do
            jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
          end

          expect(response).to be_successful
          expect(jsonapi_data).not_to be_empty
        end
      end

      context 'filtered by milestone' do
        let(:milestone) { fixture(:milestone, :lawn_q2_mowing) }
        let(:params) { { filter: { milestone_id: milestone.id } } }

        specify api_scenario: 'index-milestone-filter' do
          api_fixture('get') do
            jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
          end

          expect(response).to be_successful
        end
      end

      context 'filtered by job' do
        let(:job) { fixture(:job, :lawn_q2) }
        let(:params) { { filter: { job_id: job.id } } }

        specify api_scenario: 'index-job-filter' do
          api_fixture('get') do
            jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
          end

          expect(response).to be_successful
        end
      end

      context 'filtered by upcoming' do
        let(:params) { { filter: { upcoming: true } } }

        specify api_scenario: 'index-upcoming-filter' do
          api_fixture('get') do
            jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
          end

          expect(response).to be_successful
          today = Date.today
          expect(jsonapi_data.all? { |d| Date.parse(d.scheduled_date) >= today }).to be true
        end
      end

      context 'filtered by date range' do
        let(:params) { { filter: { date_range: '7d' } } }

        specify api_scenario: 'index-date-range-filter' do
          api_fixture('get') do
            jsonapi_get('/api/v2/schedule_visits', headers: headers, params: params)
          end

          expect(response).to be_successful
          today = Date.today
          expect(jsonapi_data.all? { |d| Date.parse(d.scheduled_date).between?(today, today + 7.days) }).to be true
        end
      end
    end

    describe '#show' do
      let(:schedule_visit) { fixture(:schedule_visit, :m44_visit) }

      specify 'show', api_scenario: 'show' do
        api_fixture('get') do
          jsonapi_get("/api/v2/schedule_visits/#{schedule_visit.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(jsonapi_data.id.to_s).to eq(schedule_visit.id.to_s)
      end
    end
  end
end
