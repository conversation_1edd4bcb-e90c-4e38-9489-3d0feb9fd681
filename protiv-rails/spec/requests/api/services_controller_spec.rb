# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::ServicesController, type: :request do
  let(:user) { fixture(:user, :david) }
  let(:organization) { fixture(:organization, :protiv) }
  let(:headers) { auth_for(user, organization) }

  describe 'GET /api/v2/services' do
    before do
      # Create services for testing using factories
      @service1 = create(:service, organization: organization, name: 'Service 1')
      @service2 = create(:service, organization: organization, name: 'Service 2', progress_type: 'units')
      @other_org_service = create(:service, name: 'Other Service')
    end

    it 'returns all services for the organization' do
      jsonapi_get '/api/v2/services', headers: headers

      expect(response).to have_http_status(:ok)

      data = json_response['data']
      service_ids = data.map { |s| s['id'].to_i }

      expect(service_ids).to include(@service1.id, @service2.id)
      expect(service_ids).not_to include(@other_org_service.id)
    end

    it 'includes progress_type attribute in response' do
      jsonapi_get '/api/v2/services', headers: headers

      expect(response).to have_http_status(:ok)

      service_data = json_response['data'].find { |s| s['id'].to_i == @service2.id }
      expect(service_data['attributes']['progress_type']).to eq('units')
    end

    it 'includes tracking_materials relationship when requested' do
      material1 = create(:catalog_item, organization: organization, item_type: 'Material')
      material2 = create(:catalog_item, organization: organization, item_type: 'Material')
      @service2.tracking_materials << [material1, material2]

      jsonapi_get '/api/v2/services?include=tracking_materials', headers: headers

      expect(response).to have_http_status(:ok)

      # Check that materials are included in the response
      included = json_response['included']
      material_ids = included.map { |i| i['id'].to_i if i['type'] == 'catalog-item' }.compact
      expect(material_ids).to include(material1.id, material2.id)
    end

    context 'when service has multiple tracking materials' do
      let!(:material1) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Material 1') }
      let!(:material2) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Material 2') }

      before do
        @service2.tracking_materials << [material1, material2]
      end

      it 'includes tracking materials in sideload' do
        jsonapi_get '/api/v2/services?include=tracking_materials', headers: headers

        expect(response).to have_http_status(:ok)

        json = JSON.parse(response.body)
        included = json['included'] || []

        # Should include both tracking materials
        material_ids = included.map { |i| i['id'].to_i if i['type'] == 'catalog-item' }.compact
        expect(material_ids).to include(material1.id, material2.id)

        # Verify material data
        material1_data = included.find { |i| i['id'] == material1.id.to_s && i['type'] == 'catalog-item' }
        expect(material1_data['attributes']['name']).to eq('Material 1')
      end
    end

    context 'when not authenticated' do
      it 'returns unauthorized' do
        jsonapi_get '/api/v2/services'
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PATCH /api/v2/services/:id' do
    before do
      @service = create(:service, organization: organization, progress_type: 'percentage')
    end

    it 'updates service progress_type' do
      params = {
        data: {
          type: 'services',
          id: @service.id,
          attributes: { progress_type: 'units' }
        }
      }

      jsonapi_patch "/api/v2/services/#{@service.id}", params, headers: headers

      expect(response).to have_http_status(:ok)
      expect(@service.reload.progress_type).to eq('units')
    end

    it 'updates service name' do
      params = {
        data: {
          type: 'services',
          id: @service.id,
          attributes: { name: 'Updated Service Name' }
        }
      }

      jsonapi_patch "/api/v2/services/#{@service.id}", params, headers: headers

      expect(response).to have_http_status(:ok)
      expect(@service.reload.name).to eq('Updated Service Name')
    end

    it 'returns validation errors for invalid progress_type' do
      params = {
        data: {
          type: 'services',
          id: @service.id,
          attributes: { progress_type: 'invalid' }
        }
      }

      jsonapi_patch "/api/v2/services/#{@service.id}", params, headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      expect(json_response['errors']).to be_present
    end

    context 'when service belongs to different organization' do
      before do
        @other_service = create(:service)
      end

      it 'returns not found' do
        params = {
          data: {
            type: 'services',
            id: @other_service.id,
            attributes: { progress_type: 'units' }
          }
        }

        jsonapi_patch "/api/v2/services/#{@other_service.id}", params, headers: headers

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'GET /api/v2/services/:id' do
    before do
      @service = create(:service, organization: organization)
    end

    it 'returns the requested service' do
      jsonapi_get "/api/v2/services/#{@service.id}", headers: headers

      expect(response).to have_http_status(:ok)

      data = json_response['data']
      expect(data['id'].to_i).to eq(@service.id)
      expect(data['type']).to eq('services')
      expect(data['attributes']['name']).to eq(@service.name)
    end

    context 'when service belongs to different organization' do
      before do
        @other_service = create(:service)
      end

      it 'returns not found' do
        jsonapi_get "/api/v2/services/#{@other_service.id}", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'Tracking Materials via Relationships' do
    let!(:service) { create(:service, organization: organization, progress_type: 'units') }
    let!(:material1) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Conduit') }
    let!(:material2) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Wire') }

    # Note: Relationship updates are now handled differently since Graphiti has limitations
    # with many-to-many relationships. These tests verify the underlying functionality works.

    it 'allows tracking materials to be updated via model methods' do
      # Test that we can update tracking materials through the model
      service.tracking_materials = [material1, material2]

      expect(service.reload.tracking_materials).to include(material1, material2)
      expect(service.tracking_materials.count).to eq(2)
    end

    it 'can clear tracking materials via model methods' do
      # First add some materials
      service.tracking_materials << [material1, material2]
      expect(service.reload.tracking_materials.count).to eq(2)

      # Then clear them
      service.tracking_materials.clear

      expect(service.reload.tracking_materials).to be_empty
    end

    it 'includes tracking materials when using sideload' do
      service.tracking_materials << [material1, material2]

      jsonapi_get "/api/v2/services/#{service.id}?include=tracking_materials", headers: headers

      expect(response).to have_http_status(:ok)

      # Check that materials are included in the response
      included = json_response['included']
      material_ids = included.map { |i| i['id'].to_i if i['type'] == 'catalog-item' }.compact
      expect(material_ids).to include(material1.id, material2.id)

      # Check relationship data in the main resource
      relationships = json_response['data']['relationships']
      expect(relationships['tracking_materials']['data'].length).to eq(2)
    end
  end

  describe 'PATCH /api/v2/services/:id/tracking_materials' do
    let!(:service) { create(:service, organization: organization, progress_type: 'units') }
    let!(:material1) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Conduit') }
    let!(:material2) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Wire') }
    let!(:other_org_material) { create(:catalog_item, item_type: 'Material', name: 'Other Material') }

    it 'replaces tracking materials via PATCH' do
      # Start with material1
      service.tracking_materials << material1
      expect(service.reload.tracking_materials.count).to eq(1)

      params = {
        material_ids: [material2.id]
      }

      patch "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      # Should only have material2 (replaced material1)
      expect(service.reload.tracking_materials).to include(material2)
      expect(service.tracking_materials).not_to include(material1)
      expect(service.tracking_materials.count).to eq(1)
    end

    it 'can clear tracking materials by sending empty array' do
      # First add some materials
      service.tracking_materials << [material1, material2]
      expect(service.reload.tracking_materials.count).to eq(2)

      params = { material_ids: [] }

      patch "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      expect(service.reload.tracking_materials).to be_empty
    end

    it 'ignores materials from other organizations' do
      params = {
        material_ids: [material1.id, other_org_material.id]
      }

      patch "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      # Should only include material1, not other_org_material
      expect(service.reload.tracking_materials).to include(material1)
      expect(service.tracking_materials).not_to include(other_org_material)
      expect(service.tracking_materials.count).to eq(1)
    end

    it 'returns error for missing material_ids parameter' do
      patch "/api/v2/services/#{service.id}/tracking_materials", params: {}, headers: headers.to_hash

      expect(response).to have_http_status(:bad_request)
      expect(json_response['errors'].first['code']).to eq('missing_parameter')
    end

    it 'returns error for non-existent service' do
      params = { material_ids: [material1.id] }

      patch "/api/v2/services/99999/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:not_found)
      expect(json_response['errors'].first['code']).to eq('not_found')
    end
  end

  describe 'POST /api/v2/services/:id/tracking_materials' do
    let!(:service) { create(:service, organization: organization, progress_type: 'units') }
    let!(:material1) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Conduit') }
    let!(:material2) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Wire') }
    let!(:material3) { create(:catalog_item, organization: organization, item_type: 'Material', name: 'Cable') }
    let!(:other_org_material) { create(:catalog_item, item_type: 'Material', name: 'Other Material') }

    it 'adds materials to existing tracking materials via POST' do
      # Start with material1
      service.tracking_materials << material1
      expect(service.reload.tracking_materials.count).to eq(1)

      params = {
        material_ids: [material2.id, material3.id]
      }

      post "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      # Should have all three materials (kept material1, added material2 & material3)
      expect(service.reload.tracking_materials).to include(material1, material2, material3)
      expect(service.tracking_materials.count).to eq(3)
    end

    it 'avoids duplicates when adding materials' do
      # Start with material1 and material2
      service.tracking_materials << [material1, material2]
      expect(service.reload.tracking_materials.count).to eq(2)

      params = {
        material_ids: [material2.id, material3.id]  # material2 is duplicate
      }

      post "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      # Should have all three materials but no duplicates
      expect(service.reload.tracking_materials).to include(material1, material2, material3)
      expect(service.tracking_materials.count).to eq(3)
    end

    it 'works when starting with no materials' do
      expect(service.reload.tracking_materials.count).to eq(0)

      params = {
        material_ids: [material1.id, material2.id]
      }

      post "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      expect(service.reload.tracking_materials).to include(material1, material2)
      expect(service.tracking_materials.count).to eq(2)
    end

    it 'ignores materials from other organizations when adding' do
      service.tracking_materials << material1
      expect(service.reload.tracking_materials.count).to eq(1)

      params = {
        material_ids: [material2.id, other_org_material.id]
      }

      post "/api/v2/services/#{service.id}/tracking_materials", params: params, headers: headers.to_hash

      expect(response).to have_http_status(:success)
      # Should only add material2, not other_org_material
      expect(service.reload.tracking_materials).to include(material1, material2)
      expect(service.tracking_materials).not_to include(other_org_material)
      expect(service.tracking_materials.count).to eq(2)
    end

    it 'returns error for missing material_ids parameter' do
      post "/api/v2/services/#{service.id}/tracking_materials", params: {}, headers: headers.to_hash

      expect(response).to have_http_status(:bad_request)
      expect(json_response['errors'].first['code']).to eq('missing_parameter')
    end
  end

  describe 'PATCH /api/v2/services/bulk_update_progress_types' do
    before do
      @service1 = create(:service, organization: organization, progress_type: 'percentage', name: 'Service 1')
      @service2 = create(:service, organization: organization, progress_type: 'percentage', name: 'Service 2')
      @service3 = create(:service, organization: organization, progress_type: 'units', name: 'Service 3')
      @other_org_service = create(:service, progress_type: 'percentage', name: 'Other Org Service')
    end

    context 'successful bulk updates' do
      it 'updates multiple services progress types' do
        params = {
          service_updates: [
            { id: @service1.id, progress_type: 'units' },
            { id: @service2.id, progress_type: 'units' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:ok)
        expect(@service1.reload.progress_type).to eq('units')
        expect(@service2.reload.progress_type).to eq('units')

        expect(json_response['meta']['updated_count']).to eq(2)
        expect(json_response['meta']['operation']).to eq('bulk_progress_type_update')
      end

      it 'handles mixed progress type updates' do
        params = {
          service_updates: [
            { id: @service1.id, progress_type: 'units' },
            { id: @service3.id, progress_type: 'percentage' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:ok)
        expect(@service1.reload.progress_type).to eq('units')
        expect(@service3.reload.progress_type).to eq('percentage')
      end

      it 'returns updated services in JSON:API format' do
        params = {
          service_updates: [
            { id: @service1.id, progress_type: 'units' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:ok)

        data = json_response['data']
        expect(data).to be_an(Array)
        expect(data.first['type']).to eq('services')
        expect(data.first['id']).to eq(@service1.id.to_s)
        expect(data.first['attributes']['progress_type']).to eq('units')
      end

      it 'handles empty service_updates array' do
        params = { service_updates: [] }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:ok)
        expect(json_response['data']).to eq([])
        expect(json_response['meta']['updated_count']).to eq(0)
      end
    end

    context 'error handling' do
      it 'fails atomically when one service has invalid progress_type' do
        params = {
          service_updates: [
            { id: @service1.id, progress_type: 'units' },
            { id: @service2.id, progress_type: 'invalid_type' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect(@service1.reload.progress_type).to eq('percentage') # Rolled back
        expect(@service2.reload.progress_type).to eq('percentage') # Not changed

        expect(json_response['errors']).to be_present
        expect(json_response['errors'].first['title']).to eq('Bulk update failed')
      end

      it 'fails when service not found' do
        params = {
          service_updates: [
            { id: 99999, progress_type: 'units' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:not_found)
        expect(json_response['errors'].first['title']).to eq('Service not found')
      end

      it 'prevents access to other organization services' do
        params = {
          service_updates: [
            { id: @other_org_service.id, progress_type: 'units' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params, headers: headers

        expect(response).to have_http_status(:not_found)
      end

      it 'returns error for missing service_updates parameter' do
        jsonapi_patch '/api/v2/services/bulk_update_progress_types', {}, headers: headers

        expect(response).to have_http_status(:bad_request)
        expect(json_response['errors'].first['title']).to eq('Missing required parameter')
      end
    end

    context 'authorization' do
      it 'requires authentication' do
        params = {
          service_updates: [
            { id: @service1.id, progress_type: 'units' }
          ]
        }

        jsonapi_patch '/api/v2/services/bulk_update_progress_types', params

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
end
