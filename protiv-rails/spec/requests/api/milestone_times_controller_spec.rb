# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::MilestoneTimesController do
  context "unauthenticated" do
    specify do
      jsonapi_get(api_milestone_times_path)

      expect(controller.current_user).to be_nil
      expect(controller.current_organization).to be_nil
      expect(response).to be_unauthorized
    end
  end

  context "authorized" do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }
    let(:job) { fixture(:job, :lawn_q2) }
    let(:employee) { fixture(:identity, :louis) }
    let(:milestone) { fixture(:milestone, :lawn_q2_milestone_1) }

    it "returns milestone_times filtered by job_id and identity_id", api_scenario: "milestone-times/by-job-and-employee" do
      api_fixture("get") do
        jsonapi_get(
          api_milestone_times_path(
            filter: { job_id: job.id, identity_id: employee.id },
            include: "identity,milestone,milestone.job",
            page: { number: 1, size: 10 }
          ),
          headers: headers
        )
      end

      expect(response).to be_successful

      data = jsonapi_data
      expect(data.size).to be > 0

      # Verify all milestone_times are for the specified job and employee
      data.each do |milestone_time|
        expect(milestone_time.sideload(:milestone).sideload(:job).id).to eq(job.id)
        expect(milestone_time.sideload(:identity).id).to eq(employee.id)
      end
    end

    it "returns milestone_times filtered by milestone_id and identity_id", api_scenario: "milestone-times/by-milestone-and-employee" do
      api_fixture("get") do
        jsonapi_get(
          api_milestone_times_path(
            filter: { milestone_id: milestone.id, identity_id: employee.id },
            include: "identity,milestone.job",
            fields: { job: "name" },
            page: { number: 1, size: 10 }
          ),
          headers: headers
        )
      end

      expect(response).to be_successful

      data = jsonapi_data
      expect(data.size).to be > 0

      # Verify all milestone_times are for the specified milestone and employee
      data.each do |milestone_time|
        milestone = milestone_time.sideload(:milestone)
        expect(milestone.id).to eq(milestone.id)
        job = milestone.sideload(:job)
        expect(job.id).to eq(job.id)
        expect(job.name).to eq(job.name)
        # Only name and id should be included in the response
        expect(job["budget"]).to be_nil
        expect(milestone_time.sideload(:identity).id).to eq(employee.id)
      end
    end
  end
end
