# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::StatementsController, api_scenario: 'statements' do
  let(:headers) { {} }
  let(:params) { {} }

  context 'when not logged in', api_scenario: 'unauthorized' do
    specify do
      api_fixture('get') do
        jsonapi_get('/api/v2/statements', headers: headers, params: params)
        expect(response).to be_unauthorized
      end
    end
  end

  describe 'when logged in', api_scenario: 'authorized' do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }

    describe '#index' do
      context 'unfiltered' do
        let(:params) { { include: 'pay_period,identity' } }

        specify api_scenario: 'index-unfiltered' do
          params.delete(:filter)

          api_fixture('get') do
            jsonapi_get('/api/v2/statements', headers: headers, params: params)
          end

          expect(response).to be_successful
          expect(jsonapi_data.length).to eq(2)
        end
      end
    end

    describe '#show' do
      let(:statement_1) { fixture(:statement, :statement_1) }
      let(:statement_2) { fixture(:statement, :statement_2) }

      specify 'show first statement', api_scenario: 'show-statement-1' do
        params[:include] = 'pay_period,identity,line_items'
        api_fixture('get') do
          jsonapi_get("/api/v2/statements/#{statement_1.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(statement_1.id)

        expect(jsonapi_data.net_amount).to eq({ "cents" => -36025, "currency_iso" => "USD" })
        expect(jsonapi_data.bonuses).to eq({ "cents" => 13486, "currency_iso" => "USD" })
        expect(jsonapi_data.deductions).to eq({ "cents" => -49511, "currency_iso" => "USD" })

        # Verify included relationships
        line_items = jsonapi_data.sideload(:line_items)
        expect(line_items.size).to eq(2)
        expect(line_items.map(&:id)).to contain_exactly(
          fixture(:statement_line_item, :statement_line_item_1).id,
          fixture(:statement_line_item, :statement_line_item_2).id
        )
      end

      specify 'show second statement', api_scenario: 'show-statement-2' do
        params[:include] = 'pay_period,identity,line_items'
        api_fixture('get') do
          jsonapi_get("/api/v2/statements/#{statement_2.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(statement_2.id)

        expect(jsonapi_data.net_amount).to eq({ "cents" => -9999, "currency_iso" => "USD" })
        expect(jsonapi_data.bonuses).to eq({ "cents" => 0, "currency_iso" => "USD" })
        expect(jsonapi_data.deductions).to eq({ "cents" => -9999, "currency_iso" => "USD" })

        # Verify previous statement data
        expect(jsonapi_data.previous_statement_id).to eq(statement_1.id.to_s)
        expect(jsonapi_data.previous_balance).to eq({ "cents" => -36025, "currency_iso" => "USD" })

        # Verify included relationships
        line_items = jsonapi_data.sideload(:line_items)
        expect(line_items.size).to eq(2)
        expect(line_items.map(&:id)).to contain_exactly(
          fixture(:statement_line_item, :statement_line_item_3).id,
          fixture(:statement_line_item, :statement_line_item_4).id
        )
      end
    end
  end
end
