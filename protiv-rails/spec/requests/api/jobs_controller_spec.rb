# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::JobsController, api_scenario: 'jobs' do
  let(:headers) { {} }
  let(:params) { {} }

  context 'when not logged in', api_scenario: 'unauthorized' do
    specify do
      api_fixture('get-jobs') do
        jsonapi_get('/api/v2/jobs', headers: headers, params: params)
        expect(response).to be_unauthorized
      end
    end
  end

  describe 'when logged in', api_scenario: 'authorized' do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }

    describe '#index' do
      # The frontend never does this, consider making the filter required
      context 'unfiltered' do
        let(:params) { { include: 'manager' } }

        specify api_scenario: 'index-unfiltered' do
          params.delete(:filter)

          api_fixture('get') do
            jsonapi_get('/api/v2/jobs', headers: headers, params: params)
          end

          expect(response).to be_successful
          expect(jsonapi_data.length).to eq(5)
        end
      end

      context 'filtered' do
        context 'default date range (7d)' do
          let(:params) { { filter: { recent: '7d' }, include: 'manager', page: { number: 1, size: 10 }, sort: '-created_date' } }

          specify api_scenario: 'index' do
            params[:filter] = { recent: '7d' }

            api_fixture('get') do
              jsonapi_get('/api/v2/jobs', headers: headers, params: params)
            end

            expect(jsonapi_data.length).to eq(3)
          end
        end

        context 'with pagination' do
          let(:params) { { filter: { recent: '60d' }, include: 'manager' } }

          specify api_scenario: 'index-paginated' do
            params[:page] = { size: 2, number: 1 }

            api_fixture('get-page1') do
              jsonapi_get('/api/v2/jobs', headers: headers, params: params)
            end

            expect(response).to be_successful
            expect(jsonapi_data.length).to eq(2)
            expect(jsonapi_meta[:pagination]).to be_present
            expect(jsonapi_meta[:pagination][:current_page]).to eq(1)
            expect(jsonapi_meta[:pagination][:total_count]).to be >= 2

            # Get the second page
            params[:page] = { size: 2, number: 2 }

            api_fixture('get-page2') do
              jsonapi_get('/api/v2/jobs', headers: headers, params: params)
            end

            expect(response).to be_successful
            expect(jsonapi_data.length).to be >= 0

            # Check that we got a valid response for page 2
            # Note: In a real app with more data, we would check for different records
            # but in the test environment we might not have enough records
            expect(jsonapi_meta[:pagination][:current_page]).to eq(2)
          end
        end

        context 'manager_id filter' do
          let(:params) { { filter: { manager_id: '1' } } }

          specify api_scenario: 'index-manager-filter' do
            api_fixture('get') do
              jsonapi_get('/api/v2/jobs', headers:, params:)
            end

            expect(jsonapi_data.length).to eq(Job.where(manager_id: 1).count)
          end
        end

        context 'non-default date range (60d)' do
          let(:params) { { filter: { recent: '60d' }, include: 'manager', page: { number: 1, size: 10 }, sort: '-created_date' } }

          specify api_scenario: 'index-60d' do
            params[:filter] = { recent: '60d' }

            api_fixture('get') do
              jsonapi_get('/api/v2/jobs', headers: headers, params: params)
            end

            expect(jsonapi_data.length).to eq(5)
          end
        end
      end
    end

    describe '#show' do
      let(:job) { fixture(:job, :lawn_q2) }

      specify 'show', api_scenario: 'show' do
        params[:include] = 'manager'
        api_fixture('get') do
          jsonapi_get("/api/v2/jobs/#{job.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(job.id)
      end
    end
  end
end
