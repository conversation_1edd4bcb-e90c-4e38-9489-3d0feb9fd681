# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::EmployeeMilestoneTimesController do
  context "when user is not authenticated" do
    specify do
      jsonapi_get(api_employee_milestone_times_path)

      expect(controller.current_user).to be_nil
      expect(controller.current_organization).to be_nil
      expect(response).to be_unauthorized
    end
  end

  describe "authorized" do
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:user) { fixture(:user, :luigi) }
    let(:identity) { fixture(:identity, :luigi) }
    let(:employee) { fixture(:identity, :louis) }
    let(:job) { fixture(:job, :lawn_q2) }
    let(:milestone) { fixture(:milestone, :lawn_q2_milestone_1) }

    let(:headers) { auth_for(user, organization) }

    it "filters by milestone_id with and without pagination", api_scenario: "employee-milestone-times" do
      # First request with pagination
      api_fixture("get-by-milestone") do
        jsonapi_get(
          api_employee_milestone_times_path(
            filter: { milestone_id: milestone.id },
            include: "identity,milestone",
            page: { number: 1, size: 10 }
          ),
          headers: headers
        )
      end

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to be >= 0
      if data.size > 0
        expect(data[0].jsonapi_type).to eq("employee-milestone-time")
        expect(data.first.sideload(:milestone).id).to eq(milestone.id)
      end

      # Second request without pagination
      api_fixture("get-by-milestone-no-pagination") do
        jsonapi_get(
          api_employee_milestone_times_path(
            filter: { milestone_id: milestone.id },
            include: "identity,milestone"
          ),
          headers: headers
        )
      end

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to be >= 0
      if data.size > 0
        expect(data[0].jsonapi_type).to eq("employee-milestone-time")
        expect(data.first.sideload(:milestone).id).to eq(milestone.id)
      end
    end

    it "filters by milestone_id and identity_id" do
      jsonapi_get(
        api_employee_milestone_times_path(
          filter: { milestone_id: milestone.id, identity_id: employee.id },
          include: "milestone.job,identity",
          fields: {
            job: "name"
          }
        ),
        headers: headers
      )

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to be >= 0
      if data.size > 0
        expect(data[0].jsonapi_type).to eq("employee-milestone-time")
        expect(data.first.sideload(:milestone).id).to eq(milestone.id)
        expect(data.first.sideload(:identity).id).to eq(employee.id)
      end
    end

    it "filters by job_id and identity_id" do
      jsonapi_get(
        api_employee_milestone_times_path(
          filter: { job_id: job.id, identity_id: employee.id },
          include: "milestone.job,identity"
        ),
        headers: headers
      )

      expect(response).to be_successful

      data = jsonapi_data

      # All returned times should be for milestones in the specified job
      if data.size > 0
        data.each do |entry|
          milestone = entry.sideload(:milestone)
          expect(milestone.sideload(:job).id).to eq(job.id)
          expect(entry.sideload(:identity).id).to eq(employee.id)
        end
      end
    end
  end
end
