# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::LaborEfficiencyBenchmarksController, type: :request do
  let(:organization) { create(:organization) }
  let(:user) { create(:user) }
  let!(:role) do
    create(:role, user: user, organization: organization, role_type: "admin")
  end
  let!(:benchmark) do
    # Use the automatically created benchmark and update its percentage
    org_benchmark = organization.labor_efficiency_benchmark
    org_benchmark.update!(target_percentage: 80.0)
    org_benchmark
  end

  let(:headers) do
    {
      "Content-Type" => "application/vnd.api+json",
      "Accept" => "application/vnd.api+json"
    }
  end

  before do
    allow_any_instance_of(Api::BaseController).to receive(:current_user).and_return(user)
    allow(Current).to receive(:user).and_return(user)
    allow(Current).to receive(:organization).and_return(organization)
  end

  describe "GET #index" do
    it "returns a list of benchmarks" do
      get "/api/v2/labor_efficiency_benchmarks?filter[organization_id]=#{organization.id}", headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json["data"].length).to eq(1)
      expect(json["data"][0]["attributes"]["target_percentage"]).to eq(80.0)
    end

    it "filters by organization_id" do
      another_org = create(:organization)
      # another_org automatically gets a benchmark created

      get "/api/v2/labor_efficiency_benchmarks?filter[organization_id]=#{organization.id}", headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json["data"].length).to eq(1)
      expect(json["data"][0]["relationships"]["organization"]["data"]["id"].to_i).to eq(organization.id)
    end
  end

  describe "GET #show" do
    it "returns the requested benchmark" do
      get "/api/v2/labor_efficiency_benchmarks/#{benchmark.id}", headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json["data"]["id"].to_i).to eq(benchmark.id)
      expect(json["data"]["attributes"]["target_percentage"]).to eq(80.0)
    end
  end

  describe "PATCH #update" do
    it "updates the benchmark's target percentage" do
      params = {
        data: {
          id: benchmark.id.to_s,
          type: "labor-efficiency-benchmarks",
          attributes: {
            target_percentage: 85.0
          }
        }
      }

      jsonapi_patch "/api/v2/labor_efficiency_benchmarks/#{benchmark.id}",
                   params,
                   headers: headers

      expect(response).to have_http_status(:ok)

      # Verify the database was updated
      benchmark.reload
      expect(benchmark.target_percentage).to eq(85.0)
    end
  end

  describe "POST #reset" do
    it "resets the benchmark to the default percentage" do
      # First, set a different percentage to ensure it changes
      benchmark.update(target_percentage: 90.0)

      post "/api/v2/labor_efficiency_benchmarks/reset",
           params: { organization_id: organization.id }.to_json,
           headers: headers

      expect(response).to have_http_status(:ok)

      # Parse the response
      json = JSON.parse(response.body)
      expect(json["data"]["attributes"]["target_percentage"].to_f).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)

      # Verify the database was updated
      benchmark.reload
      expect(benchmark.target_percentage).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
    end

    it "sets a custom target percentage when provided" do
      # First, set a different percentage to ensure it changes
      benchmark.update(target_percentage: 90.0)

      post "/api/v2/labor_efficiency_benchmarks/reset",
           params: { organization_id: organization.id, target_percentage: 85.0 }.to_json,
           headers: headers

      expect(response).to have_http_status(:ok)

      # Parse the response
      json = JSON.parse(response.body)
      expect(json["data"]["attributes"]["target_percentage"].to_f).to eq(85.0)

      # Verify the database was updated
      benchmark.reload
      expect(benchmark.target_percentage).to eq(85.0)
    end

    it "creates a new benchmark when none exists" do
      # Remove the existing benchmark
      benchmark.destroy

      post "/api/v2/labor_efficiency_benchmarks/reset",
           params: { organization_id: organization.id, target_percentage: 80.0 }.to_json,
           headers: headers

      expect(response).to have_http_status(:ok)

      # Parse the response
      json = JSON.parse(response.body)
      expect(json["data"]["attributes"]["target_percentage"].to_f).to eq(80.0)

      # Verify a new benchmark was created
      new_benchmark = organization.labor_efficiency_benchmark
      expect(new_benchmark).to be_present
      expect(new_benchmark.target_percentage).to eq(80.0)
      expect(new_benchmark.active).to be true
    end
  end
end
