# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::SessionsController, type: :request do
  let(:user) { fixture(:user, :david) }
  let(:email) { user.email }
  let(:password) { SpecHelpers::Fixtures::DEFAULT_PASSWORD }

  let(:bad_redirect) do
    "http://www.external.com/off/site/redirect"
  end

  describe "#create" do
    shared_examples "signing in without an explicit organization" do |api_scenario, user_label, post_sign_in_redirect_url|
      let(:user) { fixture(:user, user_label) }
      let(:organization) { user.organizations.first }

      it "returns a next-action redirect to the front-end app", api_scenario: api_scenario do
        api_fixture("create-session") do
          if post_sign_in_redirect_url
            jsonapi_post(api_session_path, {
              email: email,
              password: password,
              redirect: post_sign_in_redirect_url
            })
          else
            jsonapi_post(api_session_path, { email: email, password: password })
          end
        end

        body = response.parsed_body

        uri = URI(body.dig("data", "attributes", "redirect"))
        route = Rails.application.routes.recognize_path(uri.to_s)
        expect(route).to eq(controller: "ember", action: "passwordless")

        query = URI.decode_www_form(uri.query).to_h

        expect(query).to have_key("sid")
        token = SignInToken.find_signed!(query["sid"])
        expect(token.user).to eq(user)
        expect(token.organization).to eq(organization)

        if post_sign_in_redirect_url
          expect(query).to have_key("redirect")
          expect(query["redirect"]).to eq(post_sign_in_redirect_url)
        else
          expect(query).not_to have_key("redirect")
        end
      end
    end

    shared_examples "signing in with an explicit organization" do |api_scenario, user_label, org_label, post_sign_in_redirect_url|
      let(:user) { fixture(:user, user_label) }
      let(:organization) { fixture(:organization, org_label) }

      let(:api_fixture_overrides) do
        {
          "request.body.organization_id" => ->(id, recorder) {
            recorder.id_for("organization", id)
          }
        }
      end

      it "returns a next-action redirect to the front-end app", api_scenario: api_scenario do
        api_fixture("create-session") do
          if post_sign_in_redirect_url
            jsonapi_post(api_session_path, {
              email: email, password: password,
              organization_id: organization.id,
              redirect: post_sign_in_redirect_url
            })
          else
            jsonapi_post(api_session_path, {
              email: email,
              password: password,
              organization_id: organization.id
            })
          end
        end

        body = response.parsed_body

        uri = URI(body.dig("data", "attributes", "redirect"))
        route = Rails.application.routes.recognize_path(uri.to_s)
        expect(route).to eq(controller: "ember", action: "passwordless")

        query = URI.decode_www_form(uri.query).to_h

        expect(query).to have_key("sid")
        token = SignInToken.find_signed!(query["sid"])
        expect(token.user).to eq(user)
        expect(token.organization).to eq(organization)

        if post_sign_in_redirect_url
          expect(query).to have_key("redirect")
          expect(query["redirect"]).to eq(post_sign_in_redirect_url)
        else
          expect(query).not_to have_key("redirect")
        end
      end
    end

    context "with target=token parameter" do
      it "returns a direct API token for a single organization user" do
        user = fixture(:user, :laura)
        organization = user.organizations.first

        jsonapi_post(api_session_path, {
          email: user.email,
          password: SpecHelpers::Fixtures::DEFAULT_PASSWORD,
          target: "token"
        })

        expect(response).to have_http_status(:ok)

        body = response.parsed_body
        expect(body.dig("data", "type")).to eq("token")
        expect(body.dig("data", "attributes", "token")).to be_present
        expect(body.dig("data", "attributes", "user_id")).to eq(user.id)
        expect(body.dig("data", "attributes", "user_email")).to eq(user.email)
        expect(body.dig("data", "attributes", "organization_id")).to eq(organization.id)
        expect(body.dig("data", "attributes", "organization_name")).to eq(organization.name)
      end

      it "returns a direct API token with explicit organization for multi-org user" do
        user = fixture(:user, :david)
        organization = fixture(:organization, :protiv)

        jsonapi_post(api_session_path, {
          email: user.email,
          password: SpecHelpers::Fixtures::DEFAULT_PASSWORD,
          organization_id: organization.id,
          target: "token"
        })

        expect(response).to have_http_status(:ok)

        body = response.parsed_body
        expect(body.dig("data", "type")).to eq("token")
        expect(body.dig("data", "attributes", "token")).to be_present
        expect(body.dig("data", "attributes", "user_id")).to eq(user.id)
        expect(body.dig("data", "attributes", "user_email")).to eq(user.email)
        expect(body.dig("data", "attributes", "organization_id")).to eq(organization.id)
        expect(body.dig("data", "attributes", "organization_name")).to eq(organization.name)
      end

      it "returns a direct API token without organization for user with no orgs" do
        user = fixture(:user, :john_appleseed)

        jsonapi_post(api_session_path, {
          email: user.email,
          password: SpecHelpers::Fixtures::DEFAULT_PASSWORD,
          target: "token"
        })

        expect(response).to have_http_status(:ok)

        body = response.parsed_body
        expect(body.dig("data", "type")).to eq("token")
        expect(body.dig("data", "attributes", "token")).to be_present
        expect(body.dig("data", "attributes", "user_id")).to eq(user.id)
        expect(body.dig("data", "attributes", "user_email")).to eq(user.email)
        expect(body.dig("data", "attributes", "organization_id")).to be_nil
        expect(body.dig("data", "attributes", "organization_name")).to be_nil
      end
    end

    describe "with valid credentials" do
      context 'with a single organization' do
        include_examples "signing in without an explicit organization", "sign-in/valid-credentials-single-organization", :laura
      end

      context 'with multiple organizations' do
        include_examples "signing in without an explicit organization", "sign-in/valid-credentials", :david
        include_examples "signing in with an explicit organization", "sign-in/valid-credentials-with-organization", :david, :protiv
      end

      context 'with no existing organization' do
        include_examples "signing in without an explicit organization", "sign-in/no-organization", :john_appleseed
      end
    end

    describe "with invalid credentials" do
      it "returns an error", api_scenario: "sign-in/invalid-credentials" do
        api_fixture("create-session-invalid-user") do
          jsonapi_post(api_session_path, { email: "<EMAIL>", password: "wrong-password" })
        end

        api_fixture("create-session-invalid-password") do
          jsonapi_post(api_session_path, { email: email, password: "wrong-password" })
        end
      end
    end

    describe "custom redirect" do
      include_examples "signing in without an explicit organization", "sign-in/custom-redirect", :david, "/dashboard/1?foo#bar"

      it "fails on an invalid redirect parameter", api_scenario: "sign-in/invalid-redirect" do
        api_fixture("create-session") do
          jsonapi_post(api_session_path, {
            email: email,
            password: password,
            redirect: bad_redirect
          })
        end

        # We don't usually have to assert this, as it is implied by diffing the
        # fixture file. This is left behind to preserve the comment indicating
        # we may want to revisit the response code.
        expect(response.status).to eq(400) # FIXME: what's the correct response?
      end
    end
  end

  describe "#destroy" do
    before { sign_in_as(user) }

    let(:post_sign_out_redirect_url) { "/about?foo#bar" }

    it "returns a next-action url", api_scenario: "sign-out/success" do
      headers = auth_for(user)

      api_fixture("delete-session") do
        jsonapi_delete(api_session_path, headers: headers)
      end

      # We don't usually have to assert this, as it is implied by diffing the
      # fixture file. This is left behind to preserve the comment indicating
      # we may want to revisit the response code.
      expect(response.status).to eq(202) # FIXME: what's the correct response?

      body = response.parsed_body

      expect(body.dig("data", "attributes", "redirect")). to eq(sign_out_url)
    end

    it "includes a redirect parameter", api_scenario: "sign-out/custom-redirect" do
      api_fixture("delete-session") do
        jsonapi_delete_with_payload(api_session_path, { redirect: post_sign_out_redirect_url })
      end

      # We don't usually have to assert this, as it is implied by diffing the
      # fixture file. This is left behind to preserve the comment indicating
      # we may want to revisit the response code.
      expect(response.status).to eq(202) # FIXME: what's the correct response?

      body = response.parsed_body

      expect(body.dig("data", "attributes", "redirect")). to eq(
        sign_out_url(redirect: post_sign_out_redirect_url)
      )
    end

    it "fails on an invalid redirect parameter", api_scenario: "sign-out/invalid-redirect" do
      api_fixture("delete-session") do
        jsonapi_delete_with_payload(api_session_path, { redirect: bad_redirect })
      end

      # We don't usually have to assert this, as it is implied by diffing the
      # fixture file. This is left behind to preserve the comment indicating
      # we may want to revisit the response code.
      expect(response.status).to eq(400) # FIXME: what's the correct response?
    end
  end

  describe "#update" do
    let(:organization) { fixture(:organization, :protiv) }

    let(:api_fixture_overrides) do
      {
        "request.body.organization_id" => ->(v, recorder) {
          recorder.id_for("organization", v) if v
        }
      }
    end

    include SpecHelpers::Ember

    before { sign_in_as(user, organization) }

    context "with multiple organizations" do
      let(:other_organization) { fixture(:organization, :lawn_mow_inc) }

      it "switches organizations", api_scenario: "update-session/multi-valid-organizations" do
        headers = auth_for(user, organization)

        jsonapi_get(api_account_path(user.id), headers: headers)
        expect(jsonapi_data.relationships.dig('organization', 'data', 'id')).to eq(organization.id.to_s)

        expect {
          @jsonapi_data = nil
          api_fixture("update-session") do
            jsonapi_put(
              api_session_path,
              { organization_id: other_organization.id, redirect: '/dashboard/lawn-mow-inc' },
              headers: headers
            )
          end
        }.to change(SignInToken, :count).by(1)

        expect(response).to be_ok
        expect(jsonapi_data.jsonapi_type).to eq("next-action")
        expect(jsonapi_data.redirect).to start_with(passwordless_url)

        sign_in_token = SignInToken.last
        expect(sign_in_token.user_id).to eq(user.id)
        expect(sign_in_token.organization_id).to eq(other_organization.id)

        expect {
          get jsonapi_data.redirect
        }.to change(SignInToken, :count).by(-1)

        expect(session[:user_id]).to eq(user.id)
        expect(session[:organization_id]).to eq(other_organization.id)

        expect(URI(response.location).path).to eq('/dashboard/lawn-mow-inc')
        get(response.location)
        index_html = parse_index_html(response.body)
        expect(index_html.authentication_token).to be_present
        new_session = Session.find_by_token_for(:api, index_html.authentication_token)

        expect(new_session.user).to eq(user)
        expect(new_session.organization).to eq(other_organization)

        @jsonapi_data = nil
        jsonapi_get(api_account_path(user.id), headers: auth_for(index_html.authentication_token))
        expect(jsonapi_data.relationships.dig('organization', 'data', 'id')).to eq(other_organization.id.to_s)
      end
    end

    it "switches organizations from nil", api_scenario: "update-session/valid-organization" do
      headers = auth_for(user)

      jsonapi_get(api_account_path(user.id), headers: headers)
      expect(jsonapi_data.relationships.dig('organization', 'data', 'id')).to be_nil

      expect {
        @jsonapi_data = nil
        api_fixture("update-session") do
          jsonapi_put(
            api_session_path,
            { organization_id: organization.id, redirect: '/dashboard/protiv' },
            headers: headers
          )
        end
      }.to change(SignInToken, :count).by(1)

      expect(response).to be_ok
      expect(jsonapi_data.jsonapi_type).to eq("next-action")
      expect(jsonapi_data.redirect).to start_with(passwordless_url)

      sign_in_token = SignInToken.last
      expect(sign_in_token.user_id).to eq(user.id)
      expect(sign_in_token.organization_id).to eq(organization.id)

      expect {
        get jsonapi_data.redirect
      }.to change(SignInToken, :count).by(-1)

      expect(session[:user_id]).to eq(user.id)
      expect(session[:organization_id]).to eq(organization.id)

      expect(URI(response.location).path).to eq('/dashboard/protiv')
      get(response.location)
      index_html = parse_index_html(response.body)
      expect(index_html.authentication_token).to be_present
      new_session = Session.find_by_token_for(:api, index_html.authentication_token)

      expect(new_session.user).to eq(user)
      expect(new_session.organization).to eq(organization)

      @jsonapi_data = nil
      jsonapi_get(api_account_path(user.id), headers: auth_for(index_html.authentication_token))
      expect(jsonapi_data.relationships.dig('organization', 'data', 'id')).to eq(organization.id.to_s)
    end

    it "switches organization to nil", api_scenario: "update-session/null-organization" do
      headers = auth_for(user, organization)

      jsonapi_get(api_account_path(user.id), headers: headers)
      expect(jsonapi_data.relationships.dig('organization', 'data', 'id')).to eq(organization.id.to_s)

      expect {
        @jsonapi_data = nil
        api_fixture("update-session") do
          jsonapi_put(
            api_session_path,
            { organization_id: nil, redirect: '/onboarding' },
            headers: headers
          )
        end
      }.to change(SignInToken, :count).by(1)

      expect(response).to be_ok
      expect(jsonapi_data.jsonapi_type).to eq("next-action")
      expect(jsonapi_data.redirect).to start_with(passwordless_url)

      sign_in_token = SignInToken.last
      expect(sign_in_token.user_id).to eq(user.id)
      expect(sign_in_token.organization_id).to eq(nil)

      expect {
        get jsonapi_data.redirect
      }.to change(SignInToken, :count).by(-1)

      expect(session[:user_id]).to eq(user.id)
      expect(session[:organization_id]).to eq(nil)

      expect(URI(response.location).path).to eq('/onboarding')
      get(response.location)
      index_html = parse_index_html(response.body)
      expect(index_html.authentication_token).to be_present
      new_session = Session.find_by_token_for(:api, index_html.authentication_token)

      expect(new_session.user).to eq(user)
      expect(new_session.organization).to be_nil

      @jsonapi_data = nil
      jsonapi_get(api_account_path(user.id), headers: auth_for(index_html.authentication_token))
      expect(jsonapi_data.relationships.dig('organization', 'data', 'id')).to eq(nil)
    end
  end
end
