# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::CatalogItemsController, type: :request do
  let(:user) { fixture(:user, :david) }
  let(:organization) { fixture(:organization, :protiv) }
  let(:headers) { auth_for(user, organization) }

  describe 'GET /api/v2/catalog_items' do
    before do
      @material1 = create(:catalog_item, organization: organization, item_type: 'Material', name: 'Conduit')
      @material2 = create(:catalog_item, organization: organization, item_type: 'Material', name: 'Wire')
      @labor_item = create(:catalog_item, organization: organization, item_type: 'Labor', name: 'Installation')
      @other_org_item = create(:catalog_item, item_type: 'Material', name: 'Other Material')
    end

    it 'returns all catalog items for the organization' do
      jsonapi_get '/api/v2/catalog_items', headers: headers

      expect(response).to have_http_status(:ok)

      data = json_response['data']
      item_ids = data.map { |i| i['id'].to_i }

      expect(item_ids).to include(@material1.id, @material2.id, @labor_item.id)
      expect(item_ids).not_to include(@other_org_item.id)
    end

    it 'filters by item_type when specified' do
      jsonapi_get '/api/v2/catalog_items?filter[item_type]=Material', headers: headers

      expect(response).to have_http_status(:ok)

      data = json_response['data']
      item_ids = data.map { |i| i['id'].to_i }

      expect(item_ids).to include(@material1.id, @material2.id)
      expect(item_ids).not_to include(@labor_item.id, @other_org_item.id)
    end

    it 'includes catalog item attributes in response' do
      jsonapi_get '/api/v2/catalog_items', headers: headers

      expect(response).to have_http_status(:ok)

      item_data = json_response['data'].find { |i| i['id'].to_i == @material1.id }
      expect(item_data['type']).to eq('catalog-items')
      expect(item_data['attributes']['name']).to eq('Conduit')
      expect(item_data['attributes']['item_type']).to eq('Material')
    end

    context 'when not authenticated' do
      it 'returns unauthorized' do
        jsonapi_get '/api/v2/catalog_items'
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v2/catalog_items/:id' do
    before do
      @catalog_item = create(:catalog_item, organization: organization, name: 'Test Item')
    end

    it 'returns the requested catalog item' do
      jsonapi_get "/api/v2/catalog_items/#{@catalog_item.id}", headers: headers

      expect(response).to have_http_status(:ok)

      data = json_response['data']
      expect(data['id'].to_i).to eq(@catalog_item.id)
      expect(data['type']).to eq('catalog-items')
      expect(data['attributes']['name']).to eq('Test Item')
    end

    context 'when catalog item belongs to different organization' do
      before do
        @other_item = create(:catalog_item)
      end

      it 'returns not found' do
        jsonapi_get "/api/v2/catalog_items/#{@other_item.id}", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
end
