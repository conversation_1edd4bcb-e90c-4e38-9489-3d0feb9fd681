# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::IntegrationsController, type: :request do
  let(:params) { {} }
  let(:headers) { {} }
  let(:admin_user) { fixture(:user, :david) }
  let(:manager_user) { fixture(:user, :luigi) }
  let(:organization) { fixture(:organization, :lawn_mow_inc) }
  let(:integration) { fixture(:integration, :lawn_mow_inc) }

  let(:admin_token) { token_for(admin_user, organization) }
  let(:manager_token) { token_for(manager_user) }

  def make_get_request(request_path = path)
    jsonapi_get request_path, params: params, headers: headers
  end

  def make_post_request(request_path = path)
    jsonapi_post request_path, params, headers: headers
  end

  def make_put_request(request_path = path)
    jsonapi_put request_path, params, headers: headers
  end

  def make_patch_request(request_path = path)
    jsonapi_patch request_path, params, headers: headers
  end

  context 'as a non-admin' do
    let(:headers) { auth_for(manager_token) }

    describe '#index' do
      # FIXME: This behavior doesn't actually work!
      # The test was originally passing because the database was empty.
      # Now that we switched to fixtures, this actually returned the
      # integration (instead of empty as specified)!
      pending do
        make_get_request api_integrations_path
        expect(response).to be_ok
        expect(jsonapi_data).to be_empty
      end
    end

    describe '#show' do
      specify do
        make_get_request api_integration_path(integration)

        expect(response).to be_not_found
      end
    end

    describe '#create' do
    end

    describe '#update' do
    end
  end

  context 'as an admin' do
    let(:headers) { auth_for(admin_token) }

    describe '#index' do
      specify 'filtered' do
        make_get_request api_integrations_path

        expect(response).to be_ok
      end
    end

    describe '#show' do
      specify do
        make_get_request(api_integration_path(integration))
        expect(response).to be_ok
      end
    end

    let(:organization_id) { organization.id }
    let(:integration_attributes) do
      {
        source: :aspire,
        client_id: AspireCredentialsHelper.client_id,
        secret: AspireCredentialsHelper.secret
      }
    end

    describe '#create' do
      let(:params) do
        {
          data: {
            type: "integrations",
            attributes: integration_attributes
          }
        }
      end

      it "works", api_scenario: "integrations/create" do
        api_fixture("create-integration") do
          make_post_request(api_integrations_path)
        end
        expect(response).to have_http_status(:created)

        data = jsonapi_data
        expect(data.id).to be_present

        expect { data.secret }.to raise_error(GraphitiSpecHelpers::Errors::NoAttribute)
        integration = Integration.find(data.id)
        expect(integration).to be_valid
        expect(Sync::SyncIntegrationJob.jobs.count).to eq(1)
      end

      context 'with invalid params' do
        let(:integration_attributes) do
          {
            source: :aspire,
            client_id: AspireCredentialsHelper.client_id
          }
        end

        it 'fails', api_scenario: "integrations/invalid-params" do
          api_fixture("create-integration") do
            make_post_request(api_integrations_path)
          end
          expect(response).not_to be_ok
          expect(jsonapi_errors).to be_present
          error = jsonapi_errors.first

          expect(error.status.to_i).to eq(422)
          expect(error.detail).to eq("Secret can't be blank")
          expect(error.json['source']).to eq({ "pointer" => "/data/attributes/secret" })
        end
      end

      context 'for non-admin organization' do
        before { Role.where(role_type: :admin).destroy_all }

        it 'is unauthorized', api_scenario: "integrations/unauthorized" do
          api_fixture("create-integration") do
            make_post_request(api_integrations_path)
          end
          expect(response).not_to be_ok
          expect(response).to have_http_status(:unauthorized)
        end
      end
    end

    describe '#update' do
      let(:integration_attributes) do
        {
          client_id: "new_client_id",
          secret: "new_secret"
        }
      end

      let(:params) do
        {
          data: {
            type: "integration",
            id: integration.id,
            attributes: integration_attributes
          }
        }
      end

      it "clears credentials when client_id or secret changes", api_scenario: "integrations/update" do
        integration.create_credential

        api_fixture("update-integration") do
          make_put_request(api_integration_path(integration.id))
        end

        expect(integration.reload.credential).to be_nil
        expect(response).to be_ok
        expect(jsonapi_data.client_id).to eq("new_client_id")
      end
    end
  end
end
