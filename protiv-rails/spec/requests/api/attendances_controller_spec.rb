# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::AttendancesController do
  let(:manager) { fixture(:user, :laura) }
  let(:integration) { fixture(:integration, :lawn_mow_inc) }
  let(:organization) { fixture(:organization, :lawn_mow_inc) }
  let(:identity) { fixture(:identity, :louis) }
  let(:manager_identity) { fixture(:identity, :laura) }

  before do
    integration.update(source: :test_a)
  end

  describe '#create and #update' do
    let(:params) do
      {
        data: {
          type: 'attendances',
          attributes: {
            started_at: 3.hours.ago
          },
          relationships: {
            identity: {
              data: { type: 'identities', id: identity.id }
            }
          }
        }
      }
    end

    let(:headers) { auth_for(manager, organization) }

    context 'with no milestone' do
      specify do
        expect {
          jsonapi_post(api_attendances_path, params, headers:)
          expect(response).to be_successful
        }.to change { Attendance.open.count }.by(1)

        attendance = Attendance.last
        expect(attendance.identity).to eq(identity)
        expect(attendance.milestone).to be_nil
        expect(attendance.route).to be_nil
        expect(attendance.started_at).to be_within(1.second).of(3.hours.ago)
        expect(attendance.ended_at).to be_nil

        update_params = {
          data: {
            type: 'attendance',
            id: attendance.id,
            attributes: {
              ended_at: Time.current,
              break_time_seconds: 30.minutes
            }
          }
        }

        expect(Sync::SyncDatumJob.jobs.count).to eq(0)

        expect {
          jsonapi_patch(api_attendance_path(attendance), update_params, headers:)
          expect(response).to be_successful
        }.to change { Attendance.closed.count }.by(1)

        expect(Sync::SyncDatumJob.jobs.count).to eq(1)

        attendance.reload

        expect(attendance.ended_at).to be_present
        expect(attendance.created_by_user).to eq(manager)
        expect(attendance.identity).to eq(identity)
        expect(attendance.break_time_seconds).to eq(30.minutes)
        expect(attendance.manager).to eq(manager_identity)

        update_params[:data][:attributes][:ended_at] = 5.minutes.from_now

        jsonapi_patch(api_attendance_path(attendance), update_params, headers:)
        expect(response).not_to be_successful
        expect(jsonapi_errors.first.message).to eq("Attendances cannot be altered after they are closed")
      end
    end

    context 'with a milestone' do
      let(:milestone) { fixture(:milestone, :lawn_q2_milestone_1) }

      before do
        params[:data][:relationships][:milestone] = {
          data: { type: "milestone", id: milestone.id }
        }
      end

      specify do
        expect {
          jsonapi_post(api_attendances_path, params, headers:)
          expect(response).to be_successful
        }.to change { Attendance.open.count }.by(1)

        attendance = Attendance.last
        expect(attendance.identity).to eq(identity)
        expect(attendance.milestone).to eq(milestone)
        expect(attendance.route).to be_nil
        expect(attendance.started_at).to be_within(1.second).of(3.hours.ago)
        expect(attendance.ended_at).to be_nil

        update_params = {
          data: {
            type: 'attendance',
            id: attendance.id,
            attributes: {
              ended_at: Time.current,
              break_time_seconds: 30.minutes
            }
          }
        }

        expect(Sync::SyncDatumJob.jobs.count).to eq(0)

        expect {
          jsonapi_patch(api_attendance_path(attendance), update_params, headers:)
          expect(response).to be_successful
        }.to change { Attendance.closed.count }.by(1)

        expect(Sync::SyncDatumJob.jobs.count).to eq(1)

        attendance.reload

        expect(attendance.ended_at).to be_present
        expect(attendance.created_by_user).to eq(manager)
        expect(attendance.identity).to eq(identity)
        expect(attendance.break_time_seconds).to eq(30.minutes)
        expect(attendance.manager).to eq(manager_identity)

        update_params[:data][:attributes][:ended_at] = 5.minutes.from_now

        jsonapi_patch(api_attendance_path(attendance), update_params, headers:)
        expect(response).not_to be_successful
        expect(jsonapi_errors.first.message).to eq("Attendances cannot be altered after they are closed")
      end
    end
  end
end
