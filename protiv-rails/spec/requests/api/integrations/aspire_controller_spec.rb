# frozen_string_literal: true

require "rails_helper"

# This is a simplified version of the request spec that doesn't rely on fixtures or database access
RSpec.describe "Aspire Integration API", type: :request do
  describe "POST /api/v2/integrations/aspire" do
    it "has the correct route" do
      # This test simply verifies that the route exists
      # We've already tested the controller functionality in the controller spec
      route_info = Rails.application.routes.recognize_path("/api/v2/integrations/aspire", method: :post)
      expect(route_info[:controller]).to eq("api/integrations/aspire")
      expect(route_info[:action]).to eq("create")
    end
  end
end
