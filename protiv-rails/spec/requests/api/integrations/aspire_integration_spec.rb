# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Aspire Integration API", type: :request do
  let(:admin_user) { fixture(:user, :david) }
  let(:non_admin_user) { fixture(:user, :john_appleseed) }
  let(:organization) { fixture(:organization, :lawn_mow_inc) }

  let(:admin_token) { token_for(admin_user, organization) }
  let(:non_admin_token) { token_for(non_admin_user) }

  let(:client_id) { "test_client_id" }
  let(:secret_key) { "test_secret_key" }

  describe "POST /api/v2/integrations/aspire" do
    context "when authenticated as admin" do
      let(:headers) { auth_for(admin_token) }

      it "creates a new aspire integration" do
        expect {
          post api_integrations_aspire_path, params: { client_id: client_id, secret_key: secret_key }, headers: headers.to_hash
        }.to change(Integration, :count).by(1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ "message" => "Aspire credentials saved.", "organization_id" => organization.id })

        # Verify the integration was created correctly
        integration = Integration.last
        expect(integration.organization).to eq(organization)
        expect(integration.source).to eq("aspire")
        expect(integration.client_id).to eq(client_id)
        expect(integration.secret).to eq(secret_key)
      end

      it "updates an existing aspire integration" do
        # Create an initial integration
        integration = Integration.create!(
          organization: organization,
          source: "aspire",
          client_id: "old_client_id",
          secret: "old_secret_key"
        )

        expect {
          post api_integrations_aspire_path, params: { client_id: client_id, secret_key: secret_key }, headers: headers.to_hash
        }.not_to change(Integration, :count)

        expect(response).to have_http_status(:ok)

        # Verify the integration was updated
        integration.reload
        expect(integration.client_id).to eq(client_id)
        expect(integration.secret).to eq(secret_key)
      end

      context "with missing parameters" do
        it "returns an error when client_id is missing" do
          post api_integrations_aspire_path, params: { secret_key: secret_key }, headers: headers.to_hash

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)).to have_key("errors")
        end

        it "returns an error when secret_key is missing" do
          post api_integrations_aspire_path, params: { client_id: client_id }, headers: headers.to_hash

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)).to have_key("errors")
        end
      end
    end

    context "when authenticated as non-admin" do
      let(:headers) { auth_for(non_admin_token) }

      it "returns unauthorized" do
        post api_integrations_aspire_path, params: { client_id: client_id, secret_key: secret_key }, headers: headers.to_hash

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when not authenticated" do
      it "returns unauthorized" do
        post api_integrations_aspire_path, params: { client_id: client_id, secret_key: secret_key }

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
