# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Onboarding Integration API", type: :request do
  let(:admin_user) { fixture(:user, :david) }
  let(:organization) { fixture(:organization, :lawn_mow_inc) }
  let(:admin_token) { token_for(admin_user, organization) }
  let(:headers) { auth_for(admin_token) }

  describe "POST /api/v2/integrations" do
    let(:client_id) { "test_client_id_for_onboarding" }
    let(:secret) { "test_secret_for_onboarding" }
    let(:integration_attributes) do
      {
        source: :aspire,
        client_id: client_id,
        secret: secret
      }
    end
    let(:params) do
      {
        data: {
          type: "integrations",
          attributes: integration_attributes
        }
      }
    end

    context "with valid parameters" do
      it "creates an integration with client_id and secret" do
        expect {
          jsonapi_post api_integrations_path, params, headers: headers
        }.to change(Integration, :count).by(1)

        expect(response).to have_http_status(:created)

        # Verify the response
        data = jsonapi_data
        expect(data.id).to be_present
        expect(data.client_id).to eq(client_id)

        # Secret should not be returned in the response
        expect { data.secret }.to raise_error(GraphitiSpecHelpers::Errors::NoAttribute)

        # Verify the database record
        integration = Integration.find(data.id)
        expect(integration).to be_valid
        expect(integration.client_id).to eq(client_id)
        expect(integration.secret).to eq(secret)
        expect(integration.source).to eq("aspire")
        expect(integration.organization_id).to eq(organization.id)
      end
    end

    context "with missing secret for Aspire" do
      let(:integration_attributes) do
        {
          source: :aspire,
          client_id: client_id
        }
      end

      it "returns a validation error" do
        jsonapi_post api_integrations_path, params, headers: headers

        expect(response).not_to be_ok
        expect(jsonapi_errors).to be_present
        error = jsonapi_errors.first

        expect(error.status.to_i).to eq(422)
        expect(error.detail).to eq("Secret can't be blank")
        expect(error.json['source']).to eq({ "pointer" => "/data/attributes/secret" })
      end
    end
  end

  describe "PUT /api/v2/integrations/:id" do
    let!(:integration) { fixture(:integration, :lawn_mow_inc) }
    let(:new_client_id) { "updated_client_id_for_onboarding" }
    let(:new_secret) { "updated_secret_for_onboarding" }
    let(:integration_attributes) do
      {
        client_id: new_client_id,
        secret: new_secret
      }
    end
    let(:params) do
      {
        data: {
          type: "integration",
          id: integration.id,
          attributes: integration_attributes
        }
      }
    end

    it "updates the integration with new client_id and secret" do
      jsonapi_put api_integration_path(integration.id), params, headers: headers

      expect(response).to be_ok

      # Verify the response
      data = jsonapi_data
      expect(data.client_id).to eq(new_client_id)

      # Secret should not be returned in the response
      expect { data.secret }.to raise_error(GraphitiSpecHelpers::Errors::NoAttribute)

      # Verify the database record
      integration.reload
      expect(integration.client_id).to eq(new_client_id)
      expect(integration.secret).to eq(new_secret)
    end
  end
end
