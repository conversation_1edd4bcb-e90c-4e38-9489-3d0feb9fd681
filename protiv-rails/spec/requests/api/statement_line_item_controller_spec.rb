# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::StatementLineItemsController, api_scenario: 'statement-line-items' do
  let(:headers) { {} }
  let(:params) { {} }

  context 'when not logged in', api_scenario: 'unauthorized' do
    specify do
      api_fixture('get') do
        jsonapi_get('/api/v2/statement_line_items', headers: headers, params: params)
        expect(response).to be_unauthorized
      end
    end
  end

  describe 'when logged in', api_scenario: 'authorized' do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }

    describe '#index' do
      context 'unfiltered' do
        let(:params) { { include: 'pro_pay,statement,identity' } }

        specify api_scenario: 'index-unfiltered' do
          params.delete(:filter)

          api_fixture('get') do
            jsonapi_get('/api/v2/statement_line_items', headers: headers, params: params)
          end

          expect(response).to be_successful
          expect(jsonapi_data.length).to eq(4)
        end
      end
    end

    describe '#show' do
      let(:statement_1) { fixture(:statement, :statement_1) }
      let(:statement_2) { fixture(:statement, :statement_2) }

      let(:line_item_1) { fixture(:statement_line_item, :statement_line_item_1) }
      let(:line_item_3) { fixture(:statement_line_item, :statement_line_item_3) }

      specify 'show first line item', api_scenario: 'show-line-item-1' do
        params[:include] = 'pro_pay,statement,identity'
        api_fixture('get') do
          jsonapi_get("/api/v2/statement_line_items/#{line_item_1.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(line_item_1.id)

        expect(jsonapi_data.amount).to eq({ "cents" => -49511, "currency_iso" => "USD" })
        expect(jsonapi_data.status).to eq("in_process")

        # Verify included relationships
        pro_pay = jsonapi_data.sideload(:pro_pay)
        expect(pro_pay).to eq(nil)

        statement = jsonapi_data.sideload(:statement)
        expect(statement.id).to eq(statement_1.id)
      end

      specify 'show second line item', api_scenario: 'show-line-item-2' do
        params[:include] = 'pro_pay,statement,identity'
        api_fixture('get') do
          jsonapi_get("/api/v2/statement_line_items/#{line_item_3.id}", headers: headers, params: params)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(line_item_3.id)

        expect(jsonapi_data.amount).to eq({ "cents" => 85999, "currency_iso" => "USD" })
        expect(jsonapi_data.status).to eq("void")

        # Verify included relationships
        pro_pay = jsonapi_data.sideload(:pro_pay)
        expect(pro_pay).to eq(nil)

        statement = jsonapi_data.sideload(:statement)
        expect(statement.id).to eq(statement_2.id)
      end
    end

    describe '#void' do
      let(:statement_1) { fixture(:statement, :statement_1) }

      let(:line_item_1) { fixture(:statement_line_item, :statement_line_item_1) }

      specify 'void', api_scenario: 'void' do
        params[:include] = 'statement,identity'
        api_fixture('post') do
          jsonapi_post("/api/v2/statement_line_items/#{line_item_1.id}/void", params, headers: headers)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(line_item_1.id)

        expect(jsonapi_data.status).to eq("void")

        statement = jsonapi_data.sideload(:statement)
        expect(statement.id).to eq(statement_1.id)
        expect(statement.net_amount).to eq({ "cents" => 13486, "currency_iso" => "USD" })
        expect(statement.bonuses).to eq({ "cents" => 13486, "currency_iso" => "USD" })
        expect(statement.deductions).to eq({ "cents" => 0, "currency_iso" => "USD" })
      end
    end

    describe '#release' do
      let(:statement_2) { fixture(:statement, :statement_2) }

      let(:line_item_3) { fixture(:statement_line_item, :statement_line_item_3) }

      specify 'release', api_scenario: 'release' do
        params[:include] = 'statement,identity'
        api_fixture('post') do
          jsonapi_post("/api/v2/statement_line_items/#{line_item_3.id}/release", params, headers: headers)
        end

        expect(response).to be_successful
        expect(Array(jsonapi_data).length).to eq(1)
        expect(jsonapi_data.id).to eq(line_item_3.id)

        expect(jsonapi_data.status).to eq("in_process")

        statement = jsonapi_data.sideload(:statement)
        expect(statement.id).to eq(statement_2.id)
        expect(statement.net_amount).to eq({ "cents" => 76000, "currency_iso" => "USD" })
        expect(statement.bonuses).to eq({ "cents" => 85999, "currency_iso" => "USD" })
        expect(statement.deductions).to eq({ "cents" => -9999, "currency_iso" => "USD" })
      end
    end
  end
end
