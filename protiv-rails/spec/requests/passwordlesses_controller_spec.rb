# frozen_string_literal: true

require "rails_helper"

# FIXME: all of these should be migrated to an API controller

__END__
RSpec.describe Sessions::PasswordlessesController do
  include ActionMailer::TestHelper

  describe "#new" do
    specify do
      get new_sessions_passwordless_path

      expect(response).to have_http_status(:ok)
      expect(response).to render_template(:new)
    end
  end

  describe "#edit" do
    let(:user) { create(:user, verified: true) }
    let(:token) { SignInToken.create(user: user) }
    let(:valid_sid) { token.signed_id }

    specify do
      get edit_sessions_passwordless_path(sid: valid_sid)

      expect(response).to redirect_to(root_path)
      expect(flash[:notice]).to eq(I18n.t("sessions.sign_in_success"))
      expect(user.sign_in_tokens.count).to eq(0)
      expect(session[:user_id]).to eq(user.id)
      expect(controller.current_user).to eq(user)
    end

    context "when the token is invalid" do
      let(:invalid_sid) { "invalid_token" }

      specify do
        get edit_sessions_passwordless_path(sid: invalid_sid)

        expect(response).to redirect_to(new_sessions_passwordless_path)

        expect(flash[:alert]).to include(I18n.t("sessions.invalid_link"))
        expect(controller.current_user).to be_nil
      end
    end

    context "when there is no token provided" do
      specify do
        get edit_sessions_passwordless_path

        expect(response).to redirect_to(new_sessions_passwordless_path)
        expect(flash[:alert]).to include(I18n.t("sessions.invalid_link"))
        expect(controller.current_user).to be_nil
      end
    end
  end

  context "user is unverified" do
  describe "#create" do
      let(:user) { create(:user, verified: false) }

      specify do
        post("/sessions/passwordless", params: { email: user.email })

        expect(response).to redirect_to(new_sessions_passwordless_path)
        expect(flash[:alert]).to eq(I18n.t("sessions.verification_alert"))

        assert_no_emails
        assert_enqueued_emails 0
      end
    end

    context "user is verified" do
      let(:user) { create(:user, verified: true) }

      specify do
        post("/sessions/passwordless", params: { email: user.email })

        expect(response).to redirect_to(root_path)

        assert_no_emails
        assert_enqueued_emails 1
      end
    end
  end
end
