# frozen_string_literal: true

require "rails_helper"

RSpec.describe PasswordsController, type: :request do
  let(:user) { create(:user) }

  before do
    sign_in_as(user)
  end

  specify "should get edit" do
    get edit_password_url
    assert_response :success
  end

  specify "should update password" do
    patch password_url, params: { password_challenge: "Secret1*3*5*", password: "Secret6*4*2*", password_confirmation: "Secret6*4*2*" }
    assert_redirected_to root_url
  end

  specify "should not update password with wrong password challenge" do
    patch password_url, params: { password_challenge: "SecretWrong1*3", password: "Secret6*4*2*", password_confirmation: "Secret6*4*2*" }

    assert_response :unprocessable_entity
    assert_select "li", /Password challenge is invalid/
  end
end
