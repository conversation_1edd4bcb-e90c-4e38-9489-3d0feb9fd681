# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Onboarding Flow", type: :request do
  let(:user) { fixture(:user, :john_appleseed) }

  # Тесты проверяют только логику контроллера, рендеринг пропускается в самих тестах

  describe "Step 2: Connect to Aspire" do
    it "sets the onboarding step in the session" do
      # Пропускаем рендеринг для этого теста
      allow_any_instance_of(OnboardingController).to receive(:render).and_return(nil)

      get "/onboarding/step/2"

      expect(response).to be_successful
      expect(session[:onboarding_step]).to eq(2)
    end
  end

  describe "Step 3: Preferences" do
    it "sets the onboarding step in the session" do
      # Пропускаем рендеринг для этого теста
      allow_any_instance_of(OnboardingController).to receive(:render).and_return(nil)

      get "/onboarding/step/3"

      expect(response).to be_successful
      expect(session[:onboarding_step]).to eq(3)
    end
  end
end
