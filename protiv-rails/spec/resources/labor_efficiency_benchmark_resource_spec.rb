# frozen_string_literal: true

require "rails_helper"

RSpec.describe LaborEfficiencyBenchmarkResource, type: :resource do
  let(:organization) { create(:organization) }
  let(:user) { create(:user) }
  let!(:role) do
    create(:role, user: user, organization: organization, role_type: "admin")
  end
  let!(:benchmark) do
    create(:labor_efficiency_benchmark,
           organization: organization,
           level: "organization",
           target_percentage: 80.0,
           active: true)
  end

  before do
    allow(Current).to receive(:user).and_return(user)
    allow(Current).to receive(:organization).and_return(organization)
  end

  describe "filtering" do
    let(:another_org) { create(:organization) }
    let!(:another_benchmark) do
      create(:labor_efficiency_benchmark,
             organization: another_org,
             level: "organization",
             target_percentage: 70.0,
             active: true)
    end

    it "can filter by organization_id" do
      params = { filter: { organization_id: organization.id } }
      results = LaborEfficiencyBenchmarkResource.all(params).data
      expect(results.map(&:id)).to eq([benchmark.id])
    end

    it "can filter by level" do
      params = { filter: { level: "organization" } }
      results = LaborEfficiencyBenchmarkResource.all(params).data
      expect(results.map(&:id)).to include(benchmark.id, another_benchmark.id)
    end

    it "can filter by active status" do
      inactive_benchmark = create(:labor_efficiency_benchmark,
                                  organization: organization,
                                  level: "organization",
                                  target_percentage: 65.0,
                                  active: false)

      params = { filter: { active: false } }
      results = LaborEfficiencyBenchmarkResource.all(params).data
      expect(results.map(&:id)).to eq([inactive_benchmark.id])
    end
  end

  describe "attributes" do
    it "has the expected attributes defined" do
      expect(described_class.attributes).to include(:target_percentage, :level, :active)
    end
  end

  describe "relationships" do
    it "includes organization in sideloads" do
      params = { include: "organization" }
      results = LaborEfficiencyBenchmarkResource.all(params).data

      # Verify we can get the benchmark with organization included
      expect(results).to include(an_object_having_attributes(id: benchmark.id))

      # Verify the model has the relationship
      expect(LaborEfficiencyBenchmark.reflect_on_association(:organization)).to be_present
    end
  end

  describe "updating" do
    it "can update target_percentage" do
      # Directly update the benchmark using ActiveRecord
      expect {
        benchmark.update(target_percentage: 85.0)
      }.to change { benchmark.reload.target_percentage }.from(80.0).to(85.0)
    end
  end

  describe "custom actions" do
    it "can reset to default" do
      # Test the service directly
      benchmark_manager = Metrics::BenchmarkManager.new(organization)

      expect {
        benchmark_manager.reset_to_default
      }.to change { benchmark.reload.target_percentage }.to(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
    end
  end
end
