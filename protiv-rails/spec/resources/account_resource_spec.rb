# frozen_string_literal: true

require "rails_helper"

RSpec.describe AccountResource, type: :resource do
  let(:current_user) { nil }

  describe "#create" do
    let(:payload) do
      {
        data: {
          type: "account",
          attributes: {
            email: "<EMAIL>", name: "foo", password: "password123"
          }
        }
      }
    end

    specify do
      resource = AccountResource.build(payload)
      expect { resource.save }.to change { User.count }.by(1)
    end
  end
end
