# frozen_string_literal: true

require "rails_helper"

RSpec.describe UserResource, type: :resource do
  let(:current_user) { nil }
  let!(:other_users) { create_list(:user, 3) }

  context "with current_user" do
    let!(:current_user) { create(:user) }

    it "only includes the current user" do
      render

      expect(jsonapi_data.length).to eq(1)
      data = jsonapi_data.first

      expect(data.jsonapi_type).to eq("user")
      expect(data.id).to eq(current_user.id)
    end
  end

  context "with no user" do
    it "only includes the current user" do
      render

      expect(jsonapi_data.length).to eq(0)
    end
  end

  describe "attributes" do
    let!(:current_user) { create(:user) }

    it "defines attributes with correct readability and writability" do
      expect(UserResource.attributes[:name][:readable]).to eq(true)
      expect(UserResource.attributes[:name][:writable]).to eq(true)

      expect(UserResource.attributes[:email][:readable]).to eq(true)
      expect(UserResource.attributes[:email][:writable]).to eq(true)

      expect(UserResource.attributes[:phone_country_code][:readable]).to eq(true)
      expect(UserResource.attributes[:phone_country_code][:writable]).to eq(true)

      expect(UserResource.attributes[:phone][:readable]).to eq(true)
      expect(UserResource.attributes[:phone][:writable]).to eq(true)

      expect(UserResource.attributes[:verified][:readable]).to eq(true)
      expect(UserResource.attributes[:verified][:writable]).to eq(false)

      expect(UserResource.attributes[:password_change_pending_verification][:readable]).to eq(true)
      expect(UserResource.attributes[:password_change_pending_verification][:writable]).to eq(false)

      expect(UserResource.attributes[:admin][:readable]).to eq(true)
      expect(UserResource.attributes[:admin][:writable]).to eq(false)
    end

    it "returns the correct attribute values" do
      render
      data = jsonapi_data.first

      expect(data.attributes).to include(
        name: current_user.name,
        email: current_user.email,
        verified: current_user.verified,
        admin: current_user.admin,
        password_change_pending_verification: current_user.password_change_pending_verification
      )

      # These might be nil in the factory
      expect(data.attributes).to have_key(:phone_country_code)
      expect(data.attributes).to have_key(:phone)
    end
  end
end
