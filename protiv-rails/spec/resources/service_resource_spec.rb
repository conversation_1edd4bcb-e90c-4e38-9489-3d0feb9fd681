# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ServiceResource, type: :resource do
  let(:organization) { create(:organization) }
  let(:user) { create(:user) }
  let(:context) { double('context', current_organization: organization, current_user: user) }

  # Set up user as manager of the organization for authorization
  before do
    create(:role, user: user, organization: organization, role_type: "manager")
    # Set Current.organization so ServicePolicy can access it
    Current.organization = organization
  end

  describe '.bulk_update_progress_types' do
    let!(:service1) { create(:service, organization: organization, progress_type: 'percentage') }
    let!(:service2) { create(:service, organization: organization, progress_type: 'percentage') }

    it 'updates multiple services in a transaction' do
      updates = [
        { id: service1.id, progress_type: 'units' },
        { id: service2.id, progress_type: 'units' }
      ]

      result = ServiceResource.bulk_update_progress_types(updates, context)

      expect(result).to be_an(Array)
      expect(result.length).to eq(2)
      expect(service1.reload.progress_type).to eq('units')
      expect(service2.reload.progress_type).to eq('units')
    end

    it 'handles string keys for update data' do
      updates = [
        { 'id' => service1.id, 'progress_type' => 'units' }
      ]

      result = ServiceResource.bulk_update_progress_types(updates, context)

      expect(result).to be_an(Array)
      expect(result.length).to eq(1)
      expect(service1.reload.progress_type).to eq('units')
    end

    it 'rolls back all changes if one update fails' do
      updates = [
        { id: service1.id, progress_type: 'units' },
        { id: service2.id, progress_type: 'invalid' }
      ]

      expect {
        ServiceResource.bulk_update_progress_types(updates, context)
      }.to raise_error(ActiveRecord::RecordInvalid)

      expect(service1.reload.progress_type).to eq('percentage')
      expect(service2.reload.progress_type).to eq('percentage')
    end

    it 'calls authorize for each service' do
      updates = [{ id: service1.id, progress_type: 'units' }]

      # Mock the policy
      policy = double('policy', update?: true)
      allow(ServicePolicy).to receive(:new).with(user, service1).and_return(policy)

      ServiceResource.bulk_update_progress_types(updates, context)
    end

    it 'raises error when service not found' do
      updates = [{ id: 99999, progress_type: 'units' }]

      expect {
        ServiceResource.bulk_update_progress_types(updates, context)
      }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'only finds services in the current organization' do
      other_org_service = create(:service, progress_type: 'percentage')
      updates = [{ id: other_org_service.id, progress_type: 'units' }]

      expect {
        ServiceResource.bulk_update_progress_types(updates, context)
      }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'handles empty updates array' do
      result = ServiceResource.bulk_update_progress_types([], context)

      expect(result).to eq([])
    end

    it 'returns the updated service objects' do
      updates = [{ id: service1.id, progress_type: 'units' }]

      result = ServiceResource.bulk_update_progress_types(updates, context)

      expect(result.first).to eq(service1)
      expect(result.first.progress_type).to eq('units')
    end

    it 'raises authorization error when user cannot update service' do
      updates = [{ id: service1.id, progress_type: 'units' }]

      # Mock the policy to deny access
      policy = double('policy', update?: false)
      allow(ServicePolicy).to receive(:new).with(user, service1).and_return(policy)

      expect {
        ServiceResource.bulk_update_progress_types(updates, context)
      }.to raise_error(Pundit::NotAuthorizedError)
    end
  end
end
