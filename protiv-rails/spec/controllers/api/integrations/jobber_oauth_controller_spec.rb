# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::Integrations::Jobber::Oauth::JobberOauthController, type: :controller do
  pending "write tests"
  # let(:organization) { double("Organization", id: 123) }
  # let(:user) { double("User", organization: organization, organizations: [organization]) }
  # let(:oauth_service) { instance_double(JobberOauthService) }
  #
  # before do
  #   allow(controller).to receive(:current_user).and_return(user)
  #   allow(controller).to receive(:current_organization).and_return(organization)
  #   allow(controller).to receive(:require_admin_role)
  #   allow(JobberOauthService).to receive(:new).and_return(oauth_service)
  # end
  #
  # describe "GET #authorize" do
  #   let(:state) { "test_state_123" }
  #   let(:authorization_url) { "https://api.getjobber.com/api/oauth/authorize?response_type=code&client_id=test&redirect_uri=http://test.com/callback&state=#{state}" }
  #
  #   before do
  #     allow(JobberOauthService).to receive(:generate_state).and_return(state)
  #     allow(oauth_service).to receive(:authorization_url).with(state: state).and_return(authorization_url)
  #   end
  #
  #   it "generates authorization URL and stores state in session" do
  #     get :authorize
  #
  #     expect(response).to have_http_status(:ok)
  #     expect(session[:jobber_oauth_state]).to eq(state)
  #     expect(session[:jobber_oauth_organization_id]).to eq(organization.id)
  #
  #     response_body = JSON.parse(response.body)
  #     expect(response_body["data"]["attributes"]["authorization_url"]).to eq(authorization_url)
  #     expect(response_body["data"]["attributes"]["state"]).to eq(state)
  #   end
  # end
  #
  # describe "GET #callback" do
  #   let(:authorization_code) { "test_auth_code" }
  #   let(:state) { "test_state_123" }
  #   let(:access_token) { "test_access_token" }
  #   let(:refresh_token) { "test_refresh_token" }
  #   let(:expires_at) { 1.hour.from_now }
  #   let(:integration) { double("Integration", id: 456, save: true, organization_id: 123, credential: nil, build_credential: credential) }
  #   let(:credential) { double("Credential", save: true, assign_attributes: true, errors: double(full_messages: [])) }
  #
  #   before do
  #     session[:jobber_oauth_state] = state
  #     session[:jobber_oauth_organization_id] = organization.id
  #
  #     allow(JobberOauthService).to receive(:validate_state).with(state, state).and_return(true)
  #     allow(Organization).to receive(:find_by).with(id: organization.id).and_return(organization)
  #     allow(Integration).to receive(:find_or_initialize_by).and_return(integration)
  #     allow(integration).to receive(:respond_to?).with(:sync_all).and_return(true)
  #     allow(integration).to receive(:sync_all)
  #     allow(integration).to receive(:errors).and_return(double(full_messages: []))
  #   end
  #
  #   context "with valid authorization code" do
  #     before do
  #       allow(oauth_service).to receive(:exchange_code_for_token).and_return({
  #         success: true,
  #         access_token: access_token,
  #         refresh_token: refresh_token,
  #         expires_at: expires_at
  #       })
  #       allow(oauth_service).to receive(:get_account_info).and_return({ account: { id: "123", name: "Test Account" } })
  #     end
  #
  #     it "exchanges code for token and creates integration" do
  #       get :callback, params: { code: authorization_code, state: state }
  #
  #       expect(response).to have_http_status(:ok)
  #       expect(session[:jobber_oauth_state]).to be_nil
  #       expect(session[:jobber_oauth_organization_id]).to be_nil
  #
  #       expect(credential).to have_received(:assign_attributes).with(
  #         bearer_token: access_token,
  #         refresh_token: refresh_token,
  #         expires_at: expires_at
  #       )
  #       expect(integration).to have_received(:save)
  #       expect(credential).to have_received(:save)
  #       expect(integration).to have_received(:sync_all).with(async: true)
  #     end
  #   end
  #
  #   context "with invalid state" do
  #     before do
  #       allow(JobberOauthService).to receive(:validate_state).with(state, "invalid_state").and_return(false)
  #     end
  #
  #     it "returns bad request error" do
  #       get :callback, params: { code: authorization_code, state: "invalid_state" }
  #
  #       expect(response).to have_http_status(:bad_request)
  #       response_body = JSON.parse(response.body)
  #       expect(response_body["errors"][0]["title"]).to eq("Invalid State")
  #     end
  #   end
  #
  #   context "without authorization code" do
  #     it "returns unauthorized error" do
  #       get :callback, params: { state: state }
  #
  #       expect(response).to have_http_status(:unauthorized)
  #       response_body = JSON.parse(response.body)
  #       expect(response_body["errors"][0]["title"]).to eq("Authorization Denied")
  #     end
  #   end
  #
  #   context "when token exchange fails" do
  #     before do
  #       allow(oauth_service).to receive(:exchange_code_for_token).and_return({
  #         success: false,
  #         error: "invalid_grant"
  #       })
  #     end
  #
  #     it "returns unprocessable entity error" do
  #       get :callback, params: { code: authorization_code, state: state }
  #
  #       expect(response).to have_http_status(:unprocessable_entity)
  #       response_body = JSON.parse(response.body)
  #       expect(response_body["errors"][0]["title"]).to eq("Token Exchange Failed")
  #     end
  #   end
  # end
  #
  # describe "POST #refresh_token" do
  #   let(:integration) { double("Integration", id: 456, credential: credential) }
  #   let(:credential) { double("Credential", refresh_token: "test_refresh_token", update!: true) }
  #   let(:new_access_token) { "new_access_token" }
  #   let(:new_refresh_token) { "new_refresh_token" }
  #   let(:new_expires_at) { 1.hour.from_now }
  #
  #   before do
  #     allow(organization).to receive(:integrations).and_return(double(find_by: integration))
  #   end
  #
  #   context "with valid refresh token" do
  #     before do
  #       allow(oauth_service).to receive(:refresh_access_token).and_return({
  #         success: true,
  #         access_token: new_access_token,
  #         refresh_token: new_refresh_token,
  #         expires_at: new_expires_at
  #       })
  #     end
  #
  #     it "refreshes the token successfully" do
  #       post :refresh_token
  #
  #       expect(response).to have_http_status(:ok)
  #       expect(credential).to have_received(:update!).with(
  #         bearer_token: new_access_token,
  #         refresh_token: new_refresh_token,
  #         expires_at: new_expires_at
  #       )
  #
  #       response_body = JSON.parse(response.body)
  #       expect(response_body["data"]["attributes"]["message"]).to eq("Token refreshed successfully")
  #     end
  #   end
  #
  #   context "without refresh token" do
  #     let(:credential) { double("Credential", refresh_token: nil) }
  #
  #     it "returns not found error" do
  #       post :refresh_token
  #
  #       expect(response).to have_http_status(:not_found)
  #       response_body = JSON.parse(response.body)
  #       expect(response_body["errors"][0]["title"]).to eq("No Refresh Token")
  #     end
  #   end
  #
  #   context "when token refresh fails" do
  #     before do
  #       allow(oauth_service).to receive(:refresh_access_token).and_return({
  #         success: false,
  #         error: "invalid_grant"
  #       })
  #     end
  #
  #     it "returns unprocessable entity error" do
  #       post :refresh_token
  #
  #       expect(response).to have_http_status(:unprocessable_entity)
  #       response_body = JSON.parse(response.body)
  #       expect(response_body["errors"][0]["title"]).to eq("Token Refresh Failed")
  #     end
  #   end
  # end
end
