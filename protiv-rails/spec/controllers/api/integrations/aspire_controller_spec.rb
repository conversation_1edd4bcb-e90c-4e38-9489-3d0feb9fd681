# frozen_string_literal: true

require "rails_helper"

# Simple test to verify the controller's syntax
RSpec.describe Api::Integrations::AspireController, type: :controller do
  describe "POST #create" do
    let(:client_id) { "test_client_id" }
    let(:secret_key) { "test_secret_key" }
    let(:organization) { double("Organization", id: 123) }
    let(:user) { double("User", organization: organization) }
    let(:integration) { double("Integration", id: 456, save: true, organization_id: 123) }

    before do
      # Skip the require_admin_role check
      allow(controller).to receive(:require_admin_role).and_return(true)
      # Mock the current_user method
      allow(controller).to receive(:current_user).and_return(user)
      # Mock the Integration.find_or_initialize_by method
      allow(Integration).to receive(:find_or_initialize_by).and_return(integration)
      # Mock the respond_to? method with a default value
      allow(integration).to receive(:respond_to?).and_return(false)
      # Mock specific respond_to? calls
      allow(integration).to receive(:respond_to?).with(:sync_all).and_return(true)
      allow(integration).to receive(:respond_to?).with(:status=).and_return(true)
      # Mock the sync_all method
      allow(integration).to receive(:sync_all)
      # Mock the status= method
      allow(integration).to receive(:status=)
    end

    it "creates a new integration" do
      # Set expectations on the integration object
      expect(integration).to receive(:client_id=).with(client_id)
      expect(integration).to receive(:secret=).with(secret_key)
      expect(integration).to receive(:save).and_return(true)

      # Make the request
      process :create, method: :post, params: { client_id: client_id, secret_key: secret_key }

      # Verify the response
      expect(response).to have_http_status(:ok)
      # Since we mocked respond_to?(:sync_all) to return true, we should get this message
      expect(JSON.parse(response.body)).to eq({ "message" => "Aspire credentials saved.", "organization_id" => 123 })
    end
  end
end
