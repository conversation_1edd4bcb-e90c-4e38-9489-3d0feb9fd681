# frozen_string_literal: true

require "simplecov"
SimpleCov.start 'rails' do
  enable_coverage :branch
end

# This file is copied to spec/ when you run 'rails generate rspec:install'
require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'
ENV["MANDRILL_API_KEY"] ||= "test_mandrill_api_key"
ENV["DOMAIN_NAME"] ||= "protiv.com"
ENV["MAILER_FROM"] ||= "<EMAIL>"
ENV["MAILER_FROM_NAME"] ||= "Protiv"
ENV["HOST_URL"] ||= "localhost:3000"
ENV["HOST_PROTOCOL"] ||= "http"
ENV["SEND_EMAILS"] ||= "false"
require_relative '../config/environment'
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?
# Uncomment the line below in case you have `--require rails_helper` in the `.rspec` file
# that will avoid rails generators crashing because migrations haven't been run yet
# return unless Rails.env.test?
require 'rspec/rails'
# Add additional requires below this line. Rails is not loaded until this point!

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.

Rails.root.glob('spec/support/**/*.rb').sort_by(&:to_s).each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
# Skip pending migrations check for now
# begin
#   ActiveRecord::Migration.maintain_test_schema!
# rescue ActiveRecord::PendingMigrationError => e
#   abort e.to_s.strip
# end

require "graphiti_spec_helpers/rspec"

module GraphitiSpecHelpers
  module Helpers
    def jsonapi_delete_with_payload(url, payload, headers: {})
      delete url, params: payload.to_json, headers: jsonapi_headers.merge(headers)
    end
  end
end

require "sidekiq/testing"
Sidekiq::Testing.fake!

# Sidekiq::Testing doesn't call default server middlewares,
# so they must be added here
Sidekiq::Testing.server_middleware do |chain|
  chain.add SyncTracking::SidekiqServerMiddleware
end

module SessionTokenFor
  def token_for(user, organization = nil)
    Session.new(user, organization).generate_token_for(:api)
  end

  def auth_for(...)
    TestAuth.new(...)
  end

  class TestAuth
    include SessionTokenFor

    def initialize(user_or_token, organization = nil)
      if user_or_token.is_a?(User)
        @user = user_or_token
        @organization = organization
      else
        @token = user_or_token
        raise ArgumentError, "cannot supply organization with a pre-generated token" if organization
      end
    end

    def token
      @token || token_for(user, organization)
    end

    def to_hash
      { "Authorization" => "Bearer #{token}" }
    end

    private

    attr_reader :user, :organization
  end
end

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_paths = [
    Rails.root.join('spec/fixtures')
  ]

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, type: :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://rspec.info/features/7-0/rspec-rails
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also:attr_writer :attr_names be filtered via:
  # config.filter_gems_from_backtrace("gem name")
  config.include FactoryBot::Syntax::Methods
  config.include SpecHelpers::Authentication
  config.include ActiveSupport::Testing::TimeHelpers
  config.include ActiveSupport::CurrentAttributes::TestHelper
  config.include GraphitiSpecHelpers::RSpec

  # Allow preloading for factory created models
  include AssociationsHelper

  [:controller, :view, :request].each do |type|
    config.include ::Rails::Controller::Testing::TestProcess, type: type
    config.include ::Rails::Controller::Testing::TemplateAssertions, type: type
    config.include ::Rails::Controller::Testing::Integration, type: type
    config.include SessionTokenFor, type: type
  end

  config.include SpecHelpers::Api::Integration, type: :request

  config.before(:each) do
    redis = MockRedis.new
    allow(Protiv::Application).to receive(:redis) { redis }
    Sidekiq::Worker.clear_all
  end

  config.before(:each, type: :request) do
    # Change debug to info or remove this block entirely if not needed
    Rails.logger = ActiveSupport::Logger.new(nil)
  end

  if Bullet.enable?
    config.before(:each) do
      Bullet.start_request
    end

    config.after(:each) do
      Bullet.perform_out_of_channel_notifications if Bullet.notification?
      Bullet.end_request
    end
  end
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

require Rails.root.join("spec/support/aspire_credentials_helper")
AspireCredentialsHelper.auth_token
require 'vcr'
require 'webmock'

auth_header_matcher = ->(a, b) do
  if b.headers['Authorization']&.first&.start_with?("Bearer")
    a.headers['Authorization']&.first&.start_with?("Bearer")
  else
    true
  end
end

VCR.configure do |config|
  config.cassette_library_dir = "spec/support/vcr_cassettes"
  config.configure_rspec_metadata!
  config.hook_into :webmock
  config.default_cassette_options = {
    match_requests_on: [:method, :uri, :body, auth_header_matcher],
    decode_compressed_response: true
  }

  config.default_cassette_options[:record] = :none

  # FIXME: this was a temporal solution, it should be removed
  # Ignore WorkTicketVisits requests
  config.ignore_request do |request|
    request.uri.to_s.include?('WorkTicketVisits')
  end

  # Register a WebMock stub for WorkTicketVisits requests
  WebMock.stub_request(:get, /WorkTicketVisits/).to_return do
    {
      status: 200,
      headers: { 'Content-Type' => 'application/json' },
      body: [].to_json
    }
  end

  if (record_opt = ENV["RECORD_VCR"])

    record_opt = record_opt == "once" ? :once : :all

    config.default_cassette_options[:record] = record_opt

    def vcr_response_json(interaction)
      JSON.parse(interaction.response.body) rescue {}
    end

    config.filter_sensitive_data(AspireCredentialsHelper::FAKE_ASPIRE_TOKEN) do |interaction|
      AspireCredentialsHelper.auth_token
    end

    config.filter_sensitive_data(AspireCredentialsHelper::FAKE_ASPIRE_REFRESH_TOKEN) do
      AspireCredentialsHelper.refresh_token
    end

    config.filter_sensitive_data("FAKE_CLIENT_ID") do
      AspireCredentialsHelper.send(:client_id)
    end

    config.filter_sensitive_data("FAKE_ASPIRE_SECRET") do
      AspireCredentialsHelper.send(:secret)
    end
  end

  # Ignore SendGrid API calls
  config.ignore_request do |request|
    URI(request.uri).host == 'api.sendgrid.com'
  end
end

ActionDispatch::RequestEncoder.register_encoder :jsonapi, response_parser: JSON.method(:parse)
