# frozen_string_literal: true

module SpecHelpers
  module Fixtures
    # Story:
    #
    # <PERSON> is a minimal, new un-onboarded user, not belonging to any
    # organization.
    #
    # [Story is developing...]
    class JohnAppleseed < FixtureSet
      def load!
        create_fixture(
          :user,
          :john_appleseed,
          name: '<PERSON>',
          email: '<EMAIL>',
          password: DEFAULT_PASSWORD,
          verified: true,
          organization: nil,
        )
      end
    end
  end
end
