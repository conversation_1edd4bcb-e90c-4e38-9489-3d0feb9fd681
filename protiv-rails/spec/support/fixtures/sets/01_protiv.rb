# frozen_string_literal: true

module SpecHelpers
  module Fixtures
    # Story:
    #
    # <PERSON> is a prolific entrepreneur and owner of multiple businesses in the
    # greater Greater New York area.
    #
    # Unlike the rest of his other companies, <PERSON><PERSON><PERSON> focuses on the technology
    # side of things and does not directly engage in the trades services
    # businesses.
    #
    # The intention is to keep this as a pretty barebones/blank slate
    # default organization for testing onboarding and anything else that
    # doesn't depending on having a lot of fixtures already set up, so think
    # twice before adding more things here.
    #
    # For a fully setup organization, use Lawn Mow Inc. instead.
    class Protiv < FixtureSet
      def load!
        setup_org
        setup_users
      end

      def setup_org
        @org = create_fixture(
          :organization,
          :protiv,
          name: 'Protiv',
        )

        # Intentionally not setting up an integration
      end

      def setup_users
        @david = create_fixture(
          :user,
          :david,
          name: '<PERSON>',
          email: '<EMAIL>',
          password: DEFAULT_PASSWORD,
          verified: true,
          organization: @org,
          role_type: 'admin',
        )
      end
    end
  end
end
