# frozen_string_literal: true

module SpecHelpers
  module Fixtures
    # Story:
    #
    # <PERSON>lumbing is an independent (not owned by <PERSON>) plumbing company
    # co-owned by the <PERSON> brothers.
    #
    # Naturally, all the fictional workers are named after <PERSON> characters.
    #
    # The intention is to have an organization with a skeleton amount of
    # resources set up (one branch, one job, etc), so we can validate that
    # things are scoped properly.
    #
    # As a bonus, this is also an org with multiple admins.
    class MarioPlumbing < FixtureSet
      def load!
        setup_org
        setup_users
        setup_branches
        setup_jobs
      end

      def setup_org
        @org = create_fixture(
          :organization,
          :mario_plumbing,
          name: '<PERSON> Plumbing',
        )

        create_fixture(
          :integration,
          :mario_plumbing,
          organization: @org,
        )
      end

      def setup_users
        create_fixture(
          :user,
          :mario,
          email: '<EMAIL>',
          password: DEFAULT_PASSWORD,
          verified: true,
          organization: @org,
          role_type: 'admin',
        )

        AddUserToOrganization.new(
          user: fixture(:user, :luigi),
          organization: @org,
          role_type: "admin"
        ).execute!
      end

      def setup_branches
        @mushroom_kingdom = create_fixture(
          :branch,
          :mushroom_kingdom,
          name: 'Mushroom Kingdom',
          organization: @org,
        )
      end

      def setup_jobs
        create_fixture(
          :job,
          :pipe_quest,
          name: '<PERSON><PERSON> Quest',
          organization: @org,
          branch: @mushroom_kingdom,
          status: :completed,
          manager: fixture(:identity, :luigi),
          created_date: 20.days.ago
        )
      end
    end
  end
end
