# frozen_string_literal: true

module Spec<PERSON>elpers
  module Fixtures
    # 2025-02-06 13:05:09 UTC
    # 2025-02-06 08:05:09 EST
    NOW = Time.at(1738847109).utc

    DEFAULT_PASSWORD = 'password'

    module Helpers
      def fixtures
        @fixtures ||= SpecHelpers::Fixtures::Loader.fixtures
      end

      def identify_fixture(type, name)
        fixtures.fetch(type).fetch(name)
      end

      def fixture(type, name, **params)
        id = identify_fixture(type, name)
        type.to_s.classify.constantize.find(id).tap do |record|
          unless params.empty?
            record.update!(params)
          end
        end
      end
    end

    class Loader
      def self.fixtures
        @fixtures ||= Hash.new { |h, k| h[k] = {} }
      end

      def self.load
        return if @loaded

        new.instance_exec do
          truncate!

          travel_to NOW do
            conn.transaction(requires_new: true) do
              conn.uncached do
                load!
              end
            end
          end
        end

        fixtures.tap do |f|
          f.each_value(&:freeze)
          f.freeze
        end

        @loaded = true
      end

      include ActiveSupport::Testing::TimeHelpers

      def load!
        each_fixture_set do |set|
          set.load!
        end
      end

      def truncate!
        each_table do |t|
          # conn.truncate does not support the CASCADE option
          conn.exec_query("TRUNCATE TABLE #{conn.quote_table_name(t)} CASCADE")
          conn.reset_pk_sequence!(t)
        end
      end

      def conn
        @conn ||= ActiveRecord::Base.connection
      end

      def each_table
        conn.tables.each do |t|
          next if t == 'ar_internal_metadata'
          next if t == 'schema_migrations'
          yield t
        end
      end

      def each_fixture_set
        Rails.root.glob('spec/support/fixtures/sets/*.rb').sort.each do |set|
          name = File.basename(set, '.rb').remove(/^[0-9]+_/, '')
          yield "SpecHelpers::Fixtures::#{name.classify}".constantize
        end
      end
    end

    class FixtureSet
      def self.load!
        new.load!
      end

      include FactoryBot::Syntax::Methods
      include Helpers

      def load!
        raise NotImplementedError
      end

      def create_fixture(type, name, factory: type, **args)
        if fixtures[type][name]
          raise "Duplicate fixture #{type}:#{name}"
        end

        create(factory, **args).tap do |record|
          fixtures[type][name] = record.id
        end
      end
    end
  end
end

RSpec.configure do |config|
  config.include SpecHelpers::Fixtures::Helpers

  config.before(:suite) do
    SpecHelpers::Fixtures::Loader.load
  end

  config.before(:each) do
    travel_to SpecHelpers::Fixtures::NOW
  end
end
