# frozen_string_literal: true

module SpecHelpers
  module Ember
    def parse_index_html(source)
      ParsedIndexHTML.new(source)
    end

    def parse_bootstrap_script(source)
      BootstrapScriptHelper.new(source)
    end

    class BootstrapScriptHelper
      def initialize(script_inner)
        @script_inner = script_inner || ""
      end

      def statements
        @script_inner.split(";")
      end

      def payload
        statements.find do |statement|
          statement.start_with?("window._protiv_bootstrap = ")
        end&.split("=", 2).last&.yield_self do |payload|
          JSON.parse(payload)
        end
      end

      def account_id
        payload.fetch("account.id")
      end

      def authentication_token
        payload.fetch("account.authentication")
      end
    end

    class ParsedIndexHTML
      def initialize(html)
        @parsed = Nokogiri.parse(html)
      end

      def script_node
        @script_node ||= @parsed.xpath("//script[@id='protiv-bootstrap']")[0]
      end

      def redirect_script_node
        @redirect_script_node ||= @parsed.xpath("//script[@id='redirect-to-ember']")[0]
      end

      def script_inner
        @script_inner ||= BootstrapScriptHelper.new(script_node&.children&.[](0)&.to_s)
      end

      delegate :statements, :payload, :account_id, :authentication_token, to: :script_inner
    end
  end
end
