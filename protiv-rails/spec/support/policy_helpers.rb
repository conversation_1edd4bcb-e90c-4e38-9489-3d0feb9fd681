# frozen_string_literal: true

module SpecHelpers
  module PolicyHelpers
    RSpec::Matchers.define :permit_action do |action|
      match do |policy|
        policy.public_send("#{action}?")
      end

      failure_message do |policy|
        "#{policy.class} does not permit #{action} for #{policy.user.inspect}"
      end

      failure_message_when_negated do |policy|
        "#{policy.class} does permit #{action} for #{policy.user.inspect}"
      end
    end
  end
end

RSpec.configure do |config|
  config.include SpecHelpers::PolicyHelpers, type: :policy
end
