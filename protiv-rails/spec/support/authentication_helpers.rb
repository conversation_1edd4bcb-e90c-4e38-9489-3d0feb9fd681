# frozen_string_literal: true

module SpecHelpers
  module Authentication
    DEFAULT_TEST_PASSWORD = "Secret1*3*5*"

    # This user's id will be set in the session
    def sign_in_as(user, organization = nil)
      @session_stubs = {}
      @session_stubs[:user_id] = user.id

      if organization
        @session_stubs[:organization_id] = organization.id
      end
    end

    def api_authenticate(user, organization = nil)
      session = Session.new(user, organization)
      token = session.generate_token_for(:api)
      @headers ||= {}
      @headers['Authorization'] = "Bearer #{token}"
    end

    module RequestHelpers
      def get(path, env: nil, **)
        super(path, env: patch_env(env), **)
      end

      def post(path, env: nil, **)
        super(path, env: patch_env(env), **)
      end

      def patch(path, env: nil, **)
        super(path, env: patch_env(env), **)
      end

      def put(path, env: nil, **)
        super(path, env: patch_env(env), **)
      end

      def delete(path, env: nil, **)
        super(path, env: patch_env(env), **)
      end

      private

      def patch_env(env)
        env ||= {}
        session = {}

        def session.id
          "1"
        end

        env['rack.session.options'] ||= {}
        env['rack.session'] ||= session

        if @session_stubs
          # data in `rack.session` is merged into the actiondispatch
          # session in the Session::CookieStore middleware.

          @session_stubs.each do |key, value|
            env['rack.session'][key] = value

            # after the initial request is made, the ActionDispatch session
            # exists so the stubs are no longer needed. They should be
            # cleared here to avoid overwriting any changes made to the session
            # during the request(s).
            @session_stubs = nil
          end
        end

        env
      end
    end

    def self.included(base)
      if base.ancestors.include?(Rails::Controller::Testing::Integration)
        base.prepend(RequestHelpers)
      end
    end
  end
end
