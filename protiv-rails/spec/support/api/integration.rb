# frozen_string_literal: true

module SpecHelpers
  module Api
    module Integration
      DEFAULT_OVERRIDES = {
        "request.headers.Authorization" => ->(header) {
          if (token = header[/\ABearer (.+)\z/, 1]) && (session = Session.find_by_token_for(:api, token))
            if (org = session.organization)
              "Bearer test-token:#{session.user.email}+#{org.name.parameterize}"
            else
              "Bearer test-token:#{session.user.email}"
            end
          else
            header
          end
        },
        "response.body.data.attributes.redirect" => ->(path, recorder) { recorder.url_for(path) },
        "response.body.__raw_error__" => :omit,
        "<EMAIL>.__details__" => :omit,
        "<EMAIL>" => ->(id, recorder) {
          recorder.id_for("error", id)
        }
      }

      def self.included(klass)
        klass.before(:each) do |example|
          if (scenario = example.metadata[:api_scenario])
            @api_recorder = Recorder.new(example.metadata)

            # Track mapped IDs to prevent duplicates
            mapped_ids = Set.new

            fixtures.each do |type, mapping|
              mapping.each do |(label, id)|
                next if mapped_ids.include?("#{type}-#{id}")

                mapped_id = label.to_s.dasherize
                @api_recorder.map_id(type, id, mapped_id)
                mapped_ids.add("#{type}-#{id}")

                # Special cases for account/user relationship
                if type == :user
                  @api_recorder.map_id(:account, id, mapped_id)
                  mapped_ids.add("account-#{id}")
                end
              end
            end
          end
        end

        klass.after(:each) do |example|
          if example.exception.nil?
            @api_recorder&.finalize
          end
        end
      end

      def api_fixture(fixture_name, record: nil, overrides: nil, **options)
        unless @api_recorder
          raise "Cannot record API fixture: rspec example is missing `api_scenario` tag"
        end

        yield

        if overrides.nil? && self.respond_to?(:api_fixture_overrides)
          overrides = DEFAULT_OVERRIDES.merge(self.api_fixture_overrides)
        else
          overrides = DEFAULT_OVERRIDES
        end

        if record.nil?
          record = false

          if ENV['RECORD'] == 'true' || ENV['RECORD'] == 'all'
            record = true
          elsif ENV['RECORD'] == 'new'
            record = :new
          else
            record = false
          end
        end

        if ENV['CI'] == 'true' && record != false
          raise "Cannot record API fixture in CI environment, did you leave behind a `record: true`?\n" +
            "Scenario: #{@api_recorder.scenario_name}\n" +
            "Fixture: #{fixture_name}"
        end

        if record == :new
          @api_recorder&.record_or_verify(fixture_name, request, response, overrides: overrides, **options)
        elsif record
          @api_recorder&.record(fixture_name, request, response, overrides: overrides, **options)
        else
          @api_recorder&.verify(fixture_name, request, response, overrides: overrides, **options)
        end
      end

      def json_response
        JSON.parse(response.body)
      end
    end
  end
end

RSpec.configure do |config|
  config.include SpecHelpers::Api::Integration, type: :request
end
