# frozen_string_literal: true

# caches a real auth token for the purposes of VCR recording.
require "json"

class AspireCredentialsHelper
  CREDENTIALS_FILE = File.expand_path("../aspire_credentials.json", __FILE__)
  FAKE_ASPIRE_TOKEN = "FAKE_ASPIRE_TOKEN"
  FAKE_ASPIRE_REFRESH_TOKEN = "FAKE_ASPIRE_REFRESH_TOKEN"
  FAKE_CLIENT_ID = "FAKE_CLIENT_ID"
  FAKE_ASPIRE_SECRET = "FAKE_ASPIRE_SECRET"

  class << self
    if ENV['RECORD_VCR']
      def auth_token
        @auth_token ||= ensure_fresh_access_token
      end

      def refresh_token
        @refresh_token ||= begin
                             auth_token
                             cached_credentials.fetch(:refresh_token)
                           end
      end

      def client_id
        cached_credentials.fetch(:client_id)
      end

      def secret
        cached_credentials.fetch(:secret)
      end
    else
      def auth_token
        FAKE_ASPIRE_TOKEN
      end

      def refresh_token
        FAKE_ASPIRE_REFRESH_TOKEN
      end

      def client_id
        FAKE_CLIENT_ID
      end

      def secret
        FAKE_ASPIRE_SECRET
      end
    end

    private

    def cached_credentials
      @cached_credentials ||=
        if File.exist?(CREDENTIALS_FILE)
          JSON.parse(File.read(CREDENTIALS_FILE)).deep_symbolize_keys.tap do |original|
            @original_credentials = original.deep_dup
          end
        else
          @original_credentials = {}
          {}
        end
    end

    def persist_cached_credentials!
      return if @cached_credentials == @original_credentials

      File.open(CREDENTIALS_FILE, "w") do |file|
        file << JSON.pretty_generate(@cached_credentials)
      end
    end

    def ensure_fresh_access_token
      token_from_cached || token_from_refresh_request || token_from_auth_request
    end

    def allowing_net_connect
      return yield unless defined?(WebMock)

      connect_allowed_was = WebMock.net_connect_allowed?

      WebMock.allow_net_connect!

      yield
    ensure
      unless connect_allowed_was
        WebMock.disable_net_connect!
      end
    end

    def token_from_cached
      if (token = cached_credentials[:auth_token])
        token if token_expiry(token) > Time.zone.now - 10.minutes
      end
    end

    def token_from_refresh_request
      if (refresh_token = refresh_token_from_cached)
        client = AspireClient::Client.new

        result = allowing_net_connect do
          client.authorization.refresh_token(refresh_token: refresh_token)
        end

        cached_credentials[:auth_token] = result.token
        cached_credentials[:refresh_token] = result.refresh_token
        persist_cached_credentials!

        result.token
      end
    end

    def token_from_auth_request
      client = AspireClient::Client.new

      result = allowing_net_connect do
        client.authorization.post(client_id: client_id, secret: secret)
      end

      cached_credentials[:auth_token] = result.token
      cached_credentials[:refresh_token] = result.refresh_token
      persist_cached_credentials!

      result.token
    end

    def refresh_token_from_cached
      if (token = cached_credentials[:refresh_token])
        token if token_expiry(token) > Time.zone.now - 10.minutes
      end
    end

    def decode_token(jwt)
      JWT.decode(jwt, nil, false).tap do |decoded|
        if decoded.is_a?(Array)
          return decoded.first
        end
      end
    end

    def token_expiry(jwt)
      Time.zone.at(decode_token(jwt).fetch("exp"))
    end
  end
end
