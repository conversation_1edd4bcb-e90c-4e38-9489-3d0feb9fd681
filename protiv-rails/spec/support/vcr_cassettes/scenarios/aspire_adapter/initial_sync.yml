---
http_interactions:
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20in%20(109,110,112,114,115,116,118,119,120,121,122,123,124,125,126,127,128,130,131,132,133,134,135,136,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,137,108,129,111,117,113,107)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 14:36:45 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T143645Z-17db9b9cb9d4t4flhC1DTTfqcw00000012s0000000001028
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketTimeID":26,"WorkTicketID":107,"WorkTicketNumber":157,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":150.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.96Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.96Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":27,"WorkTicketID":107,"WorkTicketNumber":157,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":110.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":28,"WorkTicketID":107,"WorkTicketNumber":157,"ContactID":13,"ContactName":"Claudia
        Alas","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":null,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":29,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":150.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":30,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":110.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":31,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":13,"ContactName":"Claudia
        Alas","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.98Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.98Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":null,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":32,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":14,"ContactName":"Joe
        Cruz","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.98Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.98Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":null,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":18,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":55.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.967Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.64Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":19,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":45.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.99Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.64Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":20,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":37.5,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.99Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":15.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":21,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":75.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.99Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":22,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":15.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.027Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":23,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":11.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.03Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":24,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":9.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.03Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.65Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":25,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":7.5,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.03Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.647Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":15.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20in%20(8,2,10)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:52:45 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185244Z-1578d7644f6pgxrbhC1CH1t19s0000000gd000000000ge4p
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":161,"OpportunityServiceID":11,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2025-03-18T00:57:07.89Z","AnticStartDate":"2024-12-20T00:00:00Z","ScheduledStartDate":"2024-12-20T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":200.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":162,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":8,"OpportunityNumber":9.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-03-18T00:57:07.89Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":1,"OpportunityServiceID":2,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2024-12-13T13:26:43.99Z","AnticStartDate":"2024-12-20T00:00:00Z","ScheduledStartDate":"2025-03-18T00:00:00Z","CompleteDate":null,"ApprovedDate":"2025-01-21T00:00:00Z","WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":25,"CrewLeaderName":"John
        Franco","HoursEst":422.0,"HourCostEst":7174.0,"MaterialCostEst":130.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":27560.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":17,"ApprovedUserName":"Lawrence
        Gebhardt","WorkTicketNumber":2,"InvoiceID":null,"HoursAct":130.0,"WarrantyHoursAct":0.0,"OTHoursAct":0.0,"LaborCostAct":2995.0,"MaterialCostAct":0.0,"EquipmentCostAct":0.0,"SubCostAct":0.0,"OtherCostAct":0.0,"TotalCostAct":2995.0,"EarnedRevenue":11300.96,"RealizeRateRevenue":86.93,"OpportunityID":2,"OpportunityNumber":3.0,"EstRealizeRateRevenue":65.0,"DistributedHours":0.0,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":16,"LastModifiedByUserName":"Armando Meza","LastModifiedDateTime":"2025-04-02T20:38:59.97Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":844.00,"HoursUnscheduled":-422.00,"WorkTicketRevenues":[{"WorkTicketRevenueID":9,"RevenueMonth":"2024-12-01T00:00:00Z","RevenueAmount":11300.96,"CreatedDateTime":"2025-02-18T15:33:04.953Z","EditedByUserID":null,"EditedByUserName":null,"EditedDateTime":null,"AccountingPeriodID":null,"AccountingPeriodName":null,"AccountingPeriodYear":null}],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Contacts?$filter=ContactTypeName%20eq%20%27Sub%27%20or%20ContactTypeName%20eq%20%27Employee%27&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:22 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185522Z-17c69d65b68r7gm7hC1PDX9aa40000000ewg00000000221y
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ContactID":4,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":3,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T12:58:05.397Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":2,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T12:58:05.197Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Michael","LastName":"Fortinberry","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-04-09T12:58:05.433Z","CreatedByUserID":3,"CreatedByUserName":"Aspire
        Admin","EmployeeNumber":null,"Website":null,"EmployeePin":"mfortinberry","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":4,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":3,"LastModifiedByUserName":"Aspire
        Admin","ModifiedDate":"2024-04-09T12:59:52.927Z","ContactTags":[],"ContactSignature":null},{"ContactID":5,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":5,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T13:00:29.747Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":4,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T13:00:29.743Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"David","LastName":"Franco","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-04-09T13:00:29.733Z","CreatedByUserID":3,"CreatedByUserName":"Aspire
        Admin","EmployeeNumber":null,"Website":null,"EmployeePin":"dfranco","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":5,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2025-03-19T17:59:20.64Z","ContactTags":[],"ContactSignature":null},{"ContactID":7,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":7,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-05-13T11:39:07.793Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":6,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-05-13T11:39:07.783Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":"Mr.","FirstName":"Umesh","LastName":"Nikam","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-05-13T11:39:07.79Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"8888","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":20,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2025-02-21T14:50:58.2Z","ContactTags":[],"ContactSignature":null},{"ContactID":9,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":10,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:48:40.323Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":9,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:48:40.313Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"George","LastName":"Korsnick","ProspectRating":null,"ProspectRatingName":null,"Title":"CTO","Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-01T18:48:40.323Z","CreatedByUserID":4,"CreatedByUserName":"Michael
        Fortinberry","EmployeeNumber":null,"Website":null,"EmployeePin":"gkorsnick","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":8,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2024-12-13T13:48:45.29Z","ContactTags":[],"ContactSignature":null},{"ContactID":10,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":12,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:50:07.14Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":11,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:50:07.137Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Zach","LastName":"Kemp","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-01T18:50:07.137Z","CreatedByUserID":4,"CreatedByUserName":"Michael
        Fortinberry","EmployeeNumber":null,"Website":null,"EmployeePin":"zkemp","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":9,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2025-02-26T15:49:44.377Z","ContactTags":[],"ContactSignature":null},{"ContactID":12,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":14,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-11T18:56:31.187Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":13,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-11T18:56:31.177Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Arturo","LastName":"Archila","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-11T18:56:31.233Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"1223","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":11,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-11T18:57:51.123Z","ContactTags":[],"ContactSignature":null},{"ContactID":13,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":16,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:06:58.783Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":15,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:06:58.777Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Claudia","LastName":"Alas","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-20T21:06:58.83Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"3434","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":13,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-20T21:12:23.797Z","ContactTags":[],"ContactSignature":null},{"ContactID":14,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":18,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:07:28.727Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":17,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:07:28.723Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Joe","LastName":"Cruz","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-20T21:07:28.727Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"7526","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":12,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2025-03-17T19:01:07.197Z","ContactTags":[],"ContactSignature":null},{"ContactID":15,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":20,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:08:09.46Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":19,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:08:09.457Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Itzel","LastName":"Zeledon","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-20T21:08:09.46Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"9999","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":14,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-20T21:11:23.97Z","ContactTags":[],"ContactSignature":null},{"ContactID":17,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":22,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-22T13:44:30.457Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":21,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-22T13:44:30.41Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Armando","LastName":"Meza","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-22T13:44:30.543Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"6666","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":16,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-22T13:45:23.297Z","ContactTags":[],"ContactSignature":null},{"ContactID":18,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":25,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-12-12T13:47:37.913Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":24,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-12-12T13:47:37.907Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":"Mr.","FirstName":"Lawrence","LastName":"Gebhardt","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-12-12T13:47:37.96Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"8989","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":17,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2024-12-16T19:36:47.567Z","ContactTags":[],"ContactSignature":null},{"ContactID":23,"CompanyID":null,"CompanyName":null,"ContactTypeID":10,"ContactTypeName":"Sub","OfficeAddress":{"AddressID":34,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2025-02-21T14:54:08.253Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":33,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2025-02-21T14:54:08.253Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Test","LastName":"Sub","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2025-02-21T14:54:08.247Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":null,"AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":null,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":null,"LastModifiedByUserName":null,"ModifiedDate":null,"ContactTags":[],"ContactSignature":null},{"ContactID":25,"CompanyID":1,"CompanyName":"Town
        of Amherst","ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":38,"AddressLine1":"115
        Morris St","AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":"07302","CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2025-03-18T00:49:44.107Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":37,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2025-03-18T00:49:44.097Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"John","LastName":"Franco","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":"**********","OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2025-03-18T00:49:44.16Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"5555","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":21,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2025-03-19T17:47:06.223Z","ContactTags":[],"ContactSignature":null},{"ContactID":26,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":42,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2025-03-21T15:00:30.79Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":41,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2025-03-21T15:00:30.773Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Michael","LastName":"Enrique","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2025-03-21T15:00:30.887Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"7777","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":22,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2025-03-21T15:01:09.597Z","ContactTags":[],"ContactSignature":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Branches?$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:23 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185523Z-17c69d65b6864fc9hC1PDXbc7w0000000f2g000000005bp5
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"BranchID":2,"BranchName":"Main","Active":true,"InternalPropertyID":null,"InternalPropertyName":null,"CatalogPriceListName":null,"BranchAddressID":null,"BranchAddressLine1":null,"BranchAddressLine2":null,"BranchAddressCity":null,"BranchAddressStateProvinceCode":null,"BranchAddressZipCode":null,"BranchPhone":null,"BranchFax":null,"BranchCode":null,"BranchManagerContactID":null,"BranchManagerContactName":null,"LegalName":null,"TimeZone":null,"InvoiceNumberPrefix":null,"ReceiptNumberPrefix":null,"OpportunityNumberPrefix":null,"RegionID":null,"RegionName":null,"BillingEmailFromAccountOwner":false,"BillingEmailFromUserName":null,"BillingEmailFromEmail":null,"BillingEmailSubject":null,"BillingEmailBody":null,"InvoiceOnCompletionDescription":null,"BranchWebSite":null,"CatalogPriceListID":null,"BillingEmailCC":null},{"BranchID":3,"BranchName":"David
        Town","Active":true,"InternalPropertyID":1,"InternalPropertyName":"Test Propery","CatalogPriceListName":null,"BranchAddressID":null,"BranchAddressLine1":null,"BranchAddressLine2":null,"BranchAddressCity":null,"BranchAddressStateProvinceCode":null,"BranchAddressZipCode":null,"BranchPhone":null,"BranchFax":null,"BranchCode":null,"BranchManagerContactID":null,"BranchManagerContactName":null,"LegalName":null,"TimeZone":null,"InvoiceNumberPrefix":null,"ReceiptNumberPrefix":null,"OpportunityNumberPrefix":null,"RegionID":null,"RegionName":null,"BillingEmailFromAccountOwner":true,"BillingEmailFromUserName":null,"BillingEmailFromEmail":null,"BillingEmailSubject":null,"BillingEmailBody":null,"InvoiceOnCompletionDescription":null,"BranchWebSite":null,"CatalogPriceListID":null,"BillingEmailCC":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/CatalogItems?$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:24 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185524Z-17c69d65b68dlnnfhC1PDXqfpw00000007pg00000000fk14
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"CatalogItemID":1,"CatalogItemCategoryID":1,"ItemType":"Material","ItemName":"Top
        Soil","ItemAlternateName":"Top Soil","ItemDescription":null,"ItemCode":"","ItemCost":29.00000000,"PurchaseUnitTypeID":3,"PurchaseUnitTypeName":"cuyd","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.28Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":29.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":2,"CatalogItemCategoryID":2,"ItemType":"Material","ItemName":"Roundup","ItemAlternateName":"Roundup","ItemDescription":null,"ItemCode":"","ItemCost":0.97695313,"PurchaseUnitTypeID":4,"PurchaseUnitTypeName":"1
        gal","AllocationUnitTypeID":5,"AllocationUnitTypeName":"oz","UnitTypeAllocationConversion":128.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":125.050,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":3,"CatalogItemCategoryID":3,"ItemType":"Material","ItemName":"Irrigation
        Misc Fittings","ItemAlternateName":"Irrigation Misc Fittings","ItemDescription":null,"ItemCode":"","ItemCost":1.00000000,"PurchaseUnitTypeID":2,"PurchaseUnitTypeName":"Dollars","AllocationUnitTypeID":2,"AllocationUnitTypeName":"Dollars","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":1.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":4,"CatalogItemCategoryID":4,"ItemType":"Labor","ItemName":"Labor
        - Maint","ItemAlternateName":"Labor - Maint","ItemDescription":null,"ItemCode":"","ItemCost":15.00000000,"PurchaseUnitTypeID":6,"PurchaseUnitTypeName":"Hr","AllocationUnitTypeID":6,"AllocationUnitTypeName":"Hr","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":15.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":5,"CatalogItemCategoryID":4,"ItemType":"Labor","ItemName":"Labor
        - Enhancement","ItemAlternateName":"Labor - Enhancement","ItemDescription":null,"ItemCode":"","ItemCost":17.00000000,"PurchaseUnitTypeID":6,"PurchaseUnitTypeName":"Hr","AllocationUnitTypeID":6,"AllocationUnitTypeName":"Hr","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":17.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":6,"CatalogItemCategoryID":5,"ItemType":"Material","ItemName":"Hardwood
        Mulch","ItemAlternateName":"Hardwood Mulch","ItemDescription":null,"ItemCode":"","ItemCost":25.00000000,"PurchaseUnitTypeID":3,"PurchaseUnitTypeName":"cuyd","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":25.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":7,"CatalogItemCategoryID":6,"ItemType":"Equipment","ItemName":"Chipper
        Rental","ItemAlternateName":"Chipper Rental","ItemDescription":null,"ItemCode":"","ItemCost":138.00000000,"PurchaseUnitTypeID":7,"PurchaseUnitTypeName":"Day","AllocationUnitTypeID":7,"AllocationUnitTypeName":"Day","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":138.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":8,"CatalogItemCategoryID":7,"ItemType":"Material","ItemName":"Winter
        Gem Boxwood","ItemAlternateName":"Buxus micro. j. ''Winter Gem''","ItemDescription":null,"ItemCode":"","ItemCost":4.95000000,"PurchaseUnitTypeID":4,"PurchaseUnitTypeName":"1
        gal","AllocationUnitTypeID":4,"AllocationUnitTypeName":"1 gal","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":4.950,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":9,"CatalogItemCategoryID":7,"ItemType":"Material","ItemName":"Winter
        Gem Boxwood","ItemAlternateName":"Buxus micro. j. ''Winter Gem''","ItemDescription":null,"ItemCode":"","ItemCost":11.75000000,"PurchaseUnitTypeID":8,"PurchaseUnitTypeName":"2
        gal","AllocationUnitTypeID":8,"AllocationUnitTypeName":"2 gal","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":11.750,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":10,"CatalogItemCategoryID":8,"ItemType":"Material","ItemName":"Bag
        Salt","ItemAlternateName":"Bag Salt","ItemDescription":null,"ItemCode":null,"ItemCost":0.09000000,"PurchaseUnitTypeID":9,"PurchaseUnitTypeName":"50
        lb bag","AllocationUnitTypeID":10,"AllocationUnitTypeName":"lb","UnitTypeAllocationConversion":50.00,"EPANumber":null,"EPAName":null,"Inventory":true,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2025-02-06T18:17:36.577Z","LastUpdatedByUserID":5,"LastUpdatedByUserName":"David
        Franco","PurchaseUnitCost":4.500,"ForceUnitPricing":false,"AllocateFromMobile":true,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":11,"CatalogItemCategoryID":8,"ItemType":"Material","ItemName":"Bulk
        Salt","ItemAlternateName":"Bulk Salt","ItemDescription":null,"ItemCode":"","ItemCost":0.04300000,"PurchaseUnitTypeID":11,"PurchaseUnitTypeName":"ton","AllocationUnitTypeID":10,"AllocationUnitTypeName":"lb","UnitTypeAllocationConversion":2000.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":86.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":12,"CatalogItemCategoryID":9,"ItemType":"Sub","ItemName":"Subcontractor","ItemAlternateName":"Subcontractor","ItemDescription":null,"ItemCode":"","ItemCost":1.00000000,"PurchaseUnitTypeID":2,"PurchaseUnitTypeName":"Dollars","AllocationUnitTypeID":2,"AllocationUnitTypeName":"Dollars","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":1.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":13,"CatalogItemCategoryID":10,"ItemType":"Material","ItemName":"Autumn
        Blaze Maple","ItemAlternateName":"Autumn Blaze Maple","ItemDescription":null,"ItemCode":"","ItemCost":130.00000000,"PurchaseUnitTypeID":12,"PurchaseUnitTypeName":"2\"
        B&B","AllocationUnitTypeID":12,"AllocationUnitTypeName":"2\" B&B","UnitTypeAllocationConversion":1.00,"EPANumber":"","EPAName":"","Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2024-04-04T22:39:27.283Z","LastUpdatedByUserID":null,"LastUpdatedByUserName":null,"PurchaseUnitCost":130.000,"ForceUnitPricing":null,"AllocateFromMobile":null,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null},{"CatalogItemID":14,"CatalogItemCategoryID":4,"ItemType":"Other","ItemName":"Bonuses","ItemAlternateName":"Protiv
        Bonuses","ItemDescription":null,"ItemCode":null,"ItemCost":0.00000000,"PurchaseUnitTypeID":4,"PurchaseUnitTypeName":"1
        gal","AllocationUnitTypeID":4,"AllocationUnitTypeName":"1 gal","UnitTypeAllocationConversion":1.00,"EPANumber":null,"EPAName":null,"Inventory":false,"AvailableToBid":true,"Active":true,"LastUpdatedDateTime":"2025-04-11T15:25:22.19Z","LastUpdatedByUserID":5,"LastUpdatedByUserName":"David
        Franco","PurchaseUnitCost":0.000,"ForceUnitPricing":false,"AllocateFromMobile":false,"CatalogId":null,"CatalogName":null,"TakeoffItemID":null,"CatalogItemBranches":[],"AllBranches":true,"MaterialBarcode1":null,"MaterialBarcode2":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Properties?$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:24 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185524Z-17c69d65b68kch8rhC1PDXy1b40000000a8g000000001rgs
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"PropertyID":1,"PropertyStatusID":null,"PropertyStatusName":null,"BranchID":2,"BranchName":"Main","BranchCode":null,"AccountOwnerContactID":null,"AccountOwnerContactName":null,"ProductionManagerContactID":null,"ProductionManagerContactName":null,"PropertyAddressID":8,"CountyID":null,"PropertyAddressLine1":null,"PropertyAddressLine2":null,"PropertyAddressCity":null,"PropertyAddressStateProvinceCode":null,"PropertyAddressZipCode":null,"LocalityID":null,"LocalityName":null,"IndustryID":null,"IndustryName":null,"LeadSourceID":null,"LeadSourceName":null,"TaxJurisdictionID":null,"TaxJurisdictionName":null,"PropertyGroupID":null,"ActiveOpportunityID":null,"CompetitorID":null,"PropertyGroupName":null,"PropertyName":"Test
        Propery","PropertyNameAbr":"TP","SequenceNumber":null,"ProductionNote":null,"Active":true,"EmailInvoice":false,"Budget":null,"Note":null,"CreatedByUserID":5,"CreatedByUserName":"10","CreatedDate":"2024-05-13T12:11:05.83Z","GEOPerimeter":0.0,"GEOLocationLatitude":null,"GEOLocationLongitude":null,"PaymentTermsID":null,"PaymentTermsName":null,"SeparateInvoices":null,"Website":null,"ModifiedByUserID":5,"ModifiedByUserName":"10","ModifiedDate":"2025-02-01T20:17:23.957Z","DragDropGeoLocation":null,"GPSUpdated":true,"GPSGeofenceID":null,"SnowNote":null,"EarliestOpportunityWonDate":null,"PropertyContacts":[],"PropertyTags":[],"PropertyTakeoffItems":[],"CollectionNotes":null,"IntegrationID":null,"PropertyTypeID":null,"PropertyType":null,"PropertyTypeIntegrationCode":null},{"PropertyID":2,"PropertyStatusID":null,"PropertyStatusName":null,"BranchID":3,"BranchName":"David
        Town","BranchCode":null,"AccountOwnerContactID":5,"AccountOwnerContactName":"David
        Franco","ProductionManagerContactID":null,"ProductionManagerContactName":null,"PropertyAddressID":23,"CountyID":null,"PropertyAddressLine1":"115
        Morris St","PropertyAddressLine2":"1301","PropertyAddressCity":"Jersey City","PropertyAddressStateProvinceCode":"NJ","PropertyAddressZipCode":"07302","LocalityID":null,"LocalityName":null,"IndustryID":null,"IndustryName":null,"LeadSourceID":null,"LeadSourceName":null,"TaxJurisdictionID":null,"TaxJurisdictionName":null,"PropertyGroupID":null,"ActiveOpportunityID":null,"CompetitorID":null,"PropertyGroupName":null,"PropertyName":"David''s
        House","PropertyNameAbr":null,"SequenceNumber":null,"ProductionNote":null,"Active":true,"EmailInvoice":false,"Budget":0.00,"Note":null,"CreatedByUserID":5,"CreatedByUserName":"10","CreatedDate":"2024-12-08T17:11:40.4Z","GEOPerimeter":0.0,"GEOLocationLatitude":40.7138,"GEOLocationLongitude":-74.0389,"PaymentTermsID":null,"PaymentTermsName":null,"SeparateInvoices":false,"Website":"","ModifiedByUserID":22,"ModifiedByUserName":"10","ModifiedDate":"2025-04-22T02:32:09.437Z","DragDropGeoLocation":null,"GPSUpdated":true,"GPSGeofenceID":null,"SnowNote":null,"EarliestOpportunityWonDate":"2024-12-13T13:26:43.707Z","PropertyContacts":[],"PropertyTags":[],"PropertyTakeoffItems":[],"CollectionNotes":null,"IntegrationID":null,"PropertyTypeID":null,"PropertyType":null,"PropertyTypeIntegrationCode":null},{"PropertyID":3,"PropertyStatusID":5,"PropertyStatusName":"Customer","BranchID":2,"BranchName":"Main","BranchCode":null,"AccountOwnerContactID":3,"AccountOwnerContactName":"Aspire
        Admin","ProductionManagerContactID":null,"ProductionManagerContactName":null,"PropertyAddressID":26,"CountyID":null,"PropertyAddressLine1":null,"PropertyAddressLine2":null,"PropertyAddressCity":null,"PropertyAddressStateProvinceCode":null,"PropertyAddressZipCode":null,"LocalityID":null,"LocalityName":null,"IndustryID":null,"IndustryName":null,"LeadSourceID":null,"LeadSourceName":null,"TaxJurisdictionID":null,"TaxJurisdictionName":null,"PropertyGroupID":null,"ActiveOpportunityID":null,"CompetitorID":null,"PropertyGroupName":null,"PropertyName":"Amherst
        Rec Ballfield","PropertyNameAbr":null,"SequenceNumber":null,"ProductionNote":null,"Active":true,"EmailInvoice":false,"Budget":null,"Note":null,"CreatedByUserID":17,"CreatedByUserName":"34","CreatedDate":"2025-01-16T18:45:53.833Z","GEOPerimeter":0.0,"GEOLocationLatitude":null,"GEOLocationLongitude":null,"PaymentTermsID":null,"PaymentTermsName":null,"SeparateInvoices":null,"Website":null,"ModifiedByUserID":17,"ModifiedByUserName":"34","ModifiedDate":"2025-01-16T19:18:50.953Z","DragDropGeoLocation":null,"GPSUpdated":true,"GPSGeofenceID":null,"SnowNote":null,"EarliestOpportunityWonDate":null,"PropertyContacts":[{"PropertyID":3,"ContactID":21,"ContactName":"Joe
        Major","PrimaryContact":true,"BillingContact":false,"EmailInvoiceContact":false,"EmailNotificationsContact":false,"CompanyID":1,"CompanyName":"Town
        of Amherst","SMSNotificationsContact":false,"CreatedByUserID":17,"CreatedByUserName":"<EMAIL>","CreatedDateTime":"2025-01-16T18:57:03.75Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null}],"PropertyTags":[],"PropertyTakeoffItems":[],"CollectionNotes":null,"IntegrationID":null,"PropertyTypeID":null,"PropertyType":null,"PropertyTypeIntegrationCode":null},{"PropertyID":4,"PropertyStatusID":5,"PropertyStatusName":"Customer","BranchID":2,"BranchName":"Main","BranchCode":null,"AccountOwnerContactID":3,"AccountOwnerContactName":"Aspire
        Admin","ProductionManagerContactID":null,"ProductionManagerContactName":null,"PropertyAddressID":27,"CountyID":null,"PropertyAddressLine1":null,"PropertyAddressLine2":null,"PropertyAddressCity":null,"PropertyAddressStateProvinceCode":null,"PropertyAddressZipCode":null,"LocalityID":null,"LocalityName":null,"IndustryID":null,"IndustryName":null,"LeadSourceID":null,"LeadSourceName":null,"TaxJurisdictionID":null,"TaxJurisdictionName":null,"PropertyGroupID":null,"ActiveOpportunityID":null,"CompetitorID":null,"PropertyGroupName":null,"PropertyName":"Amherst
        Cemetary","PropertyNameAbr":null,"SequenceNumber":null,"ProductionNote":null,"Active":true,"EmailInvoice":false,"Budget":null,"Note":null,"CreatedByUserID":17,"CreatedByUserName":"34","CreatedDate":"2025-01-16T18:46:47.203Z","GEOPerimeter":0.0,"GEOLocationLatitude":null,"GEOLocationLongitude":null,"PaymentTermsID":null,"PaymentTermsName":null,"SeparateInvoices":null,"Website":null,"ModifiedByUserID":17,"ModifiedByUserName":"34","ModifiedDate":"2025-01-16T19:18:51.943Z","DragDropGeoLocation":null,"GPSUpdated":true,"GPSGeofenceID":null,"SnowNote":null,"EarliestOpportunityWonDate":null,"PropertyContacts":[{"PropertyID":4,"ContactID":21,"ContactName":"Joe
        Major","PrimaryContact":true,"BillingContact":false,"EmailInvoiceContact":false,"EmailNotificationsContact":false,"CompanyID":1,"CompanyName":"Town
        of Amherst","SMSNotificationsContact":false,"CreatedByUserID":17,"CreatedByUserName":"<EMAIL>","CreatedDateTime":"2025-01-16T18:56:45.043Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null}],"PropertyTags":[],"PropertyTakeoffItems":[],"CollectionNotes":null,"IntegrationID":null,"PropertyTypeID":null,"PropertyType":null,"PropertyTypeIntegrationCode":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Services?$filter=Active%20eq%20true&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:25 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185525Z-17c69d65b68j6fj9hC1PDXprz80000000e3000000000fyke
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ServiceID":1,"ServiceTypeID":1,"ServiceTypeName":"Mulching","ServiceName":"EN
        - Mulch","ServiceNameAbr":"Mulch","DisplayName":"Mulch","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":2,"ServiceTypeID":2,"ServiceTypeName":"Softscapes
        ","ServiceName":"EN - Plant Installation","ServiceNameAbr":"Plant Install","DisplayName":"Plant
        Installation","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":3,"ServiceTypeID":3,"ServiceTypeName":"Subcontractor","ServiceName":"EN
        - Subcontractor","ServiceNameAbr":"Subcontractor","DisplayName":"Subcontractor","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":4,"ServiceTypeID":4,"ServiceTypeName":"Mulching","ServiceName":"MT
        - Mulch Install ","ServiceNameAbr":"Mulch Install ","DisplayName":"Mulch Install
        ","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":true,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":5,"ServiceTypeID":5,"ServiceTypeName":"Maintenance","ServiceName":"MT
        - Spring Clean up","ServiceNameAbr":"Spring Clean up","DisplayName":"Spring
        Clean up","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":true,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":6,"ServiceTypeID":6,"ServiceTypeName":"Indirect","ServiceName":"OH
        - Holiday","ServiceNameAbr":"Holiday","DisplayName":"Holiday","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":7,"ServiceTypeID":6,"ServiceTypeName":"Indirect","ServiceName":"OH
        - Meetings/Admin","ServiceNameAbr":"Meetings/Admin","DisplayName":"Meetings/Admin","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":8,"ServiceTypeID":6,"ServiceTypeName":"Indirect","ServiceName":"OH
        - PTO","ServiceNameAbr":"PTO","DisplayName":"PTO","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":9,"ServiceTypeID":6,"ServiceTypeName":"Indirect","ServiceName":"OH
        - Shop Time","ServiceNameAbr":"Shop Time","DisplayName":"Shop Time","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":10,"ServiceTypeID":7,"ServiceTypeName":"Snow","ServiceName":"SN
        - Plowing T&M ","ServiceNameAbr":"Plowing T&M ","DisplayName":"Plowing T&M
        ","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":11,"ServiceTypeID":7,"ServiceTypeName":"Snow","ServiceName":"SN
        - Snow Clearing Walkways, 1 - 3\"","ServiceNameAbr":"Walkways, 1 - 3\"","DisplayName":"Snow
        Clearing Walkways, 1 - 3\"","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true},{"ServiceID":12,"ServiceTypeID":7,"ServiceTypeName":"Snow","ServiceName":"SN
        - Snow Plowing Seasonal","ServiceNameAbr":"Snow Plow Seasonal","DisplayName":"Snow
        Plow Seasonal","ServiceDescription":null,"LaborTaxable":null,"MaterialTaxable":null,"EquipmentTaxable":null,"SubTaxable":null,"OtherTaxable":null,"Active":true,"ApproveTicketOnCompletion":null,"MinimumPrice":null,"DefaultPayCodeID":null,"DefaultPayCodeName":null,"WorkersCompID":null,"WorkersCompName":null,"FormID":null,"StartFormID":null,"FormName":null,"ContractService":false,"SortOrder":null,"MultiVisit":false,"ServiceTaxOverrides":[],"ServiceBranches":[],"AllBranches":true}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20ge%202025-02-13T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:26 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185525Z-17c69d65b68kch8rhC1PDXy1b40000000a5000000000av8c
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":9,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":17,"OperationsManagerContactName":"Armando
        Meza","DivisionID":8,"DivisionName":"Construction","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":null,"LeadSourceName":null,"MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":null,"OpportunityName":"Maintenance Project","ProposalDescription1":null,"StartDate":"2025-03-19T00:00:00Z","EndDate":"2025-04-18T00:00:00Z","BidDueDate":"2025-03-19T00:00:00Z","CreatedDateTime":"2025-03-19T03:58:05.053Z","ApprovedDate":"2025-03-19T04:00:29.91Z","ProposedDate":"2025-03-19T04:01:01.28Z","WonDate":"2025-03-19T04:01:01.28Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":2685.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":10,"EstimatedCostDollars":2685.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":2685.00,"EstimatedNetProfitPercent":0.00,"EstimatedNetProfitDollars":0.00,"EstimatedGrossMarginPercent":0.00,"EstimatedGrossMarginDollars":0.00,"EstimatedLaborHours":150.00,"EstimatedLaborCostPerHour":15.00,"EstimatedRealizeRate":17.90,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":2250.00,"EstimatedMaterialCost":435.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":0.00,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":0.00,"InvoiceNote":null,"PercentComplete":0.0000,"RenewalDate":null,"ModifiedByUserID":5,"ModifiedByUserName":"David
        Franco","ModifiedDate":"2025-04-11T15:26:28.793Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":"","TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":6,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":null,"OperationsManagerContactName":null,"DivisionID":2,"DivisionName":"Maintenance","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":null,"LeadSourceName":null,"MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":null,"OpportunityName":"Arturo''s House ","ProposalDescription1":null,"StartDate":"2025-02-10T00:00:00Z","EndDate":"2025-03-12T00:00:00Z","BidDueDate":null,"CreatedDateTime":"2025-02-04T19:39:36.92Z","ApprovedDate":"2025-02-06T18:15:03.41Z","ProposedDate":"2025-02-06T18:15:08.77Z","WonDate":"2025-02-06T18:15:08.77Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":2010.80,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":7,"EstimatedCostDollars":2010.80,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":2010.80,"EstimatedNetProfitPercent":0.00,"EstimatedNetProfitDollars":0.00,"EstimatedGrossMarginPercent":0.00,"EstimatedGrossMarginDollars":0.00,"EstimatedLaborHours":117.40,"EstimatedLaborCostPerHour":15.21,"EstimatedRealizeRate":17.13,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":1785.80,"EstimatedMaterialCost":225.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":50.13,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":50.13,"InvoiceNote":null,"PercentComplete":0.0249,"RenewalDate":null,"ModifiedByUserID":5,"ModifiedByUserName":"David
        Franco","ModifiedDate":"2025-04-17T16:22:58.083Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":50.13,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":5,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":9,"OperationsManagerContactName":"George
        Korsnick","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":11,"LeadSourceName":"Advertising","MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Contract","MasterSequenceNum":null,"OpportunityName":"Maintenance
        Project (Contract)","ProposalDescription1":null,"StartDate":"2025-02-04T00:00:00Z","EndDate":"2025-09-30T00:00:00Z","BidDueDate":null,"CreatedDateTime":"2025-02-01T20:40:03.027Z","ApprovedDate":"2025-02-04T19:36:33.143Z","ProposedDate":"2025-02-04T19:36:52.617Z","WonDate":"2025-02-04T19:36:52.617Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Payment","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":1780.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":6,"EstimatedCostDollars":580.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":580.00,"EstimatedNetProfitPercent":67.42,"EstimatedNetProfitDollars":1200.00,"EstimatedGrossMarginPercent":67.42,"EstimatedGrossMarginDollars":1200.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":580.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":775.00,"ActualGrossMarginPercent":-2572.41,"ActualGrossMarginDollars":-746.00,"ActualLaborHours":47.00,"ActualLaborCostPerHour":16.49,"ActualEarnedRevenue":29.00,"InvoiceNote":null,"PercentComplete":1.0000,"RenewalDate":null,"ModifiedByUserID":5,"ModifiedByUserName":"David
        Franco","ModifiedDate":"2025-04-17T16:23:23.197Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":775.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[{"OpportunityBillingID":26,"OpportunityBillingRefID":null,"BillMonth":2,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":1,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":27,"OpportunityBillingRefID":null,"BillMonth":3,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":2,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":28,"OpportunityBillingRefID":null,"BillMonth":4,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":3,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":29,"OpportunityBillingRefID":null,"BillMonth":5,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":4,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":30,"OpportunityBillingRefID":null,"BillMonth":6,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":5,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":31,"OpportunityBillingRefID":null,"BillMonth":7,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":6,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":32,"OpportunityBillingRefID":null,"BillMonth":8,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":7,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":33,"OpportunityBillingRefID":null,"BillMonth":9,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":8,"Active":true,"OpportunityRevisionID":null}],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":11,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":26,"SalesRepContactName":"Michael
        Enrique","OperationsManagerContactID":26,"OperationsManagerContactName":"Michael
        Enrique","DivisionID":2,"DivisionName":"Maintenance","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":7,"LeadSourceName":"Customer
        Referral","MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Contract","MasterSequenceNum":null,"OpportunityName":"t&m
        test op","ProposalDescription1":null,"StartDate":"2025-04-22T00:00:00Z","EndDate":"2025-04-26T00:00:00Z","BidDueDate":"2025-04-30T00:00:00Z","CreatedDateTime":"2025-04-22T01:35:51.89Z","ApprovedDate":"2025-04-22T02:09:37.027Z","ProposedDate":"2025-04-22T02:14:23.96Z","WonDate":"2025-04-22T02:14:23.96Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"T&M","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":10.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","ApprovedUserID":22,"ApprovedUserName":"Michael Enrique","ProposedUserID":22,"ProposedUserName":"Michael
        Enrique","ClosedUserID":22,"ClosedUserName":"Michael Enrique","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":12,"EstimatedCostDollars":0.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":10.00,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":10.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":0.00,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":0.00,"InvoiceNote":null,"PercentComplete":1.0000,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T02:14:34.18Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":12,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":26,"SalesRepContactName":"Michael
        Enrique","OperationsManagerContactID":null,"OperationsManagerContactName":null,"DivisionID":8,"DivisionName":"Construction","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":null,"LeadSourceName":null,"MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Contract","MasterSequenceNum":null,"OpportunityName":"op
        t&m - fixed payment","ProposalDescription1":null,"StartDate":"2025-04-21T00:00:00Z","EndDate":"2025-04-30T00:00:00Z","BidDueDate":null,"CreatedDateTime":"2025-04-22T02:25:34.37Z","ApprovedDate":"2025-04-22T02:31:37.287Z","ProposedDate":"2025-04-22T02:32:09.43Z","WonDate":"2025-04-22T02:32:09.43Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Payment","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":20.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","ApprovedUserID":22,"ApprovedUserName":"Michael Enrique","ProposedUserID":22,"ProposedUserName":"Michael
        Enrique","ClosedUserID":22,"ClosedUserName":"Michael Enrique","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":13,"EstimatedCostDollars":0.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":20.00,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":20.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":0.00,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":0.00,"InvoiceNote":null,"PercentComplete":1.0000,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T02:47:31.313Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":"","TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[{"OpportunityBillingID":34,"OpportunityBillingRefID":null,"BillMonth":4,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":20.00,"SortOrder":1,"Active":true,"OpportunityRevisionID":null}],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":2,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":12,"OperationsManagerContactName":"Arturo
        Archila","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":6,"LeadSourceName":"Cold
        Call","MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":null,"OpportunityName":"Mary''s Project","ProposalDescription1":null,"StartDate":"2024-12-20T00:00:00Z","EndDate":null,"BidDueDate":null,"CreatedDateTime":"2024-12-10T04:23:59.68Z","ApprovedDate":"2024-12-10T04:28:36.05Z","ProposedDate":"2024-12-13T13:26:43.707Z","WonDate":"2024-12-13T13:26:43.707Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":27560.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":17,"ProposedUserName":"Lawrence
        Gebhardt","ClosedUserID":17,"ClosedUserName":"Lawrence Gebhardt","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":3,"EstimatedCostDollars":7304.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":7304.00,"EstimatedNetProfitPercent":73.50,"EstimatedNetProfitDollars":20256.00,"EstimatedGrossMarginPercent":73.50,"EstimatedGrossMarginDollars":20256.00,"EstimatedLaborHours":422.00,"EstimatedLaborCostPerHour":17.00,"EstimatedRealizeRate":65.31,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":7174.00,"EstimatedMaterialCost":130.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":2995.00,"ActualGrossMarginPercent":73.50,"ActualGrossMarginDollars":8305.96,"ActualLaborHours":130.00,"ActualLaborCostPerHour":23.04,"ActualEarnedRevenue":11300.96,"InvoiceNote":null,"PercentComplete":0.4100,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T14:10:37.5Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":2995.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":8,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":12,"OperationsManagerContactName":"Arturo
        Archila","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":6,"LeadSourceName":"Cold
        Call","MasterOpportunityID":2,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":1,"OpportunityName":"Mary''s Project","ProposalDescription1":null,"StartDate":"2024-12-20T00:00:00Z","EndDate":null,"BidDueDate":null,"CreatedDateTime":"2025-02-18T15:30:13.55Z","ApprovedDate":"2025-03-18T00:56:59.49Z","ProposedDate":"2025-03-18T00:57:07.617Z","WonDate":"2025-03-18T00:57:07.617Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":200.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":3,"EstimatedCostDollars":0.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":200.00,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":200.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":null,"ActualGrossMarginPercent":null,"ActualGrossMarginDollars":null,"ActualLaborHours":null,"ActualLaborCostPerHour":null,"ActualEarnedRevenue":null,"InvoiceNote":null,"PercentComplete":0.4100,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T14:10:37.5Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[{"OpportunityRevisionID":1,"RevisionNumber":1,"RevisionStatus":"Active","StartDate":"2024-12-20T00:00:00Z","CreatedDate":"2025-02-18T15:30:13.657Z","CreatedByUserId":17,"WonDate":"2025-03-18T00:57:07.62Z","WonByUserId":5}],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-13T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-02-12T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:27 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185526Z-17c69d65b68dlnnfhC1PDXqfpw00000007qg00000000dsan
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-12T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-02-11T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:27 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185527Z-17c69d65b68srvkdhC1PDXf2dn0000000cy000000000bzsf
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-11T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-02-10T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:28 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185527Z-17c69d65b689qn7whC1PDXwhn00000000feg00000000b00h
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-10T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-02-09T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:28 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185528Z-17c69d65b68j6fj9hC1PDXprz80000000e9g00000000084b
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-09T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-02-08T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:29 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185528Z-17c69d65b68x99vqhC1PDXfchw0000000du0000000002wcn
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-08T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-02-07T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:29 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185529Z-17c69d65b68j6fj9hC1PDXprz80000000e7g000000004cqu
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-02-07T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-01-31T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:30 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185529Z-17c69d65b68znvv7hC1PDX2tbc0000000e8g000000002743
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-01-31T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-01-24T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:30 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185530Z-17c69d65b68wgkj2hC1PDXxf7s0000000d3g00000000g7w4
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-01-24T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-01-17T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:31 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185531Z-17c69d65b68kch8rhC1PDXy1b40000000a4000000000c630
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-01-17T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-01-10T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:31 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185531Z-17c69d65b68wgkj2hC1PDXxf7s0000000d60000000009cmb
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-01-10T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202025-01-03T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:32 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185531Z-17c69d65b68kch8rhC1PDXy1b40000000a6g000000006rz3
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202025-01-03T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202024-12-27T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:32 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185532Z-17c69d65b68srvkdhC1PDXf2dn0000000d20000000002k51
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202024-12-27T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202024-12-20T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:32 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185532Z-17c69d65b68r7gm7hC1PDX9aa40000000eug000000006r23
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202024-12-20T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202024-12-13T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:33 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185533Z-17c69d65b6864fc9hC1PDXbc7w0000000f40000000001thh
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202024-12-13T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202024-12-06T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:33 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185533Z-17c69d65b68j6fj9hC1PDXprz80000000e80000000003dp7
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202024-12-06T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202024-11-29T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:34 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185534Z-17c69d65b68srvkdhC1PDXf2dn0000000d1g000000003h3b
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202024-11-29T15:05:12.000000Z)%20and%20(ModifiedDate%20ge%202024-11-22T14:55:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:34 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185534Z-17c69d65b68kch8rhC1PDXy1b40000000a2g00000000h236
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=(OpportunityStatus%20eq%20%27Won%27)%20and%20(JobStatusName%20ne%20%27Complete%27)%20and%20(JobStatusName%20ne%20%27Canceled%27)%20and%20(ModifiedDate%20le%202024-11-22T15:05:12.000000Z)&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:35 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185535Z-17c69d65b68srvkdhC1PDXf2dn0000000czg000000008ce2
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Routes?$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:36 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185535Z-17c69d65b68j6fj9hC1PDXprz80000000e2g00000000gmu8
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"RouteID":1,"BranchID":2,"BranchName":"Main","RouteName":"Claudia''s
        Team","Hours":8.00,"Color":"#78C5C5","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia
        Alas","Active":true,"ManagerContactID":5,"ManagerName":"David Franco","RouteSize":0,"DivisionID":null,"DivisionName":null,"DisplayOrder":0,"PercentTravelTime":0,"ShowDailyPlan":true,"AllowEquipmentTimeReporting":false,"EquipmentID":null,"EquipmentName":null,"RouteProperties":[],"RouteServices":[],"RouteServiceTypes":[]},{"RouteID":2,"BranchID":2,"BranchName":"Main","RouteName":"Claudia''s
        Second Route","Hours":0.00,"Color":"#78C5C5","CrewLeaderContactID":25,"CrewLeaderContactName":"John
        Franco","Active":true,"ManagerContactID":13,"ManagerName":"Claudia Alas","RouteSize":0,"DivisionID":null,"DivisionName":null,"DisplayOrder":0,"PercentTravelTime":0,"ShowDailyPlan":true,"AllowEquipmentTimeReporting":false,"EquipmentID":null,"EquipmentName":null,"RouteProperties":[{"RoutePropertyID":2,"PropertyID":4,"PropertyName":"Amherst
        Cemetary","DisplayOrder":0},{"RoutePropertyID":3,"PropertyID":3,"PropertyName":"Amherst
        Rec Ballfield","DisplayOrder":1}],"RouteServices":[],"RouteServiceTypes":[]},{"RouteID":3,"BranchID":2,"BranchName":"Main","RouteName":"David''s
        Team","Hours":0.00,"Color":"#78C5C5","CrewLeaderContactID":5,"CrewLeaderContactName":"David
        Franco","Active":true,"ManagerContactID":17,"ManagerName":"Armando Meza","RouteSize":0,"DivisionID":null,"DivisionName":null,"DisplayOrder":0,"PercentTravelTime":0,"ShowDailyPlan":true,"AllowEquipmentTimeReporting":false,"EquipmentID":null,"EquipmentName":null,"RouteProperties":[],"RouteServices":[],"RouteServiceTypes":[]},{"RouteID":4,"BranchID":2,"BranchName":"Main","RouteName":"Georges
        Team 2","Hours":8.00,"Color":"#78C5C5","CrewLeaderContactID":25,"CrewLeaderContactName":"John
        Franco","Active":true,"ManagerContactID":13,"ManagerName":"Claudia Alas","RouteSize":5,"DivisionID":null,"DivisionName":null,"DisplayOrder":0,"PercentTravelTime":0,"ShowDailyPlan":true,"AllowEquipmentTimeReporting":false,"EquipmentID":null,"EquipmentName":null,"RouteProperties":[{"RoutePropertyID":1,"PropertyID":2,"PropertyName":"David''s
        House","DisplayOrder":0},{"RoutePropertyID":4,"PropertyID":1,"PropertyName":"Test
        Propery","DisplayOrder":1}],"RouteServices":[],"RouteServiceTypes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Contacts?$filter=ContactID%20eq%2021&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:36 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185536Z-17c69d65b68wgkj2hC1PDXxf7s0000000d3g00000000g8at
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ContactID":21,"CompanyID":1,"CompanyName":"Town of Amherst","ContactTypeID":6,"ContactTypeName":"Customer","OfficeAddress":{"AddressID":29,"AddressLine1":"1
        Main St","AddressLine2":null,"City":"Amherst","StateProvinceCode":"NH","ZipCode":"03031","CreatedByUserId":17,"CreatedByUserName":"Lawrence
        Gebhardt","CreatedOn":"2025-01-16T18:51:23.623Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":28,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":17,"CreatedByUserName":"Lawrence
        Gebhardt","CreatedOn":"2025-01-16T18:51:23.607Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Joe","LastName":"Major","ProspectRating":null,"ProspectRatingName":null,"Title":"Mayor","Email":"","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2025-01-16T18:51:23.667Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","EmployeeNumber":null,"Website":null,"EmployeePin":null,"AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":null,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":null,"LastModifiedByUserName":null,"ModifiedDate":null,"ContactTags":[],"ContactSignature":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityNumber%20eq%2010&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:38 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185537Z-1578d7644f679tk2hC1CH1gxmg0000000gq000000000d1s6
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":9,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":17,"OperationsManagerContactName":"Armando
        Meza","DivisionID":8,"DivisionName":"Construction","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":null,"LeadSourceName":null,"MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":null,"OpportunityName":"Maintenance Project","ProposalDescription1":null,"StartDate":"2025-03-19T00:00:00Z","EndDate":"2025-04-18T00:00:00Z","BidDueDate":"2025-03-19T00:00:00Z","CreatedDateTime":"2025-03-19T03:58:05.053Z","ApprovedDate":"2025-03-19T04:00:29.91Z","ProposedDate":"2025-03-19T04:01:01.28Z","WonDate":"2025-03-19T04:01:01.28Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":2685.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":10,"EstimatedCostDollars":2685.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":2685.00,"EstimatedNetProfitPercent":0.00,"EstimatedNetProfitDollars":0.00,"EstimatedGrossMarginPercent":0.00,"EstimatedGrossMarginDollars":0.00,"EstimatedLaborHours":150.00,"EstimatedLaborCostPerHour":15.00,"EstimatedRealizeRate":17.90,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":2250.00,"EstimatedMaterialCost":435.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":0.00,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":0.00,"InvoiceNote":null,"PercentComplete":0.0000,"RenewalDate":null,"ModifiedByUserID":5,"ModifiedByUserName":"David
        Franco","ModifiedDate":"2025-04-11T15:26:28.793Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":"","TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20eq%209&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:38 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185538Z-1578d7644f67wcqnhC1CH1sg9w0000000gdg00000000ehf7
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":162,"OpportunityServiceID":12,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2025-03-19T04:01:01.45Z","AnticStartDate":"2025-03-19T00:00:00Z","ScheduledStartDate":"2025-03-19T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":150.0,"HourCostEst":2250.0,"MaterialCostEst":435.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2685.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":163,"InvoiceID":null,"HoursAct":0.0,"WarrantyHoursAct":0.0,"OTHoursAct":0.0,"LaborCostAct":0.0,"MaterialCostAct":0.0,"EquipmentCostAct":0.0,"SubCostAct":0.0,"OtherCostAct":0.0,"TotalCostAct":0.0,"EarnedRevenue":0.0,"RealizeRateRevenue":null,"OpportunityID":9,"OpportunityNumber":10.0,"EstRealizeRateRevenue":15.0,"DistributedHours":0.0,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-04-11T15:26:55.533Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":150.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketItems?$filter=WorkTicketID%20eq%20162&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:39 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185539Z-r166884f5bfc6gh4hC1CH1bwgg0000000h90000000009gq6
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"WorkTicketItemID":32,"CatalogItemID":1,"ItemName":"Top Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":15.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-03-19T04:01:01.54Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":162},{"WorkTicketItemID":33,"CatalogItemID":4,"ItemName":"Labor
        - Maint","ItemType":"Labor","AllocationUnitTypeID":6,"AllocationUnitTypeName":"Hr","ItemQuantityExtended":150.00,"ItemCost":15.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-03-19T04:01:01.54Z","CatalogItemCategoryID":4,"CatalogItemCategoryName":"Labor","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":162}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ItemAllocations?$filter=WorkTicketID%20eq%20162&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:40 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185539Z-r166884f5bfmjkr2hC1CH1xftc0000000h0000000000bcz7
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ItemAllocationID":14,"ReceiptItemID":null,"WorkTicketID":162,"WorkTicketNumber":163,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":0.00,"ItemUnitCost":0.090,"ItemTotalCost":0.000,"CreatedDateTime":"2025-04-11T15:26:28.343Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-04-11T15:26:28.343Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-04-11T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":13,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":0.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20eq%20162&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:40 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185540Z-1578d7644f62kg4ghC1CH122fg0000000gsg000000007bca
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/OpportunityServices?$filter=OpportunityServiceID%20eq%2012&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:41 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185540Z-1578d7644f6jqmxzhC1CH1c6bs0000000gw0000000003gww
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityServiceID":12,"OpportunityServiceGroupID":18,"BranchID":null,"BranchName":null,"ServiceID":2,"DisplayName":"Plant
        Installation","ServiceDescription":null,"OperationNotes":null,"Occur":null,"InvoiceType":"Fixed
        Price on Completion","AsNeeded":false,"ComplexityPercent":200.00,"PerHours":150.00,"ExtendedHours":150.00,"PerPrice":2685.00000,"PerPriceOverride":null,"ExtendedPrice":2685.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":2250.00,"MaterialExtendedPrice":435.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":2250.00,"MaterialPerExtendedCost":435.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":2250.00,"MaterialPerExtendedPrice":435.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":9,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":2685.00,"PerCost":2685.00000,"OverridePrice":false,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":2250.00,"MaterialExtendedCost":435.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":2685.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":150.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":65.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-03-19T03:58:12.61Z","LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","LastModifiedDateTime":"2025-04-11T15:26:55.533Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityNumber%20eq%207&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:41 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185541Z-r166884f5bfxn5mlhC1CH1014c0000000hb0000000004mfg
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":6,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":null,"OperationsManagerContactName":null,"DivisionID":2,"DivisionName":"Maintenance","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":null,"LeadSourceName":null,"MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":null,"OpportunityName":"Arturo''s House ","ProposalDescription1":null,"StartDate":"2025-02-10T00:00:00Z","EndDate":"2025-03-12T00:00:00Z","BidDueDate":null,"CreatedDateTime":"2025-02-04T19:39:36.92Z","ApprovedDate":"2025-02-06T18:15:03.41Z","ProposedDate":"2025-02-06T18:15:08.77Z","WonDate":"2025-02-06T18:15:08.77Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":2010.80,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":7,"EstimatedCostDollars":2010.80,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":2010.80,"EstimatedNetProfitPercent":0.00,"EstimatedNetProfitDollars":0.00,"EstimatedGrossMarginPercent":0.00,"EstimatedGrossMarginDollars":0.00,"EstimatedLaborHours":117.40,"EstimatedLaborCostPerHour":15.21,"EstimatedRealizeRate":17.13,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":1785.80,"EstimatedMaterialCost":225.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":50.13,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":50.13,"InvoiceNote":null,"PercentComplete":0.0249,"RenewalDate":null,"ModifiedByUserID":5,"ModifiedByUserName":"David
        Franco","ModifiedDate":"2025-04-17T16:22:58.083Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":50.13,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20eq%206&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:41 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185541Z-r166884f5bf9n49rhC1CH15ar40000000gv0000000006qpf
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":160,"OpportunityServiceID":8,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2025-02-06T18:15:09.213Z","AnticStartDate":"2025-02-10T00:00:00Z","ScheduledStartDate":"2025-02-10T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":12.4,"HourCostEst":210.8,"MaterialCostEst":225.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":435.8,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":160,"InvoiceID":null,"HoursAct":0.0,"WarrantyHoursAct":0.0,"OTHoursAct":0.0,"LaborCostAct":0.0,"MaterialCostAct":50.13,"EquipmentCostAct":0.0,"SubCostAct":0.0,"OtherCostAct":0.0,"TotalCostAct":50.13,"EarnedRevenue":50.13,"RealizeRateRevenue":null,"OpportunityID":6,"OpportunityNumber":7.0,"EstRealizeRateRevenue":17.0,"DistributedHours":0.0,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-17T18:20:40.777Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":12.40,"WorkTicketRevenues":[{"WorkTicketRevenueID":20,"RevenueMonth":"2025-02-01T00:00:00Z","RevenueAmount":45.0,"CreatedDateTime":"2025-03-17T18:20:40.777Z","EditedByUserID":null,"EditedByUserName":null,"EditedDateTime":null,"AccountingPeriodID":null,"AccountingPeriodName":null,"AccountingPeriodYear":null},{"WorkTicketRevenueID":21,"RevenueMonth":"2025-03-01T00:00:00Z","RevenueAmount":5.13,"CreatedDateTime":"2025-03-17T18:20:40.777Z","EditedByUserID":null,"EditedByUserName":null,"EditedDateTime":null,"AccountingPeriodID":null,"AccountingPeriodName":null,"AccountingPeriodYear":null}],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":159,"OpportunityServiceID":9,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2025-02-06T18:15:09.063Z","AnticStartDate":"2025-02-10T00:00:00Z","ScheduledStartDate":"2025-03-19T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":"","CrewLeaderContactID":5,"CrewLeaderName":"David
        Franco","HoursEst":105.0,"HourCostEst":1575.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":1575.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":161,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":6,"OpportunityNumber":7.0,"EstRealizeRateRevenue":15.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-17T21:49:46.37Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":105.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketItems?$filter=WorkTicketID%20in%20(160,159)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:42 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185542Z-1578d7644f6fzz89hC1CH1anc80000000gwg0000000010eq
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"WorkTicketItemID":29,"CatalogItemID":4,"ItemName":"Labor - Maint","ItemType":"Labor","AllocationUnitTypeID":6,"AllocationUnitTypeName":"Hr","ItemQuantityExtended":105.00,"ItemCost":15.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-06T18:15:09.153Z","CatalogItemCategoryID":4,"CatalogItemCategoryName":"Labor","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":159},{"WorkTicketItemID":30,"CatalogItemID":5,"ItemName":"Labor
        - Enhancement","ItemType":"Labor","AllocationUnitTypeID":6,"AllocationUnitTypeName":"Hr","ItemQuantityExtended":12.40,"ItemCost":17.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-06T18:15:09.213Z","CatalogItemCategoryID":4,"CatalogItemCategoryName":"Labor","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":160},{"WorkTicketItemID":31,"CatalogItemID":10,"ItemName":"Bag
        Salt","ItemType":"Material","AllocationUnitTypeID":10,"AllocationUnitTypeName":"lb","ItemQuantityExtended":2500.00,"ItemCost":0.09000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-06T18:15:09.213Z","CatalogItemCategoryID":8,"CatalogItemCategoryName":"Snow","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":160}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ItemAllocations?$filter=WorkTicketID%20in%20(160,159)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:43 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185542Z-1578d7644f67mckchC1CH1277n0000000h6000000000d7f9
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ItemAllocationID":2,"ReceiptItemID":null,"WorkTicketID":160,"WorkTicketNumber":160,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":500.00,"ItemUnitCost":0.090,"ItemTotalCost":45.000,"CreatedDateTime":"2025-02-06T18:18:14.093Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-02-06T18:18:14.093Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-02-06T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":1,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":500.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null},{"ItemAllocationID":4,"ReceiptItemID":null,"WorkTicketID":160,"WorkTicketNumber":160,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":12.00,"ItemUnitCost":0.090,"ItemTotalCost":1.080,"CreatedDateTime":"2025-03-17T18:18:46.677Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T18:18:46.677Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-03-17T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":3,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":12.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null},{"ItemAllocationID":6,"ReceiptItemID":null,"WorkTicketID":160,"WorkTicketNumber":160,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":25.00,"ItemUnitCost":0.090,"ItemTotalCost":2.250,"CreatedDateTime":"2025-03-17T18:19:25.44Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T18:19:25.44Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-03-17T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":5,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":25.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null},{"ItemAllocationID":8,"ReceiptItemID":null,"WorkTicketID":160,"WorkTicketNumber":160,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":20.00,"ItemUnitCost":0.090,"ItemTotalCost":1.800,"CreatedDateTime":"2025-03-17T18:19:46.09Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T18:19:46.09Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-03-17T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":7,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":20.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null},{"ItemAllocationID":10,"ReceiptItemID":null,"WorkTicketID":160,"WorkTicketNumber":160,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":3000.00,"ItemUnitCost":0.090,"ItemTotalCost":270.000,"CreatedDateTime":"2025-03-17T18:20:00.77Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T18:20:00.77Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-03-17T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":9,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":3000.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null},{"ItemAllocationID":12,"ReceiptItemID":null,"WorkTicketID":160,"WorkTicketNumber":160,"InventoryLocationID":1,"InventoryLocationName":"David
        Warehouse","DeviceID":null,"DeviceName":null,"CatalogItemID":10,"CatalogItemName":"Bag
        Salt","ItemName":"Bag Salt","ItemType":"Material","ItemQuantity":-3000.00,"ItemUnitCost":0.090,"ItemTotalCost":-270.000,"CreatedDateTime":"2025-03-17T18:20:40.767Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T18:20:40.767Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","AllocationStatus":"Received","ItemAllocationDate":"2025-03-17T00:00:00Z","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":null,"TransactionID":11,"InventoryAdjustment":false,"InvoiceID":null,"JobInventoryItemAllocationID":null,"FromWorkTicketIDToInventory":null,"PreReceivedItemQuantity":-3000.00,"SubcontractorAcceptedUserID":null,"SubcontractorAcceptedDateTime":null,"SubcontractorCreated":false,"SubcontractorRouteID":null,"SubcontractorServicesRenderedDate":null,"WorkTicketTimeID":null,"EPAName":null,"EPANumber":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20in%20(160,159)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:43 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185543Z-1578d7644f6tfdmlhC1CH1a02g0000000gy000000000euut
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/OpportunityServices?$filter=OpportunityServiceID%20in%20(8,9)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:44 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185543Z-1578d7644f6vvwf2hC1CH1ft2g0000000gg0000000008tbm
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityServiceID":8,"OpportunityServiceGroupID":14,"BranchID":null,"BranchName":null,"ServiceID":4,"DisplayName":"Mulch
        Install ","ServiceDescription":null,"OperationNotes":null,"Occur":null,"InvoiceType":"Fixed
        Price on Completion","AsNeeded":false,"ComplexityPercent":24.00,"PerHours":12.40,"ExtendedHours":12.40,"PerPrice":435.80000,"PerPriceOverride":null,"ExtendedPrice":435.80,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":210.80,"MaterialExtendedPrice":225.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":210.80,"MaterialPerExtendedCost":225.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":210.80,"MaterialPerExtendedPrice":225.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":6,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":435.80,"PerCost":435.80000,"OverridePrice":false,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":210.80,"MaterialExtendedCost":225.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":435.80,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":12.40,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":2510.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:40:04.997Z","LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","LastModifiedDateTime":"2025-03-17T18:18:53.537Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]},{"OpportunityServiceID":9,"OpportunityServiceGroupID":14,"BranchID":null,"BranchName":null,"ServiceID":12,"DisplayName":"Snow
        Plow Seasonal","ServiceDescription":null,"OperationNotes":null,"Occur":null,"InvoiceType":"Fixed
        Price on Completion","AsNeeded":false,"ComplexityPercent":5.00,"PerHours":105.00,"ExtendedHours":105.00,"PerPrice":1575.00000,"PerPriceOverride":null,"ExtendedPrice":1575.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":2,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":1575.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":1575.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":1575.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":6,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":1575.00,"PerCost":1575.00000,"OverridePrice":false,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":1575.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":1575.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":105.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":100.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:41:58.273Z","LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","LastModifiedDateTime":"2025-02-06T18:15:08.77Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityNumber%20eq%206&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:44 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185544Z-r166884f5bf2m4j6hC1CH131ag0000000gk00000000089dz
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":5,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":9,"OperationsManagerContactName":"George
        Korsnick","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":11,"LeadSourceName":"Advertising","MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Contract","MasterSequenceNum":null,"OpportunityName":"Maintenance
        Project (Contract)","ProposalDescription1":null,"StartDate":"2025-02-04T00:00:00Z","EndDate":"2025-09-30T00:00:00Z","BidDueDate":null,"CreatedDateTime":"2025-02-01T20:40:03.027Z","ApprovedDate":"2025-02-04T19:36:33.143Z","ProposedDate":"2025-02-04T19:36:52.617Z","WonDate":"2025-02-04T19:36:52.617Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Payment","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":1780.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":6,"EstimatedCostDollars":580.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":580.00,"EstimatedNetProfitPercent":67.42,"EstimatedNetProfitDollars":1200.00,"EstimatedGrossMarginPercent":67.42,"EstimatedGrossMarginDollars":1200.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":580.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":775.00,"ActualGrossMarginPercent":-2572.41,"ActualGrossMarginDollars":-746.00,"ActualLaborHours":47.00,"ActualLaborCostPerHour":16.49,"ActualEarnedRevenue":29.00,"InvoiceNote":null,"PercentComplete":1.0000,"RenewalDate":null,"ModifiedByUserID":5,"ModifiedByUserName":"David
        Franco","ModifiedDate":"2025-04-17T16:23:23.197Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":775.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[{"OpportunityBillingID":26,"OpportunityBillingRefID":null,"BillMonth":2,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":1,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":27,"OpportunityBillingRefID":null,"BillMonth":3,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":2,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":28,"OpportunityBillingRefID":null,"BillMonth":4,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":3,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":29,"OpportunityBillingRefID":null,"BillMonth":5,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":4,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":30,"OpportunityBillingRefID":null,"BillMonth":6,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":5,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":31,"OpportunityBillingRefID":null,"BillMonth":7,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":6,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":32,"OpportunityBillingRefID":null,"BillMonth":8,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":7,"Active":true,"OpportunityRevisionID":null},{"OpportunityBillingID":33,"OpportunityBillingRefID":null,"BillMonth":9,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":222.50,"SortOrder":8,"Active":true,"OpportunityRevisionID":null}],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20eq%205&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:45 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185545Z-r166884f5bflgfxmhC1CH1t2gs000000023000000000achv
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":109,"OpportunityServiceID":7,"ContractYear":1,"Occur":28,"AnnualizedOccur":28,"CreatedDateTime":"2025-02-04T19:36:53.123Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":155,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":110,"OpportunityServiceID":7,"ContractYear":1,"Occur":27,"AnnualizedOccur":27,"CreatedDateTime":"2025-02-04T19:36:53.127Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":154,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":112,"OpportunityServiceID":7,"ContractYear":1,"Occur":25,"AnnualizedOccur":25,"CreatedDateTime":"2025-02-04T19:36:53.13Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":152,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":114,"OpportunityServiceID":7,"ContractYear":1,"Occur":23,"AnnualizedOccur":23,"CreatedDateTime":"2025-02-04T19:36:53.137Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":150,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":115,"OpportunityServiceID":7,"ContractYear":1,"Occur":22,"AnnualizedOccur":22,"CreatedDateTime":"2025-02-04T19:36:53.14Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":149,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":116,"OpportunityServiceID":7,"ContractYear":1,"Occur":21,"AnnualizedOccur":21,"CreatedDateTime":"2025-02-04T19:36:53.143Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":148,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":118,"OpportunityServiceID":7,"ContractYear":1,"Occur":19,"AnnualizedOccur":19,"CreatedDateTime":"2025-02-04T19:36:53.147Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":146,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":119,"OpportunityServiceID":7,"ContractYear":1,"Occur":18,"AnnualizedOccur":18,"CreatedDateTime":"2025-02-04T19:36:53.15Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":145,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":120,"OpportunityServiceID":7,"ContractYear":1,"Occur":17,"AnnualizedOccur":17,"CreatedDateTime":"2025-02-04T19:36:53.153Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":144,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":121,"OpportunityServiceID":7,"ContractYear":1,"Occur":16,"AnnualizedOccur":16,"CreatedDateTime":"2025-02-04T19:36:53.157Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":143,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":122,"OpportunityServiceID":7,"ContractYear":1,"Occur":15,"AnnualizedOccur":15,"CreatedDateTime":"2025-02-04T19:36:53.157Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":142,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":123,"OpportunityServiceID":7,"ContractYear":1,"Occur":14,"AnnualizedOccur":14,"CreatedDateTime":"2025-02-04T19:36:53.16Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":141,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":124,"OpportunityServiceID":7,"ContractYear":1,"Occur":13,"AnnualizedOccur":13,"CreatedDateTime":"2025-02-04T19:36:53.16Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":140,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":125,"OpportunityServiceID":7,"ContractYear":1,"Occur":12,"AnnualizedOccur":12,"CreatedDateTime":"2025-02-04T19:36:53.163Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":139,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":126,"OpportunityServiceID":7,"ContractYear":1,"Occur":11,"AnnualizedOccur":11,"CreatedDateTime":"2025-02-04T19:36:53.163Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":138,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":127,"OpportunityServiceID":7,"ContractYear":1,"Occur":10,"AnnualizedOccur":10,"CreatedDateTime":"2025-02-04T19:36:53.167Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":137,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":128,"OpportunityServiceID":7,"ContractYear":1,"Occur":9,"AnnualizedOccur":9,"CreatedDateTime":"2025-02-04T19:36:53.17Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":136,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":130,"OpportunityServiceID":7,"ContractYear":1,"Occur":7,"AnnualizedOccur":7,"CreatedDateTime":"2025-02-04T19:36:53.173Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":134,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":131,"OpportunityServiceID":7,"ContractYear":1,"Occur":6,"AnnualizedOccur":6,"CreatedDateTime":"2025-02-04T19:36:53.173Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":133,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":132,"OpportunityServiceID":7,"ContractYear":1,"Occur":5,"AnnualizedOccur":5,"CreatedDateTime":"2025-02-04T19:36:53.177Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":132,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":133,"OpportunityServiceID":7,"ContractYear":1,"Occur":4,"AnnualizedOccur":4,"CreatedDateTime":"2025-02-04T19:36:53.177Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":131,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":134,"OpportunityServiceID":7,"ContractYear":1,"Occur":3,"AnnualizedOccur":3,"CreatedDateTime":"2025-02-04T19:36:53.18Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":130,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":135,"OpportunityServiceID":7,"ContractYear":1,"Occur":2,"AnnualizedOccur":2,"CreatedDateTime":"2025-02-04T19:36:53.18Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":129,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":136,"OpportunityServiceID":7,"ContractYear":1,"Occur":1,"AnnualizedOccur":1,"CreatedDateTime":"2025-02-04T19:36:53.183Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":128,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":138,"OpportunityServiceID":6,"ContractYear":1,"Occur":19,"AnnualizedOccur":19,"CreatedDateTime":"2025-02-04T19:36:53.19Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":126,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":139,"OpportunityServiceID":6,"ContractYear":1,"Occur":18,"AnnualizedOccur":18,"CreatedDateTime":"2025-02-04T19:36:53.19Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":125,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":140,"OpportunityServiceID":6,"ContractYear":1,"Occur":17,"AnnualizedOccur":17,"CreatedDateTime":"2025-02-04T19:36:53.193Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":124,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":141,"OpportunityServiceID":6,"ContractYear":1,"Occur":16,"AnnualizedOccur":16,"CreatedDateTime":"2025-02-04T19:36:53.193Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":123,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":142,"OpportunityServiceID":6,"ContractYear":1,"Occur":15,"AnnualizedOccur":15,"CreatedDateTime":"2025-02-04T19:36:53.197Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":122,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":143,"OpportunityServiceID":6,"ContractYear":1,"Occur":14,"AnnualizedOccur":14,"CreatedDateTime":"2025-02-04T19:36:53.2Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":121,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":144,"OpportunityServiceID":6,"ContractYear":1,"Occur":13,"AnnualizedOccur":13,"CreatedDateTime":"2025-02-04T19:36:53.2Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":120,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":145,"OpportunityServiceID":6,"ContractYear":1,"Occur":12,"AnnualizedOccur":12,"CreatedDateTime":"2025-02-04T19:36:53.2Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":119,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":146,"OpportunityServiceID":6,"ContractYear":1,"Occur":11,"AnnualizedOccur":11,"CreatedDateTime":"2025-02-04T19:36:53.203Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":118,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":147,"OpportunityServiceID":6,"ContractYear":1,"Occur":10,"AnnualizedOccur":10,"CreatedDateTime":"2025-02-04T19:36:53.207Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":117,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":148,"OpportunityServiceID":6,"ContractYear":1,"Occur":9,"AnnualizedOccur":9,"CreatedDateTime":"2025-02-04T19:36:53.207Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":116,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":149,"OpportunityServiceID":6,"ContractYear":1,"Occur":8,"AnnualizedOccur":8,"CreatedDateTime":"2025-02-04T19:36:53.21Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":115,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":150,"OpportunityServiceID":6,"ContractYear":1,"Occur":7,"AnnualizedOccur":7,"CreatedDateTime":"2025-02-04T19:36:53.21Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":114,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":151,"OpportunityServiceID":6,"ContractYear":1,"Occur":6,"AnnualizedOccur":6,"CreatedDateTime":"2025-02-04T19:36:53.213Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":113,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":152,"OpportunityServiceID":6,"ContractYear":1,"Occur":5,"AnnualizedOccur":5,"CreatedDateTime":"2025-02-04T19:36:53.217Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":112,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":153,"OpportunityServiceID":6,"ContractYear":1,"Occur":4,"AnnualizedOccur":4,"CreatedDateTime":"2025-02-04T19:36:53.217Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":111,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":154,"OpportunityServiceID":6,"ContractYear":1,"Occur":3,"AnnualizedOccur":3,"CreatedDateTime":"2025-02-04T19:36:53.22Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":110,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":155,"OpportunityServiceID":6,"ContractYear":1,"Occur":2,"AnnualizedOccur":2,"CreatedDateTime":"2025-02-04T19:36:53.22Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":109,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":156,"OpportunityServiceID":6,"ContractYear":1,"Occur":1,"AnnualizedOccur":1,"CreatedDateTime":"2025-02-04T19:36:53.223Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":108,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-02-06T18:16:01.463Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":108,"OpportunityServiceID":7,"ContractYear":1,"Occur":29,"AnnualizedOccur":29,"CreatedDateTime":"2025-02-04T19:36:53.12Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-03-17T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":5,"CrewLeaderName":"David
        Franco","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":156,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-17T21:42:14.33Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":129,"OpportunityServiceID":7,"ContractYear":1,"Occur":8,"AnnualizedOccur":8,"CreatedDateTime":"2025-02-04T19:36:53.17Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-02-04T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":5,"CrewLeaderName":"David
        Franco","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":135,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-17T21:45:17.197Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":111,"OpportunityServiceID":7,"ContractYear":1,"Occur":26,"AnnualizedOccur":26,"CreatedDateTime":"2025-02-04T19:36:53.13Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-03-18T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":25,"CrewLeaderName":"John
        Franco","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":153,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-18T20:28:39.87Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":117,"OpportunityServiceID":7,"ContractYear":1,"Occur":20,"AnnualizedOccur":20,"CreatedDateTime":"2025-02-04T19:36:53.143Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-03-19T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":25,"CrewLeaderName":"John
        Franco","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":147,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-18T20:29:12.867Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":113,"OpportunityServiceID":7,"ContractYear":1,"Occur":24,"AnnualizedOccur":24,"CreatedDateTime":"2025-02-04T19:36:53.133Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-03-18T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":9,"CrewLeaderName":"George
        Korsnick","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":151,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":5,"LastModifiedByUserName":"David Franco","LastModifiedDateTime":"2025-03-19T03:26:07.15Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":107,"OpportunityServiceID":7,"ContractYear":1,"Occur":30,"AnnualizedOccur":30,"CreatedDateTime":"2025-02-04T19:36:52.953Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-03-17T00:00:00Z","CompleteDate":null,"ApprovedDate":"2025-03-17T00:00:00Z","WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":25,"CrewLeaderName":"John
        Franco","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":40.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":9,"ApprovedUserName":"Zach
        Kemp","WorkTicketNumber":157,"InvoiceID":null,"HoursAct":15.0,"WarrantyHoursAct":0.0,"OTHoursAct":0.0,"LaborCostAct":260.0,"MaterialCostAct":0.0,"EquipmentCostAct":0.0,"SubCostAct":0.0,"OtherCostAct":0.0,"TotalCostAct":260.0,"EarnedRevenue":0.0,"RealizeRateRevenue":0.0,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":0.0,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":9,"LastModifiedByUserName":"Zach Kemp","LastModifiedDateTime":"2025-04-09T16:00:17.503Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":137,"OpportunityServiceID":6,"ContractYear":1,"Occur":20,"AnnualizedOccur":20,"CreatedDateTime":"2025-02-04T19:36:53.183Z","AnticStartDate":"2025-02-04T00:00:00Z","ScheduledStartDate":"2025-04-29T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":"","CrewLeaderContactID":5,"CrewLeaderName":"David
        Franco","HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":29.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":29.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":127,"InvoiceID":null,"HoursAct":32.0,"WarrantyHoursAct":0.0,"OTHoursAct":0.0,"LaborCostAct":515.0,"MaterialCostAct":0.0,"EquipmentCostAct":0.0,"SubCostAct":0.0,"OtherCostAct":0.0,"TotalCostAct":515.0,"EarnedRevenue":29.0,"RealizeRateRevenue":0.91,"OpportunityID":5,"OpportunityNumber":6.0,"EstRealizeRateRevenue":0.0,"DistributedHours":2.0,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":9,"LastModifiedByUserName":"Zach Kemp","LastModifiedDateTime":"2025-04-28T16:30:07.29Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":8.00,"HoursUnscheduled":-8.00,"WorkTicketRevenues":[{"WorkTicketRevenueID":22,"RevenueMonth":"2025-03-01T00:00:00Z","RevenueAmount":29.0,"CreatedDateTime":"2025-03-17T19:02:37.207Z","EditedByUserID":null,"EditedByUserName":null,"EditedDateTime":null,"AccountingPeriodID":null,"AccountingPeriodName":null,"AccountingPeriodYear":null}],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketItems?$filter=WorkTicketID%20in%20(109,110,112,114,115,116,118,119,120,121,122,123,124,125,126,127,128,130,131,132,133,134,135,136,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,108,129,111,117,113,107,137)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:46 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185546Z-1578d7644f677f4shC1CH18uys0000000du000000000cudu
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"WorkTicketItemID":6,"CatalogItemID":1,"ItemName":"Top Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.187Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":137},{"WorkTicketItemID":7,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.19Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":138},{"WorkTicketItemID":8,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.19Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":139},{"WorkTicketItemID":9,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.193Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":140},{"WorkTicketItemID":10,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.197Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":141},{"WorkTicketItemID":11,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.197Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":142},{"WorkTicketItemID":12,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.2Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":143},{"WorkTicketItemID":13,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.2Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":144},{"WorkTicketItemID":14,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.203Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":145},{"WorkTicketItemID":15,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.203Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":146},{"WorkTicketItemID":16,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.207Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":147},{"WorkTicketItemID":17,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.207Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":148},{"WorkTicketItemID":18,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.21Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":149},{"WorkTicketItemID":19,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.21Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":150},{"WorkTicketItemID":20,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.213Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":151},{"WorkTicketItemID":21,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.217Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":152},{"WorkTicketItemID":22,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.22Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":153},{"WorkTicketItemID":23,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.22Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":154},{"WorkTicketItemID":24,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.223Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":155},{"WorkTicketItemID":25,"CatalogItemID":1,"ItemName":"Top
        Soil","ItemType":"Material","AllocationUnitTypeID":3,"AllocationUnitTypeName":"cuyd","ItemQuantityExtended":1.00,"ItemCost":29.00000000,"ShowOnTicket":true,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-04T19:36:53.223Z","CatalogItemCategoryID":1,"CatalogItemCategoryName":"Bulk
        Materials","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":156}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ItemAllocations?$filter=WorkTicketID%20in%20(109,110,112,114,115,116,118,119,120,121,122,123,124,125,126,127,128,130,131,132,133,134,135,136,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,108,129,111,117,113,107,137)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:47 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185546Z-1578d7644f66w4fhhC1CH18y040000000e0g000000002agr
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20in%20(109,110,112,114,115,116,118,119,120,121,122,123,124,125,126,127,128,130,131,132,133,134,135,136,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,108,129,111,117,113,107,137)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:47 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185547Z-r166884f5bf9n49rhC1CH15ar40000000gpg00000000fyfs
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketTimeID":26,"WorkTicketID":107,"WorkTicketNumber":157,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":150.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.96Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.96Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":27,"WorkTicketID":107,"WorkTicketNumber":157,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":110.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":28,"WorkTicketID":107,"WorkTicketNumber":157,"ContactID":13,"ContactName":"Claudia
        Alas","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":null,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":29,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":150.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":30,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":110.0,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.977Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.977Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":31,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":13,"ContactName":"Claudia
        Alas","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.98Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.98Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":null,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":32,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":14,"ContactName":"Joe
        Cruz","WorkTicketTimeDate":null,"StartTime":"2025-03-17T00:00:00Z","EndTime":"2025-03-17T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-03-17T19:02:36.98Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","LastModifiedDateTime":null,"LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","AcceptedDateTime":"2025-03-17T19:02:36.98Z","AcceptedUserID":5,"AcceptedUserName":"David
        Franco","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":null,"RouteID":1,"RouteName":"Claudia''s
        Team","CrewLeaderContactID":13,"CrewLeaderContactName":"Claudia Alas","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":18,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":55.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.967Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.64Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":19,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":45.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.99Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.64Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":20,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":37.5,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.99Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":15.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":21,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-04T12:30:00Z","EndTime":"2025-03-04T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":75.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:24.99Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":true,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":22,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":15.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.027Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":23,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":11.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.03Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.643Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":24,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":9.0,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.03Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.65Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":25,"WorkTicketID":137,"WorkTicketNumber":127,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","WorkTicketTimeDate":null,"StartTime":"2025-03-04T00:00:00Z","EndTime":"2025-03-04T00:01:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":0.5,"OTHours":null,"WarrantyTime":false,"DistributedTime":true,"BurdenedCost":7.5,"InvoiceID":null,"CreatedDateTime":"2025-03-04T14:55:25.03Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":"2025-03-04T00:00:00Z","LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2025-03-04T15:00:22.647Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":15.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/OpportunityServices?$filter=OpportunityServiceID%20in%20(7,6)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:48 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185548Z-r166884f5bf58r6thC1CH1tvzs0000000gm00000000053cs
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityServiceID":6,"OpportunityServiceGroupID":10,"BranchID":null,"BranchName":null,"ServiceID":4,"DisplayName":"Mulch
        Install ","ServiceDescription":null,"OperationNotes":null,"Occur":20,"InvoiceType":"Fixed
        Payment","AsNeeded":false,"ComplexityPercent":20.00,"PerHours":0.00,"ExtendedHours":0.00,"PerPrice":29.00000,"PerPriceOverride":null,"ExtendedPrice":580.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":0.00,"MaterialExtendedPrice":580.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":0.00,"MaterialPerExtendedCost":29.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":0.00,"MaterialPerExtendedPrice":29.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":5,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":580.00,"PerCost":29.00000,"OverridePrice":false,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":0.00,"MaterialExtendedCost":580.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":580.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":0.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":1.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-01T20:41:07.057Z","LastModifiedByUserID":9,"LastModifiedByUserName":"Zach
        Kemp","LastModifiedDateTime":"2025-03-17T19:02:05.647Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]},{"OpportunityServiceID":7,"OpportunityServiceGroupID":10,"BranchID":null,"BranchName":null,"ServiceID":5,"DisplayName":"Spring
        Clean up","ServiceDescription":null,"OperationNotes":null,"Occur":30,"InvoiceType":"Fixed
        Payment","AsNeeded":false,"ComplexityPercent":20.00,"PerHours":0.00,"ExtendedHours":0.00,"PerPrice":0.00000,"PerPriceOverride":40.00000,"ExtendedPrice":1200.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":2,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":0.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":0.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":0.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":5,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":0.00,"PerCost":0.00000,"OverridePrice":true,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":0.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":0.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":0.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":0.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-02-01T20:41:09.27Z","LastModifiedByUserID":9,"LastModifiedByUserName":"Zach
        Kemp","LastModifiedDateTime":"2025-04-09T16:00:17.503Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityNumber%20eq%2012&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:49 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185549Z-1578d7644f6tfdmlhC1CH1a02g0000000h1g00000000a81c
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":11,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":26,"SalesRepContactName":"Michael
        Enrique","OperationsManagerContactID":26,"OperationsManagerContactName":"Michael
        Enrique","DivisionID":2,"DivisionName":"Maintenance","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":7,"LeadSourceName":"Customer
        Referral","MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Contract","MasterSequenceNum":null,"OpportunityName":"t&m
        test op","ProposalDescription1":null,"StartDate":"2025-04-22T00:00:00Z","EndDate":"2025-04-26T00:00:00Z","BidDueDate":"2025-04-30T00:00:00Z","CreatedDateTime":"2025-04-22T01:35:51.89Z","ApprovedDate":"2025-04-22T02:09:37.027Z","ProposedDate":"2025-04-22T02:14:23.96Z","WonDate":"2025-04-22T02:14:23.96Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"T&M","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":10.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","ApprovedUserID":22,"ApprovedUserName":"Michael Enrique","ProposedUserID":22,"ProposedUserName":"Michael
        Enrique","ClosedUserID":22,"ClosedUserName":"Michael Enrique","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":12,"EstimatedCostDollars":0.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":10.00,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":10.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":0.00,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":0.00,"InvoiceNote":null,"PercentComplete":1.0000,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T02:14:34.18Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20eq%2011&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:50 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185550Z-1578d7644f6vvwf2hC1CH1ft2g0000000geg00000000bk6c
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":163,"OpportunityServiceID":15,"ContractYear":1,"Occur":1,"AnnualizedOccur":1,"CreatedDateTime":"2025-04-22T02:14:24.107Z","AnticStartDate":"2025-04-22T00:00:00Z","ScheduledStartDate":"2025-04-22T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":10.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":164,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":11,"OpportunityNumber":12.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:14:24.107Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketItems?$filter=WorkTicketID%20eq%20163&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:51 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185550Z-r166884f5bfr68rmhC1CH18sk00000000gt0000000009vux
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ItemAllocations?$filter=WorkTicketID%20eq%20163&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:51 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185551Z-r166884f5bfr68rmhC1CH18sk00000000gqg00000000e037
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20eq%20163&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:52 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185551Z-1578d7644f6kcqdshC1CH181m00000000gp000000000f28f
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/OpportunityServices?$filter=OpportunityServiceID%20eq%2015&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:52 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185552Z-r166884f5bfqx8hbhC1CH1sycn000000075000000000bt3v
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityServiceID":15,"OpportunityServiceGroupID":22,"BranchID":null,"BranchName":null,"ServiceID":4,"DisplayName":"Mulch
        Install ","ServiceDescription":null,"OperationNotes":null,"Occur":1,"InvoiceType":"T&M","AsNeeded":false,"ComplexityPercent":0.00,"PerHours":0.00,"ExtendedHours":0.00,"PerPrice":0.00000,"PerPriceOverride":10.00000,"ExtendedPrice":10.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":true,"LaborMarkup":0.00,"MaterialMarkup":0.00,"EquipmentMarkup":0.00,"SubMarkup":0.00,"OtherMarkup":0.00,"NetProfitPercent":null,"LaborExtendedPrice":0.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":0.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":0.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":11,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":0.00,"PerCost":0.00000,"OverridePrice":true,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":0.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":0.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":0.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":0.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","CreatedDateTime":"2025-04-22T02:07:09.033Z","LastModifiedByUserID":22,"LastModifiedByUserName":"Michael
        Enrique","LastModifiedDateTime":"2025-04-22T02:14:23.96Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityNumber%20eq%2013&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:53 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185552Z-1578d7644f6fxjd4hC1CH1k8zs0000000gwg00000000dxwr
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":12,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":26,"SalesRepContactName":"Michael
        Enrique","OperationsManagerContactID":null,"OperationsManagerContactName":null,"DivisionID":8,"DivisionName":"Construction","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":null,"LeadSourceName":null,"MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Contract","MasterSequenceNum":null,"OpportunityName":"op
        t&m - fixed payment","ProposalDescription1":null,"StartDate":"2025-04-21T00:00:00Z","EndDate":"2025-04-30T00:00:00Z","BidDueDate":null,"CreatedDateTime":"2025-04-22T02:25:34.37Z","ApprovedDate":"2025-04-22T02:31:37.287Z","ProposedDate":"2025-04-22T02:32:09.43Z","WonDate":"2025-04-22T02:32:09.43Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Payment","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":20.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","ApprovedUserID":22,"ApprovedUserName":"Michael Enrique","ProposedUserID":22,"ProposedUserName":"Michael
        Enrique","ClosedUserID":22,"ClosedUserName":"Michael Enrique","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":13,"EstimatedCostDollars":0.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":20.00,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":20.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":0.00,"ActualGrossMarginPercent":0.00,"ActualGrossMarginDollars":0.00,"ActualLaborHours":0.00,"ActualLaborCostPerHour":0.00,"ActualEarnedRevenue":0.00,"InvoiceNote":null,"PercentComplete":1.0000,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T02:47:31.313Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":"","TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[{"OpportunityBillingID":34,"OpportunityBillingRefID":null,"BillMonth":4,"InvoiceDescription":null,"InvoiceTriggerPercent":null,"InvoiceAmount":20.00,"SortOrder":1,"Active":true,"OpportunityRevisionID":null}],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20eq%2012&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:53 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185553Z-1578d7644f67mckchC1CH1277n0000000hag0000000069dt
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":164,"OpportunityServiceID":17,"ContractYear":1,"Occur":10,"AnnualizedOccur":10,"CreatedDateTime":"2025-04-22T02:32:09.46Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":184,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.46Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":165,"OpportunityServiceID":16,"ContractYear":1,"Occur":2,"AnnualizedOccur":2,"CreatedDateTime":"2025-04-22T02:32:09.463Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":166,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.463Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":166,"OpportunityServiceID":16,"ContractYear":1,"Occur":3,"AnnualizedOccur":3,"CreatedDateTime":"2025-04-22T02:32:09.467Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":167,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.467Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":167,"OpportunityServiceID":17,"ContractYear":1,"Occur":9,"AnnualizedOccur":9,"CreatedDateTime":"2025-04-22T02:32:09.47Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":183,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.47Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":168,"OpportunityServiceID":16,"ContractYear":1,"Occur":1,"AnnualizedOccur":1,"CreatedDateTime":"2025-04-22T02:32:09.47Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":165,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.47Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":169,"OpportunityServiceID":16,"ContractYear":1,"Occur":5,"AnnualizedOccur":5,"CreatedDateTime":"2025-04-22T02:32:09.473Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":169,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.473Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":170,"OpportunityServiceID":16,"ContractYear":1,"Occur":6,"AnnualizedOccur":6,"CreatedDateTime":"2025-04-22T02:32:09.473Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":170,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.473Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":171,"OpportunityServiceID":16,"ContractYear":1,"Occur":7,"AnnualizedOccur":7,"CreatedDateTime":"2025-04-22T02:32:09.477Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":171,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.477Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":172,"OpportunityServiceID":16,"ContractYear":1,"Occur":8,"AnnualizedOccur":8,"CreatedDateTime":"2025-04-22T02:32:09.48Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":172,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.48Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":173,"OpportunityServiceID":16,"ContractYear":1,"Occur":9,"AnnualizedOccur":9,"CreatedDateTime":"2025-04-22T02:32:09.48Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":173,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.48Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":174,"OpportunityServiceID":16,"ContractYear":1,"Occur":10,"AnnualizedOccur":10,"CreatedDateTime":"2025-04-22T02:32:09.48Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":174,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.48Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":175,"OpportunityServiceID":17,"ContractYear":1,"Occur":1,"AnnualizedOccur":1,"CreatedDateTime":"2025-04-22T02:32:09.483Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":175,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.483Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":176,"OpportunityServiceID":17,"ContractYear":1,"Occur":2,"AnnualizedOccur":2,"CreatedDateTime":"2025-04-22T02:32:09.487Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":176,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.487Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":177,"OpportunityServiceID":17,"ContractYear":1,"Occur":3,"AnnualizedOccur":3,"CreatedDateTime":"2025-04-22T02:32:09.487Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":177,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.487Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":178,"OpportunityServiceID":17,"ContractYear":1,"Occur":4,"AnnualizedOccur":4,"CreatedDateTime":"2025-04-22T02:32:09.49Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":178,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.49Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":179,"OpportunityServiceID":17,"ContractYear":1,"Occur":5,"AnnualizedOccur":5,"CreatedDateTime":"2025-04-22T02:32:09.49Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":179,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.49Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":180,"OpportunityServiceID":17,"ContractYear":1,"Occur":6,"AnnualizedOccur":6,"CreatedDateTime":"2025-04-22T02:32:09.493Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":180,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.493Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":181,"OpportunityServiceID":17,"ContractYear":1,"Occur":7,"AnnualizedOccur":7,"CreatedDateTime":"2025-04-22T02:32:09.493Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":181,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.493Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":182,"OpportunityServiceID":17,"ContractYear":1,"Occur":8,"AnnualizedOccur":8,"CreatedDateTime":"2025-04-22T02:32:09.497Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":2.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":182,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.497Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":183,"OpportunityServiceID":16,"ContractYear":1,"Occur":4,"AnnualizedOccur":4,"CreatedDateTime":"2025-04-22T02:32:09.497Z","AnticStartDate":"2025-04-21T00:00:00Z","ScheduledStartDate":"2025-04-21T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":0.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":168,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":12,"OpportunityNumber":13.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-04-22T02:32:09.497Z","CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketItems?$filter=WorkTicketID%20in%20(164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:54 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185554Z-r166884f5bf9n49rhC1CH15ar40000000gsg00000000bd9n
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ItemAllocations?$filter=WorkTicketID%20in%20(164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:54 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185554Z-r166884f5bf9w2fwhC1CH1q3w800000009bg00000000b5bc
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20in%20(164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:55 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185554Z-1578d7644f6vvwf2hC1CH1ft2g0000000gd000000000cwbe
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/OpportunityServices?$filter=OpportunityServiceID%20in%20(17,16)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:55 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185555Z-1578d7644f6xdtbnhC1CH19gvc0000000gpg00000000dq0x
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityServiceID":16,"OpportunityServiceGroupID":24,"BranchID":null,"BranchName":null,"ServiceID":4,"DisplayName":"Mulch
        Install ","ServiceDescription":null,"OperationNotes":null,"Occur":10,"InvoiceType":"T&M","AsNeeded":false,"ComplexityPercent":0.00,"PerHours":0.00,"ExtendedHours":0.00,"PerPrice":0.00000,"PerPriceOverride":null,"ExtendedPrice":0.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":20.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":20.00,"EquipmentMarkup":20.00,"SubMarkup":20.00,"OtherMarkup":20.00,"NetProfitPercent":null,"LaborExtendedPrice":0.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":0.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":0.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":12,"MinimumPrice":0.00,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":0.00,"PerCost":0.00000,"OverridePrice":false,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":0.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":0.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":0.00,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":20.00,"TMSubMarkupPercent":20.00,"TMOtherMarkupPercent":20.00,"MasterOpportunityServiceID":null,"PerHoursOrig":0.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":0.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","CreatedDateTime":"2025-04-22T02:26:22.977Z","LastModifiedByUserID":22,"LastModifiedByUserName":"Michael
        Enrique","LastModifiedDateTime":"2025-04-22T02:32:09.437Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]},{"OpportunityServiceID":17,"OpportunityServiceGroupID":24,"BranchID":null,"BranchName":null,"ServiceID":5,"DisplayName":"Spring
        Clean up","ServiceDescription":null,"OperationNotes":null,"Occur":10,"InvoiceType":"Fixed
        Payment","AsNeeded":false,"ComplexityPercent":0.00,"PerHours":0.00,"ExtendedHours":0.00,"PerPrice":0.00000,"PerPriceOverride":2.00000,"ExtendedPrice":20.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":2,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":0.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":0.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":0.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":12,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":0.00,"PerCost":0.00000,"OverridePrice":true,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":0.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":0.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":0.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":0.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":22,"CreatedByUserName":"Michael
        Enrique","CreatedDateTime":"2025-04-22T02:26:31.21Z","LastModifiedByUserID":22,"LastModifiedByUserName":"Michael
        Enrique","LastModifiedDateTime":"2025-04-22T02:32:09.437Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityNumber%20eq%203&$limit=1000&$orderby=ModifiedDate&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:56 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185556Z-r166884f5bfdf4nhhC1CH1kc740000000gs000000000benx
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityID":2,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":12,"OperationsManagerContactName":"Arturo
        Archila","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":6,"LeadSourceName":"Cold
        Call","MasterOpportunityID":null,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":null,"OpportunityName":"Mary''s Project","ProposalDescription1":null,"StartDate":"2024-12-20T00:00:00Z","EndDate":null,"BidDueDate":null,"CreatedDateTime":"2024-12-10T04:23:59.68Z","ApprovedDate":"2024-12-10T04:28:36.05Z","ProposedDate":"2024-12-13T13:26:43.707Z","WonDate":"2024-12-13T13:26:43.707Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":27560.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":17,"ProposedUserName":"Lawrence
        Gebhardt","ClosedUserID":17,"ClosedUserName":"Lawrence Gebhardt","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":3,"EstimatedCostDollars":7304.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":7304.00,"EstimatedNetProfitPercent":73.50,"EstimatedNetProfitDollars":20256.00,"EstimatedGrossMarginPercent":73.50,"EstimatedGrossMarginDollars":20256.00,"EstimatedLaborHours":422.00,"EstimatedLaborCostPerHour":17.00,"EstimatedRealizeRate":65.31,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":7174.00,"EstimatedMaterialCost":130.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":2995.00,"ActualGrossMarginPercent":73.50,"ActualGrossMarginDollars":8305.96,"ActualLaborHours":130.00,"ActualLaborCostPerHour":23.04,"ActualEarnedRevenue":11300.96,"InvoiceNote":null,"PercentComplete":0.4100,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T14:10:37.5Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":2995.00,"CompleteDate":null,"OpportunityRevisions":[],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":8,"PropertyID":2,"OpportunityStatusID":15,"OpportunityStatusName":"Won","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":12,"OperationsManagerContactName":"Arturo
        Archila","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":6,"LeadSourceName":"Cold
        Call","MasterOpportunityID":2,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":1,"OpportunityName":"Mary''s Project","ProposalDescription1":null,"StartDate":"2024-12-20T00:00:00Z","EndDate":null,"BidDueDate":null,"CreatedDateTime":"2025-02-18T15:30:13.55Z","ApprovedDate":"2025-03-18T00:56:59.49Z","ProposedDate":"2025-03-18T00:57:07.617Z","WonDate":"2025-03-18T00:57:07.617Z","LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":200.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","ApprovedUserID":5,"ApprovedUserName":"David Franco","ProposedUserID":5,"ProposedUserName":"David
        Franco","ClosedUserID":5,"ClosedUserName":"David Franco","CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":3,"EstimatedCostDollars":0.00,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":200.00,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":200.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":0.00,"EstimatedRealizeRate":0.00,"EstimatedEquipmentCost":0.00,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":0.00,"EstimatedSubCost":0.00,"ActualCostDollars":null,"ActualGrossMarginPercent":null,"ActualGrossMarginDollars":null,"ActualLaborHours":null,"ActualLaborCostPerHour":null,"ActualEarnedRevenue":null,"InvoiceNote":null,"PercentComplete":0.4100,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T14:10:37.5Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":0.00,"TaxableAmount":0.00,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[{"OpportunityRevisionID":1,"RevisionNumber":1,"RevisionStatus":"Active","StartDate":"2024-12-20T00:00:00Z","CreatedDate":"2025-02-18T15:30:13.657Z","CreatedByUserId":17,"WonDate":"2025-03-18T00:57:07.62Z","WonByUserId":5}],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":9,"OpportunityStage":"Won","OpportunityStageName":"Won","OpportunityStatus":"Won"},{"OpportunityID":10,"PropertyID":2,"OpportunityStatusID":10,"OpportunityStatusName":"Bidding","SalesRepContactID":5,"SalesRepContactName":"David
        Franco","OperationsManagerContactID":12,"OperationsManagerContactName":"Arturo
        Archila","DivisionID":1,"DivisionName":"Enhancement","BranchID":3,"BranchName":"David
        Town","SalesTypeID":3,"SalesTypeName":"New Sale","TemplateOpportunityID":null,"LeadSourceID":6,"LeadSourceName":"Cold
        Call","MasterOpportunityID":2,"BillingAddressID":null,"BillingAddressLine1":null,"BillingAddressLine2":null,"BillingAddressCity":null,"BillingAddressStateProvinceCode":null,"BillingAddressZipCode":null,"OpportunityType":"Work
        Order","MasterSequenceNum":2,"OpportunityName":"Mary''s Project","ProposalDescription1":null,"StartDate":"2024-12-20T00:00:00Z","EndDate":null,"BidDueDate":null,"CreatedDateTime":"2025-04-09T16:06:25.807Z","ApprovedDate":null,"ProposedDate":null,"WonDate":null,"LostDate":null,"Probability":100.0000,"AnticipatedCloseDate":null,"InvoiceType":"Fixed
        Price on Completion","CustomerContractNum":null,"CustomerPONum":null,"BudgetedDollars":0.00,"EstimatedDollars":100.00,"StateTaxPercent":null,"FederalTaxPercent":null,"CreatedByUserID":9,"CreatedByUserName":"Zach
        Kemp","ApprovedUserID":null,"ApprovedUserName":null,"ProposedUserID":null,"ProposedUserName":null,"ClosedUserID":null,"ClosedUserName":null,"CompetitorID":null,"CompetitorName":null,"LostReason":null,"BillingContactID":null,"BillingContactName":null,"ProposalDescription2":null,"OpportunityNumber":3,"EstimatedCostDollars":null,"EstimatedOverheadDollars":0.00,"EstimatedBreakEvenDollars":0.00,"EstimatedNetProfitPercent":100.00,"EstimatedNetProfitDollars":null,"EstimatedGrossMarginPercent":100.00,"EstimatedGrossMarginDollars":100.00,"EstimatedLaborHours":0.00,"EstimatedLaborCostPerHour":null,"EstimatedRealizeRate":null,"EstimatedEquipmentCost":null,"EstimatedLaborCost":0.00,"EstimatedMaterialCost":0.00,"EstimatedOtherCost":null,"EstimatedSubCost":0.00,"ActualCostDollars":null,"ActualGrossMarginPercent":null,"ActualGrossMarginDollars":null,"ActualLaborHours":null,"ActualLaborCostPerHour":null,"ActualEarnedRevenue":null,"InvoiceNote":null,"PercentComplete":0.4100,"RenewalDate":null,"ModifiedByUserID":22,"ModifiedByUserName":"Michael
        Enrique","ModifiedDate":"2025-04-22T14:10:37.5Z","OverridePaymentTermsID":null,"OverridePaymentTerms":null,"RetainagePercent":null,"EstimatorNotes":null,"ElectronicPaymentsOverrideConvenienceFee":null,"OpportunityCanceledReasonID":null,"OpportunityCanceledReason":null,"RetainageMaturityDate":null,"DistrictName":null,"JobStatusName":"In
        Production","RegionName":null,"PaymentTermsID":null,"BillingCompanyID":null,"BillingCompanyName":null,"BranchCode":null,"PropertyName":"David''s
        House","DivisionCode":null,"TaxablePercent":null,"TaxableAmount":null,"TMPerServiceTaxableAmount":0.00,"ActualCostSub":0.00,"ActualCostMaterial":0.00,"ActualCostLabor":0.00,"CompleteDate":null,"OpportunityRevisions":[{"OpportunityRevisionID":2,"RevisionNumber":2,"RevisionStatus":"Pending","StartDate":"2024-12-20T00:00:00Z","CreatedDate":"2025-04-09T16:06:25.927Z","CreatedByUserId":9,"WonDate":null,"WonByUserId":null}],"OpportunityBillings":[],"ScheduleOfValueGroups":[],"OpportunityStageID":7,"OpportunityStage":"Estimate","OpportunityStageName":"Estimate","OpportunityStatus":"Bidding"}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTickets?$filter=OpportunityID%20in%20(2,8,10)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:57 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185556Z-r166884f5bf2nd6whC1CH1ahpg00000004r0000000002drk
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketID":161,"OpportunityServiceID":11,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2025-03-18T00:57:07.89Z","AnticStartDate":"2024-12-20T00:00:00Z","ScheduledStartDate":"2024-12-20T00:00:00Z","CompleteDate":null,"ApprovedDate":null,"WorkTicketStatusID":6,"WorkTicketStatusName":"Open","Notes":null,"CrewLeaderContactID":null,"CrewLeaderName":null,"HoursEst":0.0,"HourCostEst":0.0,"MaterialCostEst":0.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":200.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":null,"ApprovedUserName":null,"WorkTicketNumber":162,"InvoiceID":null,"HoursAct":null,"WarrantyHoursAct":null,"OTHoursAct":null,"LaborCostAct":null,"MaterialCostAct":null,"EquipmentCostAct":null,"SubCostAct":null,"OtherCostAct":null,"TotalCostAct":null,"EarnedRevenue":null,"RealizeRateRevenue":null,"OpportunityID":8,"OpportunityNumber":9.0,"EstRealizeRateRevenue":0.0,"DistributedHours":null,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":null,"LastModifiedByUserName":null,"LastModifiedDateTime":"2025-03-18T00:57:07.89Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","SubPartialOccurrence":100.0,"WorkTicketStatus":"Open","HoursScheduled":0.00,"HoursUnscheduled":0.00,"WorkTicketRevenues":[],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null},{"InvoiceNumber":null,"WorkTicketID":1,"OpportunityServiceID":2,"ContractYear":1,"Occur":1,"AnnualizedOccur":null,"CreatedDateTime":"2024-12-13T13:26:43.99Z","AnticStartDate":"2024-12-20T00:00:00Z","ScheduledStartDate":"2025-03-18T00:00:00Z","CompleteDate":null,"ApprovedDate":"2025-01-21T00:00:00Z","WorkTicketStatusID":8,"WorkTicketStatusName":"Scheduled","Notes":null,"CrewLeaderContactID":25,"CrewLeaderName":"John
        Franco","HoursEst":422.0,"HourCostEst":7174.0,"MaterialCostEst":130.0,"EquipCostEst":0.0,"SubCostEst":0.0,"OtherCostEst":0.0,"Price":27560.0,"TMCalcAmount":null,"TMOverrideAmount":null,"InvoicedAmount":null,"ApprovedUserID":17,"ApprovedUserName":"Lawrence
        Gebhardt","WorkTicketNumber":2,"InvoiceID":null,"HoursAct":130.0,"WarrantyHoursAct":0.0,"OTHoursAct":0.0,"LaborCostAct":2995.0,"MaterialCostAct":0.0,"EquipmentCostAct":0.0,"SubCostAct":0.0,"OtherCostAct":0.0,"TotalCostAct":2995.0,"EarnedRevenue":11300.96,"RealizeRateRevenue":86.93,"OpportunityID":2,"OpportunityNumber":3.0,"EstRealizeRateRevenue":65.0,"DistributedHours":0.0,"PartialOccurrence":100.0,"BranchID":3,"BranchName":"David
        Town","LastModifiedByUserID":16,"LastModifiedByUserName":"Armando Meza","LastModifiedDateTime":"2025-04-02T20:38:59.97Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","SubPartialOccurrence":100.0,"WorkTicketStatus":"Scheduled","HoursScheduled":844.00,"HoursUnscheduled":-422.00,"WorkTicketRevenues":[{"WorkTicketRevenueID":9,"RevenueMonth":"2024-12-01T00:00:00Z","RevenueAmount":11300.96,"CreatedDateTime":"2025-02-18T15:33:04.953Z","EditedByUserID":null,"EditedByUserName":null,"EditedDateTime":null,"AccountingPeriodID":null,"AccountingPeriodName":null,"AccountingPeriodYear":null}],"ReviewedDateTime":null,"ReviewedUserID":null,"ReviewedUserName":null,"StartFormDateTime":null,"StartFormUserId":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketItems?$filter=WorkTicketID%20in%20(161,1)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:57 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185557Z-1578d7644f687g6whC1CH12t7n0000000ha0000000007fzp
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"WorkTicketItemID":1,"CatalogItemID":5,"ItemName":"Labor - Enhancement","ItemType":"Labor","AllocationUnitTypeID":6,"AllocationUnitTypeName":"Hr","ItemQuantityExtended":422.00,"ItemCost":17.00000000,"ShowOnTicket":true,"CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","CreatedDateTime":"2024-12-13T13:26:44.1Z","CatalogItemCategoryID":4,"CatalogItemCategoryName":"Labor","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":1},{"WorkTicketItemID":2,"CatalogItemID":13,"ItemName":"Autumn
        Blaze Maple","ItemType":"Material","AllocationUnitTypeID":12,"AllocationUnitTypeName":"2\"
        B&B","ItemQuantityExtended":1.00,"ItemCost":130.00000000,"ShowOnTicket":true,"CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","CreatedDateTime":"2024-12-13T13:26:44.1Z","CatalogItemCategoryID":10,"CatalogItemCategoryName":"Tree","DoNotPurchase":false,"EstimatingNotes":null,"AutoExpense":false,"WorkTicketID":1}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ItemAllocations?$filter=WorkTicketID%20in%20(161,1)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:58 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185557Z-1578d7644f6t7r8dhC1CH16dbn0000000gg000000000b377
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/WorkTicketTimes?$filter=WorkTicketID%20in%20(161,1)&$limit=1000&$orderby=LastModifiedDateTime&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:58 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - 1.0,2.0
      X-Azure-Ref:
      - 20250428T185558Z-r166884f5bfc6gh4hC1CH1bwgg0000000h90000000009k3y
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"InvoiceNumber":null,"WorkTicketTimeID":1,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2024-12-13T08:00:00Z","EndTime":"2024-12-13T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":240.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:05:22.247Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:05:22.247Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":2,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2024-12-13T08:00:00Z","EndTime":"2024-12-13T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":176.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:05:22.263Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:05:22.263Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":3,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","WorkTicketTimeDate":null,"StartTime":"2024-12-13T08:00:00Z","EndTime":"2024-12-13T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":120.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:05:22.263Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:05:22.263Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":15.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":4,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2024-12-13T08:00:00Z","EndTime":"2024-12-13T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":144.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:05:22.263Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:05:22.263Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":5,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2024-12-14T08:00:00Z","EndTime":"2024-12-14T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":240.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:08:14.297Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:08:14.297Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":6,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2024-12-14T08:00:00Z","EndTime":"2024-12-14T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":176.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:08:14.3Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:08:14.3Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":7,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2024-12-14T08:00:00Z","EndTime":"2024-12-14T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":144.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:08:14.3Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:08:14.3Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":8,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","WorkTicketTimeDate":null,"StartTime":"2024-12-14T08:00:00Z","EndTime":"2024-12-14T15:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":7.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":105.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:08:14.3Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:08:14.3Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":15.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":9,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2024-12-16T08:00:00Z","EndTime":"2024-12-16T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":240.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:11:07.407Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:11:07.407Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":10,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2024-12-16T08:00:00Z","EndTime":"2024-12-16T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":176.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:11:07.41Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:11:07.41Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":11,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2024-12-16T08:00:00Z","EndTime":"2024-12-16T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":144.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:11:07.41Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:11:07.41Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":12,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2024-12-17T08:00:00Z","EndTime":"2024-12-17T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":176.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:12:39.023Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:12:39.023Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":13,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2024-12-17T08:00:00Z","EndTime":"2024-12-17T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":240.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:12:39.023Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:12:39.023Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":14,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2024-12-17T08:00:00Z","EndTime":"2024-12-17T14:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":6.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":108.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:12:39.023Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:12:39.023Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":18.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":15,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":9,"ContactName":"George
        Korsnick","WorkTicketTimeDate":null,"StartTime":"2024-12-18T08:00:00Z","EndTime":"2024-12-18T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":176.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:13:16.447Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:13:16.447Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":22.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":16,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2024-12-18T08:00:00Z","EndTime":"2024-12-18T16:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":8.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":240.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:13:16.45Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:13:16.45Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":17,"WorkTicketID":1,"WorkTicketNumber":2,"ContactID":5,"ContactName":"David
        Franco","WorkTicketTimeDate":null,"StartTime":"2024-12-19T08:00:00Z","EndTime":"2024-12-19T13:00:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":5.0,"OTHours":null,"WarrantyTime":false,"DistributedTime":null,"BurdenedCost":150.0,"InvoiceID":null,"CreatedDateTime":"2024-12-13T14:14:07.16Z","CreatedByUserID":17,"CreatedByUserName":"Lawrence
        Gebhardt","LastModifiedDateTime":null,"LastModifiedByUserID":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","AcceptedDateTime":"2024-12-13T14:14:07.16Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":0.0,"HasBreakTime":false,"BaseHourlyRate":30.0,"RouteID":4,"RouteName":"Georges
        Team 2","CrewLeaderContactID":9,"CrewLeaderContactName":"George Korsnick","BranchID":3,"BranchName":"David
        Town","BranchTimeZone":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"InvoiceNumber":null,"WorkTicketTimeID":37,"WorkTicketID":161,"WorkTicketNumber":162,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-04-08T00:00:00Z","EndTime":"2025-04-08T19:44:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":19.73,"OTHours":null,"WarrantyTime":false,"DistributedTime":false,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-04-22T19:44:24.58Z","CreatedByUserID":18,"CreatedByUserName":"Rails
        Tests","LastModifiedDateTime":null,"LastModifiedByUserID":18,"LastModifiedByUserName":"Rails
        Tests","AcceptedDateTime":null,"AcceptedUserID":null,"AcceptedUserName":null,"ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":null,"HasBreakTime":null,"BaseHourlyRate":null,"RouteID":null,"RouteName":null,"CrewLeaderContactID":5,"CrewLeaderContactName":"David
        Franco","BranchID":3,"BranchName":"David Town","BranchTimeZone":null,"GEOLocationStartLatitude":0.0,"GEOLocationStartLongitude":0.0,"GEOLocationEndLatitude":0.0,"GEOLocationEndLongitude":0.0},{"InvoiceNumber":null,"WorkTicketTimeID":33,"WorkTicketID":161,"WorkTicketNumber":162,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-04-22T16:26:00Z","EndTime":"2025-04-22T19:25:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.98,"OTHours":null,"WarrantyTime":false,"DistributedTime":false,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-04-22T19:30:09.57Z","CreatedByUserID":18,"CreatedByUserName":"Rails
        Tests","LastModifiedDateTime":"2025-04-22T00:00:00Z","LastModifiedByUserID":18,"LastModifiedByUserName":"Rails
        Tests","AcceptedDateTime":null,"AcceptedUserID":null,"AcceptedUserName":null,"ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":null,"HasBreakTime":null,"BaseHourlyRate":null,"RouteID":null,"RouteName":null,"CrewLeaderContactID":5,"CrewLeaderContactName":"David
        Franco","BranchID":3,"BranchName":"David Town","BranchTimeZone":null,"GEOLocationStartLatitude":0.0,"GEOLocationStartLongitude":0.0,"GEOLocationEndLatitude":0.0,"GEOLocationEndLongitude":0.0},{"InvoiceNumber":null,"WorkTicketTimeID":34,"WorkTicketID":161,"WorkTicketNumber":162,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-04-22T16:26:00Z","EndTime":"2025-04-22T19:25:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.98,"OTHours":null,"WarrantyTime":false,"DistributedTime":false,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-04-22T19:32:27.47Z","CreatedByUserID":18,"CreatedByUserName":"Rails
        Tests","LastModifiedDateTime":"2025-04-22T00:00:00Z","LastModifiedByUserID":18,"LastModifiedByUserName":"Rails
        Tests","AcceptedDateTime":null,"AcceptedUserID":null,"AcceptedUserName":null,"ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":null,"HasBreakTime":null,"BaseHourlyRate":null,"RouteID":null,"RouteName":null,"CrewLeaderContactID":5,"CrewLeaderContactName":"David
        Franco","BranchID":3,"BranchName":"David Town","BranchTimeZone":null,"GEOLocationStartLatitude":0.0,"GEOLocationStartLongitude":0.0,"GEOLocationEndLatitude":0.0,"GEOLocationEndLongitude":0.0},{"InvoiceNumber":null,"WorkTicketTimeID":35,"WorkTicketID":161,"WorkTicketNumber":162,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-04-22T16:26:00Z","EndTime":"2025-04-22T19:25:00Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":2.98,"OTHours":null,"WarrantyTime":false,"DistributedTime":false,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-04-22T19:40:24.61Z","CreatedByUserID":18,"CreatedByUserName":"Rails
        Tests","LastModifiedDateTime":"2025-04-22T00:00:00Z","LastModifiedByUserID":18,"LastModifiedByUserName":"Rails
        Tests","AcceptedDateTime":null,"AcceptedUserID":null,"AcceptedUserName":null,"ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":null,"HasBreakTime":null,"BaseHourlyRate":null,"RouteID":null,"RouteName":null,"CrewLeaderContactID":5,"CrewLeaderContactName":"David
        Franco","BranchID":3,"BranchName":"David Town","BranchTimeZone":null,"GEOLocationStartLatitude":0.0,"GEOLocationStartLongitude":0.0,"GEOLocationEndLatitude":0.0,"GEOLocationEndLongitude":0.0},{"InvoiceNumber":null,"WorkTicketTimeID":36,"WorkTicketID":161,"WorkTicketNumber":162,"ContactID":10,"ContactName":"Zach
        Kemp","WorkTicketTimeDate":null,"StartTime":"2025-04-07T19:44:00Z","EndTime":"2025-04-07T23:59:59.997Z","OpportunityServiceLaborRateID":null,"OpportunityServiceLaborRateName":null,"OpportunityServiceLaborRate":null,"PayCodeID":null,"PayCodeName":null,"Hours":4.25,"OTHours":null,"WarrantyTime":false,"DistributedTime":false,"BurdenedCost":null,"InvoiceID":null,"CreatedDateTime":"2025-04-22T19:44:24.557Z","CreatedByUserID":18,"CreatedByUserName":"Rails
        Tests","LastModifiedDateTime":"2025-04-22T00:00:00Z","LastModifiedByUserID":18,"LastModifiedByUserName":"Rails
        Tests","AcceptedDateTime":null,"AcceptedUserID":null,"AcceptedUserName":null,"ApprovedDateTime":null,"ApprovedUserID":null,"ApprovedUserName":null,"ExportedDateTime":null,"ExportedUserID":null,"ExportedUserName":null,"BreakTime":null,"HasBreakTime":null,"BaseHourlyRate":null,"RouteID":null,"RouteName":null,"CrewLeaderContactID":5,"CrewLeaderContactName":"David
        Franco","BranchID":3,"BranchName":"David Town","BranchTimeZone":null,"GEOLocationStartLatitude":0.0,"GEOLocationStartLongitude":0.0,"GEOLocationEndLatitude":0.0,"GEOLocationEndLongitude":0.0}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/OpportunityServices?$filter=OpportunityServiceID%20in%20(11,2)&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:59 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185558Z-r166884f5bf9n49rhC1CH15ar40000000gqg00000000e9m6
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"OpportunityServiceID":2,"OpportunityServiceGroupID":4,"BranchID":null,"BranchName":null,"ServiceID":2,"DisplayName":"Plant
        Installation","ServiceDescription":null,"OperationNotes":null,"Occur":null,"InvoiceType":"Fixed
        Price on Completion","AsNeeded":false,"ComplexityPercent":0.00,"PerHours":400.00,"ExtendedHours":400.00,"PerPrice":26000.00000,"PerPriceOverride":null,"ExtendedPrice":26000.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":26000.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":6800.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":26000.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":2,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":6800.00,"PerCost":6800.00000,"OverridePrice":false,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":6800.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":6800.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":19203.82,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":400.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":null,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":400.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2024-12-10T04:24:18.98Z","LastModifiedByUserID":16,"LastModifiedByUserName":"Armando
        Meza","LastModifiedDateTime":"2025-04-02T20:38:59.98Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]},{"OpportunityServiceID":11,"OpportunityServiceGroupID":16,"BranchID":null,"BranchName":null,"ServiceID":1,"DisplayName":"Mulch","ServiceDescription":null,"OperationNotes":null,"Occur":null,"InvoiceType":"Fixed
        Price on Completion","AsNeeded":false,"ComplexityPercent":20.00,"PerHours":0.00,"ExtendedHours":0.00,"PerPrice":0.00000,"PerPriceOverride":200.00000,"ExtendedPrice":200.00,"LaborTaxable":false,"MaterialTaxable":false,"EquipmentTaxable":false,"SubTaxable":false,"OtherTaxable":false,"SortOrder":1,"TMMaterialMarkupPercent":0.00,"RoundPrice":false,"OverridePricing":false,"LaborMarkup":0.00,"MaterialMarkup":null,"EquipmentMarkup":null,"SubMarkup":null,"OtherMarkup":null,"NetProfitPercent":null,"LaborExtendedPrice":0.00,"MaterialExtendedPrice":0.00,"EquipmentExtendedPrice":0.00,"SubExtendedPrice":0.00,"OtherExtendedPrice":0.00,"TaxableExtendedPrice":0.00,"LaborPerExtendedCost":0.00,"MaterialPerExtendedCost":0.00,"EquipmentPerExtendedCost":0.00,"SubPerExtendedCost":0.00,"OtherPerExtendedCost":0.00,"LaborPerExtendedPrice":0.00,"MaterialPerExtendedPrice":0.00,"EquipmentPerExtendedPrice":0.00,"SubPerExtendedPrice":0.00,"OtherPerExtendedPrice":0.00,"TaxablePerExtendedPrice":0.00,"OpportunityID":8,"MinimumPrice":null,"MinimumPriceApplied":false,"Overhead":0.00,"BreakEven":0.00,"PerCost":0.00000,"OverridePrice":true,"SeparateWorkTicket":false,"DefaultPayCodeID":null,"LaborExtendedCost":0.00,"MaterialExtendedCost":0.00,"EquipmentExtendedCost":0.00,"SubExtendedCost":0.00,"OtherExtendedCost":0.00,"ExtendedCost":0.00,"LaborOverhead":0.00,"MaterialOverhead":0.00,"EquipmentOverhead":0.00,"SubOverhead":0.00,"OtherOverhead":0.00,"LaborProfit":0.00,"MaterialProfit":0.00,"EquipmentProfit":0.00,"SubProfit":0.00,"OtherProfit":0.00,"TMMinimumHours":null,"ServiceNameAbrOverride":null,"PerVisitHours":0.00,"PerVisitMaterialsQty":0.00,"TMEquipmentMarkupPercent":0.00,"TMSubMarkupPercent":0.00,"TMOtherMarkupPercent":0.00,"MasterOpportunityServiceID":null,"PerHoursOrig":0.00,"OpportunityServiceStatus":"Active","AddedOpportunityRevisionID":1,"RemovedOpportunityRevisionID":null,"ChildOpportunityServiceID":null,"ParentOpportunityServiceID":null,"RevisionStartWorkTicketID":null,"DefaultSequenceNumber":null,"ServiceItemQuantity":0.00,"ChangeOrderOpportunityServiceID":null,"CreatedByUserID":5,"CreatedByUserName":"David
        Franco","CreatedDateTime":"2025-03-18T00:56:40.853Z","LastModifiedByUserID":5,"LastModifiedByUserName":"David
        Franco","LastModifiedDateTime":"2025-03-18T00:57:07.62Z","OpportunityServiceRoutes":[],"OpportunityServiceDefaultPayCodes":[]}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202025-02-14T00:00:00.000000Z%20and%20ClockStart%20le%202025-03-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:55:59 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185559Z-r166884f5bfsd9b7hC1CH10f380000000h30000000003gvh
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ClockTimeID":18,"ContactID":5,"ContactName":"David Franco","ClockStart":"2025-03-04T12:30:00Z","ClockEnd":"2025-03-04T15:30:00Z","AcceptedDateTime":"2025-03-04T14:55:24.777Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":19,"ContactID":9,"ContactName":"George
        Korsnick","ClockStart":"2025-03-04T12:30:00Z","ClockEnd":"2025-03-04T15:30:00Z","AcceptedDateTime":"2025-03-04T14:55:24.837Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":20,"ContactID":10,"ContactName":"Zach
        Kemp","ClockStart":"2025-03-04T12:30:00Z","ClockEnd":"2025-03-04T15:30:00Z","AcceptedDateTime":"2025-03-04T14:55:24.84Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":21,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","ClockStart":"2025-03-04T12:30:00Z","ClockEnd":"2025-03-04T15:30:00Z","AcceptedDateTime":"2025-03-04T14:55:24.847Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202025-01-14T00:00:00.000000Z%20and%20ClockStart%20le%202025-02-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:00 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185600Z-1578d7644f6xdtbnhC1CH19gvc0000000gr000000000an1q
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-12-14T00:00:00.000000Z%20and%20ClockStart%20le%202025-01-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:01 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185600Z-1578d7644f66df4lhC1CH1t9ps0000000gcg00000000c8e9
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ClockTimeID":5,"ContactID":5,"ContactName":"David Franco","ClockStart":"2024-12-14T08:00:00Z","ClockEnd":"2024-12-14T16:00:00Z","AcceptedDateTime":"2024-12-13T14:08:14.287Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":6,"ContactID":9,"ContactName":"George
        Korsnick","ClockStart":"2024-12-14T08:00:00Z","ClockEnd":"2024-12-14T16:00:00Z","AcceptedDateTime":"2024-12-13T14:08:14.29Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":7,"ContactID":10,"ContactName":"Zach
        Kemp","ClockStart":"2024-12-14T08:00:00Z","ClockEnd":"2024-12-14T16:00:00Z","AcceptedDateTime":"2024-12-13T14:08:14.29Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":8,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","ClockStart":"2024-12-14T08:00:00Z","ClockEnd":"2024-12-14T15:00:00Z","AcceptedDateTime":"2024-12-13T14:08:14.29Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":9,"ContactID":5,"ContactName":"David
        Franco","ClockStart":"2024-12-16T08:00:00Z","ClockEnd":"2024-12-16T16:00:00Z","AcceptedDateTime":"2024-12-13T14:11:07.397Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":10,"ContactID":9,"ContactName":"George
        Korsnick","ClockStart":"2024-12-16T08:00:00Z","ClockEnd":"2024-12-16T16:00:00Z","AcceptedDateTime":"2024-12-13T14:11:07.4Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":11,"ContactID":10,"ContactName":"Zach
        Kemp","ClockStart":"2024-12-16T08:00:00Z","ClockEnd":"2024-12-16T16:00:00Z","AcceptedDateTime":"2024-12-13T14:11:07.4Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":12,"ContactID":5,"ContactName":"David
        Franco","ClockStart":"2024-12-17T08:00:00Z","ClockEnd":"2024-12-17T16:00:00Z","AcceptedDateTime":"2024-12-13T14:12:39.017Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":13,"ContactID":9,"ContactName":"George
        Korsnick","ClockStart":"2024-12-17T08:00:00Z","ClockEnd":"2024-12-17T16:00:00Z","AcceptedDateTime":"2024-12-13T14:12:39.017Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":14,"ContactID":10,"ContactName":"Zach
        Kemp","ClockStart":"2024-12-17T08:00:00Z","ClockEnd":"2024-12-17T14:00:00Z","AcceptedDateTime":"2024-12-13T14:12:39.02Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":15,"ContactID":5,"ContactName":"David
        Franco","ClockStart":"2024-12-18T08:00:00Z","ClockEnd":"2024-12-18T16:00:00Z","AcceptedDateTime":"2024-12-13T14:13:16.44Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":16,"ContactID":9,"ContactName":"George
        Korsnick","ClockStart":"2024-12-18T08:00:00Z","ClockEnd":"2024-12-18T16:00:00Z","AcceptedDateTime":"2024-12-13T14:13:16.44Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":17,"ContactID":5,"ContactName":"David
        Franco","ClockStart":"2024-12-19T08:00:00Z","ClockEnd":"2024-12-19T13:00:00Z","AcceptedDateTime":"2024-12-13T14:14:07.157Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-11-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-12-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:01 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185601Z-r166884f5bfxn5mlhC1CH1014c0000000ha000000000769f
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ClockTimeID":1,"ContactID":5,"ContactName":"David Franco","ClockStart":"2024-12-13T08:00:00Z","ClockEnd":"2024-12-13T16:00:00Z","AcceptedDateTime":"2024-12-13T14:05:22.103Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":2,"ContactID":9,"ContactName":"George
        Korsnick","ClockStart":"2024-12-13T08:00:00Z","ClockEnd":"2024-12-13T16:00:00Z","AcceptedDateTime":"2024-12-13T14:05:22.13Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":3,"ContactID":10,"ContactName":"Zach
        Kemp","ClockStart":"2024-12-13T08:00:00Z","ClockEnd":"2024-12-13T16:00:00Z","AcceptedDateTime":"2024-12-13T14:05:22.13Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null},{"ClockTimeID":4,"ContactID":18,"ContactName":"Lawrence
        Gebhardt","ClockStart":"2024-12-13T08:00:00Z","ClockEnd":"2024-12-13T16:00:00Z","AcceptedDateTime":"2024-12-13T14:05:22.13Z","AcceptedUserID":17,"AcceptedUserName":"Lawrence
        Gebhardt","BreakTime":0.0,"AcceptedShortLunch":null,"UsedBreaks":null,"PreventedFromUsingBreaks":null,"GEOLocationStartLatitude":null,"GEOLocationStartLongitude":null,"GEOLocationEndLatitude":null,"GEOLocationEndLongitude":null}]'
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-10-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-11-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:02 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185602Z-r166884f5bfc6gh4hC1CH1bwgg0000000h90000000009kds
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-09-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-10-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:02 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185602Z-r166884f5bfnxvgrhC1CH1f8r80000000gtg00000000byr2
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-08-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-09-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:03 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185602Z-r166884f5bf9w2fwhC1CH1q3w800000009d000000000765r
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-07-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-08-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:03 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185603Z-1578d7644f67wcqnhC1CH1sg9w0000000geg00000000d4xf
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-06-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-07-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:04 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185603Z-1578d7644f6l26bnhC1CH1tqgw0000000gtg00000000a2t4
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-05-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-06-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:04 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185604Z-r166884f5bfcl2g4hC1CH1tukc0000000gxg00000000dp31
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-04-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-05-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:04 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185604Z-r166884f5bf49nb7hC1CH1unys0000000gfg00000000cugf
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-03-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-04-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:05 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185605Z-1578d7644f6xdtbnhC1CH19gvc0000000gqg00000000c4rp
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/ClockTimes?$filter=ClockStart%20ge%202024-02-14T00:00:00.000000Z%20and%20ClockStart%20le%202024-03-14T00:00:00.000000Z&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Apr 2025 18:56:05 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250428T185605Z-1578d7644f6f4s4mhC1CH13ngw000000059g00000000azhb
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Opportunities?$filter=OpportunityStatus%20eq%20%27Won%27%20and%20JobStatusName%20ne%20%27Complete%27%20and%20JobStatusName%20ne%20%27Canceled%27%20and%20ModifiedDate%20ge%202025-01-15T15:00:12.000000Z&$select=OpportunityID&$top=500
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 14 Feb 2025 15:00:12 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250214T150012Z-17c69d65b68kch8rhC1PDXy1b40000000a5000000000av8c
      Strict-Transport-Security:
      - max-age=********; includeSubDomainsmax-age=********; includeSubDomains
      X-Frame-Options:
      - SAMEORIGINSAMEORIGIN
      X-Content-Type-Options:
      - nosniffnosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;frame-ancestors 'self'
        *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: "[]"
  recorded_at: Fri, 14 Feb 2025 15:00:12 GMT
recorded_with: VCR 6.3.1
