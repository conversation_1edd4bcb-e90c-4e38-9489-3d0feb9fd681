---
http_interactions:
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Users?$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 14 Feb 2025 15:29:38 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:127c4249-5655-4ee9-8915-07b941ab6ea2
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250214T152937Z-1646ddf8c5fc8k9jhC1CH1f05c0000000c1g00000000axr8
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Frame-Options:
      - SAMEORIGIN
      X-Content-Type-Options:
      - nosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"UserID":4,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Michael","ContactLastName":"Fortinberry","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":5,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"David","ContactLastName":"Franco","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":6,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Test","ContactLastName":"API
        for protiv","Active":true,"UserRoles":[]},{"UserID":8,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"George","ContactLastName":"Korsnick","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":9,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Zach","ContactLastName":"Kemp","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":10,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"tilde","ContactLastName":"api
        test","Active":true,"UserRoles":[]},{"UserID":11,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Arturo","ContactLastName":"Archila","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":12,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Joe","ContactLastName":"Cruz","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":13,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Claudia","ContactLastName":"Alas","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":14,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Itzel","ContactLastName":"Zeledon","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":15,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Protiv","ContactLastName":"[
        JOE ]","Active":true,"UserRoles":[]},{"UserID":16,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Armando","ContactLastName":"Meza","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":17,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Lawrence","ContactLastName":"Gebhardt","Active":true,"UserRoles":[{"RoleID":14,"RoleName":"System
        Admin"}]},{"UserID":18,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Rails","ContactLastName":"Tests","Active":true,"UserRoles":[]},{"UserID":19,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Protiv","ContactLastName":"Itzel
        Test","Active":true,"UserRoles":[]},{"UserID":20,"AllBranchAccess":true,"BranchAccess":[],"ExternalContactReference":null,"ContactFirstName":"Umesh","ContactLastName":"Nikam","Active":true,"UserRoles":[{"RoleID":8,"RoleName":"Account
        Manager"},{"RoleID":15,"RoleName":"Crew Leader"}]}]'
  recorded_at: Fri, 14 Feb 2025 15:29:38 GMT
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Contacts?$filter=ContactTypeName%20eq%20%27Sub%27%20or%20ContactTypeName%20eq%20%27Employee%27&$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 14 Feb 2025 15:29:38 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:127c4249-5655-4ee9-8915-07b941ab6ea2
      Api-Supported-Versions:
      - '1.0'
      X-Azure-Ref:
      - 20250214T152938Z-er169b7d4564cncfhC1CH1trt40000000ft000000000fu7t
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Frame-Options:
      - SAMEORIGIN
      X-Content-Type-Options:
      - nosniff
      Content-Security-Policy:
      - frame-ancestors 'self' *.youraspire.com *.pendo.io;
      X-Cache:
      - CONFIG_NOCACHE
    body:
      encoding: ASCII-8BIT
      string: '[{"ContactID":3,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":null,"HomeAddress":null,"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Aspire","LastName":"Admin","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":null,"CreatedByUserID":null,"CreatedByUserName":null,"EmployeeNumber":null,"Website":null,"EmployeePin":null,"AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":3,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":null,"LastModifiedByUserName":null,"ModifiedDate":null,"ContactTags":[],"ContactSignature":null},{"ContactID":4,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":3,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T12:58:05.397Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":2,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T12:58:05.197Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Michael","LastName":"Fortinberry","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-04-09T12:58:05.433Z","CreatedByUserID":3,"CreatedByUserName":"Aspire
        Admin","EmployeeNumber":null,"Website":null,"EmployeePin":"mfortinberry","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":4,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":3,"LastModifiedByUserName":"Aspire
        Admin","ModifiedDate":"2024-04-09T12:59:52.927Z","ContactTags":[],"ContactSignature":null},{"ContactID":5,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":5,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T13:00:29.747Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":4,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":3,"CreatedByUserName":"Aspire
        Admin","CreatedOn":"2024-04-09T13:00:29.743Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"David","LastName":"Franco","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-04-09T13:00:29.733Z","CreatedByUserID":3,"CreatedByUserName":"Aspire
        Admin","EmployeeNumber":null,"Website":null,"EmployeePin":"dfranco","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":5,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2024-12-13T13:48:32.213Z","ContactTags":[],"ContactSignature":null},{"ContactID":7,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":7,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-05-13T11:39:07.793Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":6,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-05-13T11:39:07.783Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":"Mr.","FirstName":"Umesh","LastName":"Nikam","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-05-13T11:39:07.79Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"8888","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":20,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2025-02-10T19:29:29.223Z","ContactTags":[],"ContactSignature":null},{"ContactID":9,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":10,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:48:40.323Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":9,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:48:40.313Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"George","LastName":"Korsnick","ProspectRating":null,"ProspectRatingName":null,"Title":"CTO","Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-01T18:48:40.323Z","CreatedByUserID":4,"CreatedByUserName":"Michael
        Fortinberry","EmployeeNumber":null,"Website":null,"EmployeePin":"gkorsnick","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":8,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2024-12-13T13:48:45.29Z","ContactTags":[],"ContactSignature":null},{"ContactID":10,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":12,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:50:07.14Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":11,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":4,"CreatedByUserName":"Michael
        Fortinberry","CreatedOn":"2024-11-01T18:50:07.137Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Zach","LastName":"Kemp","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-01T18:50:07.137Z","CreatedByUserID":4,"CreatedByUserName":"Michael
        Fortinberry","EmployeeNumber":null,"Website":null,"EmployeePin":"zkemp","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":9,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2024-12-13T13:49:00.247Z","ContactTags":[],"ContactSignature":null},{"ContactID":12,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":14,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-11T18:56:31.187Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":13,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-11T18:56:31.177Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Arturo","LastName":"Archila","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-11T18:56:31.233Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"1223","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":11,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-11T18:57:51.123Z","ContactTags":[],"ContactSignature":null},{"ContactID":13,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":16,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:06:58.783Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":15,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:06:58.777Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Claudia","LastName":"Alas","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-20T21:06:58.83Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"3434","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":13,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-20T21:12:23.797Z","ContactTags":[],"ContactSignature":null},{"ContactID":14,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":18,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:07:28.727Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":17,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:07:28.723Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Joe","LastName":"Cruz","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-20T21:07:28.727Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"7526","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":12,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":12,"LastModifiedByUserName":"Joe
        Cruz","ModifiedDate":"2024-11-20T21:18:29.92Z","ContactTags":[],"ContactSignature":null},{"ContactID":15,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":20,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:08:09.46Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":19,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-20T21:08:09.457Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Itzel","LastName":"Zeledon","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-20T21:08:09.46Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"9999","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":14,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-20T21:11:23.97Z","ContactTags":[],"ContactSignature":null},{"ContactID":17,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":22,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-22T13:44:30.457Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":21,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-11-22T13:44:30.41Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":2,"BranchName":"Main","OwnerContactID":null,"OwnerContactName":null,"Salutation":null,"FirstName":"Armando","LastName":"Meza","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-11-22T13:44:30.543Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"6666","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":16,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":5,"LastModifiedByUserName":"David
        Franco","ModifiedDate":"2024-11-22T13:45:23.297Z","ContactTags":[],"ContactSignature":null},{"ContactID":18,"CompanyID":null,"CompanyName":null,"ContactTypeID":7,"ContactTypeName":"Employee","OfficeAddress":{"AddressID":25,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-12-12T13:47:37.913Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"HomeAddress":{"AddressID":24,"AddressLine1":null,"AddressLine2":null,"City":null,"StateProvinceCode":null,"ZipCode":null,"CreatedByUserId":5,"CreatedByUserName":"David
        Franco","CreatedOn":"2024-12-12T13:47:37.907Z","LastModifiedByUserId":null,"LastModifiedByUserName":null,"LastModifiedOn":null},"BranchID":null,"BranchName":null,"OwnerContactID":null,"OwnerContactName":null,"Salutation":"Mr.","FirstName":"Lawrence","LastName":"Gebhardt","ProspectRating":null,"ProspectRatingName":null,"Title":null,"Email":"<EMAIL>","MobilePhone":null,"OfficePhone":null,"HomePhone":null,"Fax":null,"Notes":null,"Active":true,"CreatedDateTime":"2024-12-12T13:47:37.96Z","CreatedByUserID":5,"CreatedByUserName":"David
        Franco","EmployeeNumber":null,"Website":null,"EmployeePin":"8989","AccountingSyncID":null,"ExternalContactReference":null,"TerminationDate":null,"HRNotes":null,"DefaultWorkersCompID":null,"DefaultWorkersCompName":null,"DefaultWorkersCompStateProvinceCode":null,"UserID":17,"CrewSize":null,"PromptForCrewSize":null,"SubCrewLeaderContactID":null,"PayScheduleID":null,"LastModifiedByUserId":17,"LastModifiedByUserName":"Lawrence
        Gebhardt","ModifiedDate":"2024-12-16T19:36:47.567Z","ContactTags":[],"ContactSignature":null}]'
  recorded_at: Fri, 14 Feb 2025 15:29:38 GMT
recorded_with: VCR 6.3.1
