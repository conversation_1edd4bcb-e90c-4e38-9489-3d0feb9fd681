---
http_interactions:
- request:
    method: get
    uri: https://cloud-api.youraspire.com/Branches?$limit=1000&$pageNumber=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer FAKE_ASPIRE_TOKEN
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Transfer-Encoding:
      - chunked
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept-Encoding
      Request-Context:
      - appId=cid-v1:6cb97b58-7917-425d-8de6-d220a808c882
      Date:
      - Thu, 12 Dec 2024 20:02:11 GMT
    body:
      encoding: ASCII-8BIT
      string: '[{"BranchID":2,"BranchName":"Main","Active":true,"InternalPropertyID":null,"InternalPropertyName":null,"CatalogPriceListName":null,"BranchAddressID":null,"BranchAddressLine1":null,"BranchAddressLine2":null,"BranchAddressCity":null,"BranchAddressStateProvinceCode":null,"BranchAddressZipCode":null,"BranchPhone":null,"BranchFax":null,"BranchCode":null,"BranchManagerContactID":null,"BranchManagerContactName":null,"LegalName":null,"TimeZone":null,"InvoiceNumberPrefix":null,"ReceiptNumberPrefix":null,"OpportunityNumberPrefix":null,"RegionID":null,"RegionName":null,"BillingEmailFromAccountOwner":false,"BillingEmailFromUserName":null,"BillingEmailFromEmail":null,"BillingEmailSubject":null,"BillingEmailBody":null,"InvoiceOnCompletionDescription":null,"BranchWebSite":null,"CatalogPriceListID":null,"BillingEmailCC":null},{"BranchID":3,"BranchName":"David
        Town","Active":true,"InternalPropertyID":1,"InternalPropertyName":"Test Propery","CatalogPriceListName":null,"BranchAddressID":null,"BranchAddressLine1":null,"BranchAddressLine2":null,"BranchAddressCity":null,"BranchAddressStateProvinceCode":null,"BranchAddressZipCode":null,"BranchPhone":null,"BranchFax":null,"BranchCode":null,"BranchManagerContactID":null,"BranchManagerContactName":null,"LegalName":null,"TimeZone":null,"InvoiceNumberPrefix":null,"ReceiptNumberPrefix":null,"OpportunityNumberPrefix":null,"RegionID":null,"RegionName":null,"BillingEmailFromAccountOwner":true,"BillingEmailFromUserName":null,"BillingEmailFromEmail":null,"BillingEmailSubject":null,"BillingEmailBody":null,"InvoiceOnCompletionDescription":null,"BranchWebSite":null,"CatalogPriceListID":null,"BillingEmailCC":null}]'
  recorded_at: Thu, 12 Dec 2024 20:02:11 GMT
recorded_with: VCR 6.3.1
