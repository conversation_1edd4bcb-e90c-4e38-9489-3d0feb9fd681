# frozen_string_literal: true

FactoryBot.define do
  factory :sub_pro_pay do
    pro_pay

    factory :job_sub_pro_pay do
      transient do
        job { create(:job_with_milestones, milestone_count: 3) }
        organization { job.organization }
        pro_pay { create(:pro_pay, organization: job.organization, manager: organization.identities.last) }
      end

      before(:create) do |sub_pro_pay, context|
        sub_pro_pay.pro_pay = context.pro_pay
      end

      after(:create) do |sub_pro_pay, context|
        create(:sub_pro_pay_pro_payable, pro_payable: context.job, sub_pro_pay: sub_pro_pay)
      end
    end

    factory :job_sub_pro_pay_with_time_data do
      transient do
        job { create(:job_with_milestone_and_full_time_data) }
        organization { job.organization }
        pro_pay { create(:pro_pay, organization: job.organization, manager: organization.identities.last) }
      end

      before(:create) do |sub_pro_pay, context|
        sub_pro_pay.pro_pay = context.pro_pay
      end

      after(:create) do |sub_pro_pay, context|
        create(:sub_pro_pay_pro_payable, pro_payable: context.job, sub_pro_pay: sub_pro_pay)
      end
    end

    factory :single_milestone_sub_pro_pay do
      transient do
        job { create(:job_with_milestones) }
        organization { job.organization }
        pro_pay { create(:pro_pay, organization: job.organization, manager: organization.identities.last) }
      end

      before(:create) do |sub_pro_pay, context|
        sub_pro_pay.pro_pay = context.pro_pay
      end

      after(:create) do |sub_pro_pay, context|
        create(:sub_pro_pay_pro_payable, pro_payable: context.job.milestones.first, sub_pro_pay: sub_pro_pay)
      end
    end

    factory :single_milestone_sub_pro_pay_with_time_data do
      transient do
        job { create(:job_with_milestone_and_full_time_data) }
        organization { job.organization }
        pro_pay { create(:pro_pay, organization: job.organization, manager: organization.identities.last) }
      end

      before(:create) do |sub_pro_pay, context|
        sub_pro_pay.pro_pay = context.pro_pay
      end

      after(:create) do |sub_pro_pay, context|
        create(:sub_pro_pay_pro_payable, pro_payable: context.job.milestones.first, sub_pro_pay: sub_pro_pay)
      end
    end
  end
end
