# frozen_string_literal: true

FactoryBot.define do
  factory :labor_efficiency_benchmark do
    association :organization
    level { "organization" }
    level_record_id { nil } # Only used for non-organization levels
    target_percentage { 75.0 }
    active { true }

    # Make factory idempotent using find_or_create_by for organization level benchmarks
    to_create do |instance|
      if instance.level == "organization" && instance.level_record_id.nil?
        # For organization level, find existing or create new
        existing = LaborEfficiencyBenchmark.find_by(
          organization: instance.organization,
          level: instance.level,
          level_record_id: instance.level_record_id
        )

        if existing
          # Update existing with new attributes and return it
          existing.update!(
            target_percentage: instance.target_percentage,
            active: instance.active
          )
          # Replace the instance with the existing one
          instance.id = existing.id
          instance.reload
        else
          # Create new benchmark
          instance.save!
        end
      else
        # For non-organization levels, create normally
        instance.save!
      end
    end
  end
end
