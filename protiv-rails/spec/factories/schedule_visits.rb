# frozen_string_literal: true

FactoryBot.define do
  factory :schedule_visit do
    organization { create(:organization) }
    route { create(:route) }
    milestone { create(:milestone) }
    hours { 4.5 }
    scheduled_date { Time.zone.now }
    remote_id { nil }

    trait :m44_visit do
      route { create(:route, :m44) }
      milestone { create(:milestone, :lawn_q2_milestone_1) }
      scheduled_date { Time.zone.today + 2.days }
      hours { 3.0 }
    end
  end
end
