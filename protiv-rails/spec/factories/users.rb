# frozen_string_literal: true

FactoryBot.define do
  sequence(:user_factory_email) { |n| "person#{n}@example.com" }
  sequence(:user_name) { |n| "person-#{n}" }
  sequence(:user_phone_number) { |n| "555-%03d-%04d" % [n / 10000, n % 10000] }

  factory :user do
    email { generate(:user_factory_email) }
    password { SpecHelpers::Authentication::DEFAULT_TEST_PASSWORD }

    verified { true }

    transient do
      organization { create(:organization) }
      role_type { "manager" }
    end

    admin { false }

    name { generate(:user_name) }

    phone_country_code { "1" }
    phone { generate(:user_phone_number) }

    after(:create) do |user, context|
      if context.organization
        AddUserToOrganization.new(
          user: user,
          organization: context.organization,
          role_type: context.role_type
        ).execute!
      end
    end
  end
end
