# frozen_string_literal: true

FactoryBot.define do
  factory :statement_line_item do
    association :organization
    association :identity
    association :statement

    after(:build) do |line_item, context|
      if line_item.statement
        line_item.statement.identity = line_item.identity
      end
      line_item.organization = line_item.identity.organization
    end

    factory :bonus_line_item do
      amount { Money.from_cents(10_00) }
    end

    factory :deduction_line_item do
      amount { Money.from_cents(-5_50) }
    end

    factory :held_line_item do
      amount { Money.from_cents(10_00) }
      held_days { 5 }
    end

    factory :held_indefinitely_line_item do
      amount { Money.from_cents(10_00) }
      held_indefinitely { true }
    end
  end
end
