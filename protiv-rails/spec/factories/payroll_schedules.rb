# frozen_string_literal: true

FactoryBot.define do
  factory :payroll_schedule do
    trait :with_first_branch do
      transient do
        first_branch { true }
      end
    end

    transient do
      first_branch { false }
    end

    association(:organization)
    name { "default" }
    period { "biweekly" }
    week_day { 1 }

    factory :payroll_schedule_with_period do
      after(:create) do |payroll_schedule, context|
        branch = nil
        if context.first_branch
          branch = payroll_schedule.organization.branches.first_or_create
        else
          branch = payroll_schedule.organization.branches.create
        end
        create(:pay_period,
               payroll_schedule: payroll_schedule,
               branch: branch,
               period_type: "bonus",
               start_time: payroll_schedule.start_date_from_date(Date.today),
               end_time: payroll_schedule.end_date_from_start_date(payroll_schedule.start_date_from_date(Date.today)))
      end
    end

    factory :payroll_schedule_with_prior_period do
      after(:create) do |payroll_schedule, context|
        branch = nil
        if context.first_branch
          branch = payroll_schedule.organization.branches.first_or_create
        else
          branch = payroll_schedule.organization.branches.create
        end
        create(:pay_period,
               payroll_schedule: payroll_schedule,
               branch: branch,
               period_type: "bonus",
               start_time: payroll_schedule.start_date_from_date(Time.now - 28.days),
               end_time: payroll_schedule.end_date_from_start_date(payroll_schedule.start_date_from_date(Time.now - 28.days)))
      end
    end
  end
end
