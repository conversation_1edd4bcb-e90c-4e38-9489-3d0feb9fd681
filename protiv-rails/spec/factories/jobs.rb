# frozen_string_literal: true

FactoryBot.define do
  sequence(:job_name) { |n| "job-name-#{n}" }
  factory :job do
    name { generate(:job_name) }
    organization { build_stubbed(:organization) }

    last_activity_at { 1.day.ago }

    trait :lawn_q2 do
      name { "Lawn Q2" }
      organization { create(:organization, :lawn_mow_inc) }
      job_type { "recurring" }
    end

    factory :job_with_milestones do
      organization { create(:organization_with_crew) }
      branch { organization.branches.first }
      manager { create(:identity) }

      transient do
        milestone_count { 1 }
      end

      after(:create) do |job, context|
        create_list(:milestone, context.milestone_count, job: job)
      end

      factory :job_with_milestone_and_full_time_data do
        after(:create) do |job, context|
          crew = job.organization.identities.order(:id)

          job.milestones.each do |ms|
            MilestoneTimesTestData.full_times.each do |t|
              create(:milestone_time,
                     milestone: ms,
                     identity: crew[t[:crew_num]], **t[:params])
            end
          end
        end
      end

      factory :job_with_milestone_and_split_one_time_data do
        after(:create) do |job, context|
          crew = job.organization.identities.order(:id)

          job.milestones.each do |ms|
            MilestoneTimesTestData.split_one_milestone_times.each do |t|
              create(:milestone_time,
                     milestone: ms,
                     identity: crew[t[:crew_num]], **t[:params])
            end
          end
        end
      end
    end
  end
end
