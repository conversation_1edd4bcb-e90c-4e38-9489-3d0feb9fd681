# frozen_string_literal: true

FactoryBot.define do
  factory :statement do
    trait :with_skip_update_statement do
      transient do
        skip_update_statement { true }
      end
    end

    transient do
      skip_update_statement { false }
    end

    association :organization
    association :identity
    association :pay_period

    factory :statement_with_line_items_unpaid do
      after(:create) do |statement, evaluator|
        create_list(:bonus_line_item, 5, skip_update_statement: evaluator.skip_update_statement, statement: statement, identity: statement.identity)
        create_list(:deduction_line_item, 1, skip_update_statement: evaluator.skip_update_statement, statement: statement, identity: statement.identity)
      end
    end
  end
end
