# frozen_string_literal: true

FactoryBot.define do
  factory :labor_metric do
    # Associations - Ensure these factories exist (organization, identity, milestone)
    association :organization
    association :identity
    association :milestone

    date { Date.current }
    total_clocked_seconds { 3600 } # Default 1 hour
    total_billable_seconds { 2700 } # Default 45 mins (75%)
    non_billable_seconds { 900 } # Default 15 mins
    efficiency_percentage { 75.0 }
    labor_cost_cents { 2500 } # Default $25.00
    benchmark_percentage { 75.0 }
    variance_from_benchmark { 0.0 }
    has_time_discrepancy { false }

    # Ensure related records are created if needed
    # For example, if BenchmarkFinder relies on Milestone having a Job:
    after(:build) do |metric|
      if metric.milestone && !metric.milestone.job
        metric.milestone.job = build(:job, organization: metric.organization)
      end
      # Add similar logic if milestone or job needs branch/route etc.
    end
  end
end
