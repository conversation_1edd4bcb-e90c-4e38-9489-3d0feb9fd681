# frozen_string_literal: true

FactoryBot.define do
  sequence(:branch_name) { |n| "Branch #{n}" }

  factory :branch do
    organization

    name { generate(:branch_name) }

    factory :branch_with_payroll_schedule do
      transient do
        pay_period_count { 1 }
      end

      after(:create) do |branch, evaluator|
        last_period = create(:pay_period, period_type: "bonus", branch_id: branch.id)

        last_period = create(:pay_period,
                             start_time: last_period.end_time,
                             end_time: last_period.end_time + 14.days,
                             branch: last_period.branch,
                             payroll_schedule: last_period.payroll_schedule,
                             period_type: last_period.period_type)
        last_period = create(:pay_period,
                             start_time: last_period.end_time,
                             end_time: last_period.end_time + 14.days,
                             branch: last_period.branch,
                             payroll_schedule: last_period.payroll_schedule,
                             period_type: last_period.period_type)
        create(:pay_period,
               start_time: last_period.end_time,
               end_time: last_period.end_time + 14.days,
               branch: last_period.branch,
               payroll_schedule: last_period.payroll_schedule,
               period_type: last_period.period_type)
        branch.reload
      end
    end
  end
end
