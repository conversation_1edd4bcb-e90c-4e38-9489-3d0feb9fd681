# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Cache::UpdateJobCachesJob, type: :job do
  let(:organization) { create(:organization) }
  let(:job) { create(:job, organization: organization) }

  describe '#perform' do
    context 'basic cache update' do
      it 'updates job cache columns' do
        milestone = create(:milestone, job: job, percent_complete_hundredths: 5000)

        # Verify cache gets updated
        initial_cache = job.milestone_progress_percent_hundredths

        described_class.new.perform(job.id)

        job.reload
        # Verify the milestone progress was calculated and cached
        expect(job.milestone_progress_percent_hundredths).not_to be_nil
      end

      it 'logs cache update progress' do
        milestone1 = create(:milestone, job: job)
        milestone2 = create(:milestone, job: job)

        expect(Rails.logger).to receive(:info).with("Updating job #{job.id} caches - 2 milestones")
        expect(Rails.logger).to receive(:info).with(/Job #{job.id} cache update complete - progress: [\d.]+%/)

        described_class.new.perform(job.id)
      end
    end

    context 'error handling' do
      it 'logs and re-raises errors' do
        allow(Job).to receive(:find).and_raise(StandardError.new('Test error'))

        expect(Rails.logger).to receive(:error).with("Failed to update job #{job.id} caches: Test error")

        expect {
          described_class.new.perform(job.id)
        }.to raise_error(StandardError, 'Test error')
      end
    end

    context 'progress validation warnings' do
      it 'logs warning for milestones needing material selection' do
        service = create(:service, organization: organization, progress_type: 'units')
        material = create(:catalog_item, organization: organization, item_type: 'Material')
        service.tracking_materials << material

        milestone1 = create(:milestone, job: job, service: service) # Needs material selection
        milestone2 = create(:milestone, job: job) # Regular milestone

        expect(Rails.logger).to receive(:info).with("Updating job #{job.id} caches - 2 milestones")
        expect(Rails.logger).to receive(:warn).with("Job #{job.id} has milestones needing material selection: #{milestone1.id}")
        expect(Rails.logger).to receive(:info).with(/Job #{job.id} cache update complete/)

        described_class.new.perform(job.id)
      end

      it 'does not warn when milestones have materials selected' do
        service = create(:service, organization: organization, progress_type: 'units')
        material = create(:catalog_item, organization: organization, item_type: 'Material')
        service.tracking_materials << material

        milestone = create(:milestone, job: job, service: service)
        milestone_item = create(:milestone_item, milestone: milestone, catalog_item: material)
        milestone.update!(tracked_milestone_item: milestone_item)

        expect(Rails.logger).not_to receive(:warn)

        described_class.new.perform(job.id)
      end

      it 'does not warn for percentage-based services' do
        service = create(:service, organization: organization, progress_type: 'percentage')
        milestone = create(:milestone, job: job, service: service)

        expect(Rails.logger).not_to receive(:warn)

        described_class.new.perform(job.id)
      end

      it 'does not warn when service has no eligible materials' do
        service = create(:service, organization: organization, progress_type: 'units')
        milestone = create(:milestone, job: job, service: service)

        expect(Rails.logger).not_to receive(:warn)

        described_class.new.perform(job.id)
      end

      it 'logs warning about missing material selection' do
        service = create(:service, organization: organization, progress_type: 'units')
        material = create(:catalog_item, organization: organization, item_type: 'Material')
        service.tracking_materials << material
        milestone = create(:milestone, job: job, service: service)
        milestone.update!(tracked_milestone_item: nil)

        expect(Rails.logger).to receive(:warn).with(/milestones needing material selection/)
        described_class.new.perform(job.id)
      end
    end

    context 'integration with milestone progress types' do
      it 'correctly aggregates mixed progress types' do
        # Percentage-based milestone
        service1 = create(:service, organization: organization, progress_type: 'percentage')
        milestone1 = create(:milestone, job: job, service: service1,
                           percent_complete_hundredths: 5000, # 50%
                           material_budget_cents: 10000)

        # Units-based milestone with material tracking
        service2 = create(:service, organization: organization, progress_type: 'units')
        material = create(:catalog_item, organization: organization, item_type: 'Material')
        service2.tracking_materials << material

        milestone2 = create(:milestone, job: job, service: service2,
                           material_budget_cents: 20000)
        milestone_item = create(:milestone_item, milestone: milestone2, catalog_item: material, quantity: 100.0)
        create(:item_allocation, milestone: milestone2, catalog_item: material,
               item_quantity: 25.0, organization: organization,
               unit_cost_cents: 1000, total_cost_cents: 25000, currency_code: 'USD') # 25% complete
        milestone2.update!(tracked_milestone_item: milestone_item)

        # Update milestone caches first
        milestone1.update_cache_columns
        milestone2.update_cache_columns

        described_class.new.perform(job.id)

        # Job progress should be weighted average: (50% * 10000 + 25% * 20000) / 30000 = ~33.33%
        job.reload
        expected_progress = ((50.0 * 10000 + 25.0 * 20000) / 30000.0).round(1)
        expect(job.progress_percentage).to be_within(0.1).of(expected_progress)
      end
    end

    context 'trigger integration' do
      it 'is triggered when milestone progress changes' do
        milestone = create(:milestone, job: job, percent_complete_cached_hundredths: 2500)

        expect(Cache::UpdateJobCachesJob).to receive(:perform_async).with(job.id)

        # Update the cached value, which triggers the job cache update
        milestone.update!(percent_complete_cached_hundredths: 5000)
      end
    end
  end
end
