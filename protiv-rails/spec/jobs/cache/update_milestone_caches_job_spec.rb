# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Cache::UpdateMilestoneCachesJob, type: :job do
  let(:organization) { create(:organization) }
  let(:job) { create(:job, organization: organization) }

  describe '#perform' do
    context 'basic cache update' do
      it 'updates milestone cache columns' do
        milestone = create(:milestone, job: job, percent_complete_hundredths: 5000)

        # Instead of mocking, verify the cache actually gets updated
        initial_cache = milestone.percent_complete_cached_hundredths

        described_class.new.perform(milestone.id)

        milestone.reload
        # Verify cache was updated (should sync cached with actual value)
        expect(milestone.percent_complete_cached_hundredths).to eq(milestone.percent_complete_hundredths)
      end
    end

    context 'with material tracking' do
      let(:service) { create(:service, organization: organization, progress_type: 'units') }
      let(:material) { create(:catalog_item, organization: organization, item_type: 'Material') }
      let(:milestone) { create(:milestone, job: job, service: service) }

      before do
        service.tracking_materials << material
      end

      context 'auto-assignment logic' do
        it 'auto-assigns material when service has exactly one eligible material' do
          milestone_item = create(:milestone_item, milestone: milestone, catalog_item: material)

          expect(milestone.tracked_milestone_item).to be_nil

          described_class.new.perform(milestone.id)

          expect(milestone.reload.tracked_milestone_item).to eq(milestone_item)
        end

        it 'does not auto-assign when material is already selected' do
          milestone_item = create(:milestone_item, milestone: milestone, catalog_item: material)
          milestone.update!(tracked_milestone_item: milestone_item)

          expect {
            described_class.new.perform(milestone.id)
          }.not_to change { milestone.reload.tracked_milestone_item }
        end

        it 'does not auto-assign when service has multiple materials' do
          material2 = create(:catalog_item, organization: organization, item_type: 'Material')
          service.tracking_materials << material2

          create(:milestone_item, milestone: milestone, catalog_item: material)
          create(:milestone_item, milestone: milestone, catalog_item: material2)

          described_class.new.perform(milestone.id)

          expect(milestone.reload.tracked_milestone_item).to be_nil
        end

        it 'logs auto-assignment when it occurs' do
          milestone_item = create(:milestone_item, milestone: milestone, catalog_item: material)

          expect(Rails.logger).to receive(:info).with("Auto-assigned material for milestone #{milestone.id}")

          described_class.new.perform(milestone.id)
        end
      end

      context 'material validation' do
        let(:material2) { create(:catalog_item, organization: organization, item_type: 'Material') }

        it 'clears tracked material when it becomes ineligible' do
          # First set up a valid situation
          service.tracking_materials << material2
          milestone_item = create(:milestone_item, milestone: milestone, catalog_item: material2)
          milestone.update!(tracked_milestone_item: milestone_item)

          # Now remove the material from service, making it ineligible
          service.tracking_materials.delete(material2)

          expect(Rails.logger).to receive(:info).with("Clearing invalid tracked material #{material2.id} from milestone #{milestone.id}")

          described_class.new.perform(milestone.id)

          expect(milestone.reload.tracked_milestone_item).to be_nil
        end

        it 'keeps tracked material when it remains eligible' do
          milestone_item = create(:milestone_item, milestone: milestone, catalog_item: material)
          milestone.update!(tracked_milestone_item: milestone_item)

          expect {
            described_class.new.perform(milestone.id)
          }.not_to change { milestone.reload.tracked_milestone_item }
        end
      end
    end

    context 'with percentage-based service' do
      let(:service) { create(:service, organization: organization, progress_type: 'percentage') }
      let(:milestone) { create(:milestone, job: job, service: service) }

      it 'does not attempt material tracking operations' do
        expect_any_instance_of(MilestoneProgressAssignmentService).not_to receive(:auto_assign_material_if_possible)

        described_class.new.perform(milestone.id)
      end
    end

    context 'with no service' do
      let(:milestone) { create(:milestone, job: job, service: nil) }

      it 'does not attempt material tracking operations' do
        expect_any_instance_of(MilestoneProgressAssignmentService).not_to receive(:auto_assign_material_if_possible)

        described_class.new.perform(milestone.id)
      end
    end

    context 'integration with triggers' do
      it 'is triggered when milestone items change' do
        milestone = create(:milestone, job: job)

        expect(Cache::UpdateMilestoneCachesJob).to receive(:perform_async).with(milestone.id)

        create(:milestone_item, milestone: milestone)
      end

      it 'is triggered when item allocations change' do
        milestone = create(:milestone, job: job)
        catalog_item = create(:catalog_item, organization: organization)

        expect(Cache::UpdateMilestoneCachesJob).to receive(:perform_async).with(milestone.id)

        create(:item_allocation,
               milestone: milestone,
               organization: organization,
               catalog_item: catalog_item,
               item_quantity: 10.0,
               unit_cost_cents: 1000,
               total_cost_cents: 10000,
               currency_code: 'USD')
      end

      it 'is triggered when service materials change' do
        service = create(:service, organization: organization)
        milestone = create(:milestone, job: job, service: service)
        material = create(:catalog_item, organization: organization, item_type: 'Material')

        expect(Cache::UpdateMilestoneCachesJob).to receive(:perform_async).with(milestone.id)

        service.tracking_materials << material
      end
    end
  end
end
