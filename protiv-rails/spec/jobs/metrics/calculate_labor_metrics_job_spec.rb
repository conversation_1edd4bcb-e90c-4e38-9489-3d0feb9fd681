# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Metrics::CalculateLaborMetricsJob, type: :job do
  # ---- Reset Database ----
  before do
    MilestoneTime.delete_all
    ClockTime.delete_all
    LaborMetric.delete_all
  end

  # ---- Test find_records_to_process directly ----
  describe '#find_records_to_process' do
    let(:job_instance) { described_class.new }

    # Create MINIMAL test data
    let(:organization) { create(:organization) }
    let(:job_record) { create(:job, organization: organization) }

    # --- Test normal mode ---
    context 'in normal mode' do
      it 'finds only records updated after the since timestamp with table-qualified queries' do
        # --- Setup Test Data ---
        worker = create(:identity, organization: organization)
        milestone = create(:milestone, job: job_record)

        # Create a milestone time that will be "old"
        mt_old = create(:milestone_time,
                        identity: worker,
                        milestone: milestone,
                        start_time: 1.day.ago
        )

        # Save the exact updated_at timestamp of the first record
        old_time = mt_old.updated_at

        # Get the since time with a clear 1 second buffer AFTER the first record
        since_time = old_time + 1.second

        # Travel forward with a clear gap to create second record
        travel 1.minute

        # Create a milestone time that will be "new"
        mt_new = create(:milestone_time,
                        identity: worker,
                        milestone: milestone,
                        start_time: 2.days.ago
        )

        # --- Check Current DB State ---
        puts "---- Test DB State (Normal Mode) ----"
        puts "All MilestoneTimes:"
        MilestoneTime.all.each do |mt|
          puts "  ID: #{mt.id}, Worker: #{mt.identity_id}, Milestone: #{mt.milestone_id}, start_time: #{mt.start_time.to_date}, Updated: #{mt.updated_at}"
        end
        puts "First record updated_at: #{old_time}"
        puts "Since time: #{since_time} (#{since_time - old_time} seconds after first record)"
        puts "Second record updated_at: #{mt_new.updated_at} (#{mt_new.updated_at - since_time} seconds after since_time)"

        # --- Call Method Under Test ---
        options = { since: since_time.to_s, organization_id: organization.id }
        result = job_instance.send(:find_records_to_process, options)

        # --- Debug Results ---
        puts "Results (#{result.size} items):"
        result.each do |r|
          puts "  Worker: #{r[:identity_id]}, Milestone: #{r[:milestone_id]}, Date: #{r[:date]}"
        end

        # --- Verify Results ---
        # Should only include the one created after since_time
        expect(result.size).to eq(1)
        expect(result.first[:identity_id]).to eq(worker.id)
        expect(result.first[:milestone_id]).to eq(milestone.id)
        expect(result.first[:date]).to eq(2.days.ago.to_date)
      end
    end

    # --- Test backfill mode ---
    context 'in backfill mode' do
      it 'finds records with start dates after the since date' do
        # Freeze time for consistent timestamps
        freeze_time = Time.current
        travel_to freeze_time do
          # --- Setup Test Data ---
          worker = create(:identity, organization: organization)
          milestone1 = create(:milestone, job: job_record)
          milestone2 = create(:milestone, job: job_record)

          # We want 'since' to be exactly 5 days ago for clarity
          since = 5.days.ago.beginning_of_day
          options = { since: since.to_s, backfill: true, organization_id: organization.id }

          # Create milestone time with start date BEFORE cutoff
          create(:milestone_time,
                 identity: worker,
                 milestone: milestone1,
                 start_time: 6.days.ago
          )

          # Create milestone time with start date AFTER cutoff
          create(:milestone_time,
                 identity: worker,
                 milestone: milestone2,
                 start_time: 3.days.ago
          )

          # --- Check Current DB State ---
          puts "---- Test DB State (Backfill) ----"
          puts "All MilestoneTimes:"
          MilestoneTime.all.each do |mt|
            puts "  ID: #{mt.id}, Worker: #{mt.identity_id}, Milestone: #{mt.milestone_id}, start_time: #{mt.start_time.to_date}, Updated: #{mt.updated_at}"
          end
          puts "Since cutoff: #{since.to_date}"

          # --- Call Method Under Test ---
          result = job_instance.send(:find_records_to_process, options)

          # --- Debug Results ---
          puts "Results (#{result.size} items):"
          result.each do |r|
            puts "  Worker: #{r[:identity_id]}, Milestone: #{r[:milestone_id]}, Date: #{r[:date]}"
          end

          # --- Verify Results ---
          # Should only include the one with start_time 3 days ago
          expect(result.size).to eq(1)
          expect(result.first[:identity_id]).to eq(worker.id)
          expect(result.first[:milestone_id]).to eq(milestone2.id)
          expect(result.first[:date]).to eq(3.days.ago.to_date)
        end
      end
    end
  end

  describe 'organization filtering' do
    # Add this line at the beginning of the context
    let(:job_instance) { described_class.new }

    it 'passes the organization_id to find_records_to_process' do
      org1 = create(:organization)

      # Expect organization_id to be included in params
      expect(job_instance).to receive(:find_records_to_process)
                                .with(hash_including(organization_id: org1.id))
                                .and_return([]) # Return empty to keep test simple

      job_instance.perform({ organization_id: org1.id })
    end

    it 'find_records_to_process correctly filters by organization' do
      # Create two organizations
      org1 = create(:organization)
      org2 = create(:organization)

      # Create jobs and milestones in each organization
      job1 = create(:job, organization: org1)
      job2 = create(:job, organization: org2)
      milestone1 = create(:milestone, job: job1)
      milestone2 = create(:milestone, job: job2)

      # Create workers
      worker1 = create(:identity, organization: org1)
      worker2 = create(:identity, organization: org2)

      # Create milestone times
      create(:milestone_time, identity: worker1, milestone: milestone1,
             start_time: 1.day.ago, updated_at: Time.current)
      create(:milestone_time, identity: worker2, milestone: milestone2,
             start_time: 1.day.ago, updated_at: Time.current)

      # Call find_records_to_process for org1
      result = job_instance.send(:find_records_to_process, { organization_id: org1.id })

      # Verify only org1's records are returned
      expect(result.size).to eq(1)
      expect(result.first[:milestone_id]).to eq(milestone1.id)
      expect(result.first[:identity_id]).to eq(worker1.id)
    end
  end

  describe '#calculate_and_save_metrics' do
    let(:job_instance) { described_class.new }
    let(:calculator_instance) { instance_double(Metrics::LaborMetricCalculator) }
    let(:sample_metrics) { {
      organization_id: 1,
      total_clocked_seconds: 28800,
      total_billable_seconds: 25200,
      non_billable_seconds: 3600,
      efficiency_percentage: 87.5,
      labor_cost_cents: 30000
    } }

    it 'uses the calculator and updates the labor metric record' do
      # Setup
      identity_id = 123
      milestone_id = 456
      date = Date.current - 1.day

      # Mock the calculator
      expect(Metrics::LaborMetricCalculator).to receive(:new).with(
        identity_id: identity_id,
        milestone_id: milestone_id,
        date: date
      ).and_return(calculator_instance)

      expect(calculator_instance).to receive(:call).and_return(sample_metrics)

      # Mock the labor metric find or create
      metric = instance_double(LaborMetric)
      expect(LaborMetric).to receive(:find_or_initialize_by).with(
        organization_id: sample_metrics[:organization_id],
        identity_id: identity_id,
        milestone_id: milestone_id,
        date: date
      ).and_return(metric)

      # Expect the metrics to be saved
      expect(metric).to receive(:update!).with(sample_metrics)

      # Call the method
      job_instance.send(:calculate_and_save_metrics,
                        identity_id: identity_id,
                        milestone_id: milestone_id,
                        date: date
      )
    end
  end

  describe 'error handling' do
    let(:job_instance) { described_class.new }
    let(:organization) { create(:organization) }

    it 'catches and logs errors during processing but continues with other combinations' do
      # Setup mock combinations - one good, one that will fail
      good_combo = { identity_id: 1, milestone_id: 1, date: Date.current }
      bad_combo = { identity_id: 2, milestone_id: 2, date: Date.current }

      allow(job_instance).to receive(:find_records_to_process).and_return([good_combo, bad_combo])

      # First call works
      expect(job_instance).to receive(:calculate_and_save_metrics).with(
        identity_id: good_combo[:identity_id],
        milestone_id: good_combo[:milestone_id],
        date: good_combo[:date]
      ).and_call_original

      # Mock calculator for successful case
      good_calculator = instance_double(Metrics::LaborMetricCalculator)
      allow(Metrics::LaborMetricCalculator).to receive(:new).with(
        identity_id: good_combo[:identity_id],
        milestone_id: good_combo[:milestone_id],
        date: good_combo[:date]
      ).and_return(good_calculator)

      allow(good_calculator).to receive(:call).and_return({ organization_id: 1 })
      allow(LaborMetric).to receive(:find_or_initialize_by).and_return(instance_double(LaborMetric, update!: true))

      # Second call raises error
      expect(job_instance).to receive(:calculate_and_save_metrics).with(
        identity_id: bad_combo[:identity_id],
        milestone_id: bad_combo[:milestone_id],
        date: bad_combo[:date]
      ).and_raise(StandardError.new("Test error"))

      # Expect Bugsnag notification
      expect(Bugsnag).to receive(:notify)

      # Expect error log messages in sequence
      expect(Rails.logger).to receive(:error).with(/ERROR processing combo/).ordered
      expect(Rails.logger).to receive(:error).with(/Job finished with errors/).ordered
      expect(Rails.logger).to receive(:error).with(/Failed combinations:/).ordered

      # Run the job - it should not crash despite one error
      job_instance.perform({ organization_id: organization.id })
    end
  end
end
