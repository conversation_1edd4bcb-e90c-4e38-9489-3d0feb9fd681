# frozen_string_literal: true

require "rails_helper"

RSpec.describe Sync::RefreshAspireTokenJob do
  let(:non_aspire_integration) { create(:integration) }
  let(:integration) { create(:integration, source: :aspire) }

  specify do
    expect_any_instance_of(Integration).to receive(:refresh_credential)
    described_class.new.perform(integration.id)
  end

  specify do
    expect {
      described_class.new.perform(non_aspire_integration.id)
    }.to raise_error(ActiveRecord::RecordNotFound)
  end
end
