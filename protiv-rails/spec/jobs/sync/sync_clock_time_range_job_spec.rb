# frozen_string_literal: true

require "rails_helper"

RSpec.describe Sync::SyncClockTimeRangeJob do
  before { travel_to Date.new(2025, 3, 1) }

  let(:integration) { create(:integration, source: :aspire) }
  let(:sync_status_range) do
    integration.sync_status_ranges.create(
      resource: "clock_times",
      start_timestamp: 2.weeks.ago,
      end_timestamp: 1.week.ago
    )
  end

  specify do
    expect_any_instance_of(Sync::CacheAdapter).to receive(:cache_clock_time_ranges) do |instance, **kwargs|
      expect(kwargs[:sync_status_range]).to eq(sync_status_range)
      []
    end
    described_class.new.perform(sync_status_range.id)
  end
end
