# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Sync::FixJobStatusJob, type: :job do
  let(:organization) { create(:organization) }
  let(:integration) { create(:aspire_integration, organization: organization) }
  let(:job) { create(:job, organization: organization, remote_reference: "12345") }

  describe '#perform' do
    it 'executes without errors when given valid parameters' do
      # Mock the materializer and its methods to avoid actual API calls
      materializer = double('materializer')
      allow(integration).to receive(:materializer).and_return(materializer)
      allow(materializer).to receive(:materialize_job)

      # Mock Sync::Current.track
      allow(Sync::Current).to receive(:track).and_yield

      # Mock the numbered opportunity creation
      allow(Sync::Aspire::NumberedOpportunity).to receive(:new).and_return(double('numbered_opportunity'))

      # Should not raise an error
      expect {
        described_class.new.perform(integration.id, job.id)
      }.not_to raise_error
    end

    it 'handles errors gracefully' do
      # Mock Rails logger
      allow(Rails.logger).to receive(:error)
      allow(Bugsnag).to receive(:notify) if defined?(Bugsnag)

      # Mock an error scenario - make Integration.find raise an error
      allow(Integration).to receive(:find).with(integration.id).and_raise(StandardError.new('Test error'))

      # Should not raise an error
      expect {
        described_class.new.perform(integration.id, job.id)
      }.not_to raise_error

      # Should log the error
      expect(Rails.logger).to have_received(:error).with(/Error fixing job status for job_id #{job.id}: Test error/)
    end
  end
end
