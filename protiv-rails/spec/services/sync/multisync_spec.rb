# frozen_string_literal: true

require 'set'
require "rails_helper"

RSpec.describe "multiple integrations" do
  let(:integration_a) { create(:integration, source: "test_a") }
  let(:integration_b) { create(:integration, source: "test_b") }

  # NOTE: This is speculative. Eventually we should generate workflows
  # the dictate the steps that need to happen and the order between them.
  class Multisync
    def integrations
      @integrations ||= {}
    end

    def pulls
      @pulls ||= Set.new
    end

    def updates
      @updates ||= Set.new
    end

    def creates
      @creates ||= Set.new
    end

    # FIXME: validate capabilities
    def pull(resource, from:)
      integrations[from.id] = from
      pulls << [resource, from]
    end

    def create(resource, to:)
      integrations[to.id] = to
      creates << [resource, to]
    end

    def update(resource, to:)
      integrations[to.id] = to
      updates << [resource, to]
    end

    def pull_all!
      Rails.logger.tagged("Multisync", "pull_all") do |logger|
        # FIXME: capture spawned sidekiq jobs
        pulls.each do |resource, integration|
          Rails.logger.tagged(resource, integration.id) do |logger|
            logger.info("caching #{resource}")
            integration.cache_adapter.public_send("cache_#{resource}")
            logger.info("materializing #{resource}")
            integration.materializer.public_send("materialize_#{resource}")
          end
        end
      end
    end

    def create_all!
      creates.each do |resource_integration|
        # Steps:
        # - sync_datum, either for a single resource or a batch
        # - this should contain the exact data to be pushed up to the API.
        # - The size of the sync datum should be chosen for idempotency/retryability;
        #   i.e., if the process fails halfway through a big batch, it would be difficult to
        #   know which portion succeeded and which should be replayed.
        #
        #   If the datum is a single thing, the chances of spoiling the batch are reduced.
        # - Each sync_datum should spawn a background job to do the actual API call(s)
        # - For any particular resource identifier, sync_datums _must be ordered_ - they need to be posted
        #   in the order they were created. Need some clever locks here.
        # - We can create the sync datums in activerecord callbacks or on a schedule - seems like
        #   a much tighter window can work here (every 5 minutes or less?)
      end
    end

    def update_all!
      Rails.logger.tagged("Multisync", "update_all") do |logger|
        updates.each do |resource, integration|
          # This is a bit nightmarish.
          #
          # Example: sync contact data from one system to another
          #
          # S1, S2
          #
          # - need to correlate records.
          # - S2 (target system) will need to attach its own integration record to the shared Identity
          # - how exactly that matches might be up to the config
          # - how to handle deletes in one or both systems?
          # - how to handle records that don't exist in S2?
          # - how to handle conflicts / race conditions
        end
      end
    end
  end

  pending do
    multisync = Multisync.new
    multisync.pull(:identities, from: integration_a)
    multisync.create(:identities, to: integration_b)

    integration_a.adapter.stub_identity({ name: "Alice", employee_id: "1234" })

    multisync.pull_all!

    expect(integration_a.identities.count).to eq(1)

    integration_a.identities.each do |identity|
      expect(identity.organization).to eq(integration_a.organization)
    end

    multisync.create_all!
    multisync.update_all!
  end
end
