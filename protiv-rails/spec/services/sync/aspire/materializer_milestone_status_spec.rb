# frozen_string_literal: true

require "rails_helper"

RSpec.describe Sync::Aspire::Materializer do
  let(:integration) { create(:aspire_integration) }
  let(:materializer) { described_class.new(integration: integration) }
  let(:job) { create(:job, organization: integration.organization) }

  before do
    Sync::Current.integration_id = integration.id
    allow(materializer).to receive(:local_id!).and_return(job.id)

    # Mock the remote index to avoid tracking errors
    allow(materializer).to receive(:remote_index).and_return({})

    # Mock track_error to just yield
    allow(materializer).to receive(:track_error).and_yield

    # Mock milestone_records to return an empty relation
    empty_integration_records = IntegrationRecord.where("1=0") # Empty relation
    allow(materializer).to receive(:milestone_records).and_return(empty_integration_records)
  end

  after do
    Sync::Current.reset
  end

  describe "milestone status mapping" do
    let(:opportunity) do
      double(
        opportunity_id: 123,
        opportunity_number: 456
      )
    end

    let(:opportunity_cache) do
      double(
        remote_resource: "opportunity",
        to_resource: opportunity
      )
    end

    let(:sync_caches_mock) do
      double("sync_caches").tap do |mock|
        allow(mock).to receive(:aspire_opportunities).and_return(
          double("opportunities").tap do |opp_mock|
            allow(opp_mock).to receive(:where).with(remote_primary_id: 123).and_return(
              double("opportunities_where").tap do |where_mock|
                allow(where_mock).to receive(:first).and_return(opportunity_cache)
              end
            )
          end
        )

        allow(mock).to receive(:aspire_opportunity_services).and_return(
          double("opportunity_services").tap do |opp_serv_mock|
            allow(opp_serv_mock).to receive(:where).and_return(
              double("opportunity_services_where").tap do |where_mock|
                allow(where_mock).to receive(:first).and_return(nil)
              end
            )
          end
        )
      end
    end

    before do
      allow(materializer).to receive(:sync_caches).and_return(sync_caches_mock)
    end

    context "WorkTicketStatus mapping" do
      it "maps Open to pending" do
        work_ticket = double(
          work_ticket_id: 1,
          opportunity_id: 123,
          work_ticket_status_name: "Open",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "123",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("pending")
      end

      it "maps Scheduled to scheduled" do
        work_ticket = double(
          work_ticket_id: 2,
          opportunity_id: 123,
          work_ticket_status_name: "Scheduled",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "124",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("scheduled")
      end

      it "maps Pending Approval to in_review" do
        work_ticket = double(
          work_ticket_id: 3,
          opportunity_id: 123,
          work_ticket_status_name: "Pending Approval",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "125",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("in_review")
      end

      it "maps Complete to completed" do
        work_ticket = double(
          work_ticket_id: 4,
          opportunity_id: 123,
          work_ticket_status_name: "Complete",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "126",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("completed")
      end

      it "maps Canceled to canceled" do
        work_ticket = double(
          work_ticket_id: 5,
          opportunity_id: 123,
          work_ticket_status_name: "Canceled",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "127",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("canceled")
      end
    end

    context "cost detection override" do
      it "overrides pending to in_progress when labor costs exist" do
        work_ticket = double(
          work_ticket_id: 6,
          opportunity_id: 123,
          work_ticket_status_name: "Open",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 150.50, # Has labor cost
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "128",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("in_progress")
      end

      it "overrides scheduled to in_progress when material costs exist" do
        work_ticket = double(
          work_ticket_id: 7,
          opportunity_id: 123,
          work_ticket_status_name: "Scheduled",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 75.25, # Has material cost
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "129",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("in_progress")
      end

      it "does not override completed status even with costs" do
        work_ticket = double(
          work_ticket_id: 8,
          opportunity_id: 123,
          work_ticket_status_name: "Complete",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 100.0, # Has costs but status is Complete
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 50.0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "130",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("completed")
      end

      it "does not override canceled status even with costs" do
        work_ticket = double(
          work_ticket_id: 9,
          opportunity_id: 123,
          work_ticket_status_name: "Canceled",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 100.0, # Has costs but status is Canceled
          equip_cost_est: 0,
          equipment_cost_act: 0,
          material_cost_est: 0,
          material_cost_act: 50.0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "131",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("canceled")
      end

      it "ignores estimated costs when determining status" do
        work_ticket = double(
          work_ticket_id: 10,
          opportunity_id: 123,
          work_ticket_status_name: "Open",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 500.0, # Has estimated costs but no actual costs
          labor_cost_act: 0,
          equip_cost_est: 200.0,
          equipment_cost_act: 0,
          material_cost_est: 300.0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "132",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("pending") # Should remain pending, not in_progress
      end

      it "does not override in_review status even with costs" do
        work_ticket = double(
          work_ticket_id: 11,
          opportunity_id: 123,
          work_ticket_status_name: "Pending Approval",
          last_modified_date_time: Time.current,
          created_date_time: Time.current,
          price: 0,
          hours_est: 0,
          hours_act: 0,
          hour_cost_est: 0,
          labor_cost_act: 0,
          equip_cost_est: 0,
          equipment_cost_act: 25.0, # Has equipment cost
          material_cost_est: 0,
          material_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0,
          work_ticket_number: "133",
          opportunity_service_id: nil
        )

        materializer.send(:materialize_milestone, work_ticket)

        milestone = Milestone.last
        expect(milestone.status).to eq("in_review")
      end
    end

    context "#has_work_ticket_costs?" do
      it "returns true when any actual cost field is greater than 0" do
        work_ticket = double(
          labor_cost_act: 100.0,
          material_cost_act: 0,
          equipment_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0
        )

        expect(materializer.send(:has_work_ticket_costs?, work_ticket)).to be true
      end

      it "returns false when all actual cost fields are 0 or nil" do
        work_ticket = double(
          labor_cost_act: 0,
          material_cost_act: nil,
          equipment_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0
        )

        expect(materializer.send(:has_work_ticket_costs?, work_ticket)).to be false
      end

      it "handles string cost values correctly" do
        work_ticket = double(
          labor_cost_act: "150.50",
          material_cost_act: "0",
          equipment_cost_act: 0,
          sub_cost_act: 0,
          other_cost_act: 0,
          total_cost_act: 0
        )

        expect(materializer.send(:has_work_ticket_costs?, work_ticket)).to be true
      end
    end
  end
end
