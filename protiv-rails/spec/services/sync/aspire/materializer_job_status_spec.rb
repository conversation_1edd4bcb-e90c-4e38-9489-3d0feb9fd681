# frozen_string_literal: true

require "rails_helper"

RSpec.describe Sync::Aspire::Materializer, type: :service do
  let(:organization) { create(:organization) }
  let(:integration) { create(:integration, organization: organization, source: "aspire") }
  let(:materializer) { integration.materializer }

  describe "job status mapping integration" do
    let(:branch) { create(:branch, organization: organization) }
    let(:property) { create(:property, organization: organization) }
    let(:identity) { create(:identity, organization: organization) }

    before do
      # Set up sync context
      allow(Sync::Current).to receive(:integration_id).and_return(integration.id)

      # Mock the remote index and records
      allow(materializer).to receive(:remote_index).and_return({})

      # Mock job_records to return an empty relation initially (no existing jobs)
      empty_integration_records = IntegrationRecord.where("1=0") # Empty relation
      allow(materializer).to receive(:job_records).and_return(empty_integration_records)

      allow(materializer).to receive(:local_id!).with(remote_id: anything, remote_resource: :contact).and_return(identity.id)
      allow(materializer).to receive(:local_id!).with(remote_id: anything, remote_resource: :branch).and_return(branch.id)
      allow(materializer).to receive(:local_id!).with(remote_id: anything, remote_resource: :property).and_return(property.id)
      allow(materializer).to receive(:materialize_invoice_type).and_return(nil)
      allow(materializer).to receive(:track_error).and_yield
    end

    context "for won opportunities" do
      let(:numbered_opportunity) { Sync::Aspire::NumberedOpportunity.new(12345) }

      let(:won_opportunity_complete) do
        double("opportunity",
               modified_date: Time.current,
               created_date_time: Time.current,
               operations_manager_contact_id: 1,
               branch_id: 1,
               opportunity_name: "Test Job",
               property_id: 1,
               division_name: "Test Division",
               invoice_type: nil,
               opportunity_type: "Work Order",
               opportunity_status: "Won",
               job_status_name: "Complete",
               opportunity_number: 12345,
               opportunity_id: 12345,
               master_opportunity_id: nil,
               # Cost fields
               estimated_labor_cost: nil,
               estimated_material_cost: nil,
               estimated_equipment_cost: nil,
               estimated_other_cost: nil,
               estimated_sub_cost: nil,
               estimated_cost_dollars: nil,
               estimated_dollars: nil,
               actual_cost_labor: nil,
               actual_cost_material: nil,
               actual_cost_sub: nil,
               actual_cost_dollars: nil,
               actual_earned_revenue: nil,
               budgeted_dollars: nil
        )
      end

      let(:won_opportunity_in_progress) do
        double("opportunity",
               modified_date: Time.current,
               created_date_time: Time.current,
               operations_manager_contact_id: 1,
               branch_id: 1,
               opportunity_name: "Test Job",
               property_id: 1,
               division_name: "Test Division",
               invoice_type: nil,
               opportunity_type: "Work Order",
               opportunity_status: "Won",
               job_status_name: "In Process Edited",
               opportunity_number: 12345,
               opportunity_id: 12345,
               master_opportunity_id: nil,
               # Cost fields
               estimated_labor_cost: nil,
               estimated_material_cost: nil,
               estimated_equipment_cost: nil,
               estimated_other_cost: nil,
               estimated_sub_cost: nil,
               estimated_cost_dollars: nil,
               estimated_dollars: nil,
               actual_cost_labor: nil,
               actual_cost_material: nil,
               actual_cost_sub: nil,
               actual_cost_dollars: nil,
               actual_earned_revenue: nil,
               budgeted_dollars: nil
        )
      end

      let(:won_opportunity_canceled) do
        double("opportunity",
               modified_date: Time.current,
               created_date_time: Time.current,
               operations_manager_contact_id: 1,
               branch_id: 1,
               opportunity_name: "Test Job",
               property_id: 1,
               division_name: "Test Division",
               invoice_type: nil,
               opportunity_type: "Work Order",
               opportunity_status: "Won",
               job_status_name: "Canceled",
               opportunity_number: 12345,
               opportunity_id: 12345,
               master_opportunity_id: nil,
               # Cost fields
               estimated_labor_cost: nil,
               estimated_material_cost: nil,
               estimated_equipment_cost: nil,
               estimated_other_cost: nil,
               estimated_sub_cost: nil,
               estimated_cost_dollars: nil,
               estimated_dollars: nil,
               actual_cost_labor: nil,
               actual_cost_material: nil,
               actual_cost_sub: nil,
               actual_cost_dollars: nil,
               actual_earned_revenue: nil,
               budgeted_dollars: nil
        )
      end

      it "sets status to completed for Complete JobStatusName" do
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: won_opportunity_complete)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        materializer.send(:materialize_job, numbered_opportunity)

        job = Job.find_by(remote_reference: 12345)
        expect(job.status).to eq("completed")
      end

      it "sets status to in_progress for In Process Edited JobStatusName" do
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: won_opportunity_in_progress)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        materializer.send(:materialize_job, numbered_opportunity)

        job = Job.find_by(remote_reference: 12345)
        expect(job.status).to eq("in_progress")
      end

      it "sets status to canceled for Canceled JobStatusName" do
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: won_opportunity_canceled)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        materializer.send(:materialize_job, numbered_opportunity)

        job = Job.find_by(remote_reference: 12345)
        expect(job.status).to eq("canceled")
      end
    end

    context "for pre-won opportunities" do
      let(:numbered_opportunity) { Sync::Aspire::NumberedOpportunity.new(54321) }

      let(:bidding_opportunity_no_costs) do
        double("opportunity",
               modified_date: Time.current,
               created_date_time: Time.current,
               operations_manager_contact_id: 1,
               branch_id: 1,
               opportunity_name: "Test Job",
               property_id: 1,
               division_name: "Test Division",
               invoice_type: nil,
               opportunity_type: "Work Order",
               opportunity_status: "Bidding",
               job_status_name: nil,
               opportunity_number: 54321,
               opportunity_id: 54321,
               master_opportunity_id: nil,
               # No costs
               estimated_labor_cost: nil,
               estimated_material_cost: nil,
               estimated_equipment_cost: nil,
               estimated_other_cost: nil,
               estimated_sub_cost: nil,
               estimated_cost_dollars: nil,
               estimated_dollars: nil,
               actual_cost_labor: nil,
               actual_cost_material: nil,
               actual_cost_sub: nil,
               actual_cost_dollars: nil,
               actual_earned_revenue: nil,
               budgeted_dollars: nil
        )
      end

      let(:bidding_opportunity_with_costs) do
        double("opportunity",
               modified_date: Time.current,
               created_date_time: Time.current,
               operations_manager_contact_id: 1,
               branch_id: 1,
               opportunity_name: "Test Job",
               property_id: 1,
               division_name: "Test Division",
               invoice_type: nil,
               opportunity_type: "Work Order",
               opportunity_status: "Bidding",
               job_status_name: nil,
               opportunity_number: 54321,
               opportunity_id: 54321,
               master_opportunity_id: nil,
               # Has costs
               estimated_labor_cost: 500.0,
               estimated_material_cost: nil,
               estimated_equipment_cost: nil,
               estimated_other_cost: nil,
               estimated_sub_cost: nil,
               estimated_cost_dollars: nil,
               estimated_dollars: nil,
               actual_cost_labor: nil,
               actual_cost_material: nil,
               actual_cost_sub: nil,
               actual_cost_dollars: nil,
               actual_earned_revenue: nil,
               budgeted_dollars: nil
        )
      end

      it "sets status to pending when no costs and no attendances" do
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: bidding_opportunity_no_costs)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        materializer.send(:materialize_job, numbered_opportunity)

        job = Job.find_by(remote_reference: 54321)
        expect(job.status).to eq("pending")
      end

      it "sets status to in_progress when has costs" do
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: bidding_opportunity_with_costs)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        materializer.send(:materialize_job, numbered_opportunity)

        job = Job.find_by(remote_reference: 54321)
        expect(job.status).to eq("in_progress")
      end

      it "sets status to in_progress when has attendances" do
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: bidding_opportunity_no_costs)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        # Create a job first
        materializer.send(:materialize_job, numbered_opportunity)
        job = Job.find_by(remote_reference: 54321)

        # Create milestone and milestone time to simulate attendances
        milestone = create(:milestone, job: job)
        create(:milestone_time, milestone: milestone, identity: identity)

        # Create an integration record for the existing job so it can be found on re-materialization
        remote_slug = "aspire:numbered_opportunity:54321"
        integration_record = IntegrationRecord.create!(
          integration: integration,
          record: job,
          remote_slug: remote_slug
        )

        # Mock job_records to return the existing job for re-materialization
        allow(materializer).to receive(:job_records).and_return(IntegrationRecord.where(id: integration_record.id))

        # Re-materialize the job
        materializer.send(:materialize_job, numbered_opportunity)

        job.reload
        expect(job.status).to eq("in_progress")
      end
    end

    context "status update logic" do
      let(:numbered_opportunity) { Sync::Aspire::NumberedOpportunity.new(99999) }

      let(:won_opportunity) do
        double("opportunity",
               modified_date: Time.current,
               created_date_time: Time.current,
               operations_manager_contact_id: 1,
               branch_id: 1,
               opportunity_name: "Test Job",
               property_id: 1,
               division_name: "Test Division",
               invoice_type: nil,
               opportunity_type: "Work Order",
               opportunity_status: "Won",
               job_status_name: "Complete",
               opportunity_number: 99999,
               opportunity_id: 99999,
               master_opportunity_id: nil,
               # Cost fields
               estimated_labor_cost: nil,
               estimated_material_cost: nil,
               estimated_equipment_cost: nil,
               estimated_other_cost: nil,
               estimated_sub_cost: nil,
               estimated_cost_dollars: nil,
               estimated_dollars: nil,
               actual_cost_labor: nil,
               actual_cost_material: nil,
               actual_cost_sub: nil,
               actual_cost_dollars: nil,
               actual_earned_revenue: nil,
               budgeted_dollars: nil
        )
      end

      it "does not update completed jobs back to in_progress" do
        # Create a completed job
        job = create(:job, organization: organization, remote_reference: 99999, status: :completed)

        # Create an integration record for the job
        remote_slug = "aspire:numbered_opportunity:99999"
        integration_record = IntegrationRecord.create!(
          integration: integration,
          record: job,
          remote_slug: remote_slug
        )

        # Mock finding the existing job via integration records
        allow(materializer).to receive(:job_records).and_return(IntegrationRecord.where(id: integration_record.id))
        allow(materializer).to receive(:sync_caches).and_return(double(
                                                                  by_aspire_opportunity_number: [double(to_resource: won_opportunity)],
                                                                  aspire_opportunities: double(where: double(map: []))
                                                                ))

        materializer.send(:materialize_job, numbered_opportunity)

        job.reload
        expect(job.status).to eq("completed") # Should remain completed
      end
    end
  end
end
