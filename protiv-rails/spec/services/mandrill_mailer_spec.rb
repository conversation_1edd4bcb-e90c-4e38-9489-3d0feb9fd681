# frozen_string_literal: true

require "rails_helper"

RSpec.describe Man<PERSON>ill<PERSON><PERSON><PERSON> do
  describe ".send_mail" do
    let(:to) { "<EMAIL>" }
    let(:subject) { "Test Email" }
    let(:html_content) { "<p>Test email content</p>" }
    let(:api_key) { "test_mandrill_api_key" }
    let(:from_email) { "<EMAIL>" }
    let(:from_name) { "Protiv" }
    let(:http_response) { instance_double(Net::HTTPResponse, body: response_body, code: response_code) }
    let(:response_body) { [{ "email" => to, "status" => "sent" }].to_json }
    let(:response_code) { "200" }
    let(:http) { instance_double(Net::HTTP) }

    before do
      allow(ENV).to receive(:[]).and_call_original
      allow(ENV).to receive(:[]).with("MANDRILL_API_KEY").and_return(api_key)
      allow(ENV).to receive(:fetch).with("MAILER_FROM", "<EMAIL>").and_return(from_email)
      allow(ENV).to receive(:fetch).with("MAILER_FROM_NAME", "Protiv").and_return(from_name)
      allow(Net::HTTP).to receive(:start).and_yield(http)
      allow(http).to receive(:request).and_return(http_response)
    end

    it "sends an email via Mandrill API" do
      expect(Net::HTTP).to receive(:start).with("mandrillapp.com", 443, use_ssl: true)
      result = MandrillMailer.send_mail(
        to: to,
        subject: subject,
        html_content: html_content
      )
      expect(result).to be true
    end

    context "when the API returns an error" do
      let(:response_body) { [{ "email" => to, "status" => "rejected", "reject_reason" => "Invalid API key" }].to_json }

      it "raises an error with the rejection reason" do
        expect(Rails.logger).to receive(:error).with(/Failed to send email via Mandrill: Invalid API key/)
        expect {
          MandrillMailer.send_mail(
            to: to,
            subject: subject,
            html_content: html_content
          )
        }.to raise_error(/Mandrill API error: Invalid API key/)
      end
    end

    context "when there's a network error" do
      before do
        allow(Net::HTTP).to receive(:start).and_raise(StandardError.new("Network error"))
      end

      it "logs the error and re-raises it" do
        expect(Rails.logger).to receive(:error).with("Failed to send email: Network error")
        expect {
          MandrillMailer.send_mail(
            to: to,
            subject: subject,
            html_content: html_content
          )
        }.to raise_error(StandardError, "Network error")
      end
    end
  end
end
