# frozen_string_literal: true

require "rails_helper"

RSpec.describe PayPeriodGeneratorService do
  describe "for pay periods" do
    describe "with a current payroll_schedule" do
      it "creates a future pay_period if a current pay_period exists" do
        FactoryBot.create_list(:organization, 5, :with_payroll_schedules)
        expect(PayrollSchedule.count).to eq 11
        expect(PayPeriod.count).to eq 12

        PayPeriodGeneratorService.new.execute

        expect(PayrollSchedule.count).to eq 11
        expect(PayPeriod.count).to eq 38
      end

      it "does not create a future pay_period if a current pay_period does not exists" do
        FactoryBot.create_list(:organization, 1, :with_payroll_schedules_no_pay_periods)
        expect(PayrollSchedule.count).to eq 3
        expect(PayPeriod.count).to eq 2

        PayPeriodGeneratorService.new.execute

        expect(PayrollSchedule.count).to eq 3
        expect(PayPeriod.count).to eq 8
      end
    end
  end
end
