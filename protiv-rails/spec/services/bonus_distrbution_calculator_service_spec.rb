# frozen_string_literal: true

require "rails_helper"

RSpec.describe CrewBonusDistributionCalculator do
  context "with a sub_pro_pay with hours" do
    let(:sub_pro_pay) { create(:milestone_sub_pro_pay_with_milestone_times,
                               budget_type: "hours",
                               budget_minutes: 143 * 60) }

    it "can calculate bonus distribution for equal" do
      bdc = CrewBonusDistributionCalculator.new(bonus_amount: Money.from_amount(299.50),
                                                distribution_type: "equal_rate",
                                                total_labor_cost: Money.from_amount(2995.00),
                                                total_crew_lead_labor_cost: Money.from_amount(2230.00),
                                                total_seconds_worked: 130 * 3600,
                                                total_crew_lead_seconds_worked: 85 * 3600)

      expect(bdc.equal_rate_per_hour_float.round(2)).to eq(2.30)
      share_of_bonus = bdc.share_of_bonus(crew_labor_cost: Money.from_amount(1350.00),
                                          crew_member_seconds_worked: 45 * 3600,
                                          crew_lead: false,
                                          manual_percent_hundredths: 0)
      expect(share_of_bonus).to eq Money.from_amount(103.67)
    end

    it "can calculate bonus distribution for weighted" do
      bdc = CrewBonusDistributionCalculator.new(bonus_amount: Money.from_amount(299.50),
                                                distribution_type: "equal_weighted",
                                                total_labor_cost: Money.from_amount(2995.00),
                                                total_crew_lead_labor_cost: Money.from_amount(2230.00),
                                                total_seconds_worked: 130 * 3600,
                                                total_crew_lead_seconds_worked: 85 * 3600)

      share_of_bonus = bdc.share_of_bonus(crew_labor_cost: Money.from_amount(1350.00),
                                          crew_member_seconds_worked: 45 * 3600,
                                          crew_lead: false,
                                          manual_percent_hundredths: 0)
      expect(share_of_bonus).to eq Money.from_amount(135.00)
    end

    it "can calculate bonus distribution for crew_lead_weighted for crew_leads" do
      bdc = CrewBonusDistributionCalculator.new(bonus_amount: Money.from_amount(299.50),
                                                distribution_type: "crew_lead_weighted",
                                                total_labor_cost: Money.from_amount(2995.00),
                                                total_crew_lead_labor_cost: Money.from_amount(2230.00),
                                                total_seconds_worked: 130 * 3600,
                                                total_crew_lead_seconds_worked: 85 * 3600)

      share_of_bonus = bdc.share_of_bonus(crew_labor_cost: Money.from_amount(1350.00),
                                          crew_member_seconds_worked: 45 * 3600,
                                          crew_lead: true,
                                          manual_percent_hundredths: 0)

      expect(share_of_bonus).to eq Money.from_amount(135.00)
    end

    it "can calculate bonus distribution for crew_lead_weighted for crew members" do
      bdc = CrewBonusDistributionCalculator.new(bonus_amount: Money.from_amount(299.50),
                                                distribution_type: "crew_lead_weighted",
                                                total_labor_cost: Money.from_amount(2995.00),
                                                total_crew_lead_labor_cost: Money.from_amount(2230.00),
                                                total_seconds_worked: 130 * 3600,
                                                total_crew_lead_seconds_worked: 85 * 3600)

      share_of_bonus = bdc.share_of_bonus(crew_labor_cost: Money.from_amount(540.00),
                                          crew_member_seconds_worked: 30 * 3600,
                                          crew_lead: false,
                                          manual_percent_hundredths: 0)

      expect(share_of_bonus).to eq Money.from_amount(51.00)
    end

    it "can calculate bonus distribution for manual_distribution" do
      bdc = CrewBonusDistributionCalculator.new(bonus_amount: Money.from_amount(299.50),
                                                distribution_type: "manual_distribution",
                                                total_labor_cost: Money.from_amount(2995.00),
                                                total_crew_lead_labor_cost: Money.from_amount(2230.00),
                                                total_seconds_worked: 130 * 3600,
                                                total_crew_lead_seconds_worked: 85 * 3600)
      share_of_bonus = bdc.share_of_bonus(crew_labor_cost: nil,
                                          crew_member_seconds_worked: nil,
                                          crew_lead: false,
                                          manual_percent_hundredths: 5000)

      expect(share_of_bonus).to eq Money.from_amount(149.75)
    end
  end
end
