# frozen_string_literal: true

require "rails_helper"

RSpec.describe AddUserToOrganization do
  let(:user) { create(:user) }
  let(:admin) { create(:user) }
  let(:organization) { create(:organization) }

  specify do
    result = AddUserToOrganization.new(organization: organization, user: user, role_type: "admin").execute

    expect(result).to be_ok
    expect(result.state).to be_instance_of(Role)

    expect(organization.admin_users).to include(user)
    expect(user.organizations).to include(organization)
  end

  specify do
    AddUserToOrganization.new(organization: organization, user: admin, role_type: "admin").execute
    AddUserToOrganization.new(organization: organization, user: user, role_type: "employee").execute

    expect(organization.admin_users.to_a).to contain_exactly(admin)
    expect(organization.employee_users.to_a).to contain_exactly(user)
    expect(organization.users.to_a).to contain_exactly(user, admin)
  end

  specify do
    result = AddUserToOrganization.new(organization: organization, user: user, role_type: "invalid").execute

    expect(result).not_to be_ok
    expect(result.error.message).to match(/invalid input value for enum role_type/)
  end
end
