# frozen_string_literal: true

require "rails_helper"

RSpec.describe CreateJob do
  let(:branch) { create(:branch) }

  specify do
    result = CreateJob.new(
      branch: branch,
      organization: branch.organization,
      job_name: "build-deck",
      last_activity_at: 2.weeks.ago
    ).execute

    expect(result).to be_ok

    job = result.state
    expect(job).to be_an_instance_of(Job)
    expect(job).to be_valid
    expect(job).to be_persisted
  end
end
