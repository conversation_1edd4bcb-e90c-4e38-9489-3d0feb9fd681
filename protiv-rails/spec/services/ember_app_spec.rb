# frozen_string_literal: true

require "rails_helper"

RSpec.describe EmberApp do
  include SpecHelpers::Ember

  INDEX_PATH = Rails.root.join("../@protiv/dashboard/app/index.html")

  let(:user) { nil }
  let(:organization) { nil }
  let(:additional_params) { {} }
  let(:index_html) do
    parse_index_html(ember_app.index_html(user, organization, **additional_params))
  end
  let(:bootstrap_js) do
    parse_bootstrap_script(ember_app.bootstrap_js(user, organization, **additional_params))
  end

  shared_examples_for :bootstrap_js do
    context "without user" do
      specify do
        expect(bootstrap_js.payload).to be_present
        expect(index_html.account_id).to be_nil
        expect(index_html.authentication_token).to be_nil
      end
    end

    context "with user" do
      let(:user) { create(:user) }

      specify do
        expect(bootstrap_js.payload).to be_present
        expect(bootstrap_js.account_id).to be_present
        expect(bootstrap_js.authentication_token).to be_present
      end
    end
  end

  shared_examples_for :index_html do
    context "without user" do
      specify do
        expect(index_html.script_node).to be_present
        expect(index_html.script_inner).to be_present
        expect(bootstrap_js.payload).to be_present
        expect(index_html.account_id).to be_nil
        expect(index_html.authentication_token).to be_nil
      end
    end

    context "with user" do
      let(:user) { create(:user) }

      specify do
        expect(index_html.script_node).to be_present
        expect(index_html.script_inner).to be_present
        expect(index_html.payload).to be_present
        expect(index_html.account_id).to be_present
        expect(User.find(index_html.account_id)).to eq(user)
        expect(index_html.authentication_token).to be_present
        expect(Session.find_by_token_for(:api, index_html.authentication_token).user).to eq(user)
      end
    end
  end

  describe "from index.html" do
    let(:ember_app) { EmberApp.load(path: INDEX_PATH) }

    it_behaves_like :index_html
    it_behaves_like :bootstrap_js

    specify do
      expect(index_html.redirect_script_node).to be_nil
    end

    describe "dev portal" do
      context "enabled" do
        let(:additional_params) { { dev: true } }

        specify do
          expect(bootstrap_js.payload.fetch("dev")).to be(true)
        end
      end

      context "disabled" do
        let(:additional_params) { { dev: false } }

        specify do
          # Omit the key when false – we don't need non-developers poking
          # around discovering the existence of this key by accident.
          expect(bootstrap_js.payload).not_to have_key("dev")
        end
      end
    end
  end

  describe "redirection" do
    let(:port) { rand(2000..12000) }
    let(:ember_app) { EmberApp.redirect_to_ember(port: port) }

    # These behaviors aren't as consequential for the redirect flow
    it_behaves_like :index_html
    it_behaves_like :bootstrap_js

    specify do
      expect(index_html.redirect_script_node.to_s).to match("url.port = #{port}")
    end
  end
end
