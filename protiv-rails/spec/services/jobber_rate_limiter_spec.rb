# frozen_string_literal: true

require 'rails_helper'

RSpec.describe JobberRateLimiter do
  subject(:rate_limiter) { described_class.new(integration_id: 'test_integration') }

  let(:mock_redis) { MockRedis.new }

  before do
    allow(Protiv::Application).to receive(:redis).and_return(mock_redis)
  end

  describe '#check_cost_availability' do
    context 'when no throttle status is cached' do
      it 'allows the request' do
        expect(rate_limiter.check_cost_availability(estimated_cost: 100)).to be true
      end
    end

    context 'when sufficient cost is available' do
      before do
        throttle_status = {
          maximum_available: 10000,
          currently_available: 5000,
          restore_rate: 500,
          updated_at: Time.current.to_i
        }
        mock_redis.setex(rate_limiter.send(:throttle_status_key), 60, throttle_status.to_json)
      end

      it 'allows the request' do
        expect(rate_limiter.check_cost_availability(estimated_cost: 100)).to be true
      end
    end

    context 'when insufficient cost is available' do
      before do
        throttle_status = {
          maximum_available: 10000,
          currently_available: 50,
          restore_rate: 500,
          updated_at: Time.current.to_i
        }
        mock_redis.setex(rate_limiter.send(:throttle_status_key), 60, throttle_status.to_json)
      end

      it 'raises CostLimitExceeded' do
        expect {
          rate_limiter.check_cost_availability(estimated_cost: 1000)
        }.to raise_error(JobberRateLimiter::CostLimitExceeded) do |error|
          expect(error.wait_time).to be > 0
          expect(error.available_cost).to eq(50)
          expect(error.needed_cost).to eq(1000)
        end
      end
    end

    context 'when circuit breaker is open' do
      before do
        rate_limiter.open_circuit_breaker!
      end

      it 'raises CircuitBreakerOpen' do
        expect {
          rate_limiter.check_cost_availability(estimated_cost: 100)
        }.to raise_error(JobberRateLimiter::CircuitBreakerOpen) do |error|
          expect(error.retry_after).to be > 0
        end
      end
    end
  end

  describe '#update_from_response' do
    let(:api_response) do
      {
        'extensions' => {
          'cost' => {
            'requestedQueryCost' => 150,
            'actualQueryCost' => 142,
            'throttleStatus' => {
              'maximumAvailable' => 10000,
              'currentlyAvailable' => 9858,
              'restoreRate' => 500
            }
          }
        }
      }
    end

    it 'stores throttle status' do
      rate_limiter.update_from_response(api_response)

      stored_status = rate_limiter.get_current_throttle_status
      expect(stored_status[:maximum_available]).to eq(10000)
      expect(stored_status[:currently_available]).to eq(9858)
      expect(stored_status[:restore_rate]).to eq(500)
    end

    it 'resets failure count on successful response' do
      # First, increment failure count
      rate_limiter.send(:increment_failure_count)
      expect(mock_redis.get(rate_limiter.send(:failure_count_key))).to eq('1')

      # Then update with successful response
      rate_limiter.update_from_response(api_response)
      expect(mock_redis.get(rate_limiter.send(:failure_count_key))).to be_nil
    end

    context 'with throttled response' do
      let(:throttled_response) do
        {
          'errors' => [
            { 'message' => 'Query was THROTTLED due to rate limiting' }
          ],
          'extensions' => {
            'cost' => {
              'throttleStatus' => {
                'maximumAvailable' => 10000,
                'currentlyAvailable' => 0,
                'restoreRate' => 500
              }
            }
          }
        }
      end

      it 'increments failure count' do
        rate_limiter.update_from_response(throttled_response)
        expect(mock_redis.get(rate_limiter.send(:failure_count_key))).to eq('1')
      end
    end
  end

  describe '#calculate_wait_time' do
    it 'calculates wait time based on cost deficit and restore rate' do
      wait_time = rate_limiter.calculate_wait_time(
        needed_cost: 1000,
        available_cost: 500,
        restore_rate: 250
      )

      # (1000 - 500) / 250 + 1 second buffer = 3 seconds
      expect(wait_time).to eq(3.0)
    end

    it 'returns 0 when sufficient cost is available' do
      wait_time = rate_limiter.calculate_wait_time(
        needed_cost: 500,
        available_cost: 1000,
        restore_rate: 250
      )

      expect(wait_time).to eq(0)
    end
  end

  describe '#circuit_breaker_open?' do
    it 'returns false when failure count is below threshold' do
      expect(rate_limiter.circuit_breaker_open?).to be false
    end

    it 'returns true when failure count exceeds threshold' do
      # Simulate multiple failures
      5.times { rate_limiter.send(:increment_failure_count) }
      expect(rate_limiter.circuit_breaker_open?).to be true
    end

    it 'returns false after timeout period' do
      # Open circuit breaker
      rate_limiter.open_circuit_breaker!
      expect(rate_limiter.circuit_breaker_open?).to be true

      # Simulate timeout by setting old timestamp
      old_timestamp = (Time.current - 400).to_i
      mock_redis.set(rate_limiter.send(:last_failure_key), old_timestamp)

      expect(rate_limiter.circuit_breaker_open?).to be false
    end
  end

  describe '#reset_circuit_breaker!' do
    it 'clears failure count and last failure time' do
      rate_limiter.open_circuit_breaker!
      expect(rate_limiter.circuit_breaker_open?).to be true

      rate_limiter.reset_circuit_breaker!
      expect(rate_limiter.circuit_breaker_open?).to be false
    end
  end
end
