# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Metrics::LaborMetricCalculator do
  let!(:org) { create(:organization) }
  let!(:tm_invoice_type) { create(:invoice_type, organization: org, name: 'T&M') }
  let!(:fixed_invoice_type) { create(:invoice_type, organization: org, name: 'Fixed') }
  let!(:worker_i) { create(:identity, organization: org) }
  let!(:job_i) { create(:job, organization: org) }
  let!(:milestone_i) { create(:milestone, job: job_i, invoice_type: tm_invoice_type) }
  let(:date) { Date.current }

  # Helper for time parsing
  def parse_time(time_str)
    Time.zone.parse(time_str)
  end

  describe '#call' do
    # Scenario 1: Simple T&M only case - one milestone
    context 'when calculating for a simple day with one T&M milestone' do
      let!(:clock_time) do
        create(:clock_time,
          identity: worker_i,
          start_at: date.to_time.change(hour: 9),
          end_at: date.to_time.change(hour: 17),
          break_time_seconds: 0) # 8 hours clocked
      end

      let!(:milestone_time) do
        create(:milestone_time,
          identity: worker_i,
          milestone: milestone_i,
          start_time: date.to_time.change(hour: 10),
          end_time: date.to_time.change(hour: 16), # 6 hours billable
          base_hourly_rate_cents: 2000,
          labor_cost_cents: 12000)
      end

      let!(:benchmark) { create(:labor_efficiency_benchmark, organization: org, target_percentage: 80.0) }

      subject(:result) do
        described_class.new(identity_id: worker_i.id, milestone_id: milestone_i.id, date: date).call
      end

      it 'allocates all non-billable time to the single milestone' do
        # 8 hours clocked, 6 hours billable
        # 2 hours non-billable, all allocated to this milestone
        expect(result[:total_clocked_seconds]).to eq(8 * 3600)
        expect(result[:total_billable_seconds]).to eq(6 * 3600)
        expect(result[:non_billable_seconds]).to eq(2 * 3600)

        # Efficiency: 6h out of 8h total = 75%
        expect(result[:efficiency_percentage]).to be_within(0.1).of(75.0)

        # Labor costs
        expect(result[:labor_cost_cents]).to eq(12000)
        expect(result[:non_billable_labor_cost_cents]).to eq(4000) # 2h * $20/h

        # Benchmark comparison
        expect(result[:benchmark_percentage]).to eq(80.0)
        expect(result[:variance_from_benchmark]).to be_within(0.1).of(-5.0)
      end
    end

    # Scenario 2: Multiple T&M milestones
    context 'when calculating for a day with multiple T&M milestones' do
      let!(:another_job) { create(:job, organization: org) }
      let!(:another_milestone) { create(:milestone, job: another_job, invoice_type: tm_invoice_type) }

      let!(:clock_time) do
        create(:clock_time,
          identity: worker_i,
          start_at: date.to_time.change(hour: 8),
          end_at: date.to_time.change(hour: 17),
          break_time_seconds: 3600) # 8 hours clocked (9h - 1h break)
      end

      before do
        # Worker logs 3 hours to first milestone
        create(:milestone_time,
          identity: worker_i,
          milestone: milestone_i,
          start_time: date.to_time.change(hour: 9),
          end_time: date.to_time.change(hour: 12),
          base_hourly_rate_cents: 2000,
          labor_cost_cents: 6000)

        # Worker logs 2 hours to second milestone
        create(:milestone_time,
          identity: worker_i,
          milestone: another_milestone,
          start_time: date.to_time.change(hour: 14),
          end_time: date.to_time.change(hour: 16),
          base_hourly_rate_cents: 2500,
          labor_cost_cents: 5000)
      end

      let!(:benchmark) { create(:labor_efficiency_benchmark, organization: org, target_percentage: 75.0) }

      context 'for the first milestone' do
        subject(:result) do
          described_class.new(identity_id: worker_i.id, milestone_id: milestone_i.id, date: date).call
        end

        it 'distributes non-billable time proportionally' do
          # 8 hours clocked, 5 hours total billable (3h + 2h)
          # 3 hours non-billable to be distributed
          # Milestone 1 has 3/5 (60%) of recorded time, so it gets 1.8h of non-billable
          expect(result[:total_clocked_seconds]).to eq(8 * 3600)
          expect(result[:total_billable_seconds]).to eq(3 * 3600)
          expect(result[:non_billable_seconds]).to eq((1.8 * 3600).round)

          # Efficiency: 3h / (3h + 1.8h) = 3/4.8 = 62.5%
          expect(result[:efficiency_percentage]).to be_within(0.1).of(62.5)

          # Labor costs
          expect(result[:labor_cost_cents]).to eq(6000)
          expect(result[:non_billable_labor_cost_cents]).to eq((1.8 * 2000).round)

          # Benchmark comparison
          expect(result[:benchmark_percentage]).to eq(75.0)
          expect(result[:variance_from_benchmark]).to be_within(0.1).of(-12.5)
        end
      end

      context 'for the second milestone' do
        subject(:result) do
          described_class.new(identity_id: worker_i.id, milestone_id: another_milestone.id, date: date).call
        end

        it 'distributes non-billable time proportionally' do
          # 8 hours clocked, 5 hours total billable (3h + 2h)
          # 3 hours non-billable to be distributed
          # Milestone 2 has 2/5 (40%) of recorded time, so it gets 1.2h of non-billable
          expect(result[:total_clocked_seconds]).to eq(8 * 3600)
          expect(result[:total_billable_seconds]).to eq(2 * 3600)
          expect(result[:non_billable_seconds]).to eq((1.2 * 3600).round)

          # Efficiency: 2h / (2h + 1.2h) = 2/3.2 = 62.5%
          expect(result[:efficiency_percentage]).to be_within(0.1).of(62.5)

          # Labor costs
          expect(result[:labor_cost_cents]).to eq(5000)
          expect(result[:non_billable_labor_cost_cents]).to eq((1.2 * 2500).round)

          # Benchmark comparison
          expect(result[:benchmark_percentage]).to eq(75.0)
          expect(result[:variance_from_benchmark]).to be_within(0.1).of(-12.5)
        end
      end
    end

    # Scenario 3: Mixed T&M and Fixed milestones
    context 'when calculating for a day with both T&M and Fixed milestones' do
      let!(:fixed_job) { create(:job, organization: org) }
      let!(:fixed_milestone) { create(:milestone, job: fixed_job, invoice_type: fixed_invoice_type) }

      let!(:clock_time) do
        create(:clock_time,
          identity: worker_i,
          start_at: date.to_time.change(hour: 9),
          end_at: date.to_time.change(hour: 17),
          break_time_seconds: 0) # 8 hours clocked
      end

      before do
        # Worker logs 2 hours to T&M milestone
        create(:milestone_time,
          identity: worker_i,
          milestone: milestone_i,
          start_time: date.to_time.change(hour: 9),
          end_time: date.to_time.change(hour: 11),
          base_hourly_rate_cents: 2000,
          labor_cost_cents: 4000)

        # Worker logs 3 hours to Fixed milestone
        create(:milestone_time,
          identity: worker_i,
          milestone: fixed_milestone,
          start_time: date.to_time.change(hour: 13),
          end_time: date.to_time.change(hour: 16),
          base_hourly_rate_cents: 0)
      end

      let!(:benchmark) { create(:labor_efficiency_benchmark, organization: org, target_percentage: 70.0) }

      context 'for the T&M milestone' do
        subject(:result) do
          described_class.new(identity_id: worker_i.id, milestone_id: milestone_i.id, date: date).call
        end

        it 'distributes non-billable time proportionally' do
          # 8 hours clocked, 5 hours total recorded (2h T&M + 3h Fixed)
          # 3 hours non-billable to be distributed
          # T&M milestone has 2/5 (40%) of recorded time, so it gets 1.2h of non-billable
          expect(result[:total_clocked_seconds]).to eq(8 * 3600)
          expect(result[:total_billable_seconds]).to eq(2 * 3600)
          expect(result[:non_billable_seconds]).to eq((1.2 * 3600).round)

          # Efficiency: 2h / (2h + 1.2h) = 2/3.2 = 62.5%
          expect(result[:efficiency_percentage]).to be_within(0.1).of(62.5)

          # Labor costs
          expect(result[:labor_cost_cents]).to eq(4000)
          expect(result[:non_billable_labor_cost_cents]).to eq((1.2 * 2000).round)

          # Benchmark comparison
          expect(result[:benchmark_percentage]).to eq(70.0)
          expect(result[:variance_from_benchmark]).to be_within(0.1).of(-7.5)
        end
      end

      context 'for the Fixed milestone' do
        subject(:result) do
          described_class.new(identity_id: worker_i.id, milestone_id: fixed_milestone.id, date: date).call
        end

        it 'distributes non-billable time but sets billable metrics to zero' do
          # 8 hours clocked, 5 hours total recorded (2h T&M + 3h Fixed)
          # 3 hours non-billable to be distributed
          # Fixed milestone has 3/5 (60%) of recorded time, so it gets 1.8h of non-billable
          expect(result[:total_clocked_seconds]).to eq(8 * 3600)
          expect(result[:total_billable_seconds]).to eq(0) # No billable time for Fixed
          expect(result[:non_billable_seconds]).to eq((1.8 * 3600).round)

          # Efficiency is 0% for Fixed milestone because it has no billable time
          expect(result[:efficiency_percentage]).to eq(0.0)

          # No labor costs for Fixed milestone in T&M context
          expect(result[:labor_cost_cents]).to eq(0)
          expect(result[:non_billable_labor_cost_cents]).to eq(0)

          # Benchmark comparison
          expect(result[:benchmark_percentage]).to eq(70.0)
          expect(result[:variance_from_benchmark]).to eq(-70.0)
        end
      end
    end

    # Scenario 4: No milestone time entries for this milestone
    context 'when calculating for a milestone with no time entries' do
      let!(:unused_milestone) { create(:milestone, job: job_i, invoice_type: tm_invoice_type) }

      let!(:clock_time) do
        create(:clock_time,
          identity: worker_i,
          start_at: date.to_time.change(hour: 9),
          end_at: date.to_time.change(hour: 17),
          break_time_seconds: 0) # 8 hours clocked
      end

      before do
        # Worker logs time to another milestone but not to the target milestone
        create(:milestone_time,
          identity: worker_i,
          milestone: milestone_i, # This is not the unused_milestone
          start_time: date.to_time.change(hour: 10),
          end_time: date.to_time.change(hour: 15),
          base_hourly_rate_cents: 2000)
      end

      subject(:result) do
        described_class.new(identity_id: worker_i.id, milestone_id: unused_milestone.id, date: date).call
      end

      it 'allocates no non-billable time to the unused milestone' do
        expect(result[:total_clocked_seconds]).to eq(8 * 3600)
        expect(result[:total_billable_seconds]).to eq(0)
        expect(result[:non_billable_seconds]).to eq(0) # No time recorded, so no allocation

        expect(result[:efficiency_percentage]).to eq(0.0)
        expect(result[:labor_cost_cents]).to eq(0)
        expect(result[:non_billable_labor_cost_cents]).to eq(0)
      end
    end

    # Scenario 5: No clocked time for the day
    context 'when calculating for a day with no clocked time' do
      let(:empty_date) { Date.current - 1.day } # Use a date with no clock time

      subject(:result) do
        described_class.new(identity_id: worker_i.id, milestone_id: milestone_i.id, date: empty_date).call
      end

      it 'returns all zero metrics' do
        expect(result[:total_clocked_seconds]).to eq(0)
        expect(result[:total_billable_seconds]).to eq(0)
        expect(result[:non_billable_seconds]).to eq(0)

        expect(result[:efficiency_percentage]).to eq(0.0)
        expect(result[:labor_cost_cents]).to eq(0)
        expect(result[:non_billable_labor_cost_cents]).to eq(0)
      end
    end
  end
end
