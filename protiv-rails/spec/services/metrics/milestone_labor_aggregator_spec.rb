# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Metrics::MilestoneLaborAggregator, type: :service do
  # Use let! for records that need to exist before the service is called
  let!(:organization) { create(:organization) }
  let!(:job) { create(:job, organization: organization) }
  # Set a contract price on the milestone for testing cost %
  let!(:milestone) { create(:milestone, job: job, contract_price_cents: 100_000) } # e.g., $1000.00
  let!(:identity) { create(:identity, organization: organization) }

  # --- Test Data Setup ---
  # Create some sample LaborMetric records for the milestone
  let!(:metric1) do
    create(:labor_metric,
           milestone: milestone,
           identity: identity,
           organization: organization,
           date: Date.new(2024, 5, 1),
           total_clocked_seconds: 3600, # 1 hr
           total_billable_seconds: 3000, # 50 min -> 83.33% efficiency
           non_billable_seconds: 600,
           efficiency_percentage: 83.33,
           labor_cost_cents: 2000, # $20.00
           non_billable_labor_cost_cents: 500, # $5.00
           benchmark_percentage: 75.0,
           variance_from_benchmark: 8.33)
  end

  let!(:metric2) do
    create(:labor_metric,
           milestone: milestone,
           identity: identity, # Same identity, could be different
           organization: organization,
           date: Date.new(2024, 5, 2),
           total_clocked_seconds: 7200, # 2 hrs
           total_billable_seconds: 5400, # 1.5 hrs -> 75.00% efficiency
           non_billable_seconds: 1800,
           efficiency_percentage: 75.00,
           labor_cost_cents: 4000, # $40.00
           non_billable_labor_cost_cents: 1000, # $10.00
           benchmark_percentage: 75.0, # Benchmark could potentially vary daily, but unlikely
           variance_from_benchmark: 0.0)
  end

  # A metric outside the typical date range for filtering tests
  let!(:metric_old) do
    create(:labor_metric,
           milestone: milestone,
           identity: identity,
           organization: organization,
           date: Date.new(2024, 4, 15),
           total_clocked_seconds: 1800, # 0.5 hr
           total_billable_seconds: 1800, # 0.5 hr -> 100% efficiency
           non_billable_seconds: 0,
           efficiency_percentage: 100.00,
           labor_cost_cents: 1000, # $10.00
           non_billable_labor_cost_cents: 0, # $0.00
           benchmark_percentage: 80.0, # Different benchmark for example
           variance_from_benchmark: 20.0)
  end

  # Service instance helper
  def run_aggregator(start_date: nil, end_date: nil)
    described_class.new(milestone_id: milestone.id, start_date: start_date, end_date: end_date).call
  end

  # --- Test Cases ---

  describe '#call' do
    context 'aggregating all time' do
      subject(:result) { run_aggregator }
      let(:summary) { result[:labor_metrics_summary] }

      it 'correctly sums total hours and costs' do
        expect(summary[:total_time][:hours]).to be_within(0.01).of((3600 + 7200 + 1800) / 3600.0) # 3.5 hours
        expect(summary[:billable_time][:hours]).to be_within(0.01).of((3000 + 5400 + 1800) / 3600.0) # 2.83 hours
        expect(summary[:non_billable_time][:hours]).to be_within(0.01).of((600 + 1800 + 0) / 3600.0) # 0.67 hours
        expect(summary[:billable_time][:cost_cents]).to eq(2000 + 4000 + 1000) # 7000
        expect(summary[:non_billable_time][:cost_cents]).to eq(500 + 1000 + 0) # 1500
      end

      it 'calculates efficiency percentage' do
        # (10200 / 12600) * 100 = 80.952...
        expect(summary[:billable_time][:percentage]).to be_within(0.01).of(80.95)
        expect(summary[:non_billable_time][:percentage]).to be_within(0.01).of(19.05)
        expect(summary[:total_time][:percentage]).to eq(100.0)
      end

      it 'provides benchmark details' do
        # Assuming it picks one - let's check it gets *a* valid one from the set
        expect(summary[:benchmark][:target_percentage]).to eq(metric2.benchmark_percentage)
        expect(summary[:benchmark][:actual_percentage]).to be_within(0.01).of(80.95)

        benchmark = metric2.benchmark_percentage # 75.0
        efficiency = (10200.to_f / 12600.to_f) * 100.0 # ~80.95
        expected_variance = (efficiency - benchmark).round(2) # ~5.95
        expect(summary[:benchmark][:variance]).to be_within(0.01).of(expected_variance)
      end

      it 'calculates billable cost per hour in cents' do
        total_cost_cents = 7000 + 1500
        total_billable_hours = 10200 / 3600.0
        expected_cost_per_hour_cents = (total_cost_cents / total_billable_hours).round
        expect(summary[:billable_cost_per_hour]).to eq(expected_cost_per_hour_cents)
      end

      it 'calculates cost percentages of contract' do
        # Billable cost percentage
        billable_cost_cents = 7000
        contract_cents = 100_000
        expected_percentage = ((billable_cost_cents.to_f / contract_cents.to_f) * 100.0).round(2) # 7.00
        expect(summary[:billable_time][:percentage_of_contract]).to eq(expected_percentage)

        # Non-billable cost percentage
        non_billable_cost_cents = 1500
        expected_percentage = ((non_billable_cost_cents.to_f / contract_cents.to_f) * 100.0).round(2) # 1.50
        expect(summary[:non_billable_time][:percentage_of_contract]).to eq(expected_percentage)

        # Total cost percentage
        total_cost_cents = 8500
        expected_percentage = ((total_cost_cents.to_f / contract_cents.to_f) * 100.0).round(2) # 8.50
        expect(summary[:total_time][:percentage_of_contract]).to eq(expected_percentage)
      end

      it 'calculates charge rate in cents' do
        contract_cents = 100_000
        total_billable_hours = 10200 / 3600.0
        expected_charge_rate_cents = (contract_cents / total_billable_hours).round
        expect(summary[:charge_rate]).to eq(expected_charge_rate_cents)
      end
    end

    context 'with date filtering' do
      subject(:result) { run_aggregator(start_date: Date.new(2024, 5, 1), end_date: Date.new(2024, 5, 1)) }
      let(:summary) { result[:labor_metrics_summary] }

      it 'only includes metrics within the date range' do
        expect(summary[:total_time][:hours]).to be_within(0.01).of(1.0) # Only metric1
        expect(summary[:billable_time][:hours]).to be_within(0.01).of(0.83)
        expect(summary[:non_billable_time][:hours]).to be_within(0.01).of(0.17)
        expect(summary[:billable_time][:cost_cents]).to eq(2000)
        expect(summary[:non_billable_time][:cost_cents]).to eq(500)
        expect(summary[:billable_time][:percentage]).to be_within(0.01).of(83.33)
        expect(summary[:benchmark][:target_percentage]).to eq(metric1.benchmark_percentage)
        expect(summary[:benchmark][:variance]).to be_within(0.01).of(8.33)
      end
    end

    context 'when milestone has no labor metrics' do
      subject(:result) do
        # Create a milestone with no metrics
        empty_milestone = create(:milestone, job: job, contract_price_cents: 50000)
        described_class.new(milestone_id: empty_milestone.id).call
      end
      let(:summary) { result[:labor_metrics_summary] }

      it 'returns zero/default values' do
        expect(summary[:total_time][:hours]).to eq(0.0)
        expect(summary[:billable_time][:hours]).to eq(0.0)
        expect(summary[:non_billable_time][:hours]).to eq(0.0)
        expect(summary[:billable_time][:cost_cents]).to eq(0)
        expect(summary[:non_billable_time][:cost_cents]).to eq(0)
        expect(summary[:billable_time][:percentage]).to eq(0.0)
        expect(summary[:benchmark][:target_percentage]).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
        expect(summary[:benchmark][:variance]).to eq(-Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
        expect(summary[:billable_cost_per_hour]).to eq(0)
        expect(summary[:billable_time][:percentage_of_contract]).to eq(0.0)
        expect(summary[:charge_rate]).to eq(0)
      end
    end

    context 'when milestone has zero billable hours' do
      let!(:non_billable_metric) do
        create(:labor_metric,
               milestone: milestone,
               identity: identity,
               organization: organization,
               date: Date.new(2024, 5, 3),
               total_clocked_seconds: 3600,
               total_billable_seconds: 0, # Zero billable
               non_billable_seconds: 3600,
               efficiency_percentage: 0.0,
               labor_cost_cents: 0, # Assume cost is tied to billable
               non_billable_labor_cost_cents: 1500, # $15.00
               benchmark_percentage: 75.0,
               variance_from_benchmark: -75.0)
      end

      subject(:result) { run_aggregator(start_date: Date.new(2024, 5, 3), end_date: Date.new(2024, 5, 3)) }
      let(:summary) { result[:labor_metrics_summary] }

      it 'returns 0 for billable_cost_per_hour' do
        expect(summary[:billable_cost_per_hour]).to eq(0)
      end

      it 'returns 0 for charge_rate' do
        expect(summary[:charge_rate]).to eq(0)
      end

      it 'calculates zero efficiency' do
        expect(summary[:billable_time][:percentage]).to eq(0.0)
      end
    end

    context 'when milestone has zero contract value' do
      let!(:zero_contract_milestone) { create(:milestone, job: job, contract_price_cents: 0) }
      let!(:metric_for_zero_contract) do
        create(:labor_metric,
               milestone: zero_contract_milestone,
               organization: organization,
               labor_cost_cents: 1000,
               non_billable_labor_cost_cents: 500)
      end

      subject(:result) { described_class.new(milestone_id: zero_contract_milestone.id).call }
      let(:summary) { result[:labor_metrics_summary] }

      it 'returns 0.0 for all percentage_of_contract values' do
        expect(summary[:billable_time][:percentage_of_contract]).to eq(0.0)
        expect(summary[:non_billable_time][:percentage_of_contract]).to eq(0.0)
        expect(summary[:total_time][:percentage_of_contract]).to eq(0.0)
      end
    end
  end
end
