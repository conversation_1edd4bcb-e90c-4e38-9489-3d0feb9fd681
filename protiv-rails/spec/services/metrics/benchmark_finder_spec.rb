# frozen_string_literal: true

require "rails_helper"

RSpec.describe Metrics::BenchmarkFinder do
  let(:organization) { create(:organization) }
  let(:job) { create(:job, organization: organization) }
  let(:milestone) { create(:milestone, job: job) }

  describe "#call" do
    context "when an organization-level benchmark exists" do
      let!(:org_benchmark) do
        create(:labor_efficiency_benchmark,
               organization: organization,
               level: "organization",
               target_percentage: 85.0,
               active: true)
      end

      it "returns the organization benchmark when given a milestone" do
        finder = described_class.new(milestone)
        expect(finder.call).to eq(85.0)
      end

      it "returns the organization benchmark when given a job" do
        finder = described_class.new(job)
        expect(finder.call).to eq(85.0)
      end

      it "returns the organization benchmark when given a hash with organization_id" do
        finder = described_class.new(organization_id: organization.id)
        expect(finder.call).to eq(85.0)
      end
    end

    context "when no benchmark exists" do
      it "returns the default target percentage" do
        finder = described_class.new(milestone)
        expect(finder.call).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
      end
    end

    context "when organization benchmark is inactive" do
      let!(:inactive_benchmark) do
        create(:labor_efficiency_benchmark,
               organization: organization,
               level: "organization",
               target_percentage: 85.0,
               active: false)
      end

      it "returns the default target percentage" do
        finder = described_class.new(milestone)
        expect(finder.call).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
      end
    end

    context "with invalid context" do
      it "raises an ArgumentError if organization_id can't be extracted" do
        expect { described_class.new({}) }.to raise_error(ArgumentError)
      end
    end
  end
end
