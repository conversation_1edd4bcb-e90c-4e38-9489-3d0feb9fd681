# frozen_string_literal: true

require "rails_helper"

RSpec.describe Metrics::BenchmarkManager do
  let(:organization) { create(:organization) }
  subject { described_class.new(organization) }

  describe "#set_custom_percentage" do
    context "when no benchmark exists" do
      before do
        # Remove the automatically created benchmark
        organization.labor_efficiency_benchmark&.destroy
      end

      it "creates a new benchmark with the specified percentage" do
        expect {
          subject.set_custom_percentage(85.0)
        }.to change(LaborEfficiencyBenchmark, :count).by(1)

        benchmark = LaborEfficiencyBenchmark.last
        expect(benchmark.organization).to eq(organization)
        expect(benchmark.level).to eq("organization")
        expect(benchmark.level_record_id).to be_nil
        expect(benchmark.target_percentage).to eq(85.0)
        expect(benchmark.active).to be true
      end
    end

    context "when a benchmark already exists" do
      let!(:existing_benchmark) do
        # Use the automatically created benchmark
        org_benchmark = organization.labor_efficiency_benchmark
        org_benchmark.update!(target_percentage: 75.0)
        org_benchmark
      end

      it "updates the existing benchmark" do
        expect {
          subject.set_custom_percentage(90.0)
        }.not_to change(LaborEfficiencyBenchmark, :count)

        existing_benchmark.reload
        expect(existing_benchmark.target_percentage).to eq(90.0)
        expect(existing_benchmark.active).to be true
      end
    end

    context "when an inactive benchmark exists" do
      let!(:inactive_benchmark) do
        # Use the automatically created benchmark and make it inactive
        org_benchmark = organization.labor_efficiency_benchmark
        org_benchmark.update!(target_percentage: 75.0, active: false)
        org_benchmark
      end

      it "activates and updates the existing benchmark" do
        expect {
          subject.set_custom_percentage(95.0)
        }.not_to change(LaborEfficiencyBenchmark, :count)

        inactive_benchmark.reload
        expect(inactive_benchmark.target_percentage).to eq(95.0)
        expect(inactive_benchmark.active).to be true
      end
    end

    context "with invalid percentage" do
      it "raises a validation error for percentage below 0" do
        expect {
          subject.set_custom_percentage(-5.0)
        }.to raise_error(ActiveRecord::RecordInvalid)
      end

      it "raises a validation error for percentage above 100" do
        expect {
          subject.set_custom_percentage(105.0)
        }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
  end

  describe "#reset_to_default" do
    it "sets the benchmark to the default percentage (75%)" do
      expect(subject).to receive(:set_custom_percentage)
        .with(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
        .and_call_original

      benchmark = subject.reset_to_default
      expect(benchmark.target_percentage).to eq(Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE)
    end
  end
end
