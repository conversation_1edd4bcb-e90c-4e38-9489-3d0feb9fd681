# frozen_string_literal: true

require "rails_helper"

RSpec.describe StandardMailer do
  describe ".send_mail" do
    let(:to) { "<EMAIL>" }
    let(:subject) { "Test Email" }
    let(:html_content) { "<p>Test email content</p>" }
    let(:mail) { instance_double(Mail::Message) }

    before do
      allow(Mail).to receive(:new).and_return(mail)
      allow(mail).to receive(:from=)
      allow(mail).to receive(:to=)
      allow(mail).to receive(:subject=)
      allow(mail).to receive(:html_part).and_return(double(content_type: nil, body: nil))
      allow(mail).to receive(:deliver).and_return(true)
    end

    it "creates and delivers a mail message" do
      expect(mail).to receive(:deliver)

      StandardMailer.send_mail(
        to: to,
        subject: subject,
        html_content: html_content
      )
    end

    context "when delivery fails" do
      before do
        allow(mail).to receive(:deliver).and_raise(StandardError.new("Delivery failed"))
      end

      it "logs the error and re-raises it" do
        expect(Rails.logger).to receive(:error).with("Failed to send email: Delivery failed")

        expect {
          StandardMailer.send_mail(
            to: to,
            subject: subject,
            html_content: html_content
          )
        }.to raise_error(StandardError, "Delivery failed")
      end
    end
  end
end
