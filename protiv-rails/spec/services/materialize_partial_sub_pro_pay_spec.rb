# frozen_string_literal: true

require "rails_helper"

RSpec.describe MaterializePartialSubProPay do
  describe "for a milestone based ProPay" do
    describe "with a single part" do
      let(:sub_pro_pay) do
        create(:single_milestone_sub_pro_pay_with_time_data,
               budget_type: "hours",
               distribution_type: 'equal_rate',
               budget_minutes: 143 * 60)
      end

      it "materializes an partial_sub_pro_pay to bonus line items" do
        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay
        expect(partial_sub_pro_pay.status).to eq("draft")

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok
        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(4)
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(299.50))
      end

      it "adjusts crew bonus by a bonus_pool's crew_percent" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 25_00
        sub_pro_pay.pro_pay.company_percent_hundredths = 75_00
        sub_pro_pay.pro_pay.save!

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok
        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(6)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.25))
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
      end

      it "retains crew bonus by a bonus_pool's crew_retention_percent" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 25_00
        sub_pro_pay.pro_pay.company_percent_hundredths = 75_00
        sub_pro_pay.pro_pay.crew_retention_percent_hundredths = 60_00
        sub_pro_pay.pro_pay.save!

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(10)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(29.96))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew_retention").sum(&:bonus)).to eq(Money.from_amount(44.92))
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.01))
      end

      it "assigns bonus to manager based on the bonus_pool" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 40_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 60_00
        sub_pro_pay.pro_pay.save!

        original_manager = ProPayPayable.create_or_find_by(pro_pay: sub_pro_pay.pro_pay,
                                                           payable: create(:identity),
                                                           participating: true,
                                                           rate_class: "manager")

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(original_manager.reload.participating).to eq(false)

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(5)
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.4))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.6))
      end

      it "assigns bonus to company others crew_leads and manager based on the bonus_pool" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 40_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 15_00
        sub_pro_pay.pro_pay.other_percent_hundredths = 30_00
        sub_pro_pay.pro_pay.crew_lead_percent_hundredths = 5_00
        sub_pro_pay.pro_pay.company_percent_hundredths = 10_00
        sub_pro_pay.pro_pay.save!

        original_manager = ProPayPayable.create_or_find_by(pro_pay: sub_pro_pay.pro_pay,
                                                           payable: create(:identity),
                                                           participating: true,
                                                           rate_class: "manager")

        other_one = create(:identity)
        other_two = create(:identity)
        ProPayPayable.create_or_find_by(pro_pay: sub_pro_pay.pro_pay, payable: other_one, rate_class: "other")
        ProPayPayable.create_or_find_by(pro_pay: sub_pro_pay.pro_pay, payable: other_two, rate_class: "other")

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(original_manager.reload.participating).to eq(false)

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(11)
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.4))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.15))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "other", payable: other_one).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.15))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "other", payable: other_two).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.15))
        crew_leads = sub_pro_pay.organization.identities.where(crew_lead: true)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew_lead", payable: crew_leads[0]).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.025))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew_lead", payable: crew_leads[1]).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.025))

        # company receives bonus and rounding separate source lines
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "company").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.1))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.02))
        expect(sub_pro_pay.bonus_line_items.where(payable: sub_pro_pay.organization).sum(&:bonus)).to eq(Money.from_amount((299.50 * 0.1) - 0.02))
      end
    end

    describe "with multiple parts" do
      let(:sub_pro_pay) do
        create(:single_milestone_sub_pro_pay,
               budget_type: "hours",
               distribution_type: 'equal_rate',
               budget_minutes: 143 * 60)
      end

      it "multiple partial_sub_pro_pays and related line items can be created" do
        milestone = sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable

        crew = sub_pro_pay.organization.identities.order(:id)
        #   Add some intermediate times
        MilestoneTimesTestData.first_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 50).execute

        sub_pro_pay.reload

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(181.47))
        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(5)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(181.48))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.01))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)

        partial_sub_pro_pay.finalize!

        # PP 2
        MilestoneTimesTestData.second_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 59).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state
        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(-57.59))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(2)
        expect(sub_pro_pay.bonus_line_items.count).to eq(10)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59 - 181.47))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59))

        partial_sub_pro_pay.finalize!

        # PP 3
        MilestoneTimesTestData.third_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state
        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(3)
        expect(sub_pro_pay.bonus_line_items.count).to eq(15)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(57.59 + 299.50))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(0.0))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)
      end
    end
  end

  describe "for a job based ProPay" do
    describe "with a single part" do
      let(:sub_pro_pay) do
        create(:job_sub_pro_pay_with_time_data,
               budget_type: "hours",
               distribution_type: 'equal_rate',
               budget_minutes: 143 * 60)
      end

      it "creates an partial_sub_pro_pay and related line items" do
        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(4)
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(299.50))
      end

      it "adjusts crew bonus by a bonus_pool's crew_percent" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 25_00
        sub_pro_pay.pro_pay.company_percent_hundredths = 75_00
        sub_pro_pay.pro_pay.save!

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(6)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.25))
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
      end

      it "retains crew bonus by a bonus_pool's crew_retention_percent" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 25_00
        sub_pro_pay.pro_pay.company_percent_hundredths = 75_00
        sub_pro_pay.pro_pay.crew_retention_percent_hundredths = 60_00
        sub_pro_pay.pro_pay.save!

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(10)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(29.96))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew_retention").sum(&:bonus)).to eq(Money.from_amount(44.92))
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.01))
      end

      it "assigns bonus to managers based on the bonus_pool" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 40_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 60_00
        sub_pro_pay.pro_pay.save!

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(5)
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.4))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.6))
      end

      it "assigns bonus to others based on the bonus_pool" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 40_00
        sub_pro_pay.pro_pay.other_percent_hundredths = 60_00
        sub_pro_pay.pro_pay.save!

        other_one = create(:identity)
        other_two = create(:identity)
        ProPayPayable.create_or_find_by(pro_pay: sub_pro_pay.pro_pay, payable: other_one, rate_class: "other")
        ProPayPayable.create_or_find_by(pro_pay: sub_pro_pay.pro_pay, payable: other_two, rate_class: "other")

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(6)
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.4))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "other", payable: other_one).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.3))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "other", payable: other_two).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.3))
      end

      it "assigns bonus to crew_leads based on the bonus_pool" do
        sub_pro_pay.pro_pay.crew_percent_hundredths = 40_00
        sub_pro_pay.pro_pay.crew_lead_percent_hundredths = 60_00

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(6)
        expect(sub_pro_pay.bonus_line_items.reload.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.4))
        crew_leads = sub_pro_pay.organization.identities.where(crew_lead: true)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew_lead", payable: crew_leads[0]).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.3))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew_lead", payable: crew_leads[1]).sum(&:bonus)).to eq(Money.from_amount(299.50 * 0.3))
      end
    end

    describe "with multiple parts" do
      let(:sub_pro_pay) do
        create(:job_sub_pro_pay,
               budget_type: "hours",
               distribution_type: 'equal_rate',
               budget_minutes: 143 * 60)
      end

      it "multiple partial_sub_pro_pays and related line items can be created" do
        job = sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable
        milestone = job.milestones[0]
        crew = milestone.job.organization.identities.order(:id)
        #   Add some intermediate times
        MilestoneTimesTestData.first_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 50).execute

        sub_pro_pay.reload

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(181.47))
        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(5)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(181.48))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.01))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)

        partial_sub_pro_pay.finalize!

        # PP 2
        milestone = job.milestones[1]

        MilestoneTimesTestData.second_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 59).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state
        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(-57.59))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(2)
        expect(sub_pro_pay.bonus_line_items.count).to eq(10)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59 - 181.47))

        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(-57.60))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(0.01))

        partial_sub_pro_pay.finalize!

        # PP 3
        milestone = job.milestones[2]
        MilestoneTimesTestData.third_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(3)
        expect(sub_pro_pay.bonus_line_items.count).to eq(15)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(57.59 + 299.50))

        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(299.50))

        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(0.0))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)
      end

      it "multiple partial_sub_pro_pays work with manager pool" do
        job = sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable

        sub_pro_pay.pro_pay.crew_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.save!

        milestone = job.milestones[0]
        crew = sub_pro_pay.pro_pay.organization.identities.order(:id)
        #   Add some intermediate times
        MilestoneTimesTestData.first_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 50).execute

        sub_pro_pay.reload

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(181.47))
        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(6)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(90.76))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(90.74))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.03))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)

        partial_sub_pro_pay.finalize!

        # PP 2
        sub_pro_pay.pro_pay.crew_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.save!

        manager_1 = create(:identity, name: "Manager 1")
        sub_pro_pay.pro_pay.manager = manager_1

        milestone = job.milestones[1]

        MilestoneTimesTestData.second_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 59).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(-57.59))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(2)
        expect(sub_pro_pay.bonus_line_items.count).to eq(12)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59 - 181.47))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(-28.80))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(-28.82))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(0.03))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59))

        partial_sub_pro_pay.finalize!

        # PP 3
        milestone = job.milestones[2]
        MilestoneTimesTestData.third_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(3)
        expect(sub_pro_pay.bonus_line_items.count).to eq(18)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(57.59 + 299.50))

        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(299.50))

        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(149.75))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(149.76))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.01))

        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)
      end

      it "multiple partial_sub_pro_pays support changing bonus pool composition" do
        job = sub_pro_pay.sub_pro_pay_pro_payables.first.pro_payable

        sub_pro_pay.pro_pay.crew_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.save!

        milestone = job.milestones[0]
        crew = milestone.job.organization.identities.order(:id)
        #   Add some intermediate times
        MilestoneTimesTestData.first_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 50).execute

        sub_pro_pay.reload

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(181.47))
        expect(sub_pro_pay.partial_sub_pro_pays.count).to eq(1)
        expect(sub_pro_pay.bonus_line_items.count).to eq(6)
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(90.76))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(90.74))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.03))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(partial_sub_pro_pay.bonus)

        partial_sub_pro_pay.finalize!

        # PP 2
        sub_pro_pay.pro_pay.crew_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.manager_percent_hundredths = 50_00
        sub_pro_pay.pro_pay.save!

        manager_1 = create(:identity, name: "Manager 1")
        sub_pro_pay.pro_pay.manager = manager_1
        sub_pro_pay.pro_pay.save!

        milestone = job.milestones[1]

        MilestoneTimesTestData.second_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 59).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(-57.59))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(2)
        expect(sub_pro_pay.bonus_line_items.count).to eq(13)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59 - 181.47))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(-28.80))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(-28.82))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(0.03))
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(-57.59))

        partial_sub_pro_pay.finalize!

        # PP 3
        milestone = job.milestones[2]
        MilestoneTimesTestData.third_intermediate_times.each do |t|
          MilestoneTime.create(milestone: milestone, identity: crew[t[:crew_num]], **t[:params])
        end

        sub_pro_pay.reload

        manager_2 = create(:identity, name: "Manager 2")
        sub_pro_pay.pro_pay.manager = manager_2
        sub_pro_pay.pro_pay.save!

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay

        result = MaterializePartialSubProPay.new(partial_sub_pro_pay: partial_sub_pro_pay,
                                                 estimated_percent_complete: 100).execute

        expect(result).to be_ok

        partial_sub_pro_pay = result.state

        expect(partial_sub_pro_pay.bonus).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.reload.partial_sub_pro_pays.count).to eq(3)
        # expect(sub_pro_pay.bonus_line_items.count).to eq(18)
        expect(partial_sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(57.59 + 299.50))

        payable = sub_pro_pay.bonus_line_items.collect(&:payable)

        payable.uniq.collect { |p| [p, sub_pro_pay.bonus_line_items.where(payable: p).sum(&:bonus)] }
        expect(sub_pro_pay.bonus_line_items.sum(&:bonus)).to eq(Money.from_amount(299.50))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "manager").sum(&:bonus)).to eq(Money.from_amount(149.75))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "crew").sum(&:bonus)).to eq(Money.from_amount(149.76))
        expect(sub_pro_pay.bonus_line_items.where(bonus_source: "rounding").sum(&:bonus)).to eq(Money.from_amount(-0.01))
      end
    end
  end
end
