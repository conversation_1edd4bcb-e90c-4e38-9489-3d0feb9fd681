# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ChangePasswordService do
  let(:password) { 'current-password123' }
  let(:user) { create(:user, password: password, password_confirmation: password) }
  let(:current_password) { password }
  let(:new_password) { 'new-secure-password456' }
  let(:service) { described_class.new(user, current_password, new_password) }

  describe '#call' do
    context 'when successful' do
      it 'updates the password and sets password_change_pending_verification flag' do
        expect {
          service.call
          user.reload
        }.to change { user.password_digest }.and change { user.password_change_pending_verification }.from(false).to(true)
      end

      it 'returns success' do
        result = service.call
        expect(result).to be_success
      end

      it 'generates a token for password change verification' do
        result = service.call
        expect(result.token).not_to be_nil
      end

      it 'enqueues a password change verification email' do
        expect {
          service.call
        }.to have_enqueued_mail(UserMailer, :password_change_verification_email)
      end
    end

    context 'when current_password is incorrect' do
      let(:current_password) { 'wrong-password' }

      it 'adds an error' do
        service.call
        expect(service.errors).to include(hash_including(code: "invalid_current_password"))
      end

      it 'returns failure' do
        result = service.call
        expect(result).not_to be_success
      end

      it 'does not enqueue a password change verification email' do
        expect {
          service.call
        }.not_to have_enqueued_mail(UserMailer, :password_change_verification_email)
      end
    end

    context 'when new_password is invalid' do
      let(:new_password) { 'short' }  # Less than 8 characters

      it 'adds validation errors' do
        service.call
        expect(service.errors).to include(hash_including(code: "invalid_password"))
      end

      it 'returns failure' do
        result = service.call
        expect(result).not_to be_success
      end

      it 'does not enqueue a password change verification email' do
        expect {
          service.call
        }.not_to have_enqueued_mail(UserMailer, :password_change_verification_email)
      end
    end
  end

  describe '#success?' do
    it 'returns true when there are no errors' do
      # No errors by default
      expect(service.success?).to be true
    end

    it 'returns false when there are errors' do
      service.instance_variable_set(:@errors, [{ code: 'some_error', detail: 'Error message' }])
      expect(service.success?).to be false
    end
  end
end
