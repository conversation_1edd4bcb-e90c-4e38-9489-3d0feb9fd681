# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VerifyPasswordChangeService do
  let(:user) { create(:user, password_change_pending_verification: true) }
  let(:token) { user.generate_token_for(:password_change_verification) }
  let(:service) { described_class.new(user, token) }

  describe '#call' do
    context 'when successful' do
      it 'sets password_change_pending_verification to false' do
        service.call
        expect(user.reload.password_change_pending_verification).to be false
      end

      it 'invalidates all user sessions' do
        expect(user).to receive(:invalidate_all_sessions!)
        service.call
      end

      it 'returns success' do
        expect(service.call).to be_success
      end
    end

    context 'when token is invalid' do
      let(:token) { 'invalid-token' }

      it 'does not invalidate sessions' do
        expect(user).not_to receive(:invalidate_all_sessions!)
        service.call
      end

      it 'adds an error' do
        service.call
        expect(service.errors).to include('Invalid or expired token')
      end

      it 'returns failure' do
        expect(service.call).not_to be_success
      end
    end

    context 'when token belongs to a different user' do
      let(:other_user) { create(:user, password_change_pending_verification: true) }
      let(:token) { other_user.generate_token_for(:password_change_verification) }

      it 'does not invalidate sessions for the original user' do
        expect(user).not_to receive(:invalidate_all_sessions!)
        service.call
      end

      it 'adds an error' do
        service.call
        expect(service.errors).to include('Token does not match this user')
      end

      it 'returns failure' do
        expect(service.call).not_to be_success
      end
    end

    context 'when password_change_pending_verification is already false' do
      let(:user) { create(:user, password_change_pending_verification: false) }

      it 'does not invalidate sessions' do
        expect(user).not_to receive(:invalidate_all_sessions!)
        service.call
      end

      it 'adds an error' do
        service.call
        expect(service.errors).to include('Password change already verified')
      end

      it 'returns failure' do
        expect(service.call).not_to be_success
      end
    end
  end

  describe '#success?' do
    it 'returns true when there are no errors' do
      allow(user).to receive(:invalidate_all_sessions!)
      service.call
      expect(service.success?).to be true
    end

    it 'returns false when there are errors' do
      service = described_class.new(user, 'invalid-token')
      service.call
      expect(service.success?).to be false
    end
  end
end
