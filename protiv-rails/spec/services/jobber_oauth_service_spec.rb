# frozen_string_literal: true

require "rails_helper"

RSpec.describe JobberOauthService do
  let(:client_id) { "test_client_id" }
  let(:client_secret) { "test_client_secret" }
  let(:redirect_uri) { "http://localhost:3000/callback" }
  let(:service) { described_class.new(client_id: client_id, client_secret: client_secret, redirect_uri: redirect_uri) }

  describe "#authorization_url" do
    subject { service.authorization_url(state: state) }

    context "with state parameter" do
      let(:state) { "test_state_123" }

      it "generates correct authorization URL with state" do
        expected_url = "https://api.getjobber.com/api/oauth/authorize?response_type=code&client_id=#{client_id}&redirect_uri=#{CGI.escape(redirect_uri)}&state=#{state}"
        expect(subject).to eq(expected_url)
      end
    end

    context "without state parameter" do
      let(:state) { nil }

      it "generates correct authorization URL without state" do
        expected_url = "https://api.getjobber.com/api/oauth/authorize?response_type=code&client_id=#{client_id}&redirect_uri=#{CGI.escape(redirect_uri)}"
        expect(subject).to eq(expected_url)
      end
    end
  end

  describe "#exchange_code_for_token" do
    let(:authorization_code) { "test_auth_code" }
    let(:oauth_client) { instance_double(OAuth2::Client) }
    let(:auth_code) { instance_double(OAuth2::Strategy::AuthCode) }
    let(:token) { instance_double(OAuth2::AccessToken) }

    before do
      allow(OAuth2::Client).to receive(:new).and_return(oauth_client)
      allow(oauth_client).to receive(:auth_code).and_return(auth_code)
    end

    context "when token exchange is successful" do
      let(:access_token) { "test_access_token" }
      let(:refresh_token) { "test_refresh_token" }
      let(:expires_at) { Time.current + 1.hour }

      before do
        allow(auth_code).to receive(:get_token).and_return(token)
        allow(token).to receive(:token).and_return(access_token)
        allow(token).to receive(:refresh_token).and_return(refresh_token)
        allow(token).to receive(:expires_at).and_return(expires_at.to_i)
      end

      it "returns successful result with tokens" do
        result = service.exchange_code_for_token(authorization_code: authorization_code)

        expect(result[:success]).to be true
        expect(result[:access_token]).to eq(access_token)
        expect(result[:refresh_token]).to eq(refresh_token)
        expect(result[:expires_at]).to be_within(1.second).of(expires_at)
      end
    end

    context "when token exchange fails" do
      let(:oauth_error) { OAuth2::Error.new(double(status: 400, body: '{"error": "invalid_grant"}')) }

      before do
        allow(auth_code).to receive(:get_token).and_raise(oauth_error)
        allow(oauth_error).to receive(:message).and_return("invalid_grant")
        allow(oauth_error).to receive(:description).and_return("The authorization code is invalid")
      end

      it "returns failure result with error" do
        result = service.exchange_code_for_token(authorization_code: authorization_code)

        expect(result[:success]).to be false
        expect(result[:error]).to eq("invalid_grant")
        expect(result[:error_description]).to eq("The authorization code is invalid")
      end
    end
  end

  describe "#refresh_access_token" do
    let(:refresh_token) { "test_refresh_token" }
    let(:oauth_client) { instance_double(OAuth2::Client) }
    let(:old_token) { instance_double(OAuth2::AccessToken) }
    let(:new_token) { instance_double(OAuth2::AccessToken) }

    before do
      allow(OAuth2::Client).to receive(:new).and_return(oauth_client)
      allow(OAuth2::AccessToken).to receive(:new).and_return(old_token)
    end

    context "when token refresh is successful" do
      let(:new_access_token) { "new_access_token" }
      let(:new_refresh_token) { "new_refresh_token" }
      let(:new_expires_at) { Time.current + 1.hour }

      before do
        allow(old_token).to receive(:refresh!).and_return(new_token)
        allow(new_token).to receive(:token).and_return(new_access_token)
        allow(new_token).to receive(:refresh_token).and_return(new_refresh_token)
        allow(new_token).to receive(:expires_at).and_return(new_expires_at.to_i)
      end

      it "returns successful result with new tokens" do
        result = service.refresh_access_token(refresh_token: refresh_token)

        expect(result[:success]).to be true
        expect(result[:access_token]).to eq(new_access_token)
        expect(result[:refresh_token]).to eq(new_refresh_token)
        expect(result[:expires_at]).to be_within(1.second).of(new_expires_at)
      end
    end

    context "when token refresh fails" do
      let(:oauth_error) { OAuth2::Error.new(double(status: 400, body: '{"error": "invalid_grant"}')) }

      before do
        allow(old_token).to receive(:refresh!).and_raise(oauth_error)
        allow(oauth_error).to receive(:message).and_return("invalid_grant")
        allow(oauth_error).to receive(:description).and_return("The refresh token is invalid")
      end

      it "returns failure result with error" do
        result = service.refresh_access_token(refresh_token: refresh_token)

        expect(result[:success]).to be false
        expect(result[:error]).to eq("invalid_grant")
        expect(result[:error_description]).to eq("The refresh token is invalid")
      end
    end
  end

  describe ".generate_state" do
    subject { described_class.generate_state }

    it "generates a random hex string" do
      expect(subject).to be_a(String)
      expect(subject.length).to eq(64) # 32 bytes = 64 hex characters
      expect(subject).to match(/\A[a-f0-9]+\z/)
    end

    it "generates different values on each call" do
      state1 = described_class.generate_state
      state2 = described_class.generate_state
      expect(state1).not_to eq(state2)
    end
  end

  describe ".validate_state" do
    let(:expected_state) { "test_state_123" }

    context "with matching states" do
      it "returns true" do
        result = described_class.validate_state(expected_state, expected_state)
        expect(result).to be true
      end
    end

    context "with non-matching states" do
      it "returns false" do
        result = described_class.validate_state(expected_state, "different_state")
        expect(result).to be false
      end
    end

    context "with blank expected state" do
      it "returns false" do
        result = described_class.validate_state("", expected_state)
        expect(result).to be false
      end
    end

    context "with blank received state" do
      it "returns false" do
        result = described_class.validate_state(expected_state, "")
        expect(result).to be false
      end
    end
  end
end
