# frozen_string_literal: true

require "rails_helper"

RSpec.describe PayStatementService do
  describe "for a statement" do
    describe "that is open" do
      subject(:statement) { create(:statement) }

      let!(:line_item1) { create(:statement_line_item, statement: statement, amount: 100, identity: statement.identity) }
      let!(:line_item2) { create(:statement_line_item, statement: statement, amount: 200, identity: statement.identity) }

      before { statement.reload }  # Force reload to include all line items

      it "includes updated net_amount" do
        statement.touch
        expect(statement.net_amount).to eql(Money.from_amount(300))
      end

      it "executes payment and marks all line items as paid" do
        # result = PayStatementService.new(statement: statement).execute
        statement.touch
        result = statement.pay!
        expect(result).to be true
        expect(statement.reload.status).to eq("paid")

        # Verify all line items are marked as paid
        statement.statement_line_items.each do |item|
          expect(item.paid?).to be true
        end
      end
    end
  end
end
