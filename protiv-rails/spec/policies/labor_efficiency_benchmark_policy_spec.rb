# frozen_string_literal: true

require "rails_helper"

RSpec.describe LaborEfficiencyBenchmarkPolicy, type: :policy do
  let(:organization) { create(:organization) }
  let(:benchmark) { create(:labor_efficiency_benchmark, organization: organization) }

  describe "permissions" do
    context "as an admin" do
      let(:user) { create(:user) }
      let!(:role) do
        create(:role, user: user, organization: organization, role_type: "admin")
      end

      subject { described_class.new(user, benchmark) }

      before do
        allow(Current).to receive(:organization).and_return(organization)
      end

      it { is_expected.to permit_action(:index) }
      it { is_expected.to permit_action(:show) }
      it { is_expected.to permit_action(:update) }
      it { is_expected.to permit_action(:reset) }
    end

    context "as a manager" do
      let(:user) { create(:user) }
      let!(:role) do
        create(:role, user: user, organization: organization, role_type: "manager")
      end

      subject { described_class.new(user, benchmark) }

      before do
        allow(Current).to receive(:organization).and_return(organization)
      end

      it { is_expected.to permit_action(:index) }
      it { is_expected.to permit_action(:show) }
      it { is_expected.to permit_action(:update) }
      it { is_expected.to permit_action(:reset) }
    end

    context "as an employee" do
      let(:user) { create(:user) }
      let!(:role) do
        create(:role, user: user, organization: organization, role_type: "employee")
      end

      subject { described_class.new(user, benchmark) }

      before do
        allow(Current).to receive(:organization).and_return(organization)
      end

      it { is_expected.not_to permit_action(:index) }
      it { is_expected.not_to permit_action(:show) }
      it { is_expected.not_to permit_action(:update) }
      it { is_expected.not_to permit_action(:reset) }
    end
  end

  describe "Scope" do
    let(:admin) { create(:user) }
    let(:manager) { create(:user) }
    let(:employee) { create(:user) }
    let(:another_org) { create(:organization) }

    let!(:admin_role) do
      create(:role, user: admin, organization: organization, role_type: "admin")
    end

    let!(:manager_role) do
      create(:role, user: manager, organization: organization, role_type: "manager")
    end

    let!(:employee_role) do
      create(:role, user: employee, organization: organization, role_type: "employee")
    end

    let!(:benchmark1) { create(:labor_efficiency_benchmark, organization: organization) }
    let!(:benchmark2) { create(:labor_efficiency_benchmark, organization: another_org) }

    before do
      allow(Current).to receive(:organization).and_return(organization)
    end

    it "includes only organization benchmarks for admins" do
      scope = LaborEfficiencyBenchmarkPolicy::Scope.new(admin, LaborEfficiencyBenchmark).resolve
      expect(scope).to include(benchmark1)
      expect(scope).not_to include(benchmark2)
    end

    it "includes only organization benchmarks for managers" do
      scope = LaborEfficiencyBenchmarkPolicy::Scope.new(manager, LaborEfficiencyBenchmark).resolve
      expect(scope).to include(benchmark1)
      expect(scope).not_to include(benchmark2)
    end

    it "returns none for employees" do
      scope = LaborEfficiencyBenchmarkPolicy::Scope.new(employee, LaborEfficiencyBenchmark).resolve
      expect(scope).to be_empty
    end
  end
end
