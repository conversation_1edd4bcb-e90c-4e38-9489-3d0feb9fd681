# frozen_string_literal: true

require "rails_helper"

RSpec.describe AccountPolicy do
  let(:user) { create(:user) }
  let(:record) { nil }

  subject { described_class.new(user, record) }

  specify { expect(subject.show?).to eq(true) }

  describe "#create?" do
    context "not signed in" do
      let(:user) { nil }
      specify do
        expect(subject.create?).to eq(true)
      end
    end

    context "signed in" do
      specify do
        expect(subject.create?).to eq(false)
      end
    end
  end
end
