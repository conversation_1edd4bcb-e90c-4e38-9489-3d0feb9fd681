# frozen_string_literal: true

require "rails_helper"

RSpec.describe Integrations::AspirePolicy do
  let(:user) { double("User") }
  let(:organization) { double("Organization") }
  let(:aspire) { [:integrations, :aspire] }

  subject { described_class.new(user, aspire) }

  before do
    allow(Current).to receive(:organization).and_return(organization)
  end

  describe "#create?" do
    context "when user is an admin" do
      before do
        allow(user).to receive(:admin_organizations).and_return([organization])
      end

      it "allows access" do
        expect(subject.create?).to be true
      end
    end

    context "when user is not an admin" do
      before do
        allow(user).to receive(:admin_organizations).and_return([])
      end

      it "denies access" do
        expect(subject.create?).to be false
      end
    end
  end
end
