# frozen_string_literal: true

require "rails_helper"

RSpec.describe MilestoneTimePolicy do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization) }
  let(:identity) { create(:identity, organization: organization, user: user) }
  let(:job) { create(:job, organization: organization) }
  let(:milestone) { create(:milestone, job: job) }
  let(:milestone_time) { create(:milestone_time, milestone: milestone, identity: identity) }

  let(:other_organization) { create(:organization) }
  let(:other_user) { create(:user, organization: other_organization) }
  let(:other_identity) { create(:identity, organization: other_organization, user: other_user) }
  let(:other_job) { create(:job, organization: other_organization) }
  let(:other_milestone) { create(:milestone, job: other_job) }
  let(:other_milestone_time) { create(:milestone_time, milestone: other_milestone, identity: other_identity) }

  before do
    allow(Current).to receive(:organization).and_return(organization)
  end

  describe "scoping" do
    it "correctly scopes milestone times to match milestone permissions" do
      # Test milestone policy scope
      milestone_scope = MilestonePolicy::Scope.new(user, Milestone.all).resolve
      expect(milestone_scope).to include(milestone)
      expect(milestone_scope).not_to include(other_milestone)

      # Test milestone time policy scope
      milestone_time_scope = MilestoneTimePolicy::Scope.new(user, MilestoneTime.all).resolve
      expect(milestone_time_scope).to include(milestone_time)
      expect(milestone_time_scope).not_to include(other_milestone_time)

      # Create a new milestone in user's job, but create another one in different job
      another_job = create(:job, organization: organization)
      another_milestone = create(:milestone, job: another_job)
      another_milestone_time = create(:milestone_time, milestone: another_milestone, identity: identity)

      yet_another_job = create(:job, organization: other_organization)
      yet_another_milestone = create(:milestone, job: yet_another_job)
      yet_another_milestone_time = create(:milestone_time, milestone: yet_another_milestone, identity: other_identity)

      # Rerun the scopes to verify they match
      updated_milestone_scope = MilestonePolicy::Scope.new(user, Milestone.all).resolve
      expect(updated_milestone_scope).to include(milestone, another_milestone)
      expect(updated_milestone_scope).not_to include(other_milestone, yet_another_milestone)

      updated_milestone_time_scope = MilestoneTimePolicy::Scope.new(user, MilestoneTime.all).resolve
      expect(updated_milestone_time_scope).to include(milestone_time, another_milestone_time)
      expect(updated_milestone_time_scope).not_to include(other_milestone_time, yet_another_milestone_time)
    end

    it "returns empty scope when user is nil" do
      # Test nil user with milestone policy
      nil_user_milestone_scope = MilestonePolicy::Scope.new(nil, Milestone.all).resolve
      expect(nil_user_milestone_scope).to be_empty

      # Test nil user with milestone time policy
      nil_user_milestone_time_scope = MilestoneTimePolicy::Scope.new(nil, MilestoneTime.all).resolve
      expect(nil_user_milestone_time_scope).to be_empty
    end
  end
end
