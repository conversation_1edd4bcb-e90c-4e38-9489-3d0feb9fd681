# frozen_string_literal: true

require "rails_helper"

RSpec.describe UserPolicy do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:policy) { described_class.new(current_user, user) }

  context "when current user is nil" do
    let(:current_user) { nil }

    specify { expect(policy).not_to permit(:show) }
    specify { expect(policy).not_to permit(:update) }
    specify { expect(policy).not_to permit(:new) }
    specify { expect(policy).not_to permit(:create) }
    specify { expect(policy).not_to permit(:destroy) }
  end

  context "when user is self" do
    let(:current_user) { user }

    specify { expect(policy).to permit(:show) }
    specify { expect(policy).to permit(:update) }
    specify { expect(policy).not_to permit(:new) }
    specify { expect(policy).not_to permit(:create) }
    specify { expect(policy).not_to permit(:destroy) }
  end

  context "when current_user is another user" do
    let(:current_user) { other_user }

    specify { expect(policy).not_to permit(:show) }
    specify { expect(policy).not_to permit(:update) }
    specify { expect(policy).not_to permit(:new) }
    specify { expect(policy).not_to permit(:create) }
    specify { expect(policy).not_to permit(:destroy) }
  end
end
