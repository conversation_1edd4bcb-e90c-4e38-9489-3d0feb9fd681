# frozen_string_literal: true

require "rails_helper"

RSpec.describe AdminConstraint do
  let(:request) do
    ActionDispatch::TestRequest.new({}).tap do |req|
      req.session = { user_id: user.id }
    end
  end

  context "user is admin" do
    let(:user) { create(:user, admin: true) }

    specify do
      expect(user).to be_admin
    end

    specify do
      expect(AdminConstraint.matches?(request)).to eq(true)
    end
  end

  context "user is not admin" do
    let(:user) { create(:user) }

    specify do
      expect(AdminConstraint.matches?(request)).to eq(false)
    end
  end
end
