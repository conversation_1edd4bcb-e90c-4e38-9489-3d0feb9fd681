# frozen_string_literal: true

require "rails_helper"

RSpec.describe ApplicationMailer do
  let(:mailer_instance) { ApplicationMailer.new }
  let(:message) { double(to: ["<EMAIL>"], subject: "Test Email") }
  let(:html_content) { "<p>Test email content</p>" }
  let(:mail_object) { double(deliver_now: true) }

  before do
    allow(mailer_instance).to receive(:message).and_return(message)
    allow(mailer_instance).to receive(:render_to_string).and_return(html_content)
    allow(ENV).to receive(:[]).and_call_original
    # Default to enabling email sending for most tests
    allow(ENV).to receive(:[]).with("SEND_EMAILS").and_return("true")
    # Default to non-development environment for most tests
    allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new("test"))
    # Default to non-letter_opener delivery method for most tests
    allow(ActionMailer::Base).to receive(:delivery_method).and_return(:test)
  end

  describe "#send_mail" do
    context "in development environment with letter_opener_web" do
      before do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new("development"))
        allow(ActionMailer::Base).to receive(:delivery_method).and_return(:letter_opener_web)
        allow(mailer_instance).to receive(:mail).and_return(mail_object)
      end

      context "when SEND_EMAILS is 'true'" do
        it "uses the standard Rails mailer with letter_opener_web" do
          expect(mailer_instance).to receive(:mail).with(
            to: "<EMAIL>",
            subject: "Test Email"
          ).and_return(mail_object)
          expect(mail_object).to receive(:deliver_now)

          mailer_instance.send_mail
        end
      end

      context "when SEND_EMAILS is not 'true'" do
        before do
          allow(ENV).to receive(:[]).with("SEND_EMAILS").and_return("false")
        end

        it "logs the email but does not send it" do
          expect(Rails.logger).to receive(:info).with("Email sending is disabled. Would have shown in letter_opener: <EMAIL>")
          expect(mailer_instance).not_to receive(:mail)

          mailer_instance.send_mail
        end
      end
    end

    context "in non-development environments" do
      context "when SEND_EMAILS is not 'true'" do
        shared_examples "does not send emails" do
          it "logs the email but does not send it" do
            expect(Rails.logger).to receive(:info).with("Email sending is disabled. Would have sent to: <EMAIL>")
            expect(MandrillMailer).not_to receive(:send_mail)
            expect(StandardMailer).not_to receive(:send_mail)

            mailer_instance.send_mail
          end
        end

        context "when SEND_EMAILS is 'false'" do
          before do
            allow(ENV).to receive(:[]).with("SEND_EMAILS").and_return("false")
          end

          include_examples "does not send emails"
        end

        context "when SEND_EMAILS is nil" do
          before do
            allow(ENV).to receive(:[]).with("SEND_EMAILS").and_return(nil)
          end

          include_examples "does not send emails"
        end

        context "when SEND_EMAILS is empty string" do
          before do
            allow(ENV).to receive(:[]).with("SEND_EMAILS").and_return("")
          end

          include_examples "does not send emails"
        end
      end

      context "when SEND_EMAILS is true" do
        context "when MANDRILL_API_KEY is present" do
          before do
            allow(ENV).to receive(:[]).with("MANDRILL_API_KEY").and_return("test_key")
          end

          it "uses MandrillMailer" do
            expect(MandrillMailer).to receive(:send_mail).with(
              to: "<EMAIL>",
              subject: "Test Email",
              html_content: html_content
            )

            mailer_instance.send_mail
          end
        end

        context "when MANDRILL_API_KEY is not present" do
          before do
            allow(ENV).to receive(:[]).with("MANDRILL_API_KEY").and_return(nil)
          end

          it "uses StandardMailer" do
            expect(StandardMailer).to receive(:send_mail).with(
              to: "<EMAIL>",
              subject: "Test Email",
              html_content: html_content
            )

            mailer_instance.send_mail
          end
        end
      end
    end
  end
end
