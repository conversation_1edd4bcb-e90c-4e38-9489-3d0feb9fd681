# frozen_string_literal: true

require "rails_helper"

RSpec.describe UserMailer do
  before do
    @user = create(:user)
  end

  specify "password_reset" do
    mail = UserMailer.with(user: @user).password_reset
    assert_equal "Reset your password", mail.subject
    assert_equal [@user.email], mail.to
  end

  specify "email_verification" do
    mail = UserMailer.with(user: @user).email_verification
    assert_equal "Verify your email", mail.subject
    assert_equal [@user.email], mail.to
  end

  describe "password_change_verification_email" do
    let(:token) { "sample-token-123" }
    let(:mail) { UserMailer.with(user: @user, token: token).password_change_verification_email }

    it "renders the subject" do
      expect(mail.subject).to eq("Verify your password change")
    end

    it "sends to the correct recipient" do
      expect(mail.to).to eq([@user.email])
    end

    it "includes the verification link with token" do
      expect(mail.body.encoded).to include(verify_password_change_api_user_url(@user, token: token))
    end

    context "when token is not provided" do
      let(:mail) { UserMailer.with(user: @user).password_change_verification_email }

      it "generates a token automatically" do
        # The token is generated internally so we can't test the exact value,
        # but we can verify that a URL with a token parameter is included
        expect(mail.body.encoded).to match(/token=[a-zA-Z0-9\-_]+/)
      end
    end
  end
end
