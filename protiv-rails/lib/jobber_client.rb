# frozen_string_literal: true

require_relative "jobber_client/queries"
require_relative "jobber_client/query_cost_calculator"
require_relative "jobber_client/throttle_status_tracker"
require_relative "jobber_client/pagination_manager"
require_relative "jobber_client/retry_handler"
require_relative "jobber_client/configuration"

##
# Client library for interacting with Jobber's GraphQL API using OAuth 2.0 authentication.
#
# This module provides a complete client implementation for Jobber's API,
# including authentication, GraphQL query execution, and error handling.
# It follows the same pattern as the AspireClient library.
module JobberClient
  DEFAULT_URL = "https://api.getjobber.com"

  ##
  # Custom error class for JobberClient exceptions.
  class Error < StandardError
    def initialize(inner)
      # Handle test environment VCR errors if needed
      if Rails.env.test?
        if inner.is_a?(VCR::Errors::Error)
          puts inner
          raise inner
        end
      end

      super
    end
  end

  ##
  # Custom error class for throttling-related exceptions.
  class ThrottledError < Error
    attr_reader :throttle_status, :wait_time, :response_data

    def initialize(message, throttle_status: nil, wait_time: nil, response_data: nil)
      super(message)
      @throttle_status = throttle_status
      @wait_time = wait_time
      @response_data = response_data
    end
  end

  ##
  # Custom error class for rate limit exceeded exceptions.
  class RateLimitExceededError < Error
    attr_reader :retry_after

    def initialize(message, retry_after: nil)
      super(message)
      @retry_after = retry_after
    end
  end

  ##
  # Main client class for interacting with Jobber's GraphQL API.
  #
  # This client handles all GraphQL requests to Jobber's API and manages
  # authentication headers and error handling. It works in conjunction with
  # the JobberOauthService for token management.
  class Client
    include JobberClient::Queries

    # @return [String] OAuth access token for API authentication
    attr_reader :access_token

    # @return [String] Base URL for Jobber API
    attr_reader :api_base_url

    # @return [OAuth2::Client] OAuth2 client for making authenticated requests
    attr_reader :oauth_client

    # @return [JobberRateLimiter, nil] Rate limiter instance
    attr_reader :rate_limiter

    # @return [JobberClient::Configuration] Configuration instance
    attr_reader :configuration

    # @return [JobberClient::PaginationManager] Pagination manager instance
    attr_reader :pagination_manager

    # @return [JobberClient::RetryHandler] Retry handler instance
    attr_reader :retry_handler

    ##
    # Initializes a new JobberClient::Client instance.
    #
    # @param access_token [String] Valid OAuth access token
    # @param api_base_url [String] Base URL for Jobber API (default: production URL)
    # @param integration_id [String, nil] Integration ID for rate limiting
    # @param configuration [JobberClient::Configuration, nil] Custom configuration
    def initialize(access_token, api_base_url: DEFAULT_URL, integration_id: nil, configuration: nil)
      @access_token = access_token
      @api_base_url = api_base_url
      @oauth_client = create_oauth_client
      @configuration = configuration || JobberClient::Configuration.new
      @rate_limiter = JobberRateLimiter.new(
        integration_id: integration_id,
        max_cost_percentage: @configuration.rate_limiting.max_query_cost_percentage,
        minimum_cost_threshold: @configuration.rate_limiting.minimum_cost_threshold
      )

      @pagination_manager = JobberClient::PaginationManager.new(self, rate_limiter: @rate_limiter)
      @retry_handler = JobberClient::RetryHandler.new(
        max_retries: @configuration.retry_settings.maximum_retry_attempts,
        initial_backoff: @configuration.retry_settings.initial_backoff,
        backoff_multiplier: @configuration.retry_settings.backoff_multiplier,
        max_backoff: @configuration.retry_settings.maximum_backoff,
        jitter_factor: @configuration.retry_settings.jitter_factor
      )
    end

    ##
    # Fetches the GraphQL schema from Jobber API.
    #
    # @return [Hash] GraphQL schema response
    def fetch_schema
      graphql_request(query: schema_query)
    end

    ##
    # Fetches jobs from Jobber API with automatic pagination and adaptive retry.
    #
    # @param limit [Integer] Maximum number of jobs to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param filter [Hash] GraphQL filter attributes
    # @param sort [Array] GraphQL sort attributes
    # @param search_term [String] Search term for filtering
    # @return [Array<Hash>] Array of job data
    def fetch_jobs(limit: 100, batch_size: :auto, filter: nil, sort: nil, search_term: nil)
      variables_base = {
        filter: filter,
        sort: sort,
        searchTerm: search_term
      }.compact

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :jobs_query,
        variables_base: variables_base,
        data_path: %w[data jobs edges],
        page_info_path: %w[data jobs pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches visits from Jobber API with automatic pagination and adaptive retry.
    #
    # @param job_id [String, nil] Filter visits by job ID
    # @param limit [Integer] Maximum number of visits to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param filter [Hash] GraphQL filter attributes
    # @param sort [Array] GraphQL sort attributes
    # @return [Array<Hash>] Array of visit data
    def fetch_visits(job_id: nil, limit: 100, batch_size: :auto, filter: nil, sort: nil)
      if job_id
        variables_base = {
          jobId: job_id,
          filter: filter,
          sort: sort
        }.compact

        pagination_manager.fetch_all_paginated_with_adaptive_retry(
          query_method: :visits_query,
          variables_base: variables_base,
          data_path: %w[data job visits edges],
          page_info_path: %w[data job visits pageInfo],
          batch_size: batch_size,
          max_items: limit,
          min_batch_size: configuration.adaptive_retry.default_min_batch_size
        )
      else
        # For visits without job_id, use the general visits query
        variables_base = {
          filter: filter,
          sort: sort
        }.compact

        # Always filter for completed visits
        variables_base[:filter] = (variables_base[:filter] || {}).merge(status: "COMPLETED")

        pagination_manager.fetch_all_paginated_with_adaptive_retry(
          query_method: :all_visits_query,
          variables_base: variables_base,
          data_path: %w[data visits edges],
          page_info_path: %w[data visits pageInfo],
          batch_size: batch_size,
          max_items: limit,
          min_batch_size: configuration.adaptive_retry.default_min_batch_size
        )
      end
    end

    def fetch_all_visits(batch_size: :auto, max_items: nil, min_batch_size: 5, filter: nil, sort: nil)
      variables_base = {
        filter: filter,
        sort: sort
      }.compact

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :all_visits_query,
        variables_base: variables_base,
        data_path: %w[data visits edges],
        page_info_path: %w[data visits pageInfo],
        batch_size: batch_size,
        max_items: max_items,
        min_batch_size: min_batch_size
      )
    end

    ##
    # Fetches users from Jobber API with automatic pagination and adaptive retry.
    #
    # @param limit [Integer] Maximum number of users to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param filter [Hash] GraphQL filter attributes
    # @return [Array<Hash>] Array of user data
    def fetch_users(limit: 100, batch_size: :auto, filter: nil)
      variables_base = {
        filter: filter
      }.compact

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :users_query,
        variables_base: variables_base,
        data_path: %w[data users nodes],
        page_info_path: %w[data users pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches products or services from Jobber API with automatic pagination and adaptive retry.
    #
    # @param limit [Integer] Maximum number of items to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param filter [Hash] GraphQL filter attributes
    # @return [Array<Hash>] Array of product/service data
    def fetch_products_or_services(limit: 100, batch_size: :auto, filter: nil)
      variables_base = {
        filter: filter
      }.compact

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :products_query,
        variables_base: variables_base,
        data_path: %w[data products edges],
        page_info_path: %w[data products pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches clients from Jobber API with automatic pagination and adaptive retry.
    #
    # @param limit [Integer] Maximum number of clients to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param filter [Hash] GraphQL filter attributes
    # @param sort [Hash] GraphQL sort attributes
    # @param search_term [String] Search term for filtering
    # @return [Array<Hash>] Array of client data
    def fetch_clients(limit: 100, batch_size: :auto, filter: nil, sort: nil, search_term: nil)
      variables_base = {
        filter: filter,
        sort: sort,
        searchTerm: search_term
      }.compact

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :clients_query,
        variables_base: variables_base,
        data_path: %w[data clients edges],
        page_info_path: %w[data clients pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches properties from Jobber API with automatic pagination and adaptive retry.
    #
    # @param limit [Integer] Maximum number of properties to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @return [Array<Hash>] Array of property data
    def fetch_properties(limit: 100, batch_size: :auto)
      # Note: client_id parameter is ignored as the properties query doesn't support filtering
      variables_base = {}

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :properties_query,
        variables_base: variables_base,
        data_path: %w[data properties nodes],
        page_info_path: %w[data properties pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches line items from Jobber API with automatic pagination and adaptive retry.
    #
    # @param job_id [String] Filter line items by job ID (required)
    # @param limit [Integer] Maximum number of line items to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @return [Array<Hash>] Array of line item data
    def fetch_line_items(job_id:, limit: 100, batch_size: :auto)
      variables_base = {
        jobId: job_id
      }

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :line_items_query,
        variables_base: variables_base,
        data_path: %w[data job lineItems nodes],
        page_info_path: %w[data job lineItems pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches timesheet entries from Jobber API with automatic pagination and adaptive retry.
    #
    # @param job_id [String, nil] Filter entries by job ID (not directly supported - use filter instead)
    # @param limit [Integer] Maximum number of entries to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param filter [Hash] GraphQL filter attributes
    # @param sort [Array] GraphQL sort attributes
    # @return [Array<Hash>] Array of timesheet entry data
    def fetch_timesheet_entries(job_id: nil, limit: 100, batch_size: :auto, filter: nil, sort: nil)
      variables_base = {
        filter: filter,
        sort: sort
      }.compact

      # If job_id is provided, add it to the filter
      if job_id && variables_base[:filter]
        variables_base[:filter] = variables_base[:filter].merge(jobId: job_id)
      elsif job_id
        variables_base[:filter] = { jobId: job_id }
      end

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :timesheet_entries_query,
        variables_base: variables_base,
        data_path: %w[data timeSheetEntries edges],
        page_info_path: %w[data timeSheetEntries pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Fetches quotes from Jobber API with automatic pagination and adaptive retry.
    #
    # Note: Quotes query only supports pagination - no filter or sort parameters
    # Used for budget information context and historical scope tracking
    #
    # @param limit [Integer] Maximum number of quotes to retrieve (default: 100)
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @return [Array<Hash>] Array of quote data
    def fetch_quotes(limit: 100, batch_size: :auto)
      variables_base = {}

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :quotes_query,
        variables_base: variables_base,
        data_path: %w[data quotes edges],
        page_info_path: %w[data quotes pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    def fetch_jobs_linked_product_or_services(limit: 100, batch_size: :auto)
      variables_base = {}

      pagination_manager.fetch_all_paginated_with_adaptive_retry(
        query_method: :jobs_linked_product_or_services_query,
        variables_base: variables_base,
        data_path: %w[data jobs edges],
        page_info_path: %w[data jobs pageInfo],
        batch_size: batch_size,
        max_items: limit,
        min_batch_size: configuration.adaptive_retry.default_min_batch_size
      )
    end

    ##
    # Configures rate limiting settings.
    #
    # @param max_cost_percentage [Integer] Percentage of maximum cost to use
    # @param enable_auto_pagination [Boolean] Enable automatic pagination
    # @param enable_circuit_breaker [Boolean] Enable circuit breaker
    # @return [void]
    def configure_rate_limiting(max_cost_percentage: nil, enable_auto_pagination: nil, enable_circuit_breaker: nil)
      return unless rate_limiter

      # Update rate limiter configuration
      if max_cost_percentage
        rate_limiter.instance_variable_set(:@max_cost_percentage, max_cost_percentage)
      end

      # Update configuration
      if enable_auto_pagination
        configuration.batch_processing.auto_batch_sizing = enable_auto_pagination
      end

      if enable_circuit_breaker
        configuration.circuit_breaker.enabled = enable_circuit_breaker
      end
    end

    ##
    # Gets current rate limiting status.
    #
    # @return [Hash] Rate limiting status information
    def rate_limiting_status
      return { enabled: false } unless rate_limiter

      throttle_status = rate_limiter.get_current_throttle_status

      {
        enabled: true,
        integration_id: rate_limiter.integration_id,
        max_cost_percentage: rate_limiter.max_cost_percentage,
        minimum_cost_threshold: rate_limiter.minimum_cost_threshold,
        circuit_breaker_open: rate_limiter.circuit_breaker_open?,
        current_throttle_status: throttle_status
      }
    end

    ##
    # Resets rate limiting state (useful for testing or recovery).
    #
    # @return [void]
    def reset_rate_limiting!
      return unless rate_limiter

      rate_limiter.reset_circuit_breaker!
      Rails.logger.info("Rate limiting state reset for integration: #{rate_limiter.integration_id}")
    end

    ##
    # Checks if rate limiting is enabled and active.
    #
    # @return [Boolean] True if rate limiting is enabled
    def rate_limiting_enabled?
      !rate_limiter.nil?
    end

    ##
    # Gets cost estimate for a query.
    #
    # @param query [String] GraphQL query string
    # @param variables [Hash] GraphQL variables
    # @return [Hash] Cost estimation details
    def estimate_query_cost(query, variables = {})
      cost_calculator = JobberClient::QueryCostCalculator.new
      cost_calculator.estimate_cost(query, variables)
    end

    ##
    # Executes a GraphQL request to the Jobber API using OAuth2::AccessToken with rate limiting.
    #
    # @param query [String] GraphQL query string
    # @param variables [Hash] GraphQL variables (default: {})
    # @param enable_rate_limiting [Boolean] Enable rate limiting checks (default: true)
    # @return [Hash] Parsed JSON response body
    # @raise [JobberClient::Error] If the HTTP request fails
    # @raise [JobberClient::ThrottledError] If request is throttled
    # @raise [JobberClient::RateLimitExceededError] If rate limit is exceeded
    def graphql_request(query:, variables: {}, enable_rate_limiting: true)
      # Create throttle tracker for this request
      throttle_tracker = JobberClient::ThrottleStatusTracker.new(
        integration_id: rate_limiter&.integration_id
      )

      execute_request = lambda do
        # Check rate limits before making request
        if enable_rate_limiting && rate_limiter
          cost_calculator = JobberClient::QueryCostCalculator.new
          cost_estimate = cost_calculator.estimate_cost(query, variables)

          begin
            rate_limiter.check_cost_availability(estimated_cost: cost_estimate[:estimated_cost])
          rescue JobberRateLimiter::CostLimitExceeded => e
            raise JobberClient::ThrottledError.new(
              "Rate limit exceeded: #{e.message}",
              wait_time: e.wait_time
            )
          rescue JobberRateLimiter::CircuitBreakerOpen => e
            raise JobberClient::RateLimitExceededError.new(
              "Circuit breaker open: #{e.message}",
              retry_after: e.retry_after
            )
          end
        end

        # Execute the actual request
        response = oauth_token.post("api/graphql",
                                    headers: graphql_headers,
                                    body: { query: query, variables: variables }.to_json)

        parsed_response = JSON.parse(response.body)

        # Update rate limiter with response data
        if enable_rate_limiting && rate_limiter
          rate_limiter.update_from_response(parsed_response)

          # Update throttle tracker
          throttle_status = parsed_response.dig("extensions", "cost", "throttleStatus")
          throttle_tracker.update_status(throttle_status) if throttle_status
        end

        # Check for GraphQL errors that indicate throttling
        if parsed_response["errors"]
          throttling_errors = parsed_response["errors"].select do |error|
            error["message"]&.downcase&.include?("throttled")
          end

          unless throttling_errors.empty?
            raise JobberClient::ThrottledError.new(
              "Request throttled: #{throttling_errors.first['message']}",
              throttle_status: throttle_status,
              response_data: parsed_response
            )
          end
        end

        parsed_response
      end

      # Execute the request directly (adaptive retry is handled at higher level)
      execute_request.call

    rescue OAuth2::Error => e
      Rails.logger.error("Jobber API request failed: #{e.message}")
      raise JobberClient::Error.new(e)
    end

    private

    ##
    # Creates a configured OAuth2 client instance.
    #
    # @return [OAuth2::Client] Configured OAuth2 client
    def create_oauth_client
      OAuth2::Client.new(
        nil, # client_id not needed for API requests
        nil, # client_secret not needed for API requests
        site: api_base_url
      )
    end

    ##
    # Returns an OAuth2::AccessToken instance for making authenticated requests.
    #
    # @return [OAuth2::AccessToken] OAuth2 access token
    def oauth_token
      @oauth_token ||= OAuth2::AccessToken.new(oauth_client, access_token)
    end

    ##
    # Builds headers for GraphQL requests.
    #
    # @return [Hash] Request headers
    def graphql_headers
      {
        "Content-Type" => "application/json",
        "X-JOBBER-GRAPHQL-VERSION" => "2025-01-20"
      }
    end
  end
end
