# frozen_string_literal: true

# This file was generated by a script. Direct modifications are discouraged.
# source: lib/generators/swagger_client/swagger_client_generator.rb
# command: bin/rails generate aspire --schema_path=spec/support/aspire.openapi.json

require_relative "./aspire_client/resource_methods"
require Rails.root.join("app", "services", "rate_limiter")

module AspireClient
  DEFAULT_URL = "https://cloud-api.youraspire.com"

  class Error < StandardError
    def initialize(inner)
      # FIXME: this is not ideal
      if Rails.env.test?
        if inner.is_a?(VCR::Errors::Error)
          puts inner
          raise inner
        end
      end

      super
    end
  end

  class Client
    attr_reader :token, :url, :client_id

    def initialize(token: nil, url: nil, client_id: nil)
      @token = token
      @url = url || DEFAULT_URL
      @client_id = client_id
    end

    def client
      @client ||= Faraday.new(url: url) do |builder|
        if @token.present?
          builder.request :authorization, "Bearer", @token
        end

        builder.response :logger, nil, { headers: false, bodies: { request: false, response: false } }
        builder.request :json
        builder.response :json
        builder.response :raise_error

        if client_id
          # 1200 requests per minute (20 requests per second)
          builder.request :rate_limiter,
            name: ["_aspire_rate_limit", client_id].join(":"),
            max_count: 200,
            period: 10
        end
      end
    end

    def token=(token)
      @client = nil
      @token = token
    end

    include ResourceMethods
  end
end
