# frozen_string_literal: true

class MilestoneTimesTestData
  def self.first_intermediate_times(base_date = Date.new(2024, 12, 13), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [
      { crew_num: 0, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } },
      { crew_num: 1, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD") } },
      { crew_num: 2, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(144, "USD"), base_hourly_rate: Money.from_amount(18, "USD") } },
      { crew_num: 3, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(120, "USD"), base_hourly_rate: Money.from_amount(15, "USD") } },
      { crew_num: 0, params: { start_time: base_time + 1.day, end_time: base_time + 1.day + 8.hours, labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } },
      { crew_num: 1, params: { start_time: base_time + 1.day, end_time: base_time + 1.day + 8.hours, labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD") } },
      { crew_num: 2, params: { start_time: base_time + 1.day, end_time: base_time + 1.day + 8.hours, labor_cost: Money.from_amount(144, "USD"), base_hourly_rate: Money.from_amount(18, "USD") } },
      { crew_num: 3, params: { start_time: base_time + 1.day, end_time: base_time + 1.day + 7.hours, labor_cost: Money.from_amount(105, "USD"), base_hourly_rate: Money.from_amount(15, "USD") } }
    ].freeze
  end

  def self.second_intermediate_times(base_date = Date.new(2024, 12, 16), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [
      { crew_num: 0, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } },
      { crew_num: 1, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD") } },
      { crew_num: 2, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(144, "USD"), base_hourly_rate: Money.from_amount(18, "USD") } }
    ].freeze
  end

  def self.third_intermediate_times(base_date = Date.new(2024, 12, 17), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [
      { crew_num: 0, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } },
      { crew_num: 1, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD") } },
      { crew_num: 2, params: { start_time: base_time, end_time: base_time + 6.hours, labor_cost: Money.from_amount(108, "USD"), base_hourly_rate: Money.from_amount(18, "USD") } },
      { crew_num: 0, params: { start_time: base_time + 1.days, end_time: base_time + 1.days + 8.hours, labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } },
      { crew_num: 1, params: { start_time: base_time + 1.days, end_time: base_time + 1.days + 8.hours, labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD") } },
      { crew_num: 0, params: { start_time: base_time + 2.days, end_time: base_time + 2.days + 5.hours, labor_cost: Money.from_amount(150, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } }
    ].freeze
  end

  def self.forth_callback_times(base_date = Date.new(2024, 12, 31), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [
      { crew_num: 0, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(240, "USD"), base_hourly_rate: Money.from_amount(30, "USD") } },
      { crew_num: 1, params: { start_time: base_time, end_time: base_time + 8.hours, labor_cost: Money.from_amount(176, "USD"), base_hourly_rate: Money.from_amount(22, "USD") } }
    ].freeze
  end

  def self.full_times(base_date = Date.new(2024, 12, 13), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [].concat(first_intermediate_times(base_date, base_time),
              second_intermediate_times(base_date + 3.days, base_time),
              third_intermediate_times(base_date + 4.days, base_time))
  end

  def self.split_one_milestone_times(base_date = Date.new(2024, 12, 13), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    first_intermediate_times(base_date, base_time)
  end

  def self.split_two_milestone_times(base_date = Date.new(2024, 12, 13), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [].concat(second_intermediate_times(base_date + 3.days, base_time),
              third_intermediate_times(base_date + 4.days, base_time))
  end

  def self.full_times_with_callback(base_date = Date.new(2024, 12, 13), base_time = (base_date + 13.hours).in_time_zone("America/New_York"))
    [].concat(first_intermediate_times(base_date, base_time),
              second_intermediate_times(base_date + 3.days, base_time),
              third_intermediate_times(base_date + 4.days, base_time),
              forth_callback_times(base_date + 14.days, base_time))
  end
end
