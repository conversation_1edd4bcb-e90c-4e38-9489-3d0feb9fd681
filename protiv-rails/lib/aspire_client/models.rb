# frozen_string_literal: true

# This file was generated by a script. Direct modifications are discouraged.
# source: lib/generators/swagger_client/swagger_client_generator.rb
# command: bin/rails generate aspire --schema_path=spec/support/aspire.openapi.json

module AspireClient
  module Models
    class ArrayOf
      def initialize(wrapped)
        @wrapped = wrapped
      end

      def assert_valid_value(arg)
        return unless arg
        raise "invalid value" unless arg.is_a?(Array)

        arg.each do |element|
          @wrapped.assert_valid_value(element)
        end
      end

      def cast(arg)
        arg&.map { |element| @wrapped.cast(element) }
      end
    end

    class Base
      include ActiveModel::Model
      include ActiveModel::Attributes
      include ActiveModel::Serialization

      def initialize(attributes = {})
        attributes ||= {}

        if attributes.is_a?(Array)
          super(attributes.map { |inner| inner.transform_keys { |k| k.to_s.underscore } })
        else
          super(attributes.transform_keys { |k| k.to_s.underscore })
        end
      end

      def self.assert_valid_value(*)
      end

      def self.cast(attrs)
        new(attrs)
      end

      def as_json(options = nil)
        serializable_hash.as_json(options)
      end

      private

      def read_attribute_for_serialization(attr)
        attr = attr.underscore
        res = super(attr)
        if res.is_a?(Base)
          res&.serializable_hash
        elsif @attributes[attr].type.is_a?(ArrayOf)
          res&.map { |x| x.serializable_hash }
        else
          res
        end
      end
    end

    class AspireCloudDomainTenantActivitiesODataModelActivity < Base
      attribute :activity_id, :integer
      attribute :property_id, :integer
      attribute :opportunity_id, :integer
      attribute :work_ticket_id, :integer
      attribute :activity_type, :string
      attribute :status, :string
      attribute :subject, :string
      attribute :location, :string
      attribute :include_client, :boolean
      attribute :include_crew, :boolean
      attribute :notes, :string
      attribute :priority, :string
      attribute :start_date, :datetime
      attribute :end_date, :datetime
      attribute :due_date, :datetime
      attribute :complete_date, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date, :datetime
      attribute :sent_date, :datetime
      attribute :activity_number, :integer
      attribute :invoice_id, :integer
      attribute :payment_id, :integer
      attribute :private, :boolean
      attribute :completed_by_user_id, :integer
      attribute :completed_by_user_name, :string
      attribute :is_mile_stone, :boolean
      attribute :modified_date, :datetime
      attribute :activity_category_id, :integer
      attribute :activity_category_name, :string

      validates :activity_id, presence: true, numericality: true
      validates :property_id, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :activity_type, length: (0..15), allow_blank: true
      validates :status, length: (0..15), allow_blank: true
      validates :subject, length: (0..200), allow_blank: true
      validates :location, length: (0..100), allow_blank: true
      validates :notes, length: (0..**********), allow_blank: true
      validates :priority, length: (0..15), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :activity_number, numericality: true, allow_nil: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :payment_id, numericality: true, allow_nil: true
      validates :completed_by_user_id, numericality: true, allow_nil: true
      validates :completed_by_user_name, length: (0..101), allow_blank: true
      validates :activity_category_id, numericality: true, allow_nil: true

      def attributes
        {
          "ActivityID" => nil,
          "PropertyID" => nil,
          "OpportunityID" => nil,
          "WorkTicketID" => nil,
          "ActivityType" => nil,
          "Status" => nil,
          "Subject" => nil,
          "Location" => nil,
          "IncludeClient" => nil,
          "IncludeCrew" => nil,
          "Notes" => nil,
          "Priority" => nil,
          "StartDate" => nil,
          "EndDate" => nil,
          "DueDate" => nil,
          "CompleteDate" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDate" => nil,
          "SentDate" => nil,
          "ActivityNumber" => nil,
          "InvoiceID" => nil,
          "PaymentID" => nil,
          "Private" => nil,
          "CompletedByUserID" => nil,
          "CompletedByUserName" => nil,
          "IsMileStone" => nil,
          "ModifiedDate" => nil,
          "ActivityCategoryID" => nil,
          "ActivityCategoryName" => nil
        }
      end
    end

    class AspireCloudDomainTenantActivityCategoriesODataModelActivityCategory < Base
      attribute :activity_category_id, :integer
      attribute :activity_type, :string
      attribute :activity_category_name, :string
      attribute :active, :boolean

      validates :activity_category_id, presence: true, numericality: true
      validates :activity_type, length: (0..10), allow_blank: true
      validates :activity_category_name, length: (0..50), allow_blank: true

      def attributes
        {
          "ActivityCategoryID" => nil,
          "ActivityType" => nil,
          "ActivityCategoryName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantActivitiesODataModelActivityCommentHistory < Base
      attribute :activity_comment_history_id, :integer
      attribute :contact_id, :integer
      attribute :activity_id, :integer
      attribute :contact_name, :string
      attribute :comment, :string
      attribute :created_date_time, :datetime
      attribute :attachment, :boolean
      attribute :public_comment, :boolean

      validates :activity_comment_history_id, presence: true, numericality: true
      validates :contact_id, numericality: true, allow_nil: true
      validates :activity_id, presence: true, numericality: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :comment, length: (0..**********), allow_blank: true

      def attributes
        {
          "ActivityCommentHistoryID" => nil,
          "ContactID" => nil,
          "ActivityID" => nil,
          "ContactName" => nil,
          "Comment" => nil,
          "CreatedDateTime" => nil,
          "Attachment" => nil,
          "PublicComment" => nil
        }
      end
    end

    class AspireCloudDomainTenantAddressesODataModelAddress < Base
      attribute :address_id, :integer
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_on, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_on, :datetime

      validates :address_id, presence: true, numericality: true
      validates :address_line1, length: (0..75), allow_blank: true
      validates :address_line2, length: (0..75), allow_blank: true
      validates :city, length: (0..50), allow_blank: true
      validates :state_province_code, length: (0..3), allow_blank: true
      validates :zip_code, length: (0..15), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "AddressID" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil,
          "CreatedByUserId" => nil,
          "CreatedByUserName" => nil,
          "CreatedOn" => nil,
          "LastModifiedByUserId" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedOn" => nil
        }
      end
    end

    class AspireCloudDomainTenantAttachmentsODataModelAttachment < Base
      attribute :attachment_id, :integer
      attribute :attachment_type_id, :integer
      attribute :attachment_type_name, :string
      attribute :property_id, :integer
      attribute :property_name, :string
      attribute :activity_id, :integer
      attribute :activity_number, :integer
      attribute :opportunity_id, :integer
      attribute :opportunity_number, :integer
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :attachment_name, :string
      attribute :file_extension, :string
      attribute :date_uploaded, :datetime
      attribute :property_qa_category_id, :integer
      attribute :show_customer, :boolean
      attribute :note, :string
      attribute :expose_to_crew, :boolean
      attribute :receipt_id, :integer
      attribute :receipt_number, :integer
      attribute :equipment_id, :integer
      attribute :equipment_description, :string
      attribute :original_file_name, :string
      attribute :employee_incident_id, :integer
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :attach_to_invoice, :boolean
      attribute :work_ticket_visit_id, :integer
      attribute :external_content_id, :string
      attribute :notification_template_id, :integer
      attribute :geo_location_latitude, :float
      attribute :geo_location_longitude, :float

      validates :attachment_id, presence: true, numericality: true
      validates :attachment_type_id, numericality: true, allow_nil: true
      validates :attachment_type_name, length: (0..50), allow_blank: true
      validates :property_id, numericality: true, allow_nil: true
      validates :property_name, length: (0..100), allow_blank: true
      validates :activity_id, numericality: true, allow_nil: true
      validates :activity_number, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :opportunity_number, numericality: true, allow_nil: true
      validates :contact_id, numericality: true, allow_nil: true
      validates :contact_name, length: (0..100), allow_blank: true
      validates :attachment_name, length: (0..255), allow_blank: true
      validates :file_extension, length: (0..10), allow_blank: true
      validates :property_qa_category_id, numericality: true, allow_nil: true
      validates :note, length: (0..500), allow_blank: true
      validates :receipt_id, numericality: true, allow_nil: true
      validates :receipt_number, numericality: true, allow_nil: true
      validates :equipment_id, numericality: true, allow_nil: true
      validates :equipment_description, length: (0..250), allow_blank: true
      validates :original_file_name, length: (0..255), allow_blank: true
      validates :employee_incident_id, numericality: true, allow_nil: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :work_ticket_visit_id, numericality: true, allow_nil: true
      validates :external_content_id, length: (0..255), allow_blank: true
      validates :notification_template_id, numericality: true, allow_nil: true
      validates :geo_location_latitude, numericality: true, allow_nil: true
      validates :geo_location_longitude, numericality: true, allow_nil: true

      def attributes
        {
          "AttachmentID" => nil,
          "AttachmentTypeID" => nil,
          "AttachmentTypeName" => nil,
          "PropertyID" => nil,
          "PropertyName" => nil,
          "ActivityID" => nil,
          "ActivityNumber" => nil,
          "OpportunityID" => nil,
          "OpportunityNumber" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "AttachmentName" => nil,
          "FileExtension" => nil,
          "DateUploaded" => nil,
          "PropertyQACategoryID" => nil,
          "ShowCustomer" => nil,
          "Note" => nil,
          "ExposeToCrew" => nil,
          "ReceiptID" => nil,
          "ReceiptNumber" => nil,
          "EquipmentID" => nil,
          "EquipmentDescription" => nil,
          "OriginalFileName" => nil,
          "EmployeeIncidentID" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "AttachToInvoice" => nil,
          "WorkTicketVisitID" => nil,
          "ExternalContentID" => nil,
          "NotificationTemplateID" => nil,
          "GEOLocationLatitude" => nil,
          "GEOLocationLongitude" => nil
        }
      end
    end

    class AspireCloudExternalApiAttachmentsModelsAttachmentUploadRequest < Base
      attribute :file_name, :string
      attribute :file_data, :string
      attribute :object_id, :integer
      attribute :object_code, :string
      attribute :attachment_type_id, :integer
      attribute :attach_to_invoice, :boolean
      attribute :expose_to_crew, :boolean

      validates :file_name, presence: true, length: (1..)
      validates :file_data, presence: true, length: (1..)
      validates :object_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :object_code, presence: true, length: (1..)
      validates :attachment_type_id, presence: true, numericality: true

      def attributes
        {
          "FileName" => nil,
          "FileData" => nil,
          "ObjectId" => nil,
          "ObjectCode" => nil,
          "AttachmentTypeId" => nil,
          "AttachToInvoice" => nil,
          "ExposeToCrew" => nil
        }
      end
    end

    class AspireCloudExternalApiAttachmentsModelsAttachmentFileData < Base
      attribute :attachment_id, :integer
      attribute :file_name, :string
      attribute :file_data, :string
      attribute :object_id, :integer
      attribute :object_code, :string
      attribute :attachment_type_id, :integer
      attribute :attach_to_invoice, :boolean

      validates :attachment_id, presence: true, numericality: true
      validates :file_name, presence: true, length: (1..)
      validates :file_data, presence: true, length: (1..)# Base64 encoded file content
      validates :object_id, presence: true, numericality: true
      validates :object_code, presence: true, length: (1..)
      validates :attachment_type_id, presence: true, numericality: true

      def attributes
        {
          "AttachmentID" => nil,
          "FileName" => nil,
          "FileData" => nil,
          "ObjectId" => nil,
          "ObjectCode" => nil,
          "AttachmentTypeID" => nil,
          "AttachToInvoice" => nil
        }
      end
    end

    class AspireCloudDomainTenantAttachmentTypesAttachmentType < Base
      attribute :attachment_type_id, :integer
      attribute :attachment_type_name, :string
      attribute :required, :boolean
      attribute :active, :boolean
      attribute :aspire_system_id, :integer
      attribute :can_delete, :boolean

      validates :attachment_type_id, presence: true, numericality: true
      validates :aspire_system_id, presence: true, numericality: true

      def attributes
        {
          "AttachmentTypeID" => nil,
          "AttachmentTypeName" => nil,
          "Required" => nil,
          "Active" => nil,
          "AspireSystemId" => nil,
          "CanDelete" => nil
        }
      end
    end

    class AspireCloudDomainConfigExternalApiUsersApiAuthenticationRequest < Base
      attribute :client_id, :string
      attribute :secret, :string

      validates :client_id, presence: true, length: (1..)
      validates :secret, presence: true, length: (1..)

      def attributes
        {
          "ClientId" => nil,
          "Secret" => nil
        }
      end
    end

    class AspireCloudDomainConfigExternalApiUsersApiAuthenticationResult < Base
      attribute :token, :string
      attribute :refresh_token, :string


      def attributes
        {
          "Token" => nil,
          "RefreshToken" => nil
        }
      end
    end

    class AspireCloudDomainConfigExternalApiUsersApiRefreshTokenRequest < Base
      attribute :refresh_token, :string


      def attributes
        {
          "RefreshToken" => nil
        }
      end
    end

    class AspireCloudDomainTenantBankDepositsODataModelBankDeposit < Base
      attribute :bank_deposit_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :deposit_date, :datetime
      attribute :deposit_amount, :float
      attribute :deposit_status, :string
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :date_sent_to_accounting, :datetime
      attribute :sent_to_accounting_user_id, :integer
      attribute :sent_to_accounting_user_name, :string
      attribute :accounting_message, :string
      attribute :reset_to_new_date, :datetime
      attribute :reset_to_new_user_id, :integer
      attribute :reset_to_new_user_name, :string

      validates :bank_deposit_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :deposit_amount, numericality: true, allow_nil: true
      validates :deposit_status, length: (0..50), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :sent_to_accounting_user_id, numericality: true, allow_nil: true
      validates :sent_to_accounting_user_name, length: (0..101), allow_blank: true
      validates :accounting_message, length: (0..**********), allow_blank: true
      validates :reset_to_new_user_id, numericality: true, allow_nil: true
      validates :reset_to_new_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "BankDepositID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "DepositDate" => nil,
          "DepositAmount" => nil,
          "DepositStatus" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "DateSentToAccounting" => nil,
          "SentToAccountingUserID" => nil,
          "SentToAccountingUserName" => nil,
          "AccountingMessage" => nil,
          "ResetToNewDate" => nil,
          "ResetToNewUserID" => nil,
          "ResetToNewUserName" => nil
        }
      end
    end

    class AspireCloudDomainTenantBranchesODataModelBranch < Base
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :active, :boolean
      attribute :internal_property_id, :integer
      attribute :internal_property_name, :string
      attribute :catalog_price_list_name, :string
      attribute :branch_address_id, :integer
      attribute :branch_address_line1, :string
      attribute :branch_address_line2, :string
      attribute :branch_address_city, :string
      attribute :branch_address_state_province_code, :string
      attribute :branch_address_zip_code, :string
      attribute :branch_phone, :string
      attribute :branch_fax, :string
      attribute :branch_code, :string
      attribute :branch_manager_contact_id, :integer
      attribute :branch_manager_contact_name, :string
      attribute :legal_name, :string
      attribute :time_zone, :string
      attribute :invoice_number_prefix, :string
      attribute :receipt_number_prefix, :string
      attribute :opportunity_number_prefix, :string
      attribute :region_id, :integer
      attribute :region_name, :string
      attribute :billing_email_from_account_owner, :boolean
      attribute :billing_email_from_user_name, :string
      attribute :billing_email_from_email, :string
      attribute :billing_email_subject, :string
      attribute :billing_email_body, :string
      attribute :invoice_on_completion_description, :string
      attribute :branch_web_site, :string
      attribute :catalog_price_list_id, :integer
      attribute :billing_email_cc, :string

      validates :branch_id, presence: true, numericality: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :internal_property_id, numericality: true, allow_nil: true
      validates :internal_property_name, length: (0..100), allow_blank: true
      validates :catalog_price_list_name, length: (0..50), allow_blank: true
      validates :branch_address_id, numericality: true, allow_nil: true
      validates :branch_address_line1, length: (0..75), allow_blank: true
      validates :branch_address_line2, length: (0..75), allow_blank: true
      validates :branch_address_city, length: (0..50), allow_blank: true
      validates :branch_address_state_province_code, length: (0..3), allow_blank: true
      validates :branch_address_zip_code, length: (0..15), allow_blank: true
      validates :branch_phone, length: (0..14), allow_blank: true
      validates :branch_fax, length: (0..14), allow_blank: true
      validates :branch_code, length: (0..6), allow_blank: true
      validates :branch_manager_contact_id, numericality: true, allow_nil: true
      validates :branch_manager_contact_name, length: (0..101), allow_blank: true
      validates :legal_name, length: (0..100), allow_blank: true
      validates :time_zone, length: (0..50), allow_blank: true
      validates :invoice_number_prefix, length: (0..20), allow_blank: true
      validates :receipt_number_prefix, length: (0..20), allow_blank: true
      validates :opportunity_number_prefix, length: (0..20), allow_blank: true
      validates :region_id, numericality: true, allow_nil: true
      validates :region_name, length: (0..250), allow_blank: true
      validates :billing_email_from_user_name, length: (0..101), allow_blank: true
      validates :billing_email_from_email, length: (0..255), allow_blank: true
      validates :billing_email_subject, length: (0..255), allow_blank: true
      validates :billing_email_body, length: (0..**********), allow_blank: true
      validates :invoice_on_completion_description, length: (0..**********), allow_blank: true
      validates :branch_web_site, length: (0..255), allow_blank: true
      validates :catalog_price_list_id, numericality: true, allow_nil: true
      validates :billing_email_cc, length: (0..**********), allow_blank: true

      def attributes
        {
          "BranchID" => nil,
          "BranchName" => nil,
          "Active" => nil,
          "InternalPropertyID" => nil,
          "InternalPropertyName" => nil,
          "CatalogPriceListName" => nil,
          "BranchAddressID" => nil,
          "BranchAddressLine1" => nil,
          "BranchAddressLine2" => nil,
          "BranchAddressCity" => nil,
          "BranchAddressStateProvinceCode" => nil,
          "BranchAddressZipCode" => nil,
          "BranchPhone" => nil,
          "BranchFax" => nil,
          "BranchCode" => nil,
          "BranchManagerContactID" => nil,
          "BranchManagerContactName" => nil,
          "LegalName" => nil,
          "TimeZone" => nil,
          "InvoiceNumberPrefix" => nil,
          "ReceiptNumberPrefix" => nil,
          "OpportunityNumberPrefix" => nil,
          "RegionID" => nil,
          "RegionName" => nil,
          "BillingEmailFromAccountOwner" => nil,
          "BillingEmailFromUserName" => nil,
          "BillingEmailFromEmail" => nil,
          "BillingEmailSubject" => nil,
          "BillingEmailBody" => nil,
          "InvoiceOnCompletionDescription" => nil,
          "BranchWebSite" => nil,
          "CatalogPriceListID" => nil,
          "BillingEmailCC" => nil
        }
      end
    end

    class AspireCloudDomainTenantCatalogItemCategoriesODataModelCatalogItemCategory < Base
      attribute :catalog_item_category_id, :integer
      attribute :category_name, :string
      attribute :active, :boolean

      validates :catalog_item_category_id, presence: true, numericality: true
      validates :category_name, length: (0..50), allow_blank: true

      def attributes
        {
          "CatalogItemCategoryID" => nil,
          "CategoryName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiCatalogItemsModelRequestCatalogItemInsert < Base
      attribute :catalog_item_category_id, :integer
      attribute :item_type, :string
      attribute :item_name, :string
      attribute :item_alternate_name, :string
      attribute :item_description, :string
      attribute :item_code, :string
      attribute :item_cost, :float
      attribute :purchase_unit_type_id, :integer
      attribute :allocation_unit_type_id, :integer
      attribute :unit_type_allocation_conversion, :float
      attribute :epa_number, :string
      attribute :epa_name, :string
      attribute :inventory, :boolean
      attribute :available_to_bid, :boolean
      attribute :active, :boolean
      attribute :takeoff_item_id, :integer
      attribute :purchase_unit_cost, :float
      attribute :force_unit_pricing, :boolean
      attribute :allocate_from_mobile, :boolean
      attribute :catalog_id, :integer
      attribute :material_barcode1, :string
      attribute :material_barcode2, :string

      validates :catalog_item_category_id, presence: true, numericality: true
      validates :item_type, presence: true, length: (1..)
      validates :item_cost, numericality: true, allow_nil: true
      validates :purchase_unit_type_id, numericality: true, allow_nil: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :unit_type_allocation_conversion, presence: true, numericality: { less_than_or_equal_to: **********.0, greater_than_or_equal_to: 0.01 }
      validates :takeoff_item_id, numericality: true, allow_nil: true
      validates :purchase_unit_cost, numericality: true, allow_nil: true
      validates :catalog_id, numericality: true, allow_nil: true

      def attributes
        {
          "CatalogItemCategoryID" => nil,
          "ItemType" => nil,
          "ItemName" => nil,
          "ItemAlternateName" => nil,
          "ItemDescription" => nil,
          "ItemCode" => nil,
          "ItemCost" => nil,
          "PurchaseUnitTypeID" => nil,
          "AllocationUnitTypeID" => nil,
          "UnitTypeAllocationConversion" => nil,
          "EPANumber" => nil,
          "EPAName" => nil,
          "Inventory" => nil,
          "AvailableToBid" => nil,
          "Active" => nil,
          "TakeoffItemID" => nil,
          "PurchaseUnitCost" => nil,
          "ForceUnitPricing" => nil,
          "AllocateFromMobile" => nil,
          "CatalogId" => nil,
          "MaterialBarcode1" => nil,
          "MaterialBarcode2" => nil
        }
      end
    end

    class AspireCloudExternalApiCatalogItemsModelRequestCatalogItemUpdate < Base
      attribute :catalog_item_id, :integer
      attribute :catalog_item_category_id, :integer
      attribute :item_type, :string
      attribute :item_name, :string
      attribute :item_alternate_name, :string
      attribute :item_description, :string
      attribute :item_code, :string
      attribute :item_cost, :float
      attribute :purchase_unit_type_id, :integer
      attribute :allocation_unit_type_id, :integer
      attribute :unit_type_allocation_conversion, :float
      attribute :epa_number, :string
      attribute :epa_name, :string
      attribute :inventory, :boolean
      attribute :available_to_bid, :boolean
      attribute :active, :boolean
      attribute :takeoff_item_id, :integer
      attribute :purchase_unit_cost, :float
      attribute :force_unit_pricing, :boolean
      attribute :allocate_from_mobile, :boolean
      attribute :catalog_id, :integer
      attribute :material_barcode1, :string
      attribute :material_barcode2, :string

      validates :catalog_item_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :catalog_item_category_id, presence: true, numericality: true
      validates :item_type, presence: true, length: (1..)
      validates :item_cost, numericality: true, allow_nil: true
      validates :purchase_unit_type_id, numericality: true, allow_nil: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :unit_type_allocation_conversion, presence: true, numericality: { less_than_or_equal_to: **********.0, greater_than_or_equal_to: 0.01 }
      validates :takeoff_item_id, numericality: true, allow_nil: true
      validates :purchase_unit_cost, numericality: true, allow_nil: true
      validates :catalog_id, numericality: true, allow_nil: true

      def attributes
        {
          "CatalogItemID" => nil,
          "CatalogItemCategoryID" => nil,
          "ItemType" => nil,
          "ItemName" => nil,
          "ItemAlternateName" => nil,
          "ItemDescription" => nil,
          "ItemCode" => nil,
          "ItemCost" => nil,
          "PurchaseUnitTypeID" => nil,
          "AllocationUnitTypeID" => nil,
          "UnitTypeAllocationConversion" => nil,
          "EPANumber" => nil,
          "EPAName" => nil,
          "Inventory" => nil,
          "AvailableToBid" => nil,
          "Active" => nil,
          "TakeoffItemID" => nil,
          "PurchaseUnitCost" => nil,
          "ForceUnitPricing" => nil,
          "AllocateFromMobile" => nil,
          "CatalogId" => nil,
          "MaterialBarcode1" => nil,
          "MaterialBarcode2" => nil
        }
      end
    end

    class AspireCloudDomainTenantCertificationsODataModelCertification < Base
      attribute :certification_id, :integer
      attribute :certification_type_id, :integer
      attribute :certification_type_name, :string
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :certification_date, :datetime
      attribute :expiration_date, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :certification_id, presence: true, numericality: true
      validates :certification_type_id, presence: true, numericality: true
      validates :certification_type_name, length: (0..50), allow_blank: true
      validates :contact_id, presence: true, numericality: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "CertificationID" => nil,
          "CertificationTypeID" => nil,
          "CertificationTypeName" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "CertificationDate" => nil,
          "ExpirationDate" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantCertificationTypesODataModelCertificationType < Base
      attribute :certification_type_id, :integer
      attribute :certification_type_name, :string
      attribute :active, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :certification_type_id, presence: true, numericality: true
      validates :certification_type_name, length: (0..50), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "CertificationTypeID" => nil,
          "CertificationTypeName" => nil,
          "Active" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantClockTimesODataModelClockTime < Base
      attribute :clock_time_id, :integer
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :clock_start, :datetime
      attribute :clock_end, :datetime
      attribute :accepted_date_time, :datetime
      attribute :accepted_user_id, :integer
      attribute :accepted_user_name, :string
      attribute :break_time, :float
      attribute :accepted_short_lunch, :boolean
      attribute :used_breaks, :boolean
      attribute :prevented_from_using_breaks, :boolean
      attribute :geo_location_start_latitude, :float
      attribute :geo_location_start_longitude, :float
      attribute :geo_location_end_latitude, :float
      attribute :geo_location_end_longitude, :float

      validates :clock_time_id, presence: true, numericality: true
      validates :contact_id, numericality: true, allow_nil: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :accepted_user_id, numericality: true, allow_nil: true
      validates :accepted_user_name, length: (0..101), allow_blank: true
      validates :break_time, numericality: true, allow_nil: true
      validates :geo_location_start_latitude, numericality: true, allow_nil: true
      validates :geo_location_start_longitude, numericality: true, allow_nil: true
      validates :geo_location_end_latitude, numericality: true, allow_nil: true
      validates :geo_location_end_longitude, numericality: true, allow_nil: true

      def attributes
        {
          "ClockTimeID" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "ClockStart" => nil,
          "ClockEnd" => nil,
          "AcceptedDateTime" => nil,
          "AcceptedUserID" => nil,
          "AcceptedUserName" => nil,
          "BreakTime" => nil,
          "AcceptedShortLunch" => nil,
          "UsedBreaks" => nil,
          "PreventedFromUsingBreaks" => nil,
          "GEOLocationStartLatitude" => nil,
          "GEOLocationStartLongitude" => nil,
          "GEOLocationEndLatitude" => nil,
          "GEOLocationEndLongitude" => nil
        }
      end
    end

    class AspireCloudExternalApiClockTimesModelClockTimeInsertRequest < Base
      attribute :contact_id, :integer
      attribute :clock_start_lat, :float
      attribute :clock_start_long, :float
      attribute :clock_start_date_time, :datetime
      attribute :break_time, :float
      attribute :route_id, :integer
      attribute :crew_leader_contact_id, :integer
      attribute :clock_end_lat, :float
      attribute :clock_end_long, :float
      attribute :clock_end_date_time, :datetime

      validates :contact_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :clock_start_lat, presence: true, numericality: true
      validates :clock_start_long, presence: true, numericality: true
      validates :clock_start_date_time, presence: true
      validates :break_time, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :route_id, numericality: true, inclusion: { in: (1..**********) }, allow_nil: true
      validates :crew_leader_contact_id, numericality: true, inclusion: { in: (1..**********) }, allow_nil: true
      validates :clock_end_lat, presence: true, numericality: true
      validates :clock_end_long, presence: true, numericality: true
      validates :clock_end_date_time, presence: true

      def attributes
        {
          "ContactID" => nil,
          "ClockStartLat" => nil,
          "ClockStartLong" => nil,
          "ClockStartDateTime" => nil,
          "BreakTime" => nil,
          "RouteID" => nil,
          "CrewLeaderContactID" => nil,
          "ClockEndLat" => nil,
          "ClockEndLong" => nil,
          "ClockEndDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantCompaniesODataModelCompany < Base
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :created_date_time, :datetime
      attribute :created_by_user_name, :string
      attribute :modified_date_time, :datetime
      attribute :modified_by_user_name, :string
      attribute :denied_time_email, :string
      attribute :active, :boolean
      attribute :created_by_user_id, :integer
      attribute :modified_by_user_id, :integer
      attribute :earliest_opportunity_won_date, :datetime

      validates :company_id, presence: true, numericality: true
      validates :company_name, length: (0..101), allow_blank: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :modified_by_user_name, length: (0..101), allow_blank: true
      validates :denied_time_email, length: (0..255), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :modified_by_user_id, numericality: true, allow_nil: true

      def attributes
        {
          "CompanyID" => nil,
          "CompanyName" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserName" => nil,
          "ModifiedDateTime" => nil,
          "ModifiedByUserName" => nil,
          "DeniedTimeEmail" => nil,
          "Active" => nil,
          "CreatedByUserID" => nil,
          "ModifiedByUserID" => nil,
          "EarliestOpportunityWonDate" => nil
        }
      end
    end

    class AspireCloudExternalApiCompaniesModelRequestCompanyInsertRequest < Base
      attribute :company_name, :string
      attribute :denied_time_email, :string
      attribute :active, :boolean

      validates :company_name, presence: true, length: (1..)

      def attributes
        {
          "CompanyName" => nil,
          "DeniedTimeEmail" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiCompaniesModelResponseCompanyResponse < Base
      attribute :company_id, :integer

      validates :company_id, presence: true, numericality: true

      def attributes
        {
          "CompanyID" => nil
        }
      end
    end

    class AspireCloudExternalApiCompaniesModelRequestCompanyUpdateRequest < Base
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :denied_time_email, :string
      attribute :active, :boolean

      validates :company_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :company_name, presence: true, length: (1..)

      def attributes
        {
          "CompanyID" => nil,
          "CompanyName" => nil,
          "DeniedTimeEmail" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantContactsODataModelContactType < Base
      attribute :contact_type_id, :integer
      attribute :contact_type_name, :string
      attribute :active, :boolean

      validates :contact_type_id, presence: true, numericality: true
      validates :contact_type_name, length: (0..20), allow_blank: true

      def attributes
        {
          "ContactTypeID" => nil,
          "ContactTypeName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantDivisionIntegrationCodesODataModelDivisionIntegrationCode < Base
      attribute :division_integration_code_id, :integer
      attribute :division_integration_code_name, :string
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :active, :boolean

      validates :division_integration_code_id, presence: true, numericality: true
      validates :division_integration_code_name, length: (1..50), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "DivisionIntegrationCodeID" => nil,
          "DivisionIntegrationCodeName" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantDivisionsODataModelDivision < Base
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :active, :boolean
      attribute :division_code, :string
      attribute :indirect, :boolean
      attribute :workers_comp_id, :integer
      attribute :workers_comp_name, :string
      attribute :account_number, :string
      attribute :material_expense_account_number, :string
      attribute :equipment_expense_account_number, :string
      attribute :sub_expense_account_number, :string
      attribute :other_expense_account_number, :string

      validates :division_id, presence: true, numericality: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :division_code, length: (0..6), allow_blank: true
      validates :workers_comp_id, numericality: true, allow_nil: true
      validates :workers_comp_name, length: (0..200), allow_blank: true
      validates :account_number, length: (0..50), allow_blank: true
      validates :material_expense_account_number, length: (0..50), allow_blank: true
      validates :equipment_expense_account_number, length: (0..50), allow_blank: true
      validates :sub_expense_account_number, length: (0..50), allow_blank: true
      validates :other_expense_account_number, length: (0..50), allow_blank: true

      def attributes
        {
          "DivisionID" => nil,
          "DivisionName" => nil,
          "Active" => nil,
          "DivisionCode" => nil,
          "Indirect" => nil,
          "WorkersCompID" => nil,
          "WorkersCompName" => nil,
          "AccountNumber" => nil,
          "MaterialExpenseAccountNumber" => nil,
          "EquipmentExpenseAccountNumber" => nil,
          "SubExpenseAccountNumber" => nil,
          "OtherExpenseAccountNumber" => nil
        }
      end
    end

    class AspireCloudDomainTenantEmployeeIncidentsODataModelEmployeeIncident < Base
      attribute :employee_incident_id, :integer
      attribute :employee_incident_type_id, :integer
      attribute :employee_incident_type_name, :string
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :employee_incident_date, :datetime
      attribute :cost, :float
      attribute :insurance_submission_date, :datetime
      attribute :hr_comments, :string
      attribute :employee_comments, :string
      attribute :supervisor_comments, :string
      attribute :created_by_user_id, :integer
      attribute :created_date_time, :datetime
      attribute :created_by_user_name, :string
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :supervisor_contact_id, :integer
      attribute :hr_contact_id, :integer

      validates :employee_incident_id, presence: true, numericality: true
      validates :employee_incident_type_id, presence: true, numericality: true
      validates :employee_incident_type_name, length: (0..50), allow_blank: true
      validates :contact_id, presence: true, numericality: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :cost, numericality: true, allow_nil: true
      validates :hr_comments, length: (0..1000), allow_blank: true
      validates :employee_comments, length: (0..1000), allow_blank: true
      validates :supervisor_comments, length: (0..1000), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_date_time, presence: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :supervisor_contact_id, numericality: true, allow_nil: true
      validates :hr_contact_id, numericality: true, allow_nil: true

      def attributes
        {
          "EmployeeIncidentID" => nil,
          "EmployeeIncidentTypeID" => nil,
          "EmployeeIncidentTypeName" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "EmployeeIncidentDate" => nil,
          "Cost" => nil,
          "InsuranceSubmissionDate" => nil,
          "HRComments" => nil,
          "EmployeeComments" => nil,
          "SupervisorComments" => nil,
          "CreatedByUserID" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "SupervisorContactID" => nil,
          "HRContactID" => nil
        }
      end
    end

    class AspireCloudDomainTenantEmployeeIncidentsODataModelEmployeeIncidentType < Base
      attribute :employee_incident_type_id, :integer
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :employee_incident_type_name, :string
      attribute :active, :boolean

      validates :employee_incident_type_id, presence: true, numericality: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :employee_incident_type_name, length: (0..50), allow_blank: true

      def attributes
        {
          "EmployeeIncidentTypeID" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "EmployeeIncidentTypeName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentClassODataModelEquipmentClass < Base
      attribute :equipment_class_id, :integer
      attribute :equipment_class_name, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string

      validates :equipment_class_id, presence: true, numericality: true
      validates :equipment_class_name, length: (0..100), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentClassID" => nil,
          "EquipmentClassName" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentDisposalReasonsODataModelEquipmentDisposalReason < Base
      attribute :equipment_disposal_reason_id, :integer
      attribute :disposal_reason, :string
      attribute :active, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :equipment_disposal_reason_id, presence: true, numericality: true
      validates :disposal_reason, length: (0..50), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentDisposalReasonID" => nil,
          "DisposalReason" => nil,
          "Active" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentODataModelEquipmentManufacturer < Base
      attribute :equipment_manufacturer_id, :integer
      attribute :equipment_manufacturer_name, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string

      validates :equipment_manufacturer_id, presence: true, numericality: true
      validates :equipment_manufacturer_name, length: (0..100), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentManufacturerID" => nil,
          "EquipmentManufacturerName" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentODataModelEquipmentModel < Base
      attribute :equipment_model_id, :integer
      attribute :equipment_manufacturer_id, :integer
      attribute :equipment_manufacturer_name, :string
      attribute :equipment_size_id, :integer
      attribute :equipment_size_name, :string
      attribute :equipment_class_id, :integer
      attribute :equipment_class_name, :string
      attribute :model_name, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :cap_ex_months, :integer
      attribute :meter_type, :string
      attribute :fuel_burn_rate, :float
      attribute :fuel_unit, :string

      validates :equipment_model_id, presence: true, numericality: true
      validates :equipment_manufacturer_id, presence: true, numericality: true
      validates :equipment_manufacturer_name, length: (0..100), allow_blank: true
      validates :equipment_size_id, presence: true, numericality: true
      validates :equipment_size_name, length: (0..100), allow_blank: true
      validates :equipment_class_id, presence: true, numericality: true
      validates :equipment_class_name, length: (0..100), allow_blank: true
      validates :model_name, length: (0..100), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :cap_ex_months, numericality: true, allow_nil: true
      validates :meter_type, length: (0..10), allow_blank: true
      validates :fuel_burn_rate, numericality: true, allow_nil: true
      validates :fuel_unit, length: (0..10), allow_blank: true

      def attributes
        {
          "EquipmentModelID" => nil,
          "EquipmentManufacturerID" => nil,
          "EquipmentManufacturerName" => nil,
          "EquipmentSizeID" => nil,
          "EquipmentSizeName" => nil,
          "EquipmentClassID" => nil,
          "EquipmentClassName" => nil,
          "ModelName" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "CapEXMonths" => nil,
          "MeterType" => nil,
          "FuelBurnRate" => nil,
          "FuelUnit" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentModelServiceSchedulesODataModelEquipmentModelServiceSchedule < Base
      attribute :equipment_model_service_schedule_id, :integer
      attribute :equipment_model_id, :integer
      attribute :model_name, :string
      attribute :service_schedule_type, :string
      attribute :service_schedule_calendar_type, :string
      attribute :service_schedule_value, :integer
      attribute :equipment_service_tag_id, :integer
      attribute :service_tag_name, :string
      attribute :service_schedule_cost, :float
      attribute :service_schedule_hours, :float
      attribute :active, :boolean
      attribute :reoccurring, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string

      validates :equipment_model_service_schedule_id, presence: true, numericality: true
      validates :equipment_model_id, presence: true, numericality: true
      validates :model_name, length: (0..100), allow_blank: true
      validates :service_schedule_type, length: (0..10), allow_blank: true
      validates :service_schedule_calendar_type, length: (0..10), allow_blank: true
      validates :service_schedule_value, presence: true, numericality: true
      validates :equipment_service_tag_id, presence: true, numericality: true
      validates :service_tag_name, length: (0..255), allow_blank: true
      validates :service_schedule_cost, presence: true, numericality: true
      validates :service_schedule_hours, presence: true, numericality: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentModelServiceScheduleID" => nil,
          "EquipmentModelID" => nil,
          "ModelName" => nil,
          "ServiceScheduleType" => nil,
          "ServiceScheduleCalendarType" => nil,
          "ServiceScheduleValue" => nil,
          "EquipmentServiceTagID" => nil,
          "ServiceTagName" => nil,
          "ServiceScheduleCost" => nil,
          "ServiceScheduleHours" => nil,
          "Active" => nil,
          "Reoccurring" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentReadingLogODataModelEquipmentReadingLog < Base
      attribute :equipment_reading_log_id, :integer
      attribute :equipment_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :route_id, :integer
      attribute :route_name, :string
      attribute :log_date, :datetime
      attribute :reading_date, :datetime
      attribute :meter_reading, :float
      attribute :trouble_code, :string
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :equipment_reading_log_id, presence: true, numericality: true
      validates :equipment_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :route_id, numericality: true, allow_nil: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :log_date, presence: true
      validates :reading_date, presence: true
      validates :meter_reading, presence: true, numericality: true
      validates :trouble_code, length: (0..50), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentReadingLogID" => nil,
          "EquipmentID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "RouteID" => nil,
          "RouteName" => nil,
          "LogDate" => nil,
          "ReadingDate" => nil,
          "MeterReading" => nil,
          "TroubleCode" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudExternalApiEquipmentReadingLogsModelEquipmentReadingLogInsertRequest < Base
      attribute :equipment_id, :integer
      attribute :log_date, :datetime
      attribute :reading_date, :datetime
      attribute :meter_reading, :float
      attribute :trouble_code, :string

      validates :equipment_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :log_date, presence: true
      validates :reading_date, presence: true
      validates :meter_reading, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :trouble_code, length: (0..50), allow_blank: true

      def attributes
        {
          "EquipmentID" => nil,
          "LogDate" => nil,
          "ReadingDate" => nil,
          "MeterReading" => nil,
          "TroubleCode" => nil
        }
      end
    end

    class AspireCloudExternalApiEquipmentReadingLogsModelEquipmentReadingLogUpdateRequest < Base
      attribute :equipment_reading_log_id, :integer
      attribute :equipment_id, :integer
      attribute :log_date, :datetime
      attribute :reading_date, :datetime
      attribute :meter_reading, :float
      attribute :trouble_code, :string

      validates :equipment_reading_log_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :equipment_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :log_date, presence: true
      validates :reading_date, presence: true
      validates :meter_reading, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :trouble_code, length: (0..50), allow_blank: true

      def attributes
        {
          "EquipmentReadingLogID" => nil,
          "EquipmentID" => nil,
          "LogDate" => nil,
          "ReadingDate" => nil,
          "MeterReading" => nil,
          "TroubleCode" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentRequestedServicesODataModelEquipmentRequestedService < Base
      attribute :equipment_requested_service_id, :integer
      attribute :equipment_service_tag_id, :integer
      attribute :service_tag_name, :string
      attribute :equipment_id, :integer
      attribute :equipment_service_log_id, :integer
      attribute :notes, :string
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :equipment_inspection_category_id, :integer
      attribute :equipment_inspection_category_name, :string
      attribute :equipment_inspection_id, :integer
      attribute :request_source, :string

      validates :equipment_requested_service_id, presence: true, numericality: true
      validates :equipment_service_tag_id, presence: true, numericality: true
      validates :service_tag_name, length: (0..255), allow_blank: true
      validates :equipment_id, presence: true, numericality: true
      validates :equipment_service_log_id, numericality: true, allow_nil: true
      validates :notes, length: (0..1000), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :equipment_inspection_category_id, numericality: true, allow_nil: true
      validates :equipment_inspection_category_name, length: (0..150), allow_blank: true
      validates :equipment_inspection_id, numericality: true, allow_nil: true
      validates :request_source, length: (0..50), allow_blank: true

      def attributes
        {
          "EquipmentRequestedServiceID" => nil,
          "EquipmentServiceTagID" => nil,
          "ServiceTagName" => nil,
          "EquipmentID" => nil,
          "EquipmentServiceLogID" => nil,
          "Notes" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "EquipmentInspectionCategoryID" => nil,
          "EquipmentInspectionCategoryName" => nil,
          "EquipmentInspectionID" => nil,
          "RequestSource" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentODataModelEquipment < Base
      attribute :equipment_id, :integer
      attribute :equipment_model_id, :integer
      attribute :branch_id, :integer
      attribute :division_id, :integer
      attribute :property_id, :integer
      attribute :route_id, :integer
      attribute :description, :string
      attribute :model_year, :integer
      attribute :asset_number, :string
      attribute :requested_date, :datetime
      attribute :requested_user_id, :integer
      attribute :approved_date, :datetime
      attribute :approved_user_id, :integer
      attribute :purchased_date, :datetime
      attribute :purchased_user_id, :integer
      attribute :purchase_price, :float
      attribute :in_service_date, :datetime
      attribute :in_service_user_id, :integer
      attribute :out_of_service_date, :datetime
      attribute :out_of_service_user_id, :integer
      attribute :disposed_date, :datetime
      attribute :disposed_user_id, :integer
      attribute :equipment_disposal_reason_id, :integer
      attribute :disposed_price, :float
      attribute :serial_number, :string
      attribute :engine_number, :string
      attribute :active, :boolean
      attribute :warranty_days, :integer
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :equipment_status_id, :integer
      attribute :financing_bank, :string
      attribute :pay_schedule, :integer
      attribute :plate_number, :string
      attribute :renewal_date, :datetime
      attribute :gross_vehicle_weight, :integer
      attribute :dealer, :string
      attribute :mileage_hours, :integer
      attribute :estimated_purchase_price, :float
      attribute :aspire_gps_identifier, :string
      attribute :tracker_type, :string
      attribute :equipment_model_name, :string
      attribute :branch_name, :string
      attribute :division_name, :string
      attribute :property_name, :string
      attribute :route_name, :string
      attribute :requested_user_name, :string
      attribute :approved_user_name, :string
      attribute :purchased_user_name, :string
      attribute :in_service_user_name, :string
      attribute :out_of_service_user_name, :string
      attribute :disposed_user_name, :string
      attribute :created_by_user_name, :string
      attribute :last_modified_by_user_name, :string
      attribute :equipment_disposal_reason, :string
      attribute :equipment_status_name, :string

      validates :equipment_id, presence: true, numericality: true
      validates :equipment_model_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :division_id, numericality: true, allow_nil: true
      validates :property_id, numericality: true, allow_nil: true
      validates :route_id, numericality: true, allow_nil: true
      validates :description, length: (0..250), allow_blank: true
      validates :model_year, numericality: true, allow_nil: true
      validates :asset_number, length: (0..50), allow_blank: true
      validates :requested_user_id, numericality: true, allow_nil: true
      validates :approved_user_id, numericality: true, allow_nil: true
      validates :purchased_user_id, numericality: true, allow_nil: true
      validates :purchase_price, numericality: true, allow_nil: true
      validates :in_service_user_id, numericality: true, allow_nil: true
      validates :out_of_service_user_id, numericality: true, allow_nil: true
      validates :disposed_user_id, numericality: true, allow_nil: true
      validates :equipment_disposal_reason_id, numericality: true, allow_nil: true
      validates :disposed_price, numericality: true, allow_nil: true
      validates :serial_number, length: (0..50), allow_blank: true
      validates :engine_number, length: (0..50), allow_blank: true
      validates :warranty_days, numericality: true, allow_nil: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :equipment_status_id, presence: true, numericality: true
      validates :financing_bank, length: (0..100), allow_blank: true
      validates :pay_schedule, numericality: true, allow_nil: true
      validates :plate_number, length: (0..50), allow_blank: true
      validates :gross_vehicle_weight, numericality: true, allow_nil: true
      validates :dealer, length: (0..100), allow_blank: true
      validates :mileage_hours, numericality: true, allow_nil: true
      validates :estimated_purchase_price, numericality: true, allow_nil: true
      validates :aspire_gps_identifier, length: (0..50), allow_blank: true
      validates :tracker_type, length: (0..25), allow_blank: true
      validates :equipment_model_name, length: (0..100), allow_blank: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :property_name, length: (0..100), allow_blank: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :requested_user_name, length: (0..101), allow_blank: true
      validates :approved_user_name, length: (0..101), allow_blank: true
      validates :purchased_user_name, length: (0..101), allow_blank: true
      validates :in_service_user_name, length: (0..101), allow_blank: true
      validates :out_of_service_user_name, length: (0..101), allow_blank: true
      validates :disposed_user_name, length: (0..101), allow_blank: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :equipment_disposal_reason, length: (0..50), allow_blank: true
      validates :equipment_status_name, length: (0..50), allow_blank: true

      def attributes
        {
          "EquipmentID" => nil,
          "EquipmentModelID" => nil,
          "BranchID" => nil,
          "DivisionID" => nil,
          "PropertyID" => nil,
          "RouteID" => nil,
          "Description" => nil,
          "ModelYear" => nil,
          "AssetNumber" => nil,
          "RequestedDate" => nil,
          "RequestedUserID" => nil,
          "ApprovedDate" => nil,
          "ApprovedUserID" => nil,
          "PurchasedDate" => nil,
          "PurchasedUserID" => nil,
          "PurchasePrice" => nil,
          "InServiceDate" => nil,
          "InServiceUserID" => nil,
          "OutOfServiceDate" => nil,
          "OutOfServiceUserID" => nil,
          "DisposedDate" => nil,
          "DisposedUserID" => nil,
          "EquipmentDisposalReasonID" => nil,
          "DisposedPrice" => nil,
          "SerialNumber" => nil,
          "EngineNumber" => nil,
          "Active" => nil,
          "WarrantyDays" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "EquipmentStatusID" => nil,
          "FinancingBank" => nil,
          "PaySchedule" => nil,
          "PlateNumber" => nil,
          "RenewalDate" => nil,
          "GrossVehicleWeight" => nil,
          "Dealer" => nil,
          "MileageHours" => nil,
          "EstimatedPurchasePrice" => nil,
          "AspireGPSIdentifier" => nil,
          "TrackerType" => nil,
          "EquipmentModelName" => nil,
          "BranchName" => nil,
          "DivisionName" => nil,
          "PropertyName" => nil,
          "RouteName" => nil,
          "RequestedUserName" => nil,
          "ApprovedUserName" => nil,
          "PurchasedUserName" => nil,
          "InServiceUserName" => nil,
          "OutOfServiceUserName" => nil,
          "DisposedUserName" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedByUserName" => nil,
          "EquipmentDisposalReason" => nil,
          "EquipmentStatusName" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentServiceLogsODataModelEquipmentServiceLog < Base
      attribute :equipment_service_log_id, :integer
      attribute :equipment_id, :integer
      attribute :equipment_model_service_schedule_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :route_id, :integer
      attribute :route_name, :string
      attribute :service_date, :datetime
      attribute :technician_contact_id, :integer
      attribute :technician_contact_name, :string
      attribute :service_comment, :string
      attribute :service_cost, :float
      attribute :service_hours, :float
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :meter_reading, :float
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :equipment_service_log_id, presence: true, numericality: true
      validates :equipment_id, presence: true, numericality: true
      validates :equipment_model_service_schedule_id, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :route_id, numericality: true, allow_nil: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :service_date, presence: true
      validates :technician_contact_id, numericality: true, allow_nil: true
      validates :technician_contact_name, length: (0..101), allow_blank: true
      validates :service_comment, length: (0..1000), allow_blank: true
      validates :service_cost, presence: true, numericality: true
      validates :service_hours, presence: true, numericality: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :meter_reading, numericality: true, allow_nil: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentServiceLogID" => nil,
          "EquipmentID" => nil,
          "EquipmentModelServiceScheduleID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "RouteID" => nil,
          "RouteName" => nil,
          "ServiceDate" => nil,
          "TechnicianContactID" => nil,
          "TechnicianContactName" => nil,
          "ServiceComment" => nil,
          "ServiceCost" => nil,
          "ServiceHours" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "MeterReading" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentServiceTagsODataModelEquipmentServiceTag < Base
      attribute :equipment_service_tag_id, :integer
      attribute :service_tag_name, :string
      attribute :service_tag_cost, :float
      attribute :service_tag_hours, :float
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :service_type, :string

      validates :equipment_service_tag_id, presence: true, numericality: true
      validates :service_tag_name, length: (0..255), allow_blank: true
      validates :service_tag_cost, numericality: true, allow_nil: true
      validates :service_tag_hours, numericality: true, allow_nil: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :service_type, length: (0..15), allow_blank: true

      def attributes
        {
          "EquipmentServiceTagID" => nil,
          "ServiceTagName" => nil,
          "ServiceTagCost" => nil,
          "ServiceTagHours" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "ServiceType" => nil
        }
      end
    end

    class AspireCloudDomainTenantEquipmentSizesODataModelEquipmentSize < Base
      attribute :equipment_size_id, :integer
      attribute :equipment_size_name, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :equipment_size_id, presence: true, numericality: true
      validates :equipment_size_name, length: (0..100), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "EquipmentSizeID" => nil,
          "EquipmentSizeName" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantInventoryLocationsODataModelInventoryLocation < Base
      attribute :inventory_location_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :default_location, :boolean
      attribute :active, :boolean
      attribute :address_id, :integer
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string

      validates :inventory_location_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :address_id, numericality: true, allow_nil: true

      def attributes
        {
          "InventoryLocationID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "DefaultLocation" => nil,
          "Active" => nil,
          "AddressID" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoiceBatchesODataModelInvoiceBatch < Base
      attribute :invoice_batch_id, :integer
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :submitted_by_user_id, :integer
      attribute :submitted_by_user_name, :string
      attribute :submit_date_time, :datetime
      attribute :invoice_batch_number, :integer
      attribute :customer_event_id, :integer
      attribute :customer_event_type_id, :integer
      attribute :customer_event_type_name, :string
      attribute :customer_event_name, :string
      attribute :customer_event_start_date_time, :datetime
      attribute :customer_event_end_date_time, :datetime
      attribute :customer_event_description, :string
      attribute :accounting_period_id, :integer
      attribute :accounting_period_name, :string

      validates :invoice_batch_id, presence: true, numericality: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :submitted_by_user_id, numericality: true, allow_nil: true
      validates :invoice_batch_number, numericality: true, allow_nil: true
      validates :customer_event_id, numericality: true, allow_nil: true
      validates :customer_event_type_id, numericality: true, allow_nil: true
      validates :accounting_period_id, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceBatchID" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "SubmittedByUserID" => nil,
          "SubmittedByUserName" => nil,
          "SubmitDateTime" => nil,
          "InvoiceBatchNumber" => nil,
          "CustomerEventID" => nil,
          "CustomerEventTypeID" => nil,
          "CustomerEventTypeName" => nil,
          "CustomerEventName" => nil,
          "CustomerEventStartDateTime" => nil,
          "CustomerEventEndDateTime" => nil,
          "CustomerEventDescription" => nil,
          "AccountingPeriodID" => nil,
          "AccountingPeriodName" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoiceRevenuesODataModelInvoiceRevenue < Base
      attribute :invoice_number, :integer
      attribute :invoice_revenue_id, :integer
      attribute :invoice_opportunity_id, :integer
      attribute :invoice_id, :integer
      attribute :invoice_date, :datetime
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :amount, :float
      attribute :invoice_batch_id, :integer
      attribute :division_code, :string

      validates :invoice_number, numericality: true, allow_nil: true
      validates :invoice_revenue_id, presence: true, numericality: true
      validates :invoice_opportunity_id, numericality: true, allow_nil: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :amount, numericality: true, allow_nil: true
      validates :invoice_batch_id, numericality: true, allow_nil: true
      validates :division_code, length: (0..6), allow_blank: true

      def attributes
        {
          "InvoiceNumber" => nil,
          "InvoiceRevenueID" => nil,
          "InvoiceOpportunityID" => nil,
          "InvoiceID" => nil,
          "InvoiceDate" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "Amount" => nil,
          "InvoiceBatchID" => nil,
          "DivisionCode" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoiceTaxesODataModelInvoiceTax < Base
      attribute :invoice_number, :integer
      attribute :amount, :float
      attribute :invoice_date, :datetime
      attribute :invoice_id, :integer
      attribute :invoice_tax_id, :integer
      attribute :tax_entity_id, :integer
      attribute :tax_entity_name, :string
      attribute :payment_id, :integer
      attribute :invoice_batch_id, :integer

      validates :invoice_number, numericality: true, allow_nil: true
      validates :amount, numericality: true, allow_nil: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :invoice_tax_id, presence: true, numericality: true
      validates :tax_entity_id, numericality: true, allow_nil: true
      validates :tax_entity_name, length: (0..150), allow_blank: true
      validates :payment_id, numericality: true, allow_nil: true
      validates :invoice_batch_id, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceNumber" => nil,
          "Amount" => nil,
          "InvoiceDate" => nil,
          "InvoiceID" => nil,
          "InvoiceTaxID" => nil,
          "TaxEntityID" => nil,
          "TaxEntityName" => nil,
          "PaymentID" => nil,
          "InvoiceBatchID" => nil
        }
      end
    end

    class AspireCloudExternalApiActivitiesModelsIssueRequest < Base
      attribute :include_client, :boolean
      attribute :notes, :string
      attribute :public_comment, :boolean
      attribute :assigned_to, :string
      attribute :due_date, :datetime
      attribute :opportunity_id, :integer
      attribute :priority, :string
      attribute :property_id, :integer
      attribute :start_date, :datetime
      attribute :subject, :string
      attribute :work_ticket_id, :integer

      validates :notes, presence: true
      validates :assigned_to, presence: true, length: (1..)
      validates :due_date, presence: true
      validates :opportunity_id, presence: true, numericality: true, inclusion: { in: (0..**********) }
      validates :property_id, presence: true, numericality: true, inclusion: { in: (0..**********) }
      validates :start_date, presence: true
      validates :subject, presence: true, length: (0..200)
      validates :work_ticket_id, presence: true, numericality: true, inclusion: { in: (0..**********) }

      def attributes
        {
          "IncludeClient" => nil,
          "Notes" => nil,
          "PublicComment" => nil,
          "AssignedTo" => nil,
          "DueDate" => nil,
          "OpportunityID" => nil,
          "Priority" => nil,
          "PropertyID" => nil,
          "StartDate" => nil,
          "Subject" => nil,
          "WorkTicketID" => nil
        }
      end
    end

    class AspireCloudDomainTenantItemAllocationsODataModelItemAllocation < Base
      attribute :item_allocation_id, :integer
      attribute :receipt_item_id, :integer
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :inventory_location_id, :integer
      attribute :inventory_location_name, :string
      attribute :device_id, :integer
      attribute :device_name, :string
      attribute :catalog_item_id, :integer
      attribute :catalog_item_name, :string
      attribute :item_name, :string
      attribute :item_type, :string
      attribute :item_quantity, :float
      attribute :item_unit_cost, :float
      attribute :item_total_cost, :float
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :accepted_date_time, :datetime
      attribute :accepted_user_id, :integer
      attribute :accepted_user_name, :string
      attribute :allocation_status, :string
      attribute :item_allocation_date, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :transaction_id, :integer
      attribute :inventory_adjustment, :boolean
      attribute :invoice_id, :integer
      attribute :job_inventory_item_allocation_id, :integer
      attribute :from_work_ticket_id_to_inventory, :integer
      attribute :pre_received_item_quantity, :float
      attribute :subcontractor_accepted_user_id, :integer
      attribute :subcontractor_accepted_date_time, :datetime
      attribute :subcontractor_created, :boolean
      attribute :subcontractor_route_id, :integer
      attribute :subcontractor_services_rendered_date, :datetime
      attribute :work_ticket_time_id, :integer
      attribute :epa_name, :string
      attribute :epa_number, :string

      validates :item_allocation_id, presence: true, numericality: true
      validates :receipt_item_id, numericality: true, allow_nil: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :inventory_location_id, numericality: true, allow_nil: true
      validates :inventory_location_name, length: (0..150), allow_blank: true
      validates :device_id, numericality: true, allow_nil: true
      validates :device_name, length: (0..50), allow_blank: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :catalog_item_name, length: (0..200), allow_blank: true
      validates :item_name, length: (0..200), allow_blank: true
      validates :item_type, length: (0..10), allow_blank: true
      validates :item_quantity, numericality: true, allow_nil: true
      validates :item_unit_cost, numericality: true, allow_nil: true
      validates :item_total_cost, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :accepted_user_id, numericality: true, allow_nil: true
      validates :accepted_user_name, length: (0..101), allow_blank: true
      validates :allocation_status, length: (0..10), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :transaction_id, numericality: true, allow_nil: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :job_inventory_item_allocation_id, numericality: true, allow_nil: true
      validates :from_work_ticket_id_to_inventory, numericality: true, allow_nil: true
      validates :pre_received_item_quantity, numericality: true, allow_nil: true
      validates :subcontractor_accepted_user_id, numericality: true, allow_nil: true
      validates :subcontractor_route_id, numericality: true, allow_nil: true
      validates :work_ticket_time_id, numericality: true, allow_nil: true

      def attributes
        {
          "ItemAllocationID" => nil,
          "ReceiptItemID" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "InventoryLocationID" => nil,
          "InventoryLocationName" => nil,
          "DeviceID" => nil,
          "DeviceName" => nil,
          "CatalogItemID" => nil,
          "CatalogItemName" => nil,
          "ItemName" => nil,
          "ItemType" => nil,
          "ItemQuantity" => nil,
          "ItemUnitCost" => nil,
          "ItemTotalCost" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "AcceptedDateTime" => nil,
          "AcceptedUserID" => nil,
          "AcceptedUserName" => nil,
          "AllocationStatus" => nil,
          "ItemAllocationDate" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "TransactionID" => nil,
          "InventoryAdjustment" => nil,
          "InvoiceID" => nil,
          "JobInventoryItemAllocationID" => nil,
          "FromWorkTicketIDToInventory" => nil,
          "PreReceivedItemQuantity" => nil,
          "SubcontractorAcceptedUserID" => nil,
          "SubcontractorAcceptedDateTime" => nil,
          "SubcontractorCreated" => nil,
          "SubcontractorRouteID" => nil,
          "SubcontractorServicesRenderedDate" => nil,
          "WorkTicketTimeID" => nil,
          "EPAName" => nil,
          "EPANumber" => nil
        }
      end
    end

    class AspireCloudExternalApiInventoryModelsAllocateFromInventoryPostRequest < Base
      attribute :quantity, :float
      attribute :catalog_item_id, :integer
      attribute :inventory_location_id, :integer
      attribute :work_ticket_id, :integer
      attribute :allocation_date, :datetime

      validates :quantity, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :catalog_item_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :inventory_location_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :work_ticket_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :allocation_date, presence: true

      def attributes
        {
          "Quantity" => nil,
          "CatalogItemID" => nil,
          "InventoryLocationID" => nil,
          "WorkTicketID" => nil,
          "AllocationDate" => nil
        }
      end
    end

    class AspireCloudExternalApiInventoryModelsAllocateFromInventoryPutRequest < Base
      attribute :item_allocation_id, :integer
      attribute :quantity, :float
      attribute :catalog_item_id, :integer
      attribute :inventory_location_id, :integer
      attribute :work_ticket_id, :integer
      attribute :allocation_date, :datetime

      validates :item_allocation_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :quantity, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :catalog_item_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :inventory_location_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :work_ticket_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :allocation_date, presence: true

      def attributes
        {
          "ItemAllocationId" => nil,
          "Quantity" => nil,
          "CatalogItemID" => nil,
          "InventoryLocationID" => nil,
          "WorkTicketID" => nil,
          "AllocationDate" => nil
        }
      end
    end

    class AspireCloudDomainTenantCountyODataModelCounty < Base
      attribute :locality_id, :integer
      attribute :locality_name, :string
      attribute :local_code, :string
      attribute :active, :boolean

      validates :locality_id, presence: true, numericality: true
      validates :locality_name, length: (0..50), allow_blank: true
      validates :local_code, length: (0..10), allow_blank: true

      def attributes
        {
          "LocalityID" => nil,
          "LocalityName" => nil,
          "LocalCode" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiCountiesModelLocalityInsertRequest < Base
      attribute :locality_name, :string
      attribute :local_code, :string
      attribute :active, :boolean

      validates :locality_name, presence: true, length: (1..50)
      validates :local_code, length: (0..10), allow_blank: true

      def attributes
        {
          "LocalityName" => nil,
          "LocalCode" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiCountiesModelLocalityUpdateRequest < Base
      attribute :locality_id, :integer
      attribute :locality_name, :string
      attribute :local_code, :string
      attribute :active, :boolean

      validates :locality_id, presence: true, numericality: true
      validates :locality_name, presence: true, length: (1..50)
      validates :local_code, length: (0..10), allow_blank: true

      def attributes
        {
          "LocalityID" => nil,
          "LocalityName" => nil,
          "LocalCode" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiOpportunitiesModelOpportunityInsertRequest < Base
      attribute :opportunity_name, :string
      attribute :bid_due_date, :datetime
      attribute :renewal_date, :datetime
      attribute :lead_source_id, :integer
      attribute :sales_rep_id, :integer
      attribute :anticipated_close_date, :datetime
      attribute :probability, :float
      attribute :division_id, :integer
      attribute :start_date, :datetime
      attribute :sales_type_id, :integer
      attribute :operations_mgr_id, :integer
      attribute :end_date, :datetime
      attribute :invoice_type, :string
      attribute :master_opportunity_id, :integer
      attribute :branch_id, :integer
      attribute :template_opportunity_id, :integer
      attribute :property_id, :integer
      attribute :budgeted_dollars, :float
      attribute :estimated_dollars, :float
      attribute :opportunity_status_id, :integer
      attribute :opportunity_type, :string
      attribute :opportunity_tags, :string
      attribute :customer_contract_num, :string
      attribute :customer_po_num, :string

      validates :opportunity_name, presence: true, length: (1..)
      validates :lead_source_id, numericality: true, allow_nil: true
      validates :sales_rep_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :probability, presence: true, numericality: true
      validates :division_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :sales_type_id, numericality: true, allow_nil: true
      validates :operations_mgr_id, numericality: true, allow_nil: true
      validates :master_opportunity_id, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :template_opportunity_id, numericality: true, allow_nil: true
      validates :property_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :budgeted_dollars, presence: true, numericality: true
      validates :estimated_dollars, presence: true, numericality: true
      validates :opportunity_status_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :opportunity_type, presence: true, length: (1..)
      validates :customer_contract_num, length: (0..40), allow_blank: true
      validates :customer_po_num, length: (0..20), allow_blank: true

      def attributes
        {
          "OpportunityName" => nil,
          "BidDueDate" => nil,
          "RenewalDate" => nil,
          "LeadSourceID" => nil,
          "SalesRepID" => nil,
          "AnticipatedCloseDate" => nil,
          "Probability" => nil,
          "DivisionID" => nil,
          "StartDate" => nil,
          "SalesTypeID" => nil,
          "OperationsMgrID" => nil,
          "EndDate" => nil,
          "InvoiceType" => nil,
          "MasterOpportunityID" => nil,
          "BranchID" => nil,
          "TemplateOpportunityID" => nil,
          "PropertyID" => nil,
          "BudgetedDollars" => nil,
          "EstimatedDollars" => nil,
          "OpportunityStatusID" => nil,
          "OpportunityType" => nil,
          "OpportunityTags" => nil,
          "CustomerContractNum" => nil,
          "CustomerPONum" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunityServiceGroupsODataModelOpportunityServiceGroup < Base
      attribute :opportunity_service_group_id, :integer
      attribute :opportunity_id, :integer
      attribute :group_name, :string
      attribute :sort_order, :integer
      attribute :display_group_only, :boolean
      attribute :optional_service_group, :boolean
      attribute :extended_price, :float
      attribute :overhead, :float
      attribute :break_even, :float
      attribute :labor_extended_cost, :float
      attribute :material_extended_cost, :float
      attribute :equipment_extended_cost, :float
      attribute :sub_extended_cost, :float
      attribute :other_extended_cost, :float
      attribute :extended_cost, :float
      attribute :service_item_quantity, :float
      attribute :extended_hours, :float
      attribute :labor_overhead, :float
      attribute :material_overhead, :float
      attribute :equipment_overhead, :float
      attribute :sub_overhead, :float
      attribute :other_overhead, :float
      attribute :labor_profit, :float
      attribute :material_profit, :float
      attribute :equipment_profit, :float
      attribute :sub_profit, :float
      attribute :other_profit, :float
      attribute :per_price, :float
      attribute :group_description, :string
      attribute :general_conditions_labor_extended_cost, :float
      attribute :general_conditions_material_extended_cost, :float
      attribute :general_conditions_equipment_extended_cost, :float
      attribute :general_conditions_sub_extended_cost, :float
      attribute :general_conditions_other_extended_cost, :float
      attribute :general_conditions_total_extended_cost, :float
      attribute :general_conditions_labor_overhead, :float
      attribute :general_conditions_material_overhead, :float
      attribute :general_conditions_equipment_overhead, :float
      attribute :general_conditions_sub_overhead, :float
      attribute :general_conditions_other_overhead, :float
      attribute :general_conditions_total_overhead, :float
      attribute :general_conditions_labor_extended_price, :float
      attribute :general_conditions_material_extended_price, :float
      attribute :general_conditions_equipment_extended_price, :float
      attribute :general_conditions_sub_extended_price, :float
      attribute :general_conditions_other_extended_price, :float
      attribute :general_conditions_total_extended_price, :float
      attribute :tm_per_service_extended_price, :float
      attribute :taxable_extended_price, :float

      validates :opportunity_service_group_id, presence: true, numericality: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :sort_order, numericality: true, allow_nil: true
      validates :extended_price, numericality: true, allow_nil: true
      validates :overhead, numericality: true, allow_nil: true
      validates :break_even, numericality: true, allow_nil: true
      validates :labor_extended_cost, numericality: true, allow_nil: true
      validates :material_extended_cost, numericality: true, allow_nil: true
      validates :equipment_extended_cost, numericality: true, allow_nil: true
      validates :sub_extended_cost, numericality: true, allow_nil: true
      validates :other_extended_cost, numericality: true, allow_nil: true
      validates :extended_cost, numericality: true, allow_nil: true
      validates :service_item_quantity, numericality: true, allow_nil: true
      validates :extended_hours, numericality: true, allow_nil: true
      validates :labor_overhead, numericality: true, allow_nil: true
      validates :material_overhead, numericality: true, allow_nil: true
      validates :equipment_overhead, numericality: true, allow_nil: true
      validates :sub_overhead, numericality: true, allow_nil: true
      validates :other_overhead, numericality: true, allow_nil: true
      validates :labor_profit, numericality: true, allow_nil: true
      validates :material_profit, numericality: true, allow_nil: true
      validates :equipment_profit, numericality: true, allow_nil: true
      validates :sub_profit, numericality: true, allow_nil: true
      validates :other_profit, numericality: true, allow_nil: true
      validates :per_price, numericality: true, allow_nil: true
      validates :general_conditions_labor_extended_cost, presence: true, numericality: true
      validates :general_conditions_material_extended_cost, presence: true, numericality: true
      validates :general_conditions_equipment_extended_cost, presence: true, numericality: true
      validates :general_conditions_sub_extended_cost, presence: true, numericality: true
      validates :general_conditions_other_extended_cost, presence: true, numericality: true
      validates :general_conditions_total_extended_cost, presence: true, numericality: true
      validates :general_conditions_labor_overhead, presence: true, numericality: true
      validates :general_conditions_material_overhead, presence: true, numericality: true
      validates :general_conditions_equipment_overhead, presence: true, numericality: true
      validates :general_conditions_sub_overhead, presence: true, numericality: true
      validates :general_conditions_other_overhead, presence: true, numericality: true
      validates :general_conditions_total_overhead, presence: true, numericality: true
      validates :general_conditions_labor_extended_price, presence: true, numericality: true
      validates :general_conditions_material_extended_price, presence: true, numericality: true
      validates :general_conditions_equipment_extended_price, presence: true, numericality: true
      validates :general_conditions_sub_extended_price, presence: true, numericality: true
      validates :general_conditions_other_extended_price, presence: true, numericality: true
      validates :general_conditions_total_extended_price, presence: true, numericality: true
      validates :tm_per_service_extended_price, presence: true, numericality: true
      validates :taxable_extended_price, presence: true, numericality: true

      def attributes
        {
          "OpportunityServiceGroupID" => nil,
          "OpportunityID" => nil,
          "GroupName" => nil,
          "SortOrder" => nil,
          "DisplayGroupOnly" => nil,
          "OptionalServiceGroup" => nil,
          "ExtendedPrice" => nil,
          "Overhead" => nil,
          "BreakEven" => nil,
          "LaborExtendedCost" => nil,
          "MaterialExtendedCost" => nil,
          "EquipmentExtendedCost" => nil,
          "SubExtendedCost" => nil,
          "OtherExtendedCost" => nil,
          "ExtendedCost" => nil,
          "ServiceItemQuantity" => nil,
          "ExtendedHours" => nil,
          "LaborOverhead" => nil,
          "MaterialOverhead" => nil,
          "EquipmentOverhead" => nil,
          "SubOverhead" => nil,
          "OtherOverhead" => nil,
          "LaborProfit" => nil,
          "MaterialProfit" => nil,
          "EquipmentProfit" => nil,
          "SubProfit" => nil,
          "OtherProfit" => nil,
          "PerPrice" => nil,
          "GroupDescription" => nil,
          "GeneralConditionsLaborExtendedCost" => nil,
          "GeneralConditionsMaterialExtendedCost" => nil,
          "GeneralConditionsEquipmentExtendedCost" => nil,
          "GeneralConditionsSubExtendedCost" => nil,
          "GeneralConditionsOtherExtendedCost" => nil,
          "GeneralConditionsTotalExtendedCost" => nil,
          "GeneralConditionsLaborOverhead" => nil,
          "GeneralConditionsMaterialOverhead" => nil,
          "GeneralConditionsEquipmentOverhead" => nil,
          "GeneralConditionsSubOverhead" => nil,
          "GeneralConditionsOtherOverhead" => nil,
          "GeneralConditionsTotalOverhead" => nil,
          "GeneralConditionsLaborExtendedPrice" => nil,
          "GeneralConditionsMaterialExtendedPrice" => nil,
          "GeneralConditionsEquipmentExtendedPrice" => nil,
          "GeneralConditionsSubExtendedPrice" => nil,
          "GeneralConditionsOtherExtendedPrice" => nil,
          "GeneralConditionsTotalExtendedPrice" => nil,
          "TMPerServiceExtendedPrice" => nil,
          "TaxableExtendedPrice" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelOpportunityServiceItem < Base
      attribute :opportunity_service_item_id, :integer
      attribute :opportunity_service_id, :integer
      attribute :catalog_item_id, :integer
      attribute :item_name, :string
      attribute :item_description, :string
      attribute :item_quantity, :float
      attribute :item_cost, :float
      attribute :item_cost_orig, :float
      attribute :occur, :integer
      attribute :per_price, :float
      attribute :per_price_override, :float
      attribute :extended_price, :float
      attribute :sort_order, :integer
      attribute :item_type, :string
      attribute :allocation_unit_type_id, :integer
      attribute :allocation_unit_type_name, :string
      attribute :catalog_item_category_id, :integer
      attribute :catalog_item_category_name, :string
      attribute :per_hours, :float
      attribute :extended_hours, :float
      attribute :per_hours_orig, :float
      attribute :overhead, :float
      attribute :break_even, :float
      attribute :extended_cost, :float
      attribute :extended_hours_orig, :float
      attribute :override_price, :boolean
      attribute :unit_price_override, :float
      attribute :override_unit_price, :boolean
      attribute :force_unit_pricing, :boolean
      attribute :estimating_notes, :string
      attribute :labor_extended_cost, :float
      attribute :material_extended_cost, :float
      attribute :equipment_extended_cost, :float
      attribute :sub_extended_cost, :float
      attribute :other_extended_cost, :float
      attribute :labor_overhead, :float
      attribute :material_overhead, :float
      attribute :equipment_overhead, :float
      attribute :sub_overhead, :float
      attribute :other_overhead, :float
      attribute :labor_profit, :float
      attribute :material_profit, :float
      attribute :equipment_profit, :float
      attribute :sub_profit, :float
      attribute :other_profit, :float

      validates :opportunity_service_item_id, presence: true, numericality: true
      validates :opportunity_service_id, numericality: true, allow_nil: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :item_quantity, numericality: true, allow_nil: true
      validates :item_cost, numericality: true, allow_nil: true
      validates :item_cost_orig, numericality: true, allow_nil: true
      validates :occur, numericality: true, allow_nil: true
      validates :per_price, numericality: true, allow_nil: true
      validates :per_price_override, numericality: true, allow_nil: true
      validates :extended_price, numericality: true, allow_nil: true
      validates :sort_order, numericality: true, allow_nil: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :catalog_item_category_id, numericality: true, allow_nil: true
      validates :per_hours, numericality: true, allow_nil: true
      validates :extended_hours, numericality: true, allow_nil: true
      validates :per_hours_orig, numericality: true, allow_nil: true
      validates :overhead, numericality: true, allow_nil: true
      validates :break_even, numericality: true, allow_nil: true
      validates :extended_cost, numericality: true, allow_nil: true
      validates :extended_hours_orig, numericality: true, allow_nil: true
      validates :unit_price_override, numericality: true, allow_nil: true
      validates :labor_extended_cost, numericality: true, allow_nil: true
      validates :material_extended_cost, numericality: true, allow_nil: true
      validates :equipment_extended_cost, numericality: true, allow_nil: true
      validates :sub_extended_cost, numericality: true, allow_nil: true
      validates :other_extended_cost, numericality: true, allow_nil: true
      validates :labor_overhead, numericality: true, allow_nil: true
      validates :material_overhead, numericality: true, allow_nil: true
      validates :equipment_overhead, numericality: true, allow_nil: true
      validates :sub_overhead, numericality: true, allow_nil: true
      validates :other_overhead, numericality: true, allow_nil: true
      validates :labor_profit, numericality: true, allow_nil: true
      validates :material_profit, numericality: true, allow_nil: true
      validates :equipment_profit, numericality: true, allow_nil: true
      validates :sub_profit, numericality: true, allow_nil: true
      validates :other_profit, numericality: true, allow_nil: true

      def attributes
        {
          "OpportunityServiceItemID" => nil,
          "OpportunityServiceID" => nil,
          "CatalogItemID" => nil,
          "ItemName" => nil,
          "ItemDescription" => nil,
          "ItemQuantity" => nil,
          "ItemCost" => nil,
          "ItemCostOrig" => nil,
          "Occur" => nil,
          "PerPrice" => nil,
          "PerPriceOverride" => nil,
          "ExtendedPrice" => nil,
          "SortOrder" => nil,
          "ItemType" => nil,
          "AllocationUnitTypeID" => nil,
          "AllocationUnitTypeName" => nil,
          "CatalogItemCategoryID" => nil,
          "CatalogItemCategoryName" => nil,
          "PerHours" => nil,
          "ExtendedHours" => nil,
          "PerHoursOrig" => nil,
          "Overhead" => nil,
          "BreakEven" => nil,
          "ExtendedCost" => nil,
          "ExtendedHoursOrig" => nil,
          "OverridePrice" => nil,
          "UnitPriceOverride" => nil,
          "OverrideUnitPrice" => nil,
          "ForceUnitPricing" => nil,
          "EstimatingNotes" => nil,
          "LaborExtendedCost" => nil,
          "MaterialExtendedCost" => nil,
          "EquipmentExtendedCost" => nil,
          "SubExtendedCost" => nil,
          "OtherExtendedCost" => nil,
          "LaborOverhead" => nil,
          "MaterialOverhead" => nil,
          "EquipmentOverhead" => nil,
          "SubOverhead" => nil,
          "OtherOverhead" => nil,
          "LaborProfit" => nil,
          "MaterialProfit" => nil,
          "EquipmentProfit" => nil,
          "SubProfit" => nil,
          "OtherProfit" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelOpportunityServiceKitItem < Base
      attribute :opportunity_service_kit_item_id, :integer
      attribute :opportunity_service_item_id, :integer
      attribute :opportunity_service_id, :integer
      attribute :catalog_item_id, :integer
      attribute :opportunity_service_group_id, :integer
      attribute :opportunity_id, :integer
      attribute :item_name, :string
      attribute :item_quantity, :float
      attribute :item_cost, :float
      attribute :item_factor, :float
      attribute :item_quantity_extended, :float
      attribute :waste_percent, :float
      attribute :item_cost_orig, :float
      attribute :item_factor_orig, :float
      attribute :waste_percent_orig, :float
      attribute :item_cost_extended, :float
      attribute :invert_factor, :boolean
      attribute :invert_factor_orig, :boolean
      attribute :available_to_crew, :boolean
      attribute :allocation_unit_type_id, :integer
      attribute :allocation_unit_type_name, :string
      attribute :take_off_item_id, :integer
      attribute :take_off_item_name, :string
      attribute :item_type, :string
      attribute :per_price, :float
      attribute :catalog_item_category_id, :integer
      attribute :catalog_item_category_name, :string
      attribute :per_hours, :float
      attribute :extended_hours, :float
      attribute :per_hours_orig, :float
      attribute :overhead, :float
      attribute :break_even, :float
      attribute :unit_price, :float
      attribute :item_quantity_unit, :float
      attribute :item_quantity_exact, :float

      validates :opportunity_service_kit_item_id, presence: true, numericality: true
      validates :opportunity_service_item_id, numericality: true, allow_nil: true
      validates :opportunity_service_id, numericality: true, allow_nil: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :opportunity_service_group_id, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :item_quantity, numericality: true, allow_nil: true
      validates :item_cost, numericality: true, allow_nil: true
      validates :item_factor, numericality: true, allow_nil: true
      validates :item_quantity_extended, numericality: true, allow_nil: true
      validates :waste_percent, numericality: true, allow_nil: true
      validates :item_cost_orig, numericality: true, allow_nil: true
      validates :item_factor_orig, numericality: true, allow_nil: true
      validates :waste_percent_orig, numericality: true, allow_nil: true
      validates :item_cost_extended, numericality: true, allow_nil: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :take_off_item_id, numericality: true, allow_nil: true
      validates :per_price, numericality: true, allow_nil: true
      validates :catalog_item_category_id, numericality: true, allow_nil: true
      validates :per_hours, numericality: true, allow_nil: true
      validates :extended_hours, numericality: true, allow_nil: true
      validates :per_hours_orig, numericality: true, allow_nil: true
      validates :overhead, numericality: true, allow_nil: true
      validates :break_even, numericality: true, allow_nil: true
      validates :unit_price, numericality: true, allow_nil: true
      validates :item_quantity_unit, numericality: true, allow_nil: true
      validates :item_quantity_exact, numericality: true, allow_nil: true

      def attributes
        {
          "OpportunityServiceKitItemID" => nil,
          "OpportunityServiceItemID" => nil,
          "OpportunityServiceID" => nil,
          "CatalogItemID" => nil,
          "OpportunityServiceGroupID" => nil,
          "OpportunityID" => nil,
          "ItemName" => nil,
          "ItemQuantity" => nil,
          "ItemCost" => nil,
          "ItemFactor" => nil,
          "ItemQuantityExtended" => nil,
          "WastePercent" => nil,
          "ItemCostOrig" => nil,
          "ItemFactorOrig" => nil,
          "WastePercentOrig" => nil,
          "ItemCostExtended" => nil,
          "InvertFactor" => nil,
          "InvertFactorOrig" => nil,
          "AvailableToCrew" => nil,
          "AllocationUnitTypeID" => nil,
          "AllocationUnitTypeName" => nil,
          "TakeOffItemID" => nil,
          "TakeOffItemName" => nil,
          "ItemType" => nil,
          "PerPrice" => nil,
          "CatalogItemCategoryID" => nil,
          "CatalogItemCategoryName" => nil,
          "PerHours" => nil,
          "ExtendedHours" => nil,
          "PerHoursOrig" => nil,
          "Overhead" => nil,
          "BreakEven" => nil,
          "UnitPrice" => nil,
          "ItemQuantityUnit" => nil,
          "ItemQuantityExact" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelOpportunityTag < Base
      attribute :opportunity_tag_id, :integer
      attribute :opportunity_id, :integer
      attribute :opportunity_name, :string
      attribute :tag_id, :integer
      attribute :tag_name, :string

      validates :opportunity_tag_id, presence: true, numericality: true
      validates :opportunity_id, presence: true, numericality: true
      validates :opportunity_name, length: (0..200), allow_blank: true
      validates :tag_id, presence: true, numericality: true
      validates :tag_name, length: (0..50), allow_blank: true

      def attributes
        {
          "OpportunityTagID" => nil,
          "OpportunityID" => nil,
          "OpportunityName" => nil,
          "TagID" => nil,
          "TagName" => nil
        }
      end
    end

    class AspireCloudExternalApiOpportunityTagsModelRequestOpportunityTagInsertRequest < Base
      attribute :opportunity_id, :integer
      attribute :tag_id, :integer

      validates :opportunity_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :tag_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "OpportunityID" => nil,
          "TagID" => nil
        }
      end
    end

    class AspireCloudExternalApiPartialPaymentsModelPartialPaymentInsertRequest < Base
      attribute :amount, :float
      attribute :billing_company, :string
      attribute :billing_contact, :string
      attribute :branch_name, :string
      attribute :invoice_number, :integer
      attribute :payment_date, :datetime
      attribute :payment_method, :string
      attribute :payment_note, :string
      attribute :payment_reference, :string

      validates :amount, presence: true, numericality: { greater_than_or_equal_to: 0.01 }
      validates :billing_company, length: (0..100), allow_blank: true
      validates :billing_contact, length: (0..250), allow_blank: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :invoice_number, numericality: true, allow_nil: true
      validates :payment_date, presence: true
      validates :payment_method, presence: true, length: (1..20)
      validates :payment_note, length: (0..**********), allow_blank: true
      validates :payment_reference, presence: true, length: (1..50)

      def attributes
        {
          "Amount" => nil,
          "BillingCompany" => nil,
          "BillingContact" => nil,
          "BranchName" => nil,
          "InvoiceNumber" => nil,
          "PaymentDate" => nil,
          "PaymentMethod" => nil,
          "PaymentNote" => nil,
          "PaymentReference" => nil
        }
      end
    end

    class AspireCloudDomainTenantPayCodesODataModelPayCodeModel < Base
      attribute :pay_code_id, :integer
      attribute :pay_code_name, :string
      attribute :premium_dollars, :float
      attribute :premium_percent, :float
      attribute :fixed_rate, :float
      attribute :exclude_from_ot, :boolean
      attribute :pay_code, :string
      attribute :ot_paycode, :boolean
      attribute :active, :boolean
      attribute :pay_code_type, :string

      validates :pay_code_id, presence: true, numericality: true
      validates :pay_code_name, length: (0..60), allow_blank: true
      validates :premium_dollars, numericality: true, allow_nil: true
      validates :premium_percent, numericality: true, allow_nil: true
      validates :fixed_rate, numericality: true, allow_nil: true
      validates :pay_code, length: (0..10), allow_blank: true

      def attributes
        {
          "PayCodeID" => nil,
          "PayCodeName" => nil,
          "PremiumDollars" => nil,
          "PremiumPercent" => nil,
          "FixedRate" => nil,
          "ExcludeFromOT" => nil,
          "PayCode" => nil,
          "OTPaycode" => nil,
          "Active" => nil,
          "PayCodeType" => nil
        }
      end
    end

    class AspireCloudExternalApiPayCodesModelRequestPayCodeInsertRequest < Base
      attribute :pay_code_type, :string
      attribute :pay_code_name, :string
      attribute :premium_dollars, :float
      attribute :premium_percent, :float
      attribute :fixed_rate, :float
      attribute :exclude_from_ot, :boolean
      attribute :pay_code, :string
      attribute :ot_paycode, :boolean
      attribute :active, :boolean

      validates :pay_code_name, presence: true, length: (0..60)
      validates :premium_dollars, numericality: true, allow_nil: true
      validates :premium_percent, numericality: true, allow_nil: true
      validates :fixed_rate, numericality: true, allow_nil: true
      validates :pay_code, length: (0..10), allow_blank: true

      def attributes
        {
          "PayCodeType" => nil,
          "PayCodeName" => nil,
          "PremiumDollars" => nil,
          "PremiumPercent" => nil,
          "FixedRate" => nil,
          "ExcludeFromOT" => nil,
          "PayCode" => nil,
          "OTPaycode" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiPayCodesModelResponsePayCodeResponse < Base
      attribute :pay_code_id, :integer

      validates :pay_code_id, presence: true, numericality: true

      def attributes
        {
          "PayCodeID" => nil
        }
      end
    end

    class AspireCloudExternalApiPayCodesModelRequestPayCodeUpdateRequest < Base
      attribute :pay_code_id, :integer
      attribute :pay_code_name, :string
      attribute :premium_dollars, :float
      attribute :premium_percent, :float
      attribute :fixed_rate, :float
      attribute :exclude_from_ot, :boolean
      attribute :pay_code, :string
      attribute :ot_paycode, :boolean
      attribute :active, :boolean

      validates :pay_code_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :pay_code_name, presence: true, length: (0..60)
      validates :premium_dollars, numericality: true, allow_nil: true
      validates :premium_percent, numericality: true, allow_nil: true
      validates :fixed_rate, numericality: true, allow_nil: true
      validates :pay_code, length: (0..10), allow_blank: true

      def attributes
        {
          "PayCodeID" => nil,
          "PayCodeName" => nil,
          "PremiumDollars" => nil,
          "PremiumPercent" => nil,
          "FixedRate" => nil,
          "ExcludeFromOT" => nil,
          "PayCode" => nil,
          "OTPaycode" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantPaymentCategoriesODataModelPaymentCategory < Base
      attribute :payment_category_id, :integer
      attribute :payment_category_name, :string
      attribute :active, :boolean
      attribute :sort_order, :integer
      attribute :account_number, :string

      validates :payment_category_id, presence: true, numericality: true
      validates :payment_category_name, length: (0..50), allow_blank: true
      validates :sort_order, numericality: true, allow_nil: true
      validates :account_number, length: (0..50), allow_blank: true

      def attributes
        {
          "PaymentCategoryID" => nil,
          "PaymentCategoryName" => nil,
          "Active" => nil,
          "SortOrder" => nil,
          "AccountNumber" => nil
        }
      end
    end

    class AspireCloudDomainTenantPaymentTermsNSODataModelPaymentTerms < Base
      attribute :payment_terms_id, :integer
      attribute :terms, :string
      attribute :number_of_days, :integer
      attribute :active, :boolean

      validates :payment_terms_id, presence: true, numericality: true
      validates :number_of_days, numericality: true, allow_nil: true

      def attributes
        {
          "PaymentTermsID" => nil,
          "Terms" => nil,
          "NumberOfDays" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantPayCodesODataModelPayRateOverridePayCode < Base
      attribute :pay_rate_override_pay_code_id, :integer
      attribute :pay_rate_id, :integer
      attribute :pay_code_id, :integer
      attribute :override_rate, :float
      attribute :contact_id, :integer

      validates :pay_rate_override_pay_code_id, presence: true, numericality: true
      validates :pay_rate_id, presence: true, numericality: true
      validates :pay_code_id, presence: true, numericality: true
      validates :override_rate, presence: true, numericality: true
      validates :contact_id, presence: true, numericality: true

      def attributes
        {
          "PayRateOverridePayCodeID" => nil,
          "PayRateID" => nil,
          "PayCodeID" => nil,
          "OverrideRate" => nil,
          "ContactID" => nil
        }
      end
    end

    class AspireCloudExternalApiPayRateOverridePayCodesModelPayRateOverridePayCodeRequest < Base
      attribute :pay_rate_id, :integer
      attribute :pay_code_id, :integer
      attribute :override_rate, :float

      validates :pay_rate_id, presence: true, numericality: true
      validates :pay_code_id, presence: true, numericality: true
      validates :override_rate, presence: true, numericality: true

      def attributes
        {
          "PayRateID" => nil,
          "PayCodeID" => nil,
          "OverrideRate" => nil
        }
      end
    end

    class AspireCloudDomainTenantPayRatesODataModelPayRate < Base
      attribute :pay_rate_id, :integer
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :effective_date, :datetime
      attribute :hourly_base_pay, :float
      attribute :burden_percent, :float

      validates :pay_rate_id, presence: true, numericality: true
      validates :contact_id, presence: true, numericality: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :hourly_base_pay, numericality: true, allow_nil: true
      validates :burden_percent, numericality: true, allow_nil: true

      def attributes
        {
          "PayRateID" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "EffectiveDate" => nil,
          "HourlyBasePay" => nil,
          "BurdenPercent" => nil
        }
      end
    end

    class AspireCloudExternalApiPayRatesModelRequestPayRateInsertRequest < Base
      attribute :contact_id, :integer
      attribute :effective_date, :datetime
      attribute :hourly_base_pay, :float
      attribute :burden_percent, :float

      validates :contact_id, presence: true, numericality: true
      validates :effective_date, presence: true
      validates :hourly_base_pay, numericality: { greater_than_or_equal_to: 0.0 }, allow_nil: true
      validates :burden_percent, presence: true, numericality: { less_than_or_equal_to: 100.0, greater_than_or_equal_to: 0.0 }

      def attributes
        {
          "ContactID" => nil,
          "EffectiveDate" => nil,
          "HourlyBasePay" => nil,
          "BurdenPercent" => nil
        }
      end
    end

    class AspireCloudExternalApiPayRatesModelRequestPayRateUpdateRequest < Base
      attribute :pay_rate_id, :integer
      attribute :contact_id, :integer
      attribute :effective_date, :datetime
      attribute :hourly_base_pay, :float
      attribute :burden_percent, :float

      validates :pay_rate_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :contact_id, presence: true, numericality: true
      validates :effective_date, presence: true
      validates :hourly_base_pay, numericality: { greater_than_or_equal_to: 0.0 }, allow_nil: true
      validates :burden_percent, presence: true, numericality: { less_than_or_equal_to: 100.0, greater_than_or_equal_to: 0.0 }

      def attributes
        {
          "PayRateID" => nil,
          "ContactID" => nil,
          "EffectiveDate" => nil,
          "HourlyBasePay" => nil,
          "BurdenPercent" => nil
        }
      end
    end

    class AspireCloudDomainTenantPaySchedulesODataModelPaySchedule < Base
      attribute :pay_schedule_id, :integer
      attribute :pay_schedule_name, :string
      attribute :daily_hours_before_ot, :float
      attribute :weekly_hours_before_ot, :float
      attribute :active, :boolean
      attribute :default_pay_code_id, :integer
      attribute :default_pay_code_name, :string
      attribute :default_ot_pay_code_id, :integer
      attribute :default_ot_pay_code_name, :string

      validates :pay_schedule_id, presence: true, numericality: true
      validates :pay_schedule_name, length: (0..50), allow_blank: true
      validates :daily_hours_before_ot, numericality: true, allow_nil: true
      validates :weekly_hours_before_ot, numericality: true, allow_nil: true
      validates :default_pay_code_id, numericality: true, allow_nil: true
      validates :default_pay_code_name, length: (0..60), allow_blank: true
      validates :default_ot_pay_code_id, numericality: true, allow_nil: true
      validates :default_ot_pay_code_name, length: (0..60), allow_blank: true

      def attributes
        {
          "PayScheduleID" => nil,
          "PayScheduleName" => nil,
          "DailyHoursBeforeOT" => nil,
          "WeeklyHoursBeforeOT" => nil,
          "Active" => nil,
          "DefaultPayCodeID" => nil,
          "DefaultPayCodeName" => nil,
          "DefaultOTPayCodeID" => nil,
          "DefaultOTPayCodeName" => nil
        }
      end
    end

    class AspireCloudExternalApiPaySchedulesModelRequestPayScheduleInsert < Base
      attribute :pay_schedule_name, :string
      attribute :daily_hours_before_ot, :float
      attribute :weekly_hours_before_ot, :float
      attribute :active, :boolean
      attribute :default_ot_pay_code_id, :integer
      attribute :default_pay_code_id, :integer

      validates :pay_schedule_name, presence: true, length: (1..)
      validates :daily_hours_before_ot, presence: true, numericality: { less_than_or_equal_to: 24.0, greater_than_or_equal_to: 0.0 }
      validates :weekly_hours_before_ot, presence: true, numericality: { less_than_or_equal_to: 168.0, greater_than_or_equal_to: 0.0 }
      validates :default_ot_pay_code_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :default_pay_code_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "PayScheduleName" => nil,
          "DailyHoursBeforeOT" => nil,
          "WeeklyHoursBeforeOT" => nil,
          "Active" => nil,
          "DefaultOTPayCodeID" => nil,
          "DefaultPayCodeID" => nil
        }
      end
    end

    class AspireCloudExternalApiPaySchedulesModelResponsePayScheduleResponse < Base
      attribute :pay_schedule_id, :integer

      validates :pay_schedule_id, presence: true, numericality: true

      def attributes
        {
          "PayScheduleID" => nil
        }
      end
    end

    class AspireCloudExternalApiPaySchedulesModelRequestPayScheduleUpdate < Base
      attribute :pay_schedule_id, :integer
      attribute :pay_schedule_name, :string
      attribute :daily_hours_before_ot, :float
      attribute :weekly_hours_before_ot, :float
      attribute :active, :boolean
      attribute :default_ot_pay_code_id, :integer
      attribute :default_pay_code_id, :integer

      validates :pay_schedule_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :pay_schedule_name, presence: true, length: (1..)
      validates :daily_hours_before_ot, presence: true, numericality: { less_than_or_equal_to: 24.0, greater_than_or_equal_to: 0.0 }
      validates :weekly_hours_before_ot, presence: true, numericality: { less_than_or_equal_to: 168.0, greater_than_or_equal_to: 0.0 }
      validates :default_ot_pay_code_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :default_pay_code_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "PayScheduleID" => nil,
          "PayScheduleName" => nil,
          "DailyHoursBeforeOT" => nil,
          "WeeklyHoursBeforeOT" => nil,
          "Active" => nil,
          "DefaultOTPayCodeID" => nil,
          "DefaultPayCodeID" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyModelsPropertyRequestModel < Base
      attribute :property_name, :string
      attribute :property_name_abr, :string
      attribute :property_status_id, :integer
      attribute :property_tags, :string
      attribute :branch_id, :integer
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string
      attribute :county_id, :integer
      attribute :tax_jurisdiction_id, :integer
      attribute :geo_perimeter, :float
      attribute :sequence_number, :string
      attribute :budget, :float
      attribute :account_owner_contact_id, :integer
      attribute :primary_contact_id, :integer
      attribute :billing_contact_id, :integer
      attribute :production_manager_contact_id, :integer
      attribute :active, :boolean
      attribute :separate_invoices, :boolean
      attribute :email_invoice_flag, :boolean
      attribute :update_branch_reference, :boolean
      attribute :payment_terms_id, :integer
      attribute :industry_id, :integer
      attribute :lead_source_id, :integer
      attribute :competitor_id, :integer
      attribute :property_group_id, :integer
      attribute :note, :string
      attribute :production_note, :string
      attribute :snow_note, :string
      attribute :collection_notes, :string
      attribute :integration_id, :string
      attribute :property_type_id, :integer

      validates :property_name, presence: true, length: (1..)
      validates :property_status_id, numericality: true, allow_nil: true
      validates :branch_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :county_id, numericality: true, allow_nil: true
      validates :tax_jurisdiction_id, numericality: true, allow_nil: true
      validates :geo_perimeter, presence: true, numericality: true
      validates :budget, presence: true, numericality: true
      validates :account_owner_contact_id, numericality: true, allow_nil: true
      validates :primary_contact_id, numericality: true, allow_nil: true
      validates :billing_contact_id, numericality: true, allow_nil: true
      validates :production_manager_contact_id, numericality: true, allow_nil: true
      validates :payment_terms_id, numericality: true, allow_nil: true
      validates :industry_id, numericality: true, allow_nil: true
      validates :lead_source_id, numericality: true, allow_nil: true
      validates :competitor_id, numericality: true, allow_nil: true
      validates :property_group_id, numericality: true, allow_nil: true
      validates :property_type_id, numericality: true, allow_nil: true

      def attributes
        {
          "PropertyName" => nil,
          "PropertyNameAbr" => nil,
          "PropertyStatusID" => nil,
          "PropertyTags" => nil,
          "BranchID" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil,
          "CountyID" => nil,
          "TaxJurisdictionID" => nil,
          "GEOPerimeter" => nil,
          "SequenceNumber" => nil,
          "Budget" => nil,
          "AccountOwnerContactID" => nil,
          "PrimaryContactID" => nil,
          "BillingContactID" => nil,
          "ProductionManagerContactID" => nil,
          "Active" => nil,
          "SeparateInvoices" => nil,
          "EmailInvoiceFlag" => nil,
          "UpdateBranchReference" => nil,
          "PaymentTermsID" => nil,
          "IndustryID" => nil,
          "LeadSourceID" => nil,
          "CompetitorID" => nil,
          "PropertyGroupID" => nil,
          "Note" => nil,
          "ProductionNote" => nil,
          "SnowNote" => nil,
          "CollectionNotes" => nil,
          "IntegrationID" => nil,
          "PropertyTypeID" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyModelsPropertyUpdateRequestModel < Base
      attribute :property_id, :integer
      attribute :property_name, :string
      attribute :property_name_abr, :string
      attribute :property_status_id, :integer
      attribute :property_tags, :string
      attribute :branch_id, :integer
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string
      attribute :county_id, :integer
      attribute :tax_jurisdiction_id, :integer
      attribute :geo_perimeter, :float
      attribute :sequence_number, :string
      attribute :budget, :float
      attribute :account_owner_contact_id, :integer
      attribute :primary_contact_id, :integer
      attribute :billing_contact_id, :integer
      attribute :production_manager_contact_id, :integer
      attribute :active, :boolean
      attribute :separate_invoices, :boolean
      attribute :email_invoice_flag, :boolean
      attribute :update_branch_reference, :boolean
      attribute :payment_terms_id, :integer
      attribute :industry_id, :integer
      attribute :lead_source_id, :integer
      attribute :competitor_id, :integer
      attribute :property_group_id, :integer
      attribute :note, :string
      attribute :production_note, :string
      attribute :snow_note, :string
      attribute :collection_notes, :string
      attribute :integration_id, :string
      attribute :property_type_id, :integer

      validates :property_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :property_name, presence: true, length: (1..)
      validates :property_status_id, numericality: true, allow_nil: true
      validates :branch_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :county_id, numericality: true, allow_nil: true
      validates :tax_jurisdiction_id, numericality: true, allow_nil: true
      validates :geo_perimeter, presence: true, numericality: true
      validates :budget, presence: true, numericality: true
      validates :account_owner_contact_id, numericality: true, allow_nil: true
      validates :primary_contact_id, numericality: true, allow_nil: true
      validates :billing_contact_id, numericality: true, allow_nil: true
      validates :production_manager_contact_id, numericality: true, allow_nil: true
      validates :payment_terms_id, numericality: true, allow_nil: true
      validates :industry_id, numericality: true, allow_nil: true
      validates :lead_source_id, numericality: true, allow_nil: true
      validates :competitor_id, numericality: true, allow_nil: true
      validates :property_group_id, numericality: true, allow_nil: true
      validates :property_type_id, numericality: true, allow_nil: true

      def attributes
        {
          "PropertyID" => nil,
          "PropertyName" => nil,
          "PropertyNameAbr" => nil,
          "PropertyStatusID" => nil,
          "PropertyTags" => nil,
          "BranchID" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil,
          "CountyID" => nil,
          "TaxJurisdictionID" => nil,
          "GEOPerimeter" => nil,
          "SequenceNumber" => nil,
          "Budget" => nil,
          "AccountOwnerContactID" => nil,
          "PrimaryContactID" => nil,
          "BillingContactID" => nil,
          "ProductionManagerContactID" => nil,
          "Active" => nil,
          "SeparateInvoices" => nil,
          "EmailInvoiceFlag" => nil,
          "UpdateBranchReference" => nil,
          "PaymentTermsID" => nil,
          "IndustryID" => nil,
          "LeadSourceID" => nil,
          "CompetitorID" => nil,
          "PropertyGroupID" => nil,
          "Note" => nil,
          "ProductionNote" => nil,
          "SnowNote" => nil,
          "CollectionNotes" => nil,
          "IntegrationID" => nil,
          "PropertyTypeID" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertyAvailabilitiesODataModelPropertyAvailability < Base
      attribute :property_id, :integer
      attribute :day_of_week, :integer
      attribute :start_time, :datetime
      attribute :end_time, :datetime

      validates :property_id, presence: true, numericality: true
      validates :day_of_week, presence: true, numericality: true
      validates :start_time, presence: true
      validates :end_time, presence: true

      def attributes
        {
          "PropertyId" => nil,
          "DayOfWeek" => nil,
          "StartTime" => nil,
          "EndTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertiesODataModelPropertyContactDataModel < Base
      attribute :property_name, :string
      attribute :property_id, :integer
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :primary_contact, :boolean
      attribute :billing_contact, :boolean
      attribute :email_invoice_contact, :boolean
      attribute :email_notifications_contact, :boolean
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :sms_notifications_contact, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :property_name, length: (0..100), allow_blank: true
      validates :property_id, presence: true, numericality: true
      validates :contact_id, presence: true, numericality: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :company_id, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "PropertyName" => nil,
          "PropertyID" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "PrimaryContact" => nil,
          "BillingContact" => nil,
          "EmailInvoiceContact" => nil,
          "EmailNotificationsContact" => nil,
          "CompanyID" => nil,
          "CompanyName" => nil,
          "SMSNotificationsContact" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyContactsModelsPropertyContactRequest < Base
      attribute :property_id, :integer
      attribute :contact_id, :integer
      attribute :primary_contact, :boolean
      attribute :billing_contact, :boolean
      attribute :email_invoice_contact, :boolean
      attribute :email_notifications_contact, :boolean
      attribute :sms_notifications_contact, :boolean

      validates :property_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :contact_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "PropertyID" => nil,
          "ContactID" => nil,
          "PrimaryContact" => nil,
          "BillingContact" => nil,
          "EmailInvoiceContact" => nil,
          "EmailNotificationsContact" => nil,
          "SMSNotificationsContact" => nil
        }
      end
    end

    class AspireCloudDomainTenantTableExtenderODataModelPropertyTableExtenderHeader < Base
      attribute :property_custom_field_definition_id, :integer
      attribute :column_name, :string
      attribute :column_type, :string
      attribute :custom_list # {"type" => "array", "items" => {"type" => "string"}, "nullable" => true}
      attribute :date_created, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :display_order, :integer

      validates :property_custom_field_definition_id, presence: true, numericality: true
      validates :column_name, length: (0..100), allow_blank: true
      validates :column_type, length: (0..100), allow_blank: true
      validates :date_created, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :display_order, presence: true, numericality: true

      def attributes
        {
          "PropertyCustomFieldDefinitionID" => nil,
          "ColumnName" => nil,
          "ColumnType" => nil,
          "CustomList" => nil,
          "DateCreated" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "DisplayOrder" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertyCustomFieldODataModelTableExtenderDetail < Base
      attribute :property_custom_field_value_id, :integer
      attribute :property_custom_field_definition_id, :integer
      attribute :property_id, :integer
      attribute :column_value, :string

      validates :property_custom_field_value_id, presence: true, numericality: true
      validates :property_custom_field_definition_id, presence: true, numericality: true
      validates :property_id, presence: true, numericality: true
      validates :column_value, length: (0..2000), allow_blank: true

      def attributes
        {
          "PropertyCustomFieldValueID" => nil,
          "PropertyCustomFieldDefinitionID" => nil,
          "PropertyID" => nil,
          "ColumnValue" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyCustomFieldsModelsPropertyCustomFieldInsertRequest < Base
      attribute :property_custom_field_definition_id, :integer
      attribute :property_id, :integer
      attribute :column_value, :string

      validates :property_custom_field_definition_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :property_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :column_value, length: (0..2000), allow_blank: true

      def attributes
        {
          "PropertyCustomFieldDefinitionID" => nil,
          "PropertyID" => nil,
          "ColumnValue" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyCustomFieldsModelsPropertyCustomFieldUpdateRequest < Base
      attribute :property_custom_field_value_id, :integer
      attribute :column_value, :string

      validates :property_custom_field_value_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :column_value, length: (0..2000), allow_blank: true

      def attributes
        {
          "PropertyCustomFieldValueID" => nil,
          "ColumnValue" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertyGroupsODataModelPropertyGroup < Base
      attribute :property_group_id, :integer
      attribute :property_group_name, :string
      attribute :active, :boolean

      validates :property_group_id, presence: true, numericality: true
      validates :property_group_name, length: (0..50), allow_blank: true

      def attributes
        {
          "PropertyGroupID" => nil,
          "PropertyGroupName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertyStatusesODataModelPropertyStatus < Base
      attribute :property_status_id, :integer
      attribute :property_status_name, :string
      attribute :active, :boolean

      validates :property_status_id, presence: true, numericality: true
      validates :property_status_name, length: (0..50), allow_blank: true

      def attributes
        {
          "PropertyStatusID" => nil,
          "PropertyStatusName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertyTypesODataModelPropertyType < Base
      attribute :property_type_id, :integer
      attribute :property_type_name, :string
      attribute :integration_code_name, :string
      attribute :active, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string

      validates :property_type_id, presence: true, numericality: true
      validates :property_type_name, length: (0..50), allow_blank: true
      validates :integration_code_name, length: (0..50), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "PropertyTypeID" => nil,
          "PropertyTypeName" => nil,
          "IntegrationCodeName" => nil,
          "Active" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil
        }
      end
    end

    class AspireCloudDomainTenantProspectRatingsODataModelProspectRating < Base
      attribute :prospect_rating_id, :integer
      attribute :prospect_rating_name, :string

      validates :prospect_rating_id, presence: true, numericality: true

      def attributes
        {
          "ProspectRatingID" => nil,
          "ProspectRatingName" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelResponseReceiptResponse < Base
      attribute :receipt_id, :integer

      validates :receipt_id, presence: true, numericality: true

      def attributes
        {
          "ReceiptID" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelRequestApproveReceiptRequest < Base
      attribute :receipt_id, :integer
      attribute :receive, :boolean
      attribute :vendor_invoice_num, :string
      attribute :vendor_invoice_date, :datetime

      validates :receipt_id, presence: true, numericality: true
      validates :vendor_invoice_num, length: (1..50), allow_blank: true

      def attributes
        {
          "ReceiptID" => nil,
          "Receive" => nil,
          "VendorInvoiceNum" => nil,
          "VendorInvoiceDate" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelRequestReceiveReceiptRequest < Base
      attribute :receipt_id, :integer

      validates :receipt_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "ReceiptID" => nil
        }
      end
    end

    class AspireCloudDomainTenantReceiptStatusesODataModelReceiptStatusModel < Base
      attribute :receipt_status_id, :integer
      attribute :receipt_status, :string
      attribute :receipt_status_name, :string

      validates :receipt_status_id, presence: true, numericality: true
      validates :receipt_status, length: (0..20), allow_blank: true
      validates :receipt_status_name, length: (0..50), allow_blank: true

      def attributes
        {
          "ReceiptStatusID" => nil,
          "ReceiptStatus" => nil,
          "ReceiptStatusName" => nil
        }
      end
    end

    class AspireCloudDomainTenantRegionsODataModelRegion < Base
      attribute :region_id, :integer
      attribute :region_name, :string
      attribute :legal_name, :string
      attribute :address_id, :integer
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :address_city, :string
      attribute :address_state_province_code, :string
      attribute :address_zip_code, :string
      attribute :manager_contact_id, :integer
      attribute :manager_name, :string
      attribute :billing_email_from_account_owner, :boolean
      attribute :billing_email_from_user_id, :integer
      attribute :billing_email_from_user_name, :string
      attribute :billing_email_address, :string
      attribute :billing_email_cc, :string
      attribute :billing_email_subject, :string
      attribute :billing_email_body, :string
      attribute :invoice_on_completion_description, :string
      attribute :region_phone_number, :string
      attribute :region_fax_number, :string
      attribute :district_id, :integer
      attribute :district_name, :string

      validates :region_id, presence: true, numericality: true
      validates :region_name, length: (0..250), allow_blank: true
      validates :legal_name, length: (0..250), allow_blank: true
      validates :address_id, numericality: true, allow_nil: true
      validates :address_line1, length: (0..75), allow_blank: true
      validates :address_line2, length: (0..75), allow_blank: true
      validates :address_city, length: (0..50), allow_blank: true
      validates :address_state_province_code, length: (0..3), allow_blank: true
      validates :address_zip_code, length: (0..15), allow_blank: true
      validates :manager_contact_id, numericality: true, allow_nil: true
      validates :manager_name, length: (0..101), allow_blank: true
      validates :billing_email_from_user_id, numericality: true, allow_nil: true
      validates :billing_email_from_user_name, length: (0..101), allow_blank: true
      validates :billing_email_address, length: (0..255), allow_blank: true
      validates :billing_email_cc, length: (0..**********), allow_blank: true
      validates :billing_email_subject, length: (0..255), allow_blank: true
      validates :billing_email_body, length: (0..**********), allow_blank: true
      validates :invoice_on_completion_description, length: (0..**********), allow_blank: true
      validates :region_phone_number, length: (0..25), allow_blank: true
      validates :region_fax_number, length: (0..25), allow_blank: true
      validates :district_id, numericality: true, allow_nil: true
      validates :district_name, length: (0..200), allow_blank: true

      def attributes
        {
          "RegionID" => nil,
          "RegionName" => nil,
          "LegalName" => nil,
          "AddressID" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "AddressCity" => nil,
          "AddressStateProvinceCode" => nil,
          "AddressZipCode" => nil,
          "ManagerContactID" => nil,
          "ManagerName" => nil,
          "BillingEmailFromAccountOwner" => nil,
          "BillingEmailFromUserID" => nil,
          "BillingEmailFromUserName" => nil,
          "BillingEmailAddress" => nil,
          "BillingEmailCC" => nil,
          "BillingEmailSubject" => nil,
          "BillingEmailBody" => nil,
          "InvoiceOnCompletionDescription" => nil,
          "RegionPhoneNumber" => nil,
          "RegionFaxNumber" => nil,
          "DistrictID" => nil,
          "DistrictName" => nil
        }
      end
    end

    class AspireCloudDomainTenantRevenueVariancesODataModelRevenueVariance < Base
      attribute :revenue_variance_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :adjustment_date, :datetime
      attribute :opportunity_id, :integer
      attribute :opportunity_number, :integer
      attribute :contract_year, :integer
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :adjustment, :float
      attribute :earned_revenue, :float
      attribute :invoice_revenue, :float
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :accounting_period_id, :integer

      validates :revenue_variance_id, presence: true, numericality: true
      validates :branch_id, presence: true, numericality: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :adjustment_date, presence: true
      validates :opportunity_id, presence: true, numericality: true
      validates :opportunity_number, numericality: true, allow_nil: true
      validates :contract_year, presence: true, numericality: true
      validates :division_id, presence: true, numericality: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :adjustment, presence: true, numericality: true
      validates :earned_revenue, presence: true, numericality: true
      validates :invoice_revenue, presence: true, numericality: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :accounting_period_id, numericality: true, allow_nil: true

      def attributes
        {
          "RevenueVarianceID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "AdjustmentDate" => nil,
          "OpportunityID" => nil,
          "OpportunityNumber" => nil,
          "ContractYear" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "Adjustment" => nil,
          "EarnedRevenue" => nil,
          "InvoiceRevenue" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "AccountingPeriodID" => nil
        }
      end
    end

    class AspireCloudDomainTenantRolesODataModelRole < Base
      attribute :role_id, :integer
      attribute :role_name, :string
      attribute :required, :boolean
      attribute :role_value, :integer

      validates :role_id, presence: true, numericality: true
      validates :role_name, length: (1..50), allow_blank: true
      validates :role_value, numericality: true, allow_nil: true

      def attributes
        {
          "RoleID" => nil,
          "RoleName" => nil,
          "Required" => nil,
          "RoleValue" => nil
        }
      end
    end

    class AspireCloudDomainTenantSalesTypesODataModelSalesType < Base
      attribute :sales_type_id, :integer
      attribute :sales_type_name, :string
      attribute :active, :boolean

      validates :sales_type_id, presence: true, numericality: true
      validates :sales_type_name, length: (0..30), allow_blank: true

      def attributes
        {
          "SalesTypeID" => nil,
          "SalesTypeName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiServiceTaxOverridesModelRequestServiceTaxOverrideInsertRequest < Base
      attribute :service_id, :integer
      attribute :state_province_code, :string
      attribute :labor_taxable, :boolean
      attribute :material_taxable, :boolean
      attribute :equipment_taxable, :boolean
      attribute :sub_taxable, :boolean
      attribute :other_taxable, :boolean

      validates :service_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :state_province_code, presence: true, length: (1..)

      def attributes
        {
          "ServiceID" => nil,
          "StateProvinceCode" => nil,
          "LaborTaxable" => nil,
          "MaterialTaxable" => nil,
          "EquipmentTaxable" => nil,
          "SubTaxable" => nil,
          "OtherTaxable" => nil
        }
      end
    end

    class AspireCloudExternalApiServiceTaxOverridesModelRequestServiceTaxOverrideUpdateRequest < Base
      attribute :service_tax_override_id, :integer
      attribute :service_id, :integer
      attribute :state_province_code, :string
      attribute :labor_taxable, :boolean
      attribute :material_taxable, :boolean
      attribute :equipment_taxable, :boolean
      attribute :sub_taxable, :boolean
      attribute :other_taxable, :boolean

      validates :service_tax_override_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :service_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :state_province_code, presence: true, length: (1..)

      def attributes
        {
          "ServiceTaxOverrideID" => nil,
          "ServiceID" => nil,
          "StateProvinceCode" => nil,
          "LaborTaxable" => nil,
          "MaterialTaxable" => nil,
          "EquipmentTaxable" => nil,
          "SubTaxable" => nil,
          "OtherTaxable" => nil
        }
      end
    end

    class AspireCloudDomainTenantServiceTypeIntegrationCodesODataModelServiceTypeIntegrationCode < Base
      attribute :service_type_integration_code_id, :integer
      attribute :service_type_integration_code_name, :string
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :active, :boolean

      validates :service_type_integration_code_id, presence: true, numericality: true
      validates :service_type_integration_code_name, length: (1..50), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "ServiceTypeIntegrationCodeID" => nil,
          "ServiceTypeIntegrationCodeName" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantServiceTypesODataModelServiceType < Base
      attribute :service_type_id, :integer
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :service_type_name, :string
      attribute :sort_order, :integer
      attribute :active, :boolean

      validates :service_type_id, presence: true, numericality: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :service_type_name, length: (0..100), allow_blank: true
      validates :sort_order, numericality: true, allow_nil: true

      def attributes
        {
          "ServiceTypeID" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "ServiceTypeName" => nil,
          "SortOrder" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantTagsODataModelTag < Base
      attribute :tag_id, :integer
      attribute :tag_name, :string
      attribute :tag_type, :string
      attribute :active, :boolean

      validates :tag_id, presence: true, numericality: true
      validates :tag_name, length: (0..50), allow_blank: true
      validates :tag_type, length: (0..1), allow_blank: true

      def attributes
        {
          "TagID" => nil,
          "TagName" => nil,
          "TagType" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantTakeOffODataModelTakeoffGroup < Base
      attribute :takeoff_group_id, :integer
      attribute :takeoff_group_name, :string
      attribute :sort_order, :integer
      attribute :active, :boolean

      validates :takeoff_group_id, presence: true, numericality: true
      validates :takeoff_group_name, length: (0..100), allow_blank: true
      validates :sort_order, numericality: true, allow_nil: true

      def attributes
        {
          "TakeoffGroupID" => nil,
          "TakeoffGroupName" => nil,
          "SortOrder" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantTakeOffODataModelTakeoffItem < Base
      attribute :takeoff_item_id, :integer
      attribute :takeoff_group_id, :integer
      attribute :takeoff_group_name, :string
      attribute :unit_type_id, :integer
      attribute :unit_type_name, :string
      attribute :takeoff_item_name, :string
      attribute :active, :boolean
      attribute :primary_takeoff, :boolean
      attribute :sort_order, :integer
      attribute :table_extender_header_id, :integer
      attribute :show_in_lists, :boolean

      validates :takeoff_item_id, presence: true, numericality: true
      validates :takeoff_group_id, numericality: true, allow_nil: true
      validates :takeoff_group_name, length: (0..100), allow_blank: true
      validates :unit_type_id, numericality: true, allow_nil: true
      validates :unit_type_name, length: (0..50), allow_blank: true
      validates :takeoff_item_name, length: (0..100), allow_blank: true
      validates :sort_order, numericality: true, allow_nil: true
      validates :table_extender_header_id, numericality: true, allow_nil: true

      def attributes
        {
          "TakeoffItemID" => nil,
          "TakeoffGroupID" => nil,
          "TakeoffGroupName" => nil,
          "UnitTypeID" => nil,
          "UnitTypeName" => nil,
          "TakeoffItemName" => nil,
          "Active" => nil,
          "PrimaryTakeoff" => nil,
          "SortOrder" => nil,
          "TableExtenderHeaderID" => nil,
          "ShowInLists" => nil
        }
      end
    end

    class AspireCloudExternalApiActivitiesModelsTaskRequest < Base
      attribute :assigned_to, :string
      attribute :due_date, :datetime
      attribute :notes, :string
      attribute :opportunity_id, :integer
      attribute :priority, :string
      attribute :property_id, :integer
      attribute :start_date, :datetime
      attribute :subject, :string
      attribute :work_ticket_id, :integer

      validates :assigned_to, presence: true, length: (1..)
      validates :due_date, presence: true
      validates :opportunity_id, presence: true, numericality: true, inclusion: { in: (0..**********) }
      validates :property_id, presence: true, numericality: true, inclusion: { in: (0..**********) }
      validates :start_date, presence: true
      validates :subject, presence: true, length: (0..200)
      validates :work_ticket_id, presence: true, numericality: true, inclusion: { in: (0..**********) }

      def attributes
        {
          "AssignedTo" => nil,
          "DueDate" => nil,
          "Notes" => nil,
          "OpportunityID" => nil,
          "Priority" => nil,
          "PropertyID" => nil,
          "StartDate" => nil,
          "Subject" => nil,
          "WorkTicketID" => nil
        }
      end
    end

    class AspireCloudDomainTenantTaxEntitiesODataModelTaxEntity < Base
      attribute :tax_entity_id, :integer
      attribute :tax_entity_name, :string
      attribute :tax_percent, :float
      attribute :active, :boolean

      validates :tax_entity_id, presence: true, numericality: true
      validates :tax_entity_name, length: (0..150), allow_blank: true
      validates :tax_percent, numericality: true, allow_nil: true

      def attributes
        {
          "TaxEntityID" => nil,
          "TaxEntityName" => nil,
          "TaxPercent" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiTaxEntitiesModelsTaxEntityInsertRequest < Base
      attribute :tax_entity_name, :string
      attribute :tax_percent, :float
      attribute :active, :boolean

      validates :tax_entity_name, presence: true, length: (1..150)
      validates :tax_percent, numericality: true, allow_nil: true

      def attributes
        {
          "TaxEntityName" => nil,
          "TaxPercent" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiTaxEntitiesModelsTaxEntityUpdateRequest < Base
      attribute :tax_entity_id, :integer
      attribute :tax_entity_name, :string
      attribute :tax_percent, :float
      attribute :active, :boolean

      validates :tax_entity_id, presence: true, numericality: true
      validates :tax_entity_name, presence: true, length: (1..150)
      validates :tax_percent, numericality: true, allow_nil: true

      def attributes
        {
          "TaxEntityID" => nil,
          "TaxEntityName" => nil,
          "TaxPercent" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiTaxJurisdictionsModelTaxJurisdictionInsertRequest < Base
      attribute :tax_jurisdiction_name, :string
      attribute :federal_tax_percent, :float
      attribute :active, :boolean
      attribute :tax_entity_jurisdictions # {"type" => "array", "items" => {"type" => "integer", "format" => "int32"}, "nullable" => true}

      validates :tax_jurisdiction_name, presence: true, length: (1..50)
      validates :federal_tax_percent, numericality: true, allow_nil: true

      def attributes
        {
          "TaxJurisdictionName" => nil,
          "FederalTaxPercent" => nil,
          "Active" => nil,
          "TaxEntityJurisdictions" => nil
        }
      end
    end

    class AspireCloudExternalApiTaxJurisdictionsModelTaxJurisdictionUpdateRequest < Base
      attribute :tax_jurisdiction_id, :integer
      attribute :tax_jurisdiction_name, :string
      attribute :federal_tax_percent, :float
      attribute :active, :boolean
      attribute :tax_entity_jurisdictions # {"type" => "array", "items" => {"type" => "integer", "format" => "int32"}, "nullable" => true}

      validates :tax_jurisdiction_id, presence: true, numericality: true
      validates :tax_jurisdiction_name, presence: true, length: (1..50)
      validates :federal_tax_percent, numericality: true, allow_nil: true

      def attributes
        {
          "TaxJurisdictionID" => nil,
          "TaxJurisdictionName" => nil,
          "FederalTaxPercent" => nil,
          "Active" => nil,
          "TaxEntityJurisdictions" => nil
        }
      end
    end

    class AspireCloudDomainTenantUnitTypesODataModelUnitType < Base
      attribute :unit_type_id, :integer
      attribute :unit_type_name, :string
      attribute :active, :boolean

      validates :unit_type_id, presence: true, numericality: true

      def attributes
        {
          "UnitTypeID" => nil,
          "UnitTypeName" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiUsersModelsUserInsertRequest < Base
      attribute :all_branch_access, :boolean
      attribute :branch_access # {"type" => "array", "items" => {"type" => "integer", "format" => "int32"}, "nullable" => true}
      attribute :external_contact_reference, :string
      attribute :password, :string
      attribute :active, :boolean
      attribute :user_roles # {"minItems" => 1, "type" => "array", "items" => {"type" => "integer", "format" => "int32"}}

      validates :external_contact_reference, presence: true, length: (1..255)
      validates :password, presence: true, length: (1..500)
      validates :user_roles, presence: true, length: (1..)

      def attributes
        {
          "AllBranchAccess" => nil,
          "BranchAccess" => nil,
          "ExternalContactReference" => nil,
          "Password" => nil,
          "Active" => nil,
          "UserRoles" => nil
        }
      end
    end

    class AspireCloudExternalApiUsersModelsUserUpdateRequest < Base
      attribute :user_id, :integer
      attribute :all_branch_access, :boolean
      attribute :branch_access # {"type" => "array", "items" => {"type" => "integer", "format" => "int32"}, "nullable" => true}
      attribute :password, :string
      attribute :active, :boolean
      attribute :user_roles # {"minItems" => 1, "type" => "array", "items" => {"type" => "integer", "format" => "int32"}}

      validates :user_id, presence: true, numericality: true
      validates :password, length: (..500), allow_blank: true
      validates :user_roles, presence: true, length: (1..)

      def attributes
        {
          "UserId" => nil,
          "AllBranchAccess" => nil,
          "BranchAccess" => nil,
          "Password" => nil,
          "Active" => nil,
          "UserRoles" => nil
        }
      end
    end

    class AspireCloudDomainTenantVendorsODataModelVendor < Base
      attribute :vendor_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :vendor_name, :string
      attribute :accounting_vendor_id, :string
      attribute :created_date_time, :datetime
      attribute :active, :boolean

      validates :vendor_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :vendor_name, length: (0..150), allow_blank: true
      validates :accounting_vendor_id, length: (0..150), allow_blank: true
      validates :created_date_time, presence: true

      def attributes
        {
          "VendorID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "VendorName" => nil,
          "AccountingVendorID" => nil,
          "CreatedDateTime" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiVendorsModelRequestVendorInsertRequest < Base
      attribute :branch_id, :integer
      attribute :vendor_name, :string
      attribute :accounting_vendor_id, :string
      attribute :billing_terms, :string
      attribute :active, :boolean

      validates :branch_id, numericality: true, inclusion: { in: (1..**********) }, allow_nil: true
      validates :vendor_name, presence: true, length: (0..150)
      validates :accounting_vendor_id, length: (0..150), allow_blank: true
      validates :billing_terms, length: (0..10), allow_blank: true

      def attributes
        {
          "BranchID" => nil,
          "VendorName" => nil,
          "AccountingVendorID" => nil,
          "BillingTerms" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudExternalApiVendorsModelResponseVendorResponse < Base
      attribute :vendor_id, :integer

      validates :vendor_id, presence: true, numericality: true

      def attributes
        {
          "VendorID" => nil
        }
      end
    end

    class AspireCloudExternalApiVendorsModelRequestVendorUpdateRequest < Base
      attribute :vendor_id, :integer
      attribute :branch_id, :integer
      attribute :vendor_name, :string
      attribute :accounting_vendor_id, :string
      attribute :billing_terms, :string
      attribute :active, :boolean

      validates :vendor_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :branch_id, numericality: true, inclusion: { in: (1..**********) }, allow_nil: true
      validates :vendor_name, presence: true, length: (0..150)
      validates :accounting_vendor_id, length: (0..150), allow_blank: true
      validates :billing_terms, length: (0..10), allow_blank: true

      def attributes
        {
          "VendorID" => nil,
          "BranchID" => nil,
          "VendorName" => nil,
          "AccountingVendorID" => nil,
          "BillingTerms" => nil,
          "Active" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkersCompODataModelWorkersComp < Base
      attribute :workers_comp_id, :integer
      attribute :workers_comp_name, :string
      attribute :workers_comp_code, :string
      attribute :created_date_time, :datetime
      attribute :created_by_user_name, :string
      attribute :modified_date_time, :datetime
      attribute :modified_by_user_name, :string
      attribute :created_by_user_id, :integer
      attribute :modified_by_user_id, :integer

      validates :workers_comp_id, presence: true, numericality: true
      validates :workers_comp_name, length: (0..200), allow_blank: true
      validates :workers_comp_code, length: (0..10), allow_blank: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :modified_by_user_name, length: (0..101), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :modified_by_user_id, numericality: true, allow_nil: true

      def attributes
        {
          "WorkersCompID" => nil,
          "WorkersCompName" => nil,
          "WorkersCompCode" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserName" => nil,
          "ModifiedDateTime" => nil,
          "ModifiedByUserName" => nil,
          "CreatedByUserID" => nil,
          "ModifiedByUserID" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketCanceledReasonsODataModelWorkTicketCanceledReason < Base
      attribute :work_ticket_canceled_reason_id, :integer
      attribute :work_ticket_canceled_reason_name, :string
      attribute :active, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :work_ticket_canceled_reason_id, presence: true, numericality: true
      validates :work_ticket_canceled_reason_name, length: (1..100), allow_blank: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :created_date_time, presence: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "WorkTicketCanceledReasonId" => nil,
          "WorkTicketCanceledReasonName" => nil,
          "Active" => nil,
          "CreatedByUserId" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserId" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketsODataModelWorkTicketItemModel < Base
      attribute :work_ticket_item_id, :integer
      attribute :catalog_item_id, :integer
      attribute :item_name, :string
      attribute :item_type, :string
      attribute :allocation_unit_type_id, :integer
      attribute :allocation_unit_type_name, :string
      attribute :item_quantity_extended, :float
      attribute :item_cost, :float
      attribute :show_on_ticket, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :catalog_item_category_id, :integer
      attribute :catalog_item_category_name, :string
      attribute :do_not_purchase, :boolean
      attribute :estimating_notes, :string
      attribute :auto_expense, :boolean
      attribute :work_ticket_id, :integer

      validates :work_ticket_item_id, presence: true, numericality: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :item_name, length: (0..200), allow_blank: true
      validates :item_type, length: (0..10), allow_blank: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :allocation_unit_type_name, length: (0..50), allow_blank: true
      validates :item_quantity_extended, numericality: true, allow_nil: true
      validates :item_cost, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :catalog_item_category_id, numericality: true, allow_nil: true
      validates :catalog_item_category_name, length: (0..50), allow_blank: true
      validates :estimating_notes, length: (0..**********), allow_blank: true
      validates :work_ticket_id, presence: true, numericality: true

      def attributes
        {
          "WorkTicketItemID" => nil,
          "CatalogItemID" => nil,
          "ItemName" => nil,
          "ItemType" => nil,
          "AllocationUnitTypeID" => nil,
          "AllocationUnitTypeName" => nil,
          "ItemQuantityExtended" => nil,
          "ItemCost" => nil,
          "ShowOnTicket" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "CatalogItemCategoryID" => nil,
          "CatalogItemCategoryName" => nil,
          "DoNotPurchase" => nil,
          "EstimatingNotes" => nil,
          "AutoExpense" => nil,
          "WorkTicketID" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketRevenuesODataModelWorkTicketRevenueModel < Base
      attribute :work_ticket_revenue_id, :integer
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :revenue_month, :datetime
      attribute :revenue_amount, :float
      attribute :created_date_time, :datetime
      attribute :edited_by_user_id, :integer
      attribute :edited_by_user_name, :string
      attribute :edited_date_time, :datetime
      attribute :accounting_period_id, :integer

      validates :work_ticket_revenue_id, presence: true, numericality: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :revenue_amount, numericality: true, allow_nil: true
      validates :edited_by_user_id, numericality: true, allow_nil: true
      validates :edited_by_user_name, length: (0..101), allow_blank: true
      validates :accounting_period_id, numericality: true, allow_nil: true

      def attributes
        {
          "WorkTicketRevenueID" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "RevenueMonth" => nil,
          "RevenueAmount" => nil,
          "CreatedDateTime" => nil,
          "EditedByUserID" => nil,
          "EditedByUserName" => nil,
          "EditedDateTime" => nil,
          "AccountingPeriodID" => nil
        }
      end
    end

    class AspireCloudExternalApiWorkTicketsCreateAsNeededWorkTicketRequest < Base
      attribute :opportunity_service_id, :integer
      attribute :route_id, :integer
      attribute :scheduled_start_date, :datetime
      attribute :start_date_time, :datetime
      attribute :end_date_time, :datetime
      attribute :hours_per_day, :float

      validates :opportunity_service_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :route_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :scheduled_start_date, presence: true
      validates :hours_per_day, numericality: true, allow_nil: true

      def attributes
        {
          "OpportunityServiceId" => nil,
          "RouteId" => nil,
          "ScheduledStartDate" => nil,
          "StartDateTime" => nil,
          "EndDateTime" => nil,
          "HoursPerDay" => nil
        }
      end
    end

    class AspireCloudExternalApiWorkTicketStatusModelMarkWorkTicketAsReviewedRequest < Base
      attribute :work_ticket_id, :integer
      attribute :reviewed_date_time, :datetime
      attribute :reviewed_user_id, :integer

      validates :work_ticket_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :reviewed_date_time, presence: true
      validates :reviewed_user_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "WorkTicketID" => nil,
          "ReviewedDateTime" => nil,
          "ReviewedUserID" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketTimesODataModelWorkTicketTime < Base
      attribute :invoice_number, :integer
      attribute :work_ticket_time_id, :integer
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :work_ticket_time_date, :datetime
      attribute :start_time, :datetime
      attribute :end_time, :datetime
      attribute :opportunity_service_labor_rate_id, :integer
      attribute :opportunity_service_labor_rate_name, :string
      attribute :opportunity_service_labor_rate, :float
      attribute :pay_code_id, :integer
      attribute :pay_code_name, :string
      attribute :hours, :float
      attribute :ot_hours, :float
      attribute :warranty_time, :boolean
      attribute :distributed_time, :boolean
      attribute :burdened_cost, :float
      attribute :invoice_id, :integer
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :accepted_date_time, :datetime
      attribute :accepted_user_id, :integer
      attribute :accepted_user_name, :string
      attribute :approved_date_time, :datetime
      attribute :approved_user_id, :integer
      attribute :approved_user_name, :string
      attribute :exported_date_time, :datetime
      attribute :exported_user_id, :integer
      attribute :exported_user_name, :string
      attribute :break_time, :float
      attribute :has_break_time, :boolean
      attribute :base_hourly_rate, :float
      attribute :route_id, :integer
      attribute :route_name, :string
      attribute :crew_leader_contact_id, :integer
      attribute :crew_leader_contact_name, :string
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :branch_time_zone, :string
      attribute :geo_location_start_latitude, :float
      attribute :geo_location_start_longitude, :float
      attribute :geo_location_end_latitude, :float
      attribute :geo_location_end_longitude, :float

      validates :invoice_number, numericality: true, allow_nil: true
      validates :work_ticket_time_id, presence: true, numericality: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :contact_id, numericality: true, allow_nil: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :opportunity_service_labor_rate_id, numericality: true, allow_nil: true
      validates :opportunity_service_labor_rate_name, length: (0..50), allow_blank: true
      validates :opportunity_service_labor_rate, numericality: true, allow_nil: true
      validates :pay_code_id, numericality: true, allow_nil: true
      validates :pay_code_name, length: (0..60), allow_blank: true
      validates :hours, numericality: true, allow_nil: true
      validates :ot_hours, numericality: true, allow_nil: true
      validates :burdened_cost, numericality: true, allow_nil: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :accepted_user_id, numericality: true, allow_nil: true
      validates :accepted_user_name, length: (0..101), allow_blank: true
      validates :approved_user_id, numericality: true, allow_nil: true
      validates :approved_user_name, length: (0..101), allow_blank: true
      validates :exported_user_id, numericality: true, allow_nil: true
      validates :exported_user_name, length: (0..101), allow_blank: true
      validates :break_time, numericality: true, allow_nil: true
      validates :base_hourly_rate, numericality: true, allow_nil: true
      validates :route_id, numericality: true, allow_nil: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :crew_leader_contact_id, numericality: true, allow_nil: true
      validates :crew_leader_contact_name, length: (0..101), allow_blank: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :branch_time_zone, length: (0..50), allow_blank: true
      validates :geo_location_start_latitude, numericality: true, allow_nil: true
      validates :geo_location_start_longitude, numericality: true, allow_nil: true
      validates :geo_location_end_latitude, numericality: true, allow_nil: true
      validates :geo_location_end_longitude, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceNumber" => nil,
          "WorkTicketTimeID" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "WorkTicketTimeDate" => nil,
          "StartTime" => nil,
          "EndTime" => nil,
          "OpportunityServiceLaborRateID" => nil,
          "OpportunityServiceLaborRateName" => nil,
          "OpportunityServiceLaborRate" => nil,
          "PayCodeID" => nil,
          "PayCodeName" => nil,
          "Hours" => nil,
          "OTHours" => nil,
          "WarrantyTime" => nil,
          "DistributedTime" => nil,
          "BurdenedCost" => nil,
          "InvoiceID" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "AcceptedDateTime" => nil,
          "AcceptedUserID" => nil,
          "AcceptedUserName" => nil,
          "ApprovedDateTime" => nil,
          "ApprovedUserID" => nil,
          "ApprovedUserName" => nil,
          "ExportedDateTime" => nil,
          "ExportedUserID" => nil,
          "ExportedUserName" => nil,
          "BreakTime" => nil,
          "HasBreakTime" => nil,
          "BaseHourlyRate" => nil,
          "RouteID" => nil,
          "RouteName" => nil,
          "CrewLeaderContactID" => nil,
          "CrewLeaderContactName" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "BranchTimeZone" => nil,
          "GEOLocationStartLatitude" => nil,
          "GEOLocationStartLongitude" => nil,
          "GEOLocationEndLatitude" => nil,
          "GEOLocationEndLongitude" => nil
        }
      end
    end

    class AspireCloudExternalApiWorkTicketTimesModelWorkTicketTimeInsertRequest < Base
      attribute :contact_id, :integer
      attribute :work_ticket_id, :integer
      attribute :start_time, :datetime
      attribute :start_latitude, :float
      attribute :start_longitude, :float
      attribute :route_id, :integer
      attribute :crew_leader_contact_id, :integer
      attribute :end_time, :datetime
      attribute :end_latitude, :float
      attribute :end_longitude, :float

      validates :contact_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :work_ticket_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :start_time, presence: true
      validates :start_latitude, numericality: true, allow_nil: true
      validates :start_longitude, numericality: true, allow_nil: true
      validates :route_id, numericality: true, allow_nil: true
      validates :crew_leader_contact_id, numericality: true, allow_nil: true
      validates :end_time, presence: true
      validates :end_latitude, numericality: true, allow_nil: true
      validates :end_longitude, numericality: true, allow_nil: true

      def attributes
        {
          "ContactID" => nil,
          "WorkTicketID" => nil,
          "StartTime" => nil,
          "StartLatitude" => nil,
          "StartLongitude" => nil,
          "RouteID" => nil,
          "CrewLeaderContactID" => nil,
          "EndTime" => nil,
          "EndLatitude" => nil,
          "EndLongitude" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketVisitsODataModelWorkTicketVisitNote < Base
      attribute :work_ticket_visit_note_id, :integer
      attribute :work_ticket_visit_id, :integer
      attribute :work_ticket_id, :integer
      attribute :note, :string
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :is_public, :boolean
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :route_id, :integer
      attribute :scheduled_date, :datetime
      attribute :start_date_time, :datetime
      attribute :end_date_time, :datetime
      attribute :route_name, :string
      attribute :work_ticket_number, :integer

      validates :work_ticket_visit_note_id, presence: true, numericality: true
      validates :work_ticket_visit_id, presence: true, numericality: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :note, length: (0..), allow_blank: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :route_id, numericality: true, allow_nil: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :work_ticket_number, numericality: true, allow_nil: true

      def attributes
        {
          "WorkTicketVisitNoteID" => nil,
          "WorkTicketVisitID" => nil,
          "WorkTicketID" => nil,
          "Note" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "IsPublic" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "RouteID" => nil,
          "ScheduledDate" => nil,
          "StartDateTime" => nil,
          "EndDateTime" => nil,
          "RouteName" => nil,
          "WorkTicketNumber" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketVisitsODataModelWorkTicketVisit < Base
      attribute :work_ticket_visit_id, :integer
      attribute :route_id, :integer
      attribute :scheduled_date, :datetime
      attribute :sequence_num, :integer
      attribute :work_ticket_id, :integer
      attribute :hours, :float
      attribute :route_name, :string
      attribute :work_ticket_number, :integer

      validates :work_ticket_visit_id, presence: true, numericality: true
      validates :route_id, numericality: true, allow_nil: true
      validates :sequence_num, numericality: true, allow_nil: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :hours, numericality: true, allow_nil: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :work_ticket_number, numericality: true, allow_nil: true

      def attributes
        {
          "WorkTicketVisitID" => nil,
          "RouteID" => nil,
          "ScheduledDate" => nil,
          "SequenceNum" => nil,
          "WorkTicketID" => nil,
          "Hours" => nil,
          "RouteName" => nil,
          "WorkTicketNumber" => nil
        }
      end
    end

    class AspireCloudDomainTenantCatalogItemsODataModelCatalogItemBranch < Base
      attribute :catalog_item_branch_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string

      validates :catalog_item_branch_id, presence: true, numericality: true
      validates :branch_id, presence: true, numericality: true
      validates :branch_name, length: (1..50), allow_blank: true

      def attributes
        {
          "CatalogItemBranchID" => nil,
          "BranchID" => nil,
          "BranchName" => nil
        }
      end
    end

    class AspireCloudDomainTenantContactsODataModelContactTag < Base
      attribute :contact_tag_id, :integer
      attribute :tag_id, :integer
      attribute :tag_name, :string

      validates :contact_tag_id, presence: true, numericality: true
      validates :tag_id, presence: true, numericality: true
      validates :tag_name, length: (0..750), allow_blank: true

      def attributes
        {
          "ContactTagID" => nil,
          "TagID" => nil,
          "TagName" => nil
        }
      end
    end

    class AspireCloudExternalApiContactsModelContactRequest < Base
      attribute :first_name, :string
      attribute :last_name, :string
      attribute :company_id, :integer
      attribute :contact_type_id, :integer
      attribute :branch_id, :integer
      attribute :owner_contact_id, :integer
      attribute :owner_contact_name, :string
      attribute :salutation, :string
      attribute :prospect_rating, :integer
      attribute :prospect_rating_name, :string
      attribute :title, :string
      attribute :email, :string
      attribute :mobile_phone, :string
      attribute :office_phone, :string
      attribute :home_phone, :string
      attribute :fax, :string
      attribute :notes, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_name, :string
      attribute :employee_number, :string
      attribute :website, :string
      attribute :employee_pin, :string
      attribute :accounting_sync_id, :string
      attribute :external_contact_reference, :string
      attribute :termination_date, :datetime
      attribute :hr_notes, :string
      attribute :default_workers_comp_id, :integer
      attribute :user_id, :integer

      validates :first_name, presence: true, length: (1..)
      validates :last_name, presence: true, length: (1..)
      validates :company_id, numericality: true, allow_nil: true
      validates :contact_type_id, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :owner_contact_id, numericality: true, allow_nil: true
      validates :prospect_rating, numericality: true, allow_nil: true
      validates :default_workers_comp_id, numericality: true, allow_nil: true
      validates :user_id, numericality: true, allow_nil: true

      def attributes
        {
          "FirstName" => nil,
          "LastName" => nil,
          "CompanyID" => nil,
          "ContactTypeID" => nil,
          "BranchID" => nil,
          "OwnerContactID" => nil,
          "OwnerContactName" => nil,
          "Salutation" => nil,
          "ProspectRating" => nil,
          "ProspectRatingName" => nil,
          "Title" => nil,
          "Email" => nil,
          "MobilePhone" => nil,
          "OfficePhone" => nil,
          "HomePhone" => nil,
          "Fax" => nil,
          "Notes" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserName" => nil,
          "EmployeeNumber" => nil,
          "Website" => nil,
          "EmployeePin" => nil,
          "AccountingSyncID" => nil,
          "ExternalContactReference" => nil,
          "TerminationDate" => nil,
          "HRNotes" => nil,
          "DefaultWorkersCompID" => nil,
          "UserID" => nil
        }
      end
    end

    class AspireCloudExternalApiContactsModelContactAddressRequest < Base
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string

      validates :address_line1, length: (0..75), allow_blank: true
      validates :address_line2, length: (0..75), allow_blank: true
      validates :city, length: (0..50), allow_blank: true
      validates :state_province_code, length: (0..3), allow_blank: true
      validates :zip_code, length: (0..15), allow_blank: true

      def attributes
        {
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil
        }
      end
    end

    class AspireCloudExternalApiContactsModelContactExtendedRequest < Base
      attribute :default_workers_comp_name, :string
      attribute :default_workers_comp_state_province_code, :string
      attribute :contact_id, :integer
      attribute :first_name, :string
      attribute :last_name, :string
      attribute :company_id, :integer
      attribute :contact_type_id, :integer
      attribute :branch_id, :integer
      attribute :owner_contact_id, :integer
      attribute :owner_contact_name, :string
      attribute :salutation, :string
      attribute :prospect_rating, :integer
      attribute :prospect_rating_name, :string
      attribute :title, :string
      attribute :email, :string
      attribute :mobile_phone, :string
      attribute :office_phone, :string
      attribute :home_phone, :string
      attribute :fax, :string
      attribute :notes, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_name, :string
      attribute :employee_number, :string
      attribute :website, :string
      attribute :employee_pin, :string
      attribute :accounting_sync_id, :string
      attribute :external_contact_reference, :string
      attribute :termination_date, :datetime
      attribute :hr_notes, :string
      attribute :default_workers_comp_id, :integer
      attribute :user_id, :integer

      validates :contact_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :first_name, presence: true, length: (1..)
      validates :last_name, presence: true, length: (1..)
      validates :company_id, numericality: true, allow_nil: true
      validates :contact_type_id, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :owner_contact_id, numericality: true, allow_nil: true
      validates :prospect_rating, numericality: true, allow_nil: true
      validates :default_workers_comp_id, numericality: true, allow_nil: true
      validates :user_id, numericality: true, allow_nil: true

      def attributes
        {
          "DefaultWorkersCompName" => nil,
          "DefaultWorkersCompStateProvinceCode" => nil,
          "ContactID" => nil,
          "FirstName" => nil,
          "LastName" => nil,
          "CompanyID" => nil,
          "ContactTypeID" => nil,
          "BranchID" => nil,
          "OwnerContactID" => nil,
          "OwnerContactName" => nil,
          "Salutation" => nil,
          "ProspectRating" => nil,
          "ProspectRatingName" => nil,
          "Title" => nil,
          "Email" => nil,
          "MobilePhone" => nil,
          "OfficePhone" => nil,
          "HomePhone" => nil,
          "Fax" => nil,
          "Notes" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserName" => nil,
          "EmployeeNumber" => nil,
          "Website" => nil,
          "EmployeePin" => nil,
          "AccountingSyncID" => nil,
          "ExternalContactReference" => nil,
          "TerminationDate" => nil,
          "HRNotes" => nil,
          "DefaultWorkersCompID" => nil,
          "UserID" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelOpportunityRevision < Base
      attribute :opportunity_revision_id, :integer
      attribute :revision_number, :integer
      attribute :revision_status, :string
      attribute :start_date, :datetime
      attribute :created_date, :datetime
      attribute :created_by_user_id, :integer
      attribute :won_date, :datetime
      attribute :won_by_user_id, :integer

      validates :opportunity_revision_id, numericality: true, allow_nil: true
      validates :revision_number, presence: true, numericality: true
      validates :revision_status, length: (0..10), allow_blank: true
      validates :start_date, presence: true
      validates :created_date, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :won_by_user_id, numericality: true, allow_nil: true

      def attributes
        {
          "OpportunityRevisionID" => nil,
          "RevisionNumber" => nil,
          "RevisionStatus" => nil,
          "StartDate" => nil,
          "CreatedDate" => nil,
          "CreatedByUserId" => nil,
          "WonDate" => nil,
          "WonByUserId" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelOpportunityBilling < Base
      attribute :opportunity_billing_id, :integer
      attribute :opportunity_billing_ref_id, :integer
      attribute :bill_month, :integer
      attribute :invoice_description, :string
      attribute :invoice_trigger_percent, :float
      attribute :invoice_amount, :float
      attribute :sort_order, :integer
      attribute :active, :boolean
      attribute :opportunity_revision_id, :integer

      validates :opportunity_billing_id, presence: true, numericality: true
      validates :opportunity_billing_ref_id, numericality: true, allow_nil: true
      validates :bill_month, numericality: true, allow_nil: true
      validates :invoice_trigger_percent, numericality: true, allow_nil: true
      validates :invoice_amount, numericality: true, allow_nil: true
      validates :sort_order, numericality: true, allow_nil: true
      validates :opportunity_revision_id, numericality: true, allow_nil: true

      def attributes
        {
          "OpportunityBillingID" => nil,
          "OpportunityBillingRefID" => nil,
          "BillMonth" => nil,
          "InvoiceDescription" => nil,
          "InvoiceTriggerPercent" => nil,
          "InvoiceAmount" => nil,
          "SortOrder" => nil,
          "Active" => nil,
          "OpportunityRevisionID" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunityServicesODataModelOpportunityServiceRoute < Base
      attribute :route_id, :integer
      attribute :route_name, :string

      validates :route_id, presence: true, numericality: true
      validates :route_name, length: (0..100), allow_blank: true

      def attributes
        {
          "RouteID" => nil,
          "RouteName" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunityServiceDefaultPayCodesODataModelOpportunityServiceDefaultPayCode < Base
      attribute :opportunity_service_default_pay_code_id, :integer
      attribute :pay_code_id, :integer
      attribute :pay_schedule_id, :integer

      validates :opportunity_service_default_pay_code_id, presence: true, numericality: true
      validates :pay_code_id, presence: true, numericality: true
      validates :pay_schedule_id, numericality: true, allow_nil: true

      def attributes
        {
          "OpportunityServiceDefaultPayCodeId" => nil,
          "PayCodeId" => nil,
          "PayScheduleId" => nil
        }
      end
    end

    class AspireCloudDomainTenantPaymentsODataModelPaymentAllocation < Base
      attribute :invoice_number, :integer
      attribute :payment_allocation_id, :integer
      attribute :invoice_id, :integer
      attribute :amount, :float
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime

      validates :invoice_number, numericality: true, allow_nil: true
      validates :payment_allocation_id, presence: true, numericality: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :amount, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceNumber" => nil,
          "PaymentAllocationID" => nil,
          "InvoiceID" => nil,
          "Amount" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertiesODataModelPropertyContact < Base
      attribute :property_id, :integer
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :primary_contact, :boolean
      attribute :billing_contact, :boolean
      attribute :email_invoice_contact, :boolean
      attribute :email_notifications_contact, :boolean
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :sms_notifications_contact, :boolean
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime

      validates :property_id, presence: true, numericality: true
      validates :contact_id, presence: true, numericality: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :company_id, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "PropertyID" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "PrimaryContact" => nil,
          "BillingContact" => nil,
          "EmailInvoiceContact" => nil,
          "EmailNotificationsContact" => nil,
          "CompanyID" => nil,
          "CompanyName" => nil,
          "SMSNotificationsContact" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertiesODataModelPropertyTag < Base
      attribute :property_tag_id, :integer
      attribute :tag_id, :integer
      attribute :tag_name, :string

      validates :property_tag_id, presence: true, numericality: true
      validates :tag_id, numericality: true, allow_nil: true
      validates :tag_name, length: (0..50), allow_blank: true

      def attributes
        {
          "PropertyTagID" => nil,
          "TagID" => nil,
          "TagName" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertiesODataModelPropertyTakeoffItem < Base
      attribute :property_takeoff_item_id, :integer
      attribute :takeoff_item_id, :integer
      attribute :takeoff_item_name, :string
      attribute :takeoff_item_value, :float

      validates :property_takeoff_item_id, presence: true, numericality: true
      validates :takeoff_item_id, numericality: true, allow_nil: true
      validates :takeoff_item_name, length: (0..100), allow_blank: true
      validates :takeoff_item_value, numericality: true, allow_nil: true

      def attributes
        {
          "PropertyTakeoffItemID" => nil,
          "TakeoffItemID" => nil,
          "TakeoffItemName" => nil,
          "TakeoffItemValue" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyAvailabilityModelsAvailabilityTime < Base
      attribute :day_of_week, :integer
      attribute :start_time, :datetime
      attribute :end_time, :datetime

      validates :day_of_week, numericality: true, inclusion: { in: (0..6) }, allow_nil: true
      validates :start_time, presence: true
      validates :end_time, presence: true

      def attributes
        {
          "DayOfWeek" => nil,
          "StartTime" => nil,
          "EndTime" => nil
        }
      end
    end

    class AspireCloudDomainTenantPurchaseReceiptODataModelReceiptExtraCost < Base
      attribute :receipt_extra_cost_id, :integer
      attribute :extra_cost_type, :string
      attribute :extra_cost, :float

      validates :receipt_extra_cost_id, presence: true, numericality: true
      validates :extra_cost_type, length: (0..20), allow_blank: true
      validates :extra_cost, presence: true, numericality: true

      def attributes
        {
          "ReceiptExtraCostID" => nil,
          "ExtraCostType" => nil,
          "ExtraCost" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelReceiptExtraCost < Base
      attribute :receipt_extra_cost_id, :integer
      attribute :extra_cost_type, :string
      attribute :extra_cost, :float

      validates :receipt_extra_cost_id, presence: true, numericality: true
      validates :extra_cost_type, presence: true, length: (1..)
      validates :extra_cost, presence: true, numericality: { greater_than_or_equal_to: 0.0 }

      def attributes
        {
          "ReceiptExtraCostID" => nil,
          "ExtraCostType" => nil,
          "ExtraCost" => nil
        }
      end
    end

    class AspireCloudDomainTenantRoutesODataModelRouteProperty < Base
      attribute :route_property_id, :integer
      attribute :property_id, :integer
      attribute :property_name, :string
      attribute :display_order, :integer

      validates :route_property_id, presence: true, numericality: true
      validates :property_id, numericality: true, allow_nil: true
      validates :property_name, length: (0..100), allow_blank: true
      validates :display_order, numericality: true, allow_nil: true

      def attributes
        {
          "RoutePropertyID" => nil,
          "PropertyID" => nil,
          "PropertyName" => nil,
          "DisplayOrder" => nil
        }
      end
    end

    class AspireCloudDomainTenantRoutesODataModelRouteService < Base
      attribute :service_id, :integer
      attribute :service_name, :string

      validates :service_id, presence: true, numericality: true
      validates :service_name, length: (0..150), allow_blank: true

      def attributes
        {
          "ServiceID" => nil,
          "ServiceName" => nil
        }
      end
    end

    class AspireCloudDomainTenantRoutesODataModelRouteServiceType < Base
      attribute :service_type_id, :integer
      attribute :service_type_name, :string

      validates :service_type_id, presence: true, numericality: true
      validates :service_type_name, length: (0..100), allow_blank: true

      def attributes
        {
          "ServiceTypeID" => nil,
          "ServiceTypeName" => nil
        }
      end
    end

    class AspireCloudDomainTenantServicesODataModelServiceTaxOverride < Base
      attribute :service_tax_override_id, :integer
      attribute :state_province_code, :string
      attribute :labor_taxable, :boolean
      attribute :material_taxable, :boolean
      attribute :equipment_taxable, :boolean
      attribute :sub_taxable, :boolean
      attribute :other_taxable, :boolean

      validates :service_tax_override_id, presence: true, numericality: true
      validates :state_province_code, length: (0..3), allow_blank: true

      def attributes
        {
          "ServiceTaxOverrideID" => nil,
          "StateProvinceCode" => nil,
          "LaborTaxable" => nil,
          "MaterialTaxable" => nil,
          "EquipmentTaxable" => nil,
          "SubTaxable" => nil,
          "OtherTaxable" => nil
        }
      end
    end

    class AspireCloudDomainTenantServicesODataModelServiceBranch < Base
      attribute :service_branch_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string

      validates :service_branch_id, presence: true, numericality: true
      validates :branch_id, presence: true, numericality: true
      validates :branch_name, length: (1..50), allow_blank: true

      def attributes
        {
          "ServiceBranchID" => nil,
          "BranchID" => nil,
          "BranchName" => nil
        }
      end
    end

    class AspireCloudDomainTenantTaxJurisdictionsODataModelTaxEntityJurisdiction < Base
      attribute :tax_entity_id, :integer
      attribute :tax_entity_name, :string

      validates :tax_entity_id, presence: true, numericality: true
      validates :tax_entity_name, length: (0..150), allow_blank: true

      def attributes
        {
          "TaxEntityID" => nil,
          "TaxEntityName" => nil
        }
      end
    end

    class AspireCloudDomainTenantUsersODataModelBranchAccess < Base
      attribute :branch_id, :integer
      attribute :branch_name, :string

      validates :branch_id, presence: true, numericality: true
      validates :branch_name, length: (1..50), allow_blank: true

      def attributes
        {
          "BranchID" => nil,
          "BranchName" => nil
        }
      end
    end

    class AspireCloudDomainTenantUsersODataModelUserRole < Base
      attribute :role_id, :integer
      attribute :role_name, :string

      validates :role_id, presence: true, numericality: true
      validates :role_name, length: (1..50), allow_blank: true

      def attributes
        {
          "RoleID" => nil,
          "RoleName" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketsODataModelWorkTicketRevenue < Base
      attribute :work_ticket_revenue_id, :integer
      attribute :revenue_month, :datetime
      attribute :revenue_amount, :float
      attribute :created_date_time, :datetime
      attribute :edited_by_user_id, :integer
      attribute :edited_by_user_name, :string
      attribute :edited_date_time, :datetime
      attribute :accounting_period_id, :integer
      attribute :accounting_period_name, :string
      attribute :accounting_period_year, :integer

      validates :work_ticket_revenue_id, presence: true, numericality: true
      validates :revenue_amount, numericality: true, allow_nil: true
      validates :edited_by_user_id, numericality: true, allow_nil: true
      validates :edited_by_user_name, length: (0..101), allow_blank: true
      validates :accounting_period_id, numericality: true, allow_nil: true
      validates :accounting_period_name, length: (0..50), allow_blank: true
      validates :accounting_period_year, numericality: true, allow_nil: true

      def attributes
        {
          "WorkTicketRevenueID" => nil,
          "RevenueMonth" => nil,
          "RevenueAmount" => nil,
          "CreatedDateTime" => nil,
          "EditedByUserID" => nil,
          "EditedByUserName" => nil,
          "EditedDateTime" => nil,
          "AccountingPeriodID" => nil,
          "AccountingPeriodName" => nil,
          "AccountingPeriodYear" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelScheduleOfValue < Base
      attribute :schedule_of_value_id, :integer
      attribute :master_opportunity_id, :integer
      attribute :linked_to_a_service, :boolean
      attribute :service_id, :integer
      attribute :opportunity_service_id, :integer
      attribute :description, :string
      attribute :quantity, :float
      attribute :unit_price, :float
      attribute :total_price, :float
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :deleted, :boolean
      attribute :available_to_invoice, :boolean
      attribute :sort_order, :integer

      validates :schedule_of_value_id, presence: true, numericality: true
      validates :master_opportunity_id, numericality: true, allow_nil: true
      validates :service_id, numericality: true, allow_nil: true
      validates :opportunity_service_id, numericality: true, allow_nil: true
      validates :quantity, numericality: true, allow_nil: true
      validates :unit_price, numericality: true, allow_nil: true
      validates :total_price, numericality: true, allow_nil: true
      validates :created_date_time, presence: true
      validates :created_by_user_id, presence: true, numericality: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :sort_order, presence: true, numericality: true

      def attributes
        {
          "ScheduleOfValueID" => nil,
          "MasterOpportunityID" => nil,
          "LinkedToAService" => nil,
          "ServiceID" => nil,
          "OpportunityServiceID" => nil,
          "Description" => nil,
          "Quantity" => nil,
          "UnitPrice" => nil,
          "TotalPrice" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "Deleted" => nil,
          "AvailableToInvoice" => nil,
          "SortOrder" => nil
        }
      end
    end

    class AspireCloudDomainTenantPurchaseReceiptODataModelItemAllocation < Base
      attribute :item_allocation_id, :integer
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :opportunity_id, :integer
      attribute :opportunity_number, :integer
      attribute :inventory_location_id, :integer
      attribute :inventory_location_name, :string
      attribute :catalog_item_id, :integer
      attribute :item_name, :string
      attribute :item_type, :string
      attribute :item_unit_cost, :float
      attribute :item_total_cost, :float
      attribute :item_quantity, :float

      validates :item_allocation_id, presence: true, numericality: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :opportunity_number, numericality: true, allow_nil: true
      validates :inventory_location_id, numericality: true, allow_nil: true
      validates :inventory_location_name, length: (0..150), allow_blank: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :item_name, length: (0..200), allow_blank: true
      validates :item_type, length: (0..10), allow_blank: true
      validates :item_unit_cost, numericality: true, allow_nil: true
      validates :item_total_cost, numericality: true, allow_nil: true
      validates :item_quantity, numericality: true, allow_nil: true

      def attributes
        {
          "ItemAllocationID" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "OpportunityID" => nil,
          "OpportunityNumber" => nil,
          "InventoryLocationID" => nil,
          "InventoryLocationName" => nil,
          "CatalogItemID" => nil,
          "ItemName" => nil,
          "ItemType" => nil,
          "ItemUnitCost" => nil,
          "ItemTotalCost" => nil,
          "ItemQuantity" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelItemAllocationModel < Base
      attribute :work_ticket_id, :integer
      attribute :inventory_location_id, :integer
      attribute :item_quantity, :float

      validates :work_ticket_id, presence: true, numericality: true
      validates :inventory_location_id, presence: true, numericality: true
      validates :item_quantity, presence: true, numericality: { greater_than_or_equal_to: 0.0 }

      def attributes
        {
          "WorkTicketID" => nil,
          "InventoryLocationID" => nil,
          "ItemQuantity" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoicesODataModelInvoiceOpportunityServiceItem < Base
      attribute :invoice_opportunity_service_item_id, :integer
      attribute :quantity, :float
      attribute :unit_price, :float
      attribute :ext_price, :float
      attribute :description, :string
      attribute :orig_quantity, :float
      attribute :orig_unit_price, :float
      attribute :allocation_unit_type_id, :integer
      attribute :allocation_unit_type_name, :string
      attribute :item_type, :string

      validates :invoice_opportunity_service_item_id, presence: true, numericality: true
      validates :quantity, numericality: true, allow_nil: true
      validates :unit_price, numericality: true, allow_nil: true
      validates :ext_price, numericality: true, allow_nil: true
      validates :description, length: (0..**********), allow_blank: true
      validates :orig_quantity, numericality: true, allow_nil: true
      validates :orig_unit_price, numericality: true, allow_nil: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :allocation_unit_type_name, length: (0..50), allow_blank: true
      validates :item_type, length: (0..10), allow_blank: true

      def attributes
        {
          "InvoiceOpportunityServiceItemID" => nil,
          "Quantity" => nil,
          "UnitPrice" => nil,
          "ExtPrice" => nil,
          "Description" => nil,
          "OrigQuantity" => nil,
          "OrigUnitPrice" => nil,
          "AllocationUnitTypeID" => nil,
          "AllocationUnitTypeName" => nil,
          "ItemType" => nil
        }
      end
    end

    class AspireCloudDomainTenantCatalogItemsODataModelCatalogItem < Base
      attribute :catalog_item_id, :integer
      attribute :catalog_item_category_id, :integer
      attribute :item_type, :string
      attribute :item_name, :string
      attribute :item_alternate_name, :string
      attribute :item_description, :string
      attribute :item_code, :string
      attribute :item_cost, :float
      attribute :purchase_unit_type_id, :integer
      attribute :purchase_unit_type_name, :string
      attribute :allocation_unit_type_id, :integer
      attribute :allocation_unit_type_name, :string
      attribute :unit_type_allocation_conversion, :float
      attribute :epa_number, :string
      attribute :epa_name, :string
      attribute :inventory, :boolean
      attribute :available_to_bid, :boolean
      attribute :active, :boolean
      attribute :last_updated_date_time, :datetime
      attribute :last_updated_by_user_id, :integer
      attribute :last_updated_by_user_name, :string
      attribute :purchase_unit_cost, :float
      attribute :force_unit_pricing, :boolean
      attribute :allocate_from_mobile, :boolean
      attribute :catalog_id, :integer
      attribute :catalog_name, :string
      attribute :takeoff_item_id, :integer
      attribute :catalog_item_branches, ArrayOf.new(AspireCloudDomainTenantCatalogItemsODataModelCatalogItemBranch)
      attribute :all_branches, :boolean
      attribute :material_barcode1, :string
      attribute :material_barcode2, :string

      validates :catalog_item_id, presence: true, numericality: true
      validates :catalog_item_category_id, numericality: true, allow_nil: true
      validates :item_type, length: (0..10), allow_blank: true
      validates :item_name, length: (0..200), allow_blank: true
      validates :item_alternate_name, length: (0..200), allow_blank: true
      validates :item_description, length: (0..**********), allow_blank: true
      validates :item_code, length: (0..50), allow_blank: true
      validates :item_cost, numericality: true, allow_nil: true
      validates :purchase_unit_type_id, numericality: true, allow_nil: true
      validates :purchase_unit_type_name, length: (0..50), allow_blank: true
      validates :allocation_unit_type_id, numericality: true, allow_nil: true
      validates :allocation_unit_type_name, length: (0..50), allow_blank: true
      validates :unit_type_allocation_conversion, numericality: true, allow_nil: true
      validates :epa_number, length: (0..50), allow_blank: true
      validates :epa_name, length: (0..250), allow_blank: true
      validates :last_updated_by_user_id, numericality: true, allow_nil: true
      validates :last_updated_by_user_name, length: (0..101), allow_blank: true
      validates :purchase_unit_cost, numericality: true, allow_nil: true
      validates :catalog_id, numericality: true, allow_nil: true
      validates :catalog_name, length: (0..200), allow_blank: true
      validates :takeoff_item_id, numericality: true, allow_nil: true

      def attributes
        {
          "CatalogItemID" => nil,
          "CatalogItemCategoryID" => nil,
          "ItemType" => nil,
          "ItemName" => nil,
          "ItemAlternateName" => nil,
          "ItemDescription" => nil,
          "ItemCode" => nil,
          "ItemCost" => nil,
          "PurchaseUnitTypeID" => nil,
          "PurchaseUnitTypeName" => nil,
          "AllocationUnitTypeID" => nil,
          "AllocationUnitTypeName" => nil,
          "UnitTypeAllocationConversion" => nil,
          "EPANumber" => nil,
          "EPAName" => nil,
          "Inventory" => nil,
          "AvailableToBid" => nil,
          "Active" => nil,
          "LastUpdatedDateTime" => nil,
          "LastUpdatedByUserID" => nil,
          "LastUpdatedByUserName" => nil,
          "PurchaseUnitCost" => nil,
          "ForceUnitPricing" => nil,
          "AllocateFromMobile" => nil,
          "CatalogId" => nil,
          "CatalogName" => nil,
          "TakeoffItemID" => nil,
          "CatalogItemBranches" => nil,
          "AllBranches" => nil,
          "MaterialBarcode1" => nil,
          "MaterialBarcode2" => nil
        }
      end
    end

    class AspireCloudDomainTenantContactsODataModelContact < Base
      attribute :contact_id, :integer
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :contact_type_id, :integer
      attribute :contact_type_name, :string
      attribute :office_address, AspireCloudDomainTenantAddressesODataModelAddress
      attribute :home_address, AspireCloudDomainTenantAddressesODataModelAddress
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :owner_contact_id, :integer
      attribute :owner_contact_name, :string
      attribute :salutation, :string
      attribute :first_name, :string
      attribute :last_name, :string
      attribute :prospect_rating, :string
      attribute :prospect_rating_name, :string
      attribute :title, :string
      attribute :email, :string
      attribute :mobile_phone, :string
      attribute :office_phone, :string
      attribute :home_phone, :string
      attribute :fax, :string
      attribute :notes, :string
      attribute :active, :boolean
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :employee_number, :string
      attribute :website, :string
      attribute :employee_pin, :string
      attribute :accounting_sync_id, :string
      attribute :external_contact_reference, :string
      attribute :termination_date, :datetime
      attribute :hr_notes, :string
      attribute :default_workers_comp_id, :integer
      attribute :default_workers_comp_name, :string
      attribute :default_workers_comp_state_province_code, :string
      attribute :user_id, :integer
      attribute :crew_size, :integer
      attribute :prompt_for_crew_size, :boolean
      attribute :sub_crew_leader_contact_id, :integer
      attribute :pay_schedule_id, :integer
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :modified_date, :datetime
      attribute :contact_tags, ArrayOf.new(AspireCloudDomainTenantContactsODataModelContactTag)
      attribute :contact_signature, :string

      validates :contact_id, presence: true, numericality: true
      validates :company_id, numericality: true, allow_nil: true
      validates :company_name, length: (0..100), allow_blank: true
      validates :contact_type_id, numericality: true, allow_nil: true
      validates :contact_type_name, length: (0..20), allow_blank: true
      validates :office_address, presence: true
      validates :home_address, presence: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :owner_contact_id, numericality: true, allow_nil: true
      validates :owner_contact_name, length: (0..101), allow_blank: true
      validates :salutation, length: (0..20), allow_blank: true
      validates :first_name, length: (0..50), allow_blank: true
      validates :last_name, length: (0..50), allow_blank: true
      validates :prospect_rating, length: (0..4), allow_blank: true
      validates :prospect_rating_name, length: (0..4), allow_blank: true
      validates :title, length: (0..50), allow_blank: true
      validates :email, length: (0..255), allow_blank: true
      validates :mobile_phone, length: (0..25), allow_blank: true
      validates :office_phone, length: (0..25), allow_blank: true
      validates :home_phone, length: (0..25), allow_blank: true
      validates :fax, length: (0..25), allow_blank: true
      validates :notes, length: (0..**********), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :employee_number, length: (0..25), allow_blank: true
      validates :website, length: (0..255), allow_blank: true
      validates :employee_pin, length: (0..50), allow_blank: true
      validates :external_contact_reference, length: (0..255), allow_blank: true
      validates :hr_notes, length: (0..**********), allow_blank: true
      validates :default_workers_comp_id, numericality: true, allow_nil: true
      validates :default_workers_comp_name, length: (0..200), allow_blank: true
      validates :default_workers_comp_state_province_code, length: (0..3), allow_blank: true
      validates :user_id, numericality: true, allow_nil: true
      validates :crew_size, numericality: true, allow_nil: true
      validates :sub_crew_leader_contact_id, numericality: true, allow_nil: true
      validates :pay_schedule_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :contact_signature, length: (0..**********), allow_blank: true

      def attributes
        {
          "ContactID" => nil,
          "CompanyID" => nil,
          "CompanyName" => nil,
          "ContactTypeID" => nil,
          "ContactTypeName" => nil,
          "OfficeAddress" => nil,
          "HomeAddress" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "OwnerContactID" => nil,
          "OwnerContactName" => nil,
          "Salutation" => nil,
          "FirstName" => nil,
          "LastName" => nil,
          "ProspectRating" => nil,
          "ProspectRatingName" => nil,
          "Title" => nil,
          "Email" => nil,
          "MobilePhone" => nil,
          "OfficePhone" => nil,
          "HomePhone" => nil,
          "Fax" => nil,
          "Notes" => nil,
          "Active" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "EmployeeNumber" => nil,
          "Website" => nil,
          "EmployeePin" => nil,
          "AccountingSyncID" => nil,
          "ExternalContactReference" => nil,
          "TerminationDate" => nil,
          "HRNotes" => nil,
          "DefaultWorkersCompID" => nil,
          "DefaultWorkersCompName" => nil,
          "DefaultWorkersCompStateProvinceCode" => nil,
          "UserID" => nil,
          "CrewSize" => nil,
          "PromptForCrewSize" => nil,
          "SubCrewLeaderContactID" => nil,
          "PayScheduleID" => nil,
          "LastModifiedByUserId" => nil,
          "LastModifiedByUserName" => nil,
          "ModifiedDate" => nil,
          "ContactTags" => nil,
          "ContactSignature" => nil
        }
      end
    end

    class AspireCloudExternalApiContactsModelContactInsertRequest < Base
      attribute :contact, AspireCloudExternalApiContactsModelContactRequest
      attribute :home_address, AspireCloudExternalApiContactsModelContactAddressRequest
      attribute :office_address, AspireCloudExternalApiContactsModelContactAddressRequest
      attribute :contact_tags, :string
      attribute :pay_schedule_id, :integer

      validates :contact, presence: true
      validates :home_address, presence: true
      validates :office_address, presence: true
      validates :pay_schedule_id, presence: true, numericality: true

      def attributes
        {
          "Contact" => nil,
          "HomeAddress" => nil,
          "OfficeAddress" => nil,
          "ContactTags" => nil,
          "PayScheduleID" => nil
        }
      end
    end

    class AspireCloudExternalApiContactsModelContactUpdateRequest < Base
      attribute :contact, AspireCloudExternalApiContactsModelContactExtendedRequest
      attribute :home_address, AspireCloudExternalApiContactsModelContactAddressRequest
      attribute :office_address, AspireCloudExternalApiContactsModelContactAddressRequest
      attribute :hr_detail, :integer
      attribute :update_opportunities, :boolean
      attribute :resend_emails, :boolean
      attribute :update_invoices_company, :boolean
      attribute :contact_tags, :string
      attribute :pay_schedule_id, :integer
      attribute :update_email_address, :boolean
      attribute :update_pin, :boolean
      attribute :update_tags, :boolean

      validates :contact, presence: true
      validates :home_address, presence: true
      validates :office_address, presence: true
      validates :hr_detail, numericality: true, allow_nil: true
      validates :pay_schedule_id, presence: true, numericality: true

      def attributes
        {
          "Contact" => nil,
          "HomeAddress" => nil,
          "OfficeAddress" => nil,
          "HRDetail" => nil,
          "UpdateOpportunities" => nil,
          "ResendEmails" => nil,
          "UpdateInvoicesCompany" => nil,
          "ContactTags" => nil,
          "PayScheduleID" => nil,
          "UpdateEmailAddress" => nil,
          "UpdatePin" => nil,
          "UpdateTags" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunityServicesODataModelOpportunityService < Base
      attribute :opportunity_service_id, :integer
      attribute :opportunity_service_group_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :service_id, :integer
      attribute :display_name, :string
      attribute :service_description, :string
      attribute :operation_notes, :string
      attribute :occur, :integer
      attribute :invoice_type, :string
      attribute :as_needed, :boolean
      attribute :complexity_percent, :float
      attribute :per_hours, :float
      attribute :extended_hours, :float
      attribute :per_price, :float
      attribute :per_price_override, :float
      attribute :extended_price, :float
      attribute :labor_taxable, :boolean
      attribute :material_taxable, :boolean
      attribute :equipment_taxable, :boolean
      attribute :sub_taxable, :boolean
      attribute :other_taxable, :boolean
      attribute :sort_order, :integer
      attribute :tm_material_markup_percent, :float
      attribute :round_price, :boolean
      attribute :override_pricing, :boolean
      attribute :labor_markup, :float
      attribute :material_markup, :float
      attribute :equipment_markup, :float
      attribute :sub_markup, :float
      attribute :other_markup, :float
      attribute :net_profit_percent, :float
      attribute :labor_extended_price, :float
      attribute :material_extended_price, :float
      attribute :equipment_extended_price, :float
      attribute :sub_extended_price, :float
      attribute :other_extended_price, :float
      attribute :taxable_extended_price, :float
      attribute :labor_per_extended_cost, :float
      attribute :material_per_extended_cost, :float
      attribute :equipment_per_extended_cost, :float
      attribute :sub_per_extended_cost, :float
      attribute :other_per_extended_cost, :float
      attribute :labor_per_extended_price, :float
      attribute :material_per_extended_price, :float
      attribute :equipment_per_extended_price, :float
      attribute :sub_per_extended_price, :float
      attribute :other_per_extended_price, :float
      attribute :taxable_per_extended_price, :float
      attribute :opportunity_id, :integer
      attribute :minimum_price, :float
      attribute :minimum_price_applied, :boolean
      attribute :overhead, :float
      attribute :break_even, :float
      attribute :per_cost, :float
      attribute :override_price, :boolean
      attribute :separate_work_ticket, :boolean
      attribute :default_pay_code_id, :integer
      attribute :labor_extended_cost, :float
      attribute :material_extended_cost, :float
      attribute :equipment_extended_cost, :float
      attribute :sub_extended_cost, :float
      attribute :other_extended_cost, :float
      attribute :extended_cost, :float
      attribute :labor_overhead, :float
      attribute :material_overhead, :float
      attribute :equipment_overhead, :float
      attribute :sub_overhead, :float
      attribute :other_overhead, :float
      attribute :labor_profit, :float
      attribute :material_profit, :float
      attribute :equipment_profit, :float
      attribute :sub_profit, :float
      attribute :other_profit, :float
      attribute :tm_minimum_hours, :float
      attribute :service_name_abr_override, :string
      attribute :per_visit_hours, :float
      attribute :per_visit_materials_qty, :float
      attribute :tm_equipment_markup_percent, :float
      attribute :tm_sub_markup_percent, :float
      attribute :tm_other_markup_percent, :float
      attribute :master_opportunity_service_id, :integer
      attribute :per_hours_orig, :float
      attribute :opportunity_service_status, :string
      attribute :added_opportunity_revision_id, :integer
      attribute :removed_opportunity_revision_id, :integer
      attribute :child_opportunity_service_id, :integer
      attribute :parent_opportunity_service_id, :integer
      attribute :revision_start_work_ticket_id, :integer
      attribute :default_sequence_number, :integer
      attribute :service_item_quantity, :float
      attribute :change_order_opportunity_service_id, :integer
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :opportunity_service_routes, ArrayOf.new(AspireCloudDomainTenantOpportunityServicesODataModelOpportunityServiceRoute)
      attribute :opportunity_service_default_pay_codes, ArrayOf.new(AspireCloudDomainTenantOpportunityServiceDefaultPayCodesODataModelOpportunityServiceDefaultPayCode)

      validates :opportunity_service_id, presence: true, numericality: true
      validates :opportunity_service_group_id, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :service_id, numericality: true, allow_nil: true
      validates :display_name, length: (0..150), allow_blank: true
      validates :service_description, length: (0..**********), allow_blank: true
      validates :operation_notes, length: (0..**********), allow_blank: true
      validates :occur, numericality: true, allow_nil: true
      validates :invoice_type, length: (0..35), allow_blank: true
      validates :complexity_percent, numericality: true, allow_nil: true
      validates :per_hours, numericality: true, allow_nil: true
      validates :extended_hours, numericality: true, allow_nil: true
      validates :per_price, numericality: true, allow_nil: true
      validates :per_price_override, numericality: true, allow_nil: true
      validates :extended_price, numericality: true, allow_nil: true
      validates :sort_order, numericality: true, allow_nil: true
      validates :tm_material_markup_percent, numericality: true, allow_nil: true
      validates :labor_markup, numericality: true, allow_nil: true
      validates :material_markup, numericality: true, allow_nil: true
      validates :equipment_markup, numericality: true, allow_nil: true
      validates :sub_markup, numericality: true, allow_nil: true
      validates :other_markup, numericality: true, allow_nil: true
      validates :net_profit_percent, numericality: true, allow_nil: true
      validates :labor_extended_price, numericality: true, allow_nil: true
      validates :material_extended_price, numericality: true, allow_nil: true
      validates :equipment_extended_price, numericality: true, allow_nil: true
      validates :sub_extended_price, numericality: true, allow_nil: true
      validates :other_extended_price, numericality: true, allow_nil: true
      validates :taxable_extended_price, numericality: true, allow_nil: true
      validates :labor_per_extended_cost, numericality: true, allow_nil: true
      validates :material_per_extended_cost, numericality: true, allow_nil: true
      validates :equipment_per_extended_cost, numericality: true, allow_nil: true
      validates :sub_per_extended_cost, numericality: true, allow_nil: true
      validates :other_per_extended_cost, numericality: true, allow_nil: true
      validates :labor_per_extended_price, numericality: true, allow_nil: true
      validates :material_per_extended_price, numericality: true, allow_nil: true
      validates :equipment_per_extended_price, numericality: true, allow_nil: true
      validates :sub_per_extended_price, numericality: true, allow_nil: true
      validates :other_per_extended_price, numericality: true, allow_nil: true
      validates :taxable_per_extended_price, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :minimum_price, numericality: true, allow_nil: true
      validates :overhead, numericality: true, allow_nil: true
      validates :break_even, numericality: true, allow_nil: true
      validates :per_cost, numericality: true, allow_nil: true
      validates :default_pay_code_id, numericality: true, allow_nil: true
      validates :labor_extended_cost, numericality: true, allow_nil: true
      validates :material_extended_cost, numericality: true, allow_nil: true
      validates :equipment_extended_cost, numericality: true, allow_nil: true
      validates :sub_extended_cost, numericality: true, allow_nil: true
      validates :other_extended_cost, numericality: true, allow_nil: true
      validates :extended_cost, numericality: true, allow_nil: true
      validates :labor_overhead, numericality: true, allow_nil: true
      validates :material_overhead, numericality: true, allow_nil: true
      validates :equipment_overhead, numericality: true, allow_nil: true
      validates :sub_overhead, numericality: true, allow_nil: true
      validates :other_overhead, numericality: true, allow_nil: true
      validates :labor_profit, numericality: true, allow_nil: true
      validates :material_profit, numericality: true, allow_nil: true
      validates :equipment_profit, numericality: true, allow_nil: true
      validates :sub_profit, numericality: true, allow_nil: true
      validates :other_profit, numericality: true, allow_nil: true
      validates :tm_minimum_hours, numericality: true, allow_nil: true
      validates :service_name_abr_override, length: (0..20), allow_blank: true
      validates :per_visit_hours, presence: true, numericality: true
      validates :per_visit_materials_qty, presence: true, numericality: true
      validates :tm_equipment_markup_percent, presence: true, numericality: true
      validates :tm_sub_markup_percent, presence: true, numericality: true
      validates :tm_other_markup_percent, presence: true, numericality: true
      validates :master_opportunity_service_id, numericality: true, allow_nil: true
      validates :per_hours_orig, numericality: true, allow_nil: true
      validates :opportunity_service_status, length: (0..10), allow_blank: true
      validates :added_opportunity_revision_id, numericality: true, allow_nil: true
      validates :removed_opportunity_revision_id, numericality: true, allow_nil: true
      validates :child_opportunity_service_id, numericality: true, allow_nil: true
      validates :parent_opportunity_service_id, numericality: true, allow_nil: true
      validates :revision_start_work_ticket_id, numericality: true, allow_nil: true
      validates :default_sequence_number, numericality: true, allow_nil: true
      validates :service_item_quantity, numericality: true, allow_nil: true
      validates :change_order_opportunity_service_id, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "OpportunityServiceID" => nil,
          "OpportunityServiceGroupID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "ServiceID" => nil,
          "DisplayName" => nil,
          "ServiceDescription" => nil,
          "OperationNotes" => nil,
          "Occur" => nil,
          "InvoiceType" => nil,
          "AsNeeded" => nil,
          "ComplexityPercent" => nil,
          "PerHours" => nil,
          "ExtendedHours" => nil,
          "PerPrice" => nil,
          "PerPriceOverride" => nil,
          "ExtendedPrice" => nil,
          "LaborTaxable" => nil,
          "MaterialTaxable" => nil,
          "EquipmentTaxable" => nil,
          "SubTaxable" => nil,
          "OtherTaxable" => nil,
          "SortOrder" => nil,
          "TMMaterialMarkupPercent" => nil,
          "RoundPrice" => nil,
          "OverridePricing" => nil,
          "LaborMarkup" => nil,
          "MaterialMarkup" => nil,
          "EquipmentMarkup" => nil,
          "SubMarkup" => nil,
          "OtherMarkup" => nil,
          "NetProfitPercent" => nil,
          "LaborExtendedPrice" => nil,
          "MaterialExtendedPrice" => nil,
          "EquipmentExtendedPrice" => nil,
          "SubExtendedPrice" => nil,
          "OtherExtendedPrice" => nil,
          "TaxableExtendedPrice" => nil,
          "LaborPerExtendedCost" => nil,
          "MaterialPerExtendedCost" => nil,
          "EquipmentPerExtendedCost" => nil,
          "SubPerExtendedCost" => nil,
          "OtherPerExtendedCost" => nil,
          "LaborPerExtendedPrice" => nil,
          "MaterialPerExtendedPrice" => nil,
          "EquipmentPerExtendedPrice" => nil,
          "SubPerExtendedPrice" => nil,
          "OtherPerExtendedPrice" => nil,
          "TaxablePerExtendedPrice" => nil,
          "OpportunityID" => nil,
          "MinimumPrice" => nil,
          "MinimumPriceApplied" => nil,
          "Overhead" => nil,
          "BreakEven" => nil,
          "PerCost" => nil,
          "OverridePrice" => nil,
          "SeparateWorkTicket" => nil,
          "DefaultPayCodeID" => nil,
          "LaborExtendedCost" => nil,
          "MaterialExtendedCost" => nil,
          "EquipmentExtendedCost" => nil,
          "SubExtendedCost" => nil,
          "OtherExtendedCost" => nil,
          "ExtendedCost" => nil,
          "LaborOverhead" => nil,
          "MaterialOverhead" => nil,
          "EquipmentOverhead" => nil,
          "SubOverhead" => nil,
          "OtherOverhead" => nil,
          "LaborProfit" => nil,
          "MaterialProfit" => nil,
          "EquipmentProfit" => nil,
          "SubProfit" => nil,
          "OtherProfit" => nil,
          "TMMinimumHours" => nil,
          "ServiceNameAbrOverride" => nil,
          "PerVisitHours" => nil,
          "PerVisitMaterialsQty" => nil,
          "TMEquipmentMarkupPercent" => nil,
          "TMSubMarkupPercent" => nil,
          "TMOtherMarkupPercent" => nil,
          "MasterOpportunityServiceID" => nil,
          "PerHoursOrig" => nil,
          "OpportunityServiceStatus" => nil,
          "AddedOpportunityRevisionID" => nil,
          "RemovedOpportunityRevisionID" => nil,
          "ChildOpportunityServiceID" => nil,
          "ParentOpportunityServiceID" => nil,
          "RevisionStartWorkTicketID" => nil,
          "DefaultSequenceNumber" => nil,
          "ServiceItemQuantity" => nil,
          "ChangeOrderOpportunityServiceID" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "OpportunityServiceRoutes" => nil,
          "OpportunityServiceDefaultPayCodes" => nil
        }
      end
    end

    class AspireCloudDomainTenantPaymentsODataModelPayment < Base
      attribute :payment_allocations, ArrayOf.new(AspireCloudDomainTenantPaymentsODataModelPaymentAllocation)
      attribute :payment_id, :integer
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :contact_id, :integer
      attribute :contact_name, :string
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :bank_deposit_id, :integer
      attribute :payment_type, :string
      attribute :payment_reference, :string
      attribute :payment_amount, :float
      attribute :payment_date, :datetime
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :payment_note, :string
      attribute :payment_category_id, :integer
      attribute :payment_category_name, :string
      attribute :credit_memo_number, :integer
      attribute :property_id, :integer
      attribute :opportunity_id, :integer
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :sale_amount, :float
      attribute :tax_jurisdiction_id, :integer
      attribute :tax_jurisdiction_name, :string
      attribute :tax_amount, :float
      attribute :taxable_amount, :float
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :electronic_payment_id, :integer
      attribute :billing_address_id, :integer

      validates :payment_id, presence: true, numericality: true
      validates :company_id, numericality: true, allow_nil: true
      validates :company_name, length: (0..100), allow_blank: true
      validates :contact_id, numericality: true, allow_nil: true
      validates :contact_name, length: (0..101), allow_blank: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :bank_deposit_id, numericality: true, allow_nil: true
      validates :payment_type, length: (0..20), allow_blank: true
      validates :payment_reference, length: (0..50), allow_blank: true
      validates :payment_amount, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :payment_note, length: (0..**********), allow_blank: true
      validates :payment_category_id, numericality: true, allow_nil: true
      validates :payment_category_name, length: (0..50), allow_blank: true
      validates :credit_memo_number, numericality: true, allow_nil: true
      validates :property_id, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :sale_amount, numericality: true, allow_nil: true
      validates :tax_jurisdiction_id, numericality: true, allow_nil: true
      validates :tax_jurisdiction_name, length: (0..50), allow_blank: true
      validates :tax_amount, numericality: true, allow_nil: true
      validates :taxable_amount, numericality: true, allow_nil: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :electronic_payment_id, numericality: true, allow_nil: true
      validates :billing_address_id, numericality: true, allow_nil: true

      def attributes
        {
          "PaymentAllocations" => nil,
          "PaymentID" => nil,
          "CompanyID" => nil,
          "CompanyName" => nil,
          "ContactID" => nil,
          "ContactName" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "BankDepositID" => nil,
          "PaymentType" => nil,
          "PaymentReference" => nil,
          "PaymentAmount" => nil,
          "PaymentDate" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "PaymentNote" => nil,
          "PaymentCategoryID" => nil,
          "PaymentCategoryName" => nil,
          "CreditMemoNumber" => nil,
          "PropertyID" => nil,
          "OpportunityID" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "SaleAmount" => nil,
          "TaxJurisdictionID" => nil,
          "TaxJurisdictionName" => nil,
          "TaxAmount" => nil,
          "TaxableAmount" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "ElectronicPaymentID" => nil,
          "BillingAddressID" => nil
        }
      end
    end

    class AspireCloudDomainTenantPropertiesODataModelProperty < Base
      attribute :property_id, :integer
      attribute :property_status_id, :integer
      attribute :property_status_name, :string
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :branch_code, :string
      attribute :account_owner_contact_id, :integer
      attribute :account_owner_contact_name, :string
      attribute :production_manager_contact_id, :integer
      attribute :production_manager_contact_name, :string
      attribute :property_address_id, :integer
      attribute :county_id, :integer
      attribute :property_address_line1, :string
      attribute :property_address_line2, :string
      attribute :property_address_city, :string
      attribute :property_address_state_province_code, :string
      attribute :property_address_zip_code, :string
      attribute :locality_id, :integer
      attribute :locality_name, :string
      attribute :industry_id, :integer
      attribute :industry_name, :string
      attribute :lead_source_id, :integer
      attribute :lead_source_name, :string
      attribute :tax_jurisdiction_id, :integer
      attribute :tax_jurisdiction_name, :string
      attribute :property_group_id, :integer
      attribute :active_opportunity_id, :integer
      attribute :competitor_id, :integer
      attribute :property_group_name, :string
      attribute :property_name, :string
      attribute :property_name_abr, :string
      attribute :sequence_number, :string
      attribute :production_note, :string
      attribute :active, :boolean
      attribute :email_invoice, :boolean
      attribute :budget, :float
      attribute :note, :string
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date, :datetime
      attribute :geo_perimeter, :float
      attribute :geo_location_latitude, :float
      attribute :geo_location_longitude, :float
      attribute :payment_terms_id, :integer
      attribute :payment_terms_name, :string
      attribute :separate_invoices, :boolean
      attribute :website, :string
      attribute :modified_by_user_id, :integer
      attribute :modified_by_user_name, :string
      attribute :modified_date, :datetime
      attribute :drag_drop_geo_location, :boolean
      attribute :gps_updated, :boolean
      attribute :gps_geofence_id, :string
      attribute :snow_note, :string
      attribute :earliest_opportunity_won_date, :datetime
      attribute :property_contacts, ArrayOf.new(AspireCloudDomainTenantPropertiesODataModelPropertyContact)
      attribute :property_tags, ArrayOf.new(AspireCloudDomainTenantPropertiesODataModelPropertyTag)
      attribute :property_takeoff_items, ArrayOf.new(AspireCloudDomainTenantPropertiesODataModelPropertyTakeoffItem)
      attribute :collection_notes, :string
      attribute :integration_id, :string
      attribute :property_type_id, :integer
      attribute :property_type, :string
      attribute :property_type_integration_code, :string

      validates :property_id, presence: true, numericality: true
      validates :property_status_id, numericality: true, allow_nil: true
      validates :property_status_name, length: (0..50), allow_blank: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :branch_code, length: (0..6), allow_blank: true
      validates :account_owner_contact_id, numericality: true, allow_nil: true
      validates :account_owner_contact_name, length: (0..101), allow_blank: true
      validates :production_manager_contact_id, numericality: true, allow_nil: true
      validates :production_manager_contact_name, length: (0..101), allow_blank: true
      validates :property_address_id, numericality: true, allow_nil: true
      validates :county_id, numericality: true, allow_nil: true
      validates :property_address_line1, length: (0..75), allow_blank: true
      validates :property_address_line2, length: (0..75), allow_blank: true
      validates :property_address_city, length: (0..50), allow_blank: true
      validates :property_address_state_province_code, length: (0..3), allow_blank: true
      validates :property_address_zip_code, length: (0..15), allow_blank: true
      validates :locality_id, numericality: true, allow_nil: true
      validates :locality_name, length: (0..50), allow_blank: true
      validates :industry_id, numericality: true, allow_nil: true
      validates :industry_name, length: (0..50), allow_blank: true
      validates :lead_source_id, numericality: true, allow_nil: true
      validates :lead_source_name, length: (0..75), allow_blank: true
      validates :tax_jurisdiction_id, numericality: true, allow_nil: true
      validates :tax_jurisdiction_name, length: (0..50), allow_blank: true
      validates :property_group_id, numericality: true, allow_nil: true
      validates :active_opportunity_id, numericality: true, allow_nil: true
      validates :competitor_id, numericality: true, allow_nil: true
      validates :property_group_name, length: (0..50), allow_blank: true
      validates :property_name, length: (0..100), allow_blank: true
      validates :property_name_abr, length: (0..20), allow_blank: true
      validates :sequence_number, length: (0..10), allow_blank: true
      validates :production_note, length: (0..**********), allow_blank: true
      validates :budget, numericality: true, allow_nil: true
      validates :note, length: (0..**********), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :geo_perimeter, numericality: true, allow_nil: true
      validates :geo_location_latitude, numericality: true, allow_nil: true
      validates :geo_location_longitude, numericality: true, allow_nil: true
      validates :payment_terms_id, numericality: true, allow_nil: true
      validates :payment_terms_name, length: (0..20), allow_blank: true
      validates :website, length: (0..255), allow_blank: true
      validates :modified_by_user_id, numericality: true, allow_nil: true
      validates :modified_by_user_name, length: (0..101), allow_blank: true
      validates :gps_geofence_id, length: (0..50), allow_blank: true
      validates :snow_note, length: (0..8000), allow_blank: true
      validates :collection_notes, length: (0..), allow_blank: true
      validates :integration_id, length: (0..255), allow_blank: true
      validates :property_type_id, numericality: true, allow_nil: true
      validates :property_type_integration_code, length: (0..255), allow_blank: true

      def attributes
        {
          "PropertyID" => nil,
          "PropertyStatusID" => nil,
          "PropertyStatusName" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "BranchCode" => nil,
          "AccountOwnerContactID" => nil,
          "AccountOwnerContactName" => nil,
          "ProductionManagerContactID" => nil,
          "ProductionManagerContactName" => nil,
          "PropertyAddressID" => nil,
          "CountyID" => nil,
          "PropertyAddressLine1" => nil,
          "PropertyAddressLine2" => nil,
          "PropertyAddressCity" => nil,
          "PropertyAddressStateProvinceCode" => nil,
          "PropertyAddressZipCode" => nil,
          "LocalityID" => nil,
          "LocalityName" => nil,
          "IndustryID" => nil,
          "IndustryName" => nil,
          "LeadSourceID" => nil,
          "LeadSourceName" => nil,
          "TaxJurisdictionID" => nil,
          "TaxJurisdictionName" => nil,
          "PropertyGroupID" => nil,
          "ActiveOpportunityID" => nil,
          "CompetitorID" => nil,
          "PropertyGroupName" => nil,
          "PropertyName" => nil,
          "PropertyNameAbr" => nil,
          "SequenceNumber" => nil,
          "ProductionNote" => nil,
          "Active" => nil,
          "EmailInvoice" => nil,
          "Budget" => nil,
          "Note" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDate" => nil,
          "GEOPerimeter" => nil,
          "GEOLocationLatitude" => nil,
          "GEOLocationLongitude" => nil,
          "PaymentTermsID" => nil,
          "PaymentTermsName" => nil,
          "SeparateInvoices" => nil,
          "Website" => nil,
          "ModifiedByUserID" => nil,
          "ModifiedByUserName" => nil,
          "ModifiedDate" => nil,
          "DragDropGeoLocation" => nil,
          "GPSUpdated" => nil,
          "GPSGeofenceID" => nil,
          "SnowNote" => nil,
          "EarliestOpportunityWonDate" => nil,
          "PropertyContacts" => nil,
          "PropertyTags" => nil,
          "PropertyTakeoffItems" => nil,
          "CollectionNotes" => nil,
          "IntegrationID" => nil,
          "PropertyTypeID" => nil,
          "PropertyType" => nil,
          "PropertyTypeIntegrationCode" => nil
        }
      end
    end

    class AspireCloudExternalApiPropertyAvailabilityModelsPropertyAvailabilityRequestModel < Base
      attribute :availability_times, ArrayOf.new(AspireCloudExternalApiPropertyAvailabilityModelsAvailabilityTime)
      attribute :property_id, :integer

      validates :property_id, presence: true, numericality: true, inclusion: { in: (1..**********) }

      def attributes
        {
          "AvailabilityTimes" => nil,
          "PropertyId" => nil
        }
      end
    end

    class AspireCloudDomainTenantRoutesODataModelRoute < Base
      attribute :route_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :route_name, :string
      attribute :hours, :float
      attribute :color, :string
      attribute :crew_leader_contact_id, :integer
      attribute :crew_leader_contact_name, :string
      attribute :active, :boolean
      attribute :manager_contact_id, :integer
      attribute :manager_name, :string
      attribute :route_size, :integer
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :display_order, :integer
      attribute :percent_travel_time, :integer
      attribute :show_daily_plan, :boolean
      attribute :allow_equipment_time_reporting, :boolean
      attribute :equipment_id, :integer
      attribute :equipment_name, :string
      attribute :route_properties, ArrayOf.new(AspireCloudDomainTenantRoutesODataModelRouteProperty)
      attribute :route_services, ArrayOf.new(AspireCloudDomainTenantRoutesODataModelRouteService)
      attribute :route_service_types, ArrayOf.new(AspireCloudDomainTenantRoutesODataModelRouteServiceType)

      validates :route_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :route_name, length: (0..100), allow_blank: true
      validates :hours, numericality: true, allow_nil: true
      validates :color, length: (0..10), allow_blank: true
      validates :crew_leader_contact_id, numericality: true, allow_nil: true
      validates :crew_leader_contact_name, length: (0..101), allow_blank: true
      validates :manager_contact_id, numericality: true, allow_nil: true
      validates :manager_name, length: (0..101), allow_blank: true
      validates :route_size, numericality: true, allow_nil: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :display_order, numericality: true, allow_nil: true
      validates :percent_travel_time, numericality: true, allow_nil: true
      validates :equipment_id, numericality: true, allow_nil: true
      validates :equipment_name, length: (0..250), allow_blank: true

      def attributes
        {
          "RouteID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "RouteName" => nil,
          "Hours" => nil,
          "Color" => nil,
          "CrewLeaderContactID" => nil,
          "CrewLeaderContactName" => nil,
          "Active" => nil,
          "ManagerContactID" => nil,
          "ManagerName" => nil,
          "RouteSize" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "DisplayOrder" => nil,
          "PercentTravelTime" => nil,
          "ShowDailyPlan" => nil,
          "AllowEquipmentTimeReporting" => nil,
          "EquipmentID" => nil,
          "EquipmentName" => nil,
          "RouteProperties" => nil,
          "RouteServices" => nil,
          "RouteServiceTypes" => nil
        }
      end
    end

    class AspireCloudDomainTenantServicesODataModelService < Base
      attribute :service_id, :integer
      attribute :service_type_id, :integer
      attribute :service_type_name, :string
      attribute :service_name, :string
      attribute :service_name_abr, :string
      attribute :display_name, :string
      attribute :service_description, :string
      attribute :labor_taxable, :boolean
      attribute :material_taxable, :boolean
      attribute :equipment_taxable, :boolean
      attribute :sub_taxable, :boolean
      attribute :other_taxable, :boolean
      attribute :active, :boolean
      attribute :approve_ticket_on_completion, :boolean
      attribute :minimum_price, :float
      attribute :default_pay_code_id, :integer
      attribute :default_pay_code_name, :string
      attribute :workers_comp_id, :integer
      attribute :workers_comp_name, :string
      attribute :form_id, :integer
      attribute :start_form_id, :integer
      attribute :form_name, :string
      attribute :contract_service, :boolean
      attribute :sort_order, :integer
      attribute :multi_visit, :boolean
      attribute :service_tax_overrides, ArrayOf.new(AspireCloudDomainTenantServicesODataModelServiceTaxOverride)
      attribute :service_branches, ArrayOf.new(AspireCloudDomainTenantServicesODataModelServiceBranch)
      attribute :all_branches, :boolean

      validates :service_id, presence: true, numericality: true
      validates :service_type_id, numericality: true, allow_nil: true
      validates :service_type_name, length: (0..100), allow_blank: true
      validates :service_name, length: (0..150), allow_blank: true
      validates :service_name_abr, length: (0..20), allow_blank: true
      validates :display_name, length: (0..150), allow_blank: true
      validates :service_description, length: (0..**********), allow_blank: true
      validates :minimum_price, numericality: true, allow_nil: true
      validates :default_pay_code_id, numericality: true, allow_nil: true
      validates :default_pay_code_name, length: (0..60), allow_blank: true
      validates :workers_comp_id, numericality: true, allow_nil: true
      validates :workers_comp_name, length: (0..200), allow_blank: true
      validates :form_id, numericality: true, allow_nil: true
      validates :start_form_id, numericality: true, allow_nil: true
      validates :form_name, length: (0..50), allow_blank: true
      validates :sort_order, numericality: true, allow_nil: true

      def attributes
        {
          "ServiceID" => nil,
          "ServiceTypeID" => nil,
          "ServiceTypeName" => nil,
          "ServiceName" => nil,
          "ServiceNameAbr" => nil,
          "DisplayName" => nil,
          "ServiceDescription" => nil,
          "LaborTaxable" => nil,
          "MaterialTaxable" => nil,
          "EquipmentTaxable" => nil,
          "SubTaxable" => nil,
          "OtherTaxable" => nil,
          "Active" => nil,
          "ApproveTicketOnCompletion" => nil,
          "MinimumPrice" => nil,
          "DefaultPayCodeID" => nil,
          "DefaultPayCodeName" => nil,
          "WorkersCompID" => nil,
          "WorkersCompName" => nil,
          "FormID" => nil,
          "StartFormID" => nil,
          "FormName" => nil,
          "ContractService" => nil,
          "SortOrder" => nil,
          "MultiVisit" => nil,
          "ServiceTaxOverrides" => nil,
          "ServiceBranches" => nil,
          "AllBranches" => nil
        }
      end
    end

    class AspireCloudDomainTenantTaxJurisdictionsODataModelTaxJurisdiction < Base
      attribute :tax_jurisdiction_id, :integer
      attribute :tax_jurisdiction_name, :string
      attribute :state_tax_percent, :float
      attribute :federal_tax_percent, :float
      attribute :active, :boolean
      attribute :tax_entity_jurisdictions, ArrayOf.new(AspireCloudDomainTenantTaxJurisdictionsODataModelTaxEntityJurisdiction)

      validates :tax_jurisdiction_id, presence: true, numericality: true
      validates :tax_jurisdiction_name, length: (0..50), allow_blank: true
      validates :state_tax_percent, numericality: true, allow_nil: true
      validates :federal_tax_percent, numericality: true, allow_nil: true

      def attributes
        {
          "TaxJurisdictionID" => nil,
          "TaxJurisdictionName" => nil,
          "StateTaxPercent" => nil,
          "FederalTaxPercent" => nil,
          "Active" => nil,
          "TaxEntityJurisdictions" => nil
        }
      end
    end

    class AspireCloudDomainTenantUsersODataModelUser < Base
      attribute :user_id, :integer
      attribute :all_branch_access, :boolean
      attribute :branch_access, ArrayOf.new(AspireCloudDomainTenantUsersODataModelBranchAccess)
      attribute :external_contact_reference, :string
      attribute :contact_first_name, :string
      attribute :contact_last_name, :string
      attribute :active, :boolean
      attribute :user_roles, ArrayOf.new(AspireCloudDomainTenantUsersODataModelUserRole)

      validates :user_id, presence: true, numericality: true
      validates :external_contact_reference, length: (1..255), allow_blank: true
      validates :contact_first_name, length: (1..50), allow_blank: true
      validates :contact_last_name, length: (1..50), allow_blank: true

      def attributes
        {
          "UserID" => nil,
          "AllBranchAccess" => nil,
          "BranchAccess" => nil,
          "ExternalContactReference" => nil,
          "ContactFirstName" => nil,
          "ContactLastName" => nil,
          "Active" => nil,
          "UserRoles" => nil
        }
      end
    end

    class AspireCloudDomainTenantWorkTicketsODataModelWorkTicket < Base
      attribute :invoice_number, :integer
      attribute :work_ticket_id, :integer
      attribute :opportunity_service_id, :integer
      attribute :contract_year, :integer
      attribute :occur, :integer
      attribute :annualized_occur, :integer
      attribute :created_date_time, :datetime
      attribute :antic_start_date, :datetime
      attribute :scheduled_start_date, :datetime
      attribute :complete_date, :datetime
      attribute :approved_date, :datetime
      attribute :work_ticket_status_id, :integer
      attribute :work_ticket_status_name, :string
      attribute :notes, :string
      attribute :crew_leader_contact_id, :integer
      attribute :crew_leader_name, :string
      attribute :hours_est, :float
      attribute :hour_cost_est, :float
      attribute :material_cost_est, :float
      attribute :equip_cost_est, :float
      attribute :sub_cost_est, :float
      attribute :other_cost_est, :float
      attribute :price, :float
      attribute :tm_calc_amount, :float
      attribute :tm_override_amount, :float
      attribute :invoiced_amount, :float
      attribute :approved_user_id, :integer
      attribute :approved_user_name, :string
      attribute :work_ticket_number, :integer
      attribute :invoice_id, :integer
      attribute :hours_act, :float
      attribute :warranty_hours_act, :float
      attribute :ot_hours_act, :float
      attribute :labor_cost_act, :float
      attribute :material_cost_act, :float
      attribute :equipment_cost_act, :float
      attribute :sub_cost_act, :float
      attribute :other_cost_act, :float
      attribute :total_cost_act, :float
      attribute :earned_revenue, :float
      attribute :realize_rate_revenue, :float
      attribute :opportunity_id, :integer
      attribute :opportunity_number, :float
      attribute :est_realize_rate_revenue, :float
      attribute :distributed_hours, :float
      attribute :partial_occurrence, :float
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :sub_partial_occurrence, :float
      attribute :work_ticket_status, :string
      attribute :hours_scheduled, :float
      attribute :hours_unscheduled, :float
      attribute :work_ticket_revenues, ArrayOf.new(AspireCloudDomainTenantWorkTicketsODataModelWorkTicketRevenue)
      attribute :reviewed_date_time, :datetime
      attribute :reviewed_user_id, :integer
      attribute :reviewed_user_name, :string
      attribute :start_form_date_time, :datetime
      attribute :start_form_user_id, :integer

      validates :invoice_number, numericality: true, allow_nil: true
      validates :work_ticket_id, presence: true, numericality: true
      validates :opportunity_service_id, numericality: true, allow_nil: true
      validates :contract_year, numericality: true, allow_nil: true
      validates :occur, numericality: true, allow_nil: true
      validates :annualized_occur, numericality: true, allow_nil: true
      validates :work_ticket_status_id, numericality: true, allow_nil: true
      validates :work_ticket_status_name, length: (0..100), allow_blank: true
      validates :notes, length: (0..**********), allow_blank: true
      validates :crew_leader_contact_id, numericality: true, allow_nil: true
      validates :crew_leader_name, length: (0..101), allow_blank: true
      validates :hours_est, numericality: true, allow_nil: true
      validates :hour_cost_est, numericality: true, allow_nil: true
      validates :material_cost_est, numericality: true, allow_nil: true
      validates :equip_cost_est, numericality: true, allow_nil: true
      validates :sub_cost_est, numericality: true, allow_nil: true
      validates :other_cost_est, numericality: true, allow_nil: true
      validates :price, numericality: true, allow_nil: true
      validates :tm_calc_amount, numericality: true, allow_nil: true
      validates :tm_override_amount, numericality: true, allow_nil: true
      validates :invoiced_amount, numericality: true, allow_nil: true
      validates :approved_user_id, numericality: true, allow_nil: true
      validates :approved_user_name, length: (0..101), allow_blank: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :invoice_id, numericality: true, allow_nil: true
      validates :hours_act, numericality: true, allow_nil: true
      validates :warranty_hours_act, numericality: true, allow_nil: true
      validates :ot_hours_act, numericality: true, allow_nil: true
      validates :labor_cost_act, numericality: true, allow_nil: true
      validates :material_cost_act, numericality: true, allow_nil: true
      validates :equipment_cost_act, numericality: true, allow_nil: true
      validates :sub_cost_act, numericality: true, allow_nil: true
      validates :other_cost_act, numericality: true, allow_nil: true
      validates :total_cost_act, numericality: true, allow_nil: true
      validates :earned_revenue, numericality: true, allow_nil: true
      validates :realize_rate_revenue, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :opportunity_number, numericality: true, allow_nil: true
      validates :est_realize_rate_revenue, numericality: true, allow_nil: true
      validates :distributed_hours, numericality: true, allow_nil: true
      validates :partial_occurrence, numericality: true, allow_nil: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :sub_partial_occurrence, presence: true, numericality: true
      validates :work_ticket_status, length: (0..20), allow_blank: true
      validates :hours_scheduled, presence: true, numericality: true
      validates :hours_unscheduled, presence: true, numericality: true
      validates :reviewed_user_id, numericality: true, allow_nil: true
      validates :reviewed_user_name, length: (0..101), allow_blank: true
      validates :start_form_user_id, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceNumber" => nil,
          "WorkTicketID" => nil,
          "OpportunityServiceID" => nil,
          "ContractYear" => nil,
          "Occur" => nil,
          "AnnualizedOccur" => nil,
          "CreatedDateTime" => nil,
          "AnticStartDate" => nil,
          "ScheduledStartDate" => nil,
          "CompleteDate" => nil,
          "ApprovedDate" => nil,
          "WorkTicketStatusID" => nil,
          "WorkTicketStatusName" => nil,
          "Notes" => nil,
          "CrewLeaderContactID" => nil,
          "CrewLeaderName" => nil,
          "HoursEst" => nil,
          "HourCostEst" => nil,
          "MaterialCostEst" => nil,
          "EquipCostEst" => nil,
          "SubCostEst" => nil,
          "OtherCostEst" => nil,
          "Price" => nil,
          "TMCalcAmount" => nil,
          "TMOverrideAmount" => nil,
          "InvoicedAmount" => nil,
          "ApprovedUserID" => nil,
          "ApprovedUserName" => nil,
          "WorkTicketNumber" => nil,
          "InvoiceID" => nil,
          "HoursAct" => nil,
          "WarrantyHoursAct" => nil,
          "OTHoursAct" => nil,
          "LaborCostAct" => nil,
          "MaterialCostAct" => nil,
          "EquipmentCostAct" => nil,
          "SubCostAct" => nil,
          "OtherCostAct" => nil,
          "TotalCostAct" => nil,
          "EarnedRevenue" => nil,
          "RealizeRateRevenue" => nil,
          "OpportunityID" => nil,
          "OpportunityNumber" => nil,
          "EstRealizeRateRevenue" => nil,
          "DistributedHours" => nil,
          "PartialOccurrence" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "SubPartialOccurrence" => nil,
          "WorkTicketStatus" => nil,
          "HoursScheduled" => nil,
          "HoursUnscheduled" => nil,
          "WorkTicketRevenues" => nil,
          "ReviewedDateTime" => nil,
          "ReviewedUserID" => nil,
          "ReviewedUserName" => nil,
          "StartFormDateTime" => nil,
          "StartFormUserId" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelScheduleOfValueGroup < Base
      attribute :id, :integer
      attribute :name, :string
      attribute :available_to_invoice, :boolean
      attribute :sort_order, :integer
      attribute :schedule_of_values, ArrayOf.new(AspireCloudDomainTenantOpportunitiesODataModelScheduleOfValue)

      validates :id, presence: true, numericality: true
      validates :sort_order, presence: true, numericality: true

      def attributes
        {
          "Id" => nil,
          "Name" => nil,
          "AvailableToInvoice" => nil,
          "SortOrder" => nil,
          "ScheduleOfValues" => nil
        }
      end
    end

    class AspireCloudDomainTenantPurchaseReceiptODataModelReceiptItem < Base
      attribute :receipt_item_id, :integer
      attribute :catalog_item_id, :integer
      attribute :catalog_item_name, :string
      attribute :item_name, :string
      attribute :item_quantity, :float
      attribute :item_unit_cost, :float
      attribute :item_extended_cost, :float
      attribute :item_type, :string
      attribute :item_est_unit_cost, :float
      attribute :received_quantity, :float
      attribute :catalog_item_category_id, :integer
      attribute :item_allocations, ArrayOf.new(AspireCloudDomainTenantPurchaseReceiptODataModelItemAllocation)

      validates :receipt_item_id, presence: true, numericality: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :catalog_item_name, length: (0..200), allow_blank: true
      validates :item_name, length: (0..200), allow_blank: true
      validates :item_quantity, numericality: true, allow_nil: true
      validates :item_unit_cost, numericality: true, allow_nil: true
      validates :item_extended_cost, numericality: true, allow_nil: true
      validates :item_type, length: (0..10), allow_blank: true
      validates :item_est_unit_cost, numericality: true, allow_nil: true
      validates :received_quantity, numericality: true, allow_nil: true
      validates :catalog_item_category_id, numericality: true, allow_nil: true

      def attributes
        {
          "ReceiptItemID" => nil,
          "CatalogItemID" => nil,
          "CatalogItemName" => nil,
          "ItemName" => nil,
          "ItemQuantity" => nil,
          "ItemUnitCost" => nil,
          "ItemExtendedCost" => nil,
          "ItemType" => nil,
          "ItemEstUnitCost" => nil,
          "ReceivedQuantity" => nil,
          "CatalogItemCategoryID" => nil,
          "ItemAllocations" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelReceiptItemModel < Base
      attribute :receipt_item_id, :integer
      attribute :catalog_item_id, :integer
      attribute :catalog_item_category_id, :integer
      attribute :item_name, :string
      attribute :item_quantity, :float
      attribute :item_unit_cost, :float
      attribute :item_type, :string
      attribute :item_allocations, ArrayOf.new(AspireCloudExternalApiReceiptsModelItemAllocationModel)
      attribute :receipt_item_price, :float
      attribute :item_est_unit_cost, :float

      validates :receipt_item_id, presence: true, numericality: true
      validates :catalog_item_id, numericality: true, allow_nil: true
      validates :catalog_item_category_id, numericality: true, allow_nil: true
      validates :item_name, presence: true, length: (1..)
      validates :item_quantity, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :item_unit_cost, presence: true, numericality: { greater_than_or_equal_to: 0.0 }
      validates :item_type, presence: true, length: (1..)
      validates :receipt_item_price, presence: true, numericality: true
      validates :item_est_unit_cost, presence: true, numericality: true

      def attributes
        {
          "ReceiptItemID" => nil,
          "CatalogItemID" => nil,
          "CatalogItemCategoryID" => nil,
          "ItemName" => nil,
          "ItemQuantity" => nil,
          "ItemUnitCost" => nil,
          "ItemType" => nil,
          "ItemAllocations" => nil,
          "ReceiptItemPrice" => nil,
          "ItemEstUnitCost" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoicesODataModelInvoiceOpportunityService < Base
      attribute :invoice_opportunity_service_id, :integer
      attribute :description, :string
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :amount, :float
      attribute :taxable_amount, :float
      attribute :state_tax_percent, :float
      attribute :state_tax_amount, :float
      attribute :federal_tax_percent, :float
      attribute :federal_tax_amount, :float
      attribute :complete_date_time, :datetime
      attribute :orig_amount, :float
      attribute :schedule_of_value_description, :string
      attribute :schedule_of_value_quantity, :float
      attribute :schedule_of_value_unit_price, :float
      attribute :schedule_of_value_percent, :float
      attribute :schedule_of_value_group_name, :string
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :created_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :invoice_opportunity_service_items, ArrayOf.new(AspireCloudDomainTenantInvoicesODataModelInvoiceOpportunityServiceItem)

      validates :invoice_opportunity_service_id, presence: true, numericality: true
      validates :description, length: (0..**********), allow_blank: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :amount, numericality: true, allow_nil: true
      validates :taxable_amount, numericality: true, allow_nil: true
      validates :state_tax_percent, numericality: true, allow_nil: true
      validates :state_tax_amount, numericality: true, allow_nil: true
      validates :federal_tax_percent, numericality: true, allow_nil: true
      validates :federal_tax_amount, numericality: true, allow_nil: true
      validates :orig_amount, numericality: true, allow_nil: true
      validates :schedule_of_value_description, length: (0..250), allow_blank: true
      validates :schedule_of_value_quantity, numericality: true, allow_nil: true
      validates :schedule_of_value_unit_price, numericality: true, allow_nil: true
      validates :schedule_of_value_percent, numericality: true, allow_nil: true
      validates :schedule_of_value_group_name, length: (0..250), allow_blank: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceOpportunityServiceID" => nil,
          "Description" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "Amount" => nil,
          "TaxableAmount" => nil,
          "StateTaxPercent" => nil,
          "StateTaxAmount" => nil,
          "FederalTaxPercent" => nil,
          "FederalTaxAmount" => nil,
          "CompleteDateTime" => nil,
          "OrigAmount" => nil,
          "ScheduleOfValueDescription" => nil,
          "ScheduleOfValueQuantity" => nil,
          "ScheduleOfValueUnitPrice" => nil,
          "ScheduleOfValuePercent" => nil,
          "ScheduleOfValueGroupName" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "CreatedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "InvoiceOpportunityServiceItems" => nil
        }
      end
    end

    class AspireCloudDomainTenantOpportunitiesODataModelOpportunity < Base
      attribute :opportunity_id, :integer
      attribute :property_id, :integer
      attribute :opportunity_status_id, :integer
      attribute :opportunity_status_name, :string
      attribute :sales_rep_contact_id, :integer
      attribute :sales_rep_contact_name, :string
      attribute :operations_manager_contact_id, :integer
      attribute :operations_manager_contact_name, :string
      attribute :division_id, :integer
      attribute :division_name, :string
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :sales_type_id, :integer
      attribute :sales_type_name, :string
      attribute :template_opportunity_id, :integer
      attribute :lead_source_id, :integer
      attribute :lead_source_name, :string
      attribute :master_opportunity_id, :integer
      attribute :billing_address_id, :integer
      attribute :billing_address_line1, :string
      attribute :billing_address_line2, :string
      attribute :billing_address_city, :string
      attribute :billing_address_state_province_code, :string
      attribute :billing_address_zip_code, :string
      attribute :opportunity_type, :string
      attribute :master_sequence_num, :integer
      attribute :opportunity_name, :string
      attribute :proposal_description1, :string
      attribute :start_date, :datetime
      attribute :end_date, :datetime
      attribute :bid_due_date, :datetime
      attribute :created_date_time, :datetime
      attribute :approved_date, :datetime
      attribute :proposed_date, :datetime
      attribute :won_date, :datetime
      attribute :lost_date, :datetime
      attribute :probability, :float
      attribute :anticipated_close_date, :datetime
      attribute :invoice_type, :string
      attribute :customer_contract_num, :string
      attribute :customer_po_num, :string
      attribute :budgeted_dollars, :float
      attribute :estimated_dollars, :float
      attribute :state_tax_percent, :float
      attribute :federal_tax_percent, :float
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :approved_user_id, :integer
      attribute :approved_user_name, :string
      attribute :proposed_user_id, :integer
      attribute :proposed_user_name, :string
      attribute :closed_user_id, :integer
      attribute :closed_user_name, :string
      attribute :competitor_id, :integer
      attribute :competitor_name, :string
      attribute :lost_reason, :string
      attribute :billing_contact_id, :integer
      attribute :billing_contact_name, :string
      attribute :proposal_description2, :string
      attribute :opportunity_number, :integer
      attribute :estimated_cost_dollars, :float
      attribute :estimated_overhead_dollars, :float
      attribute :estimated_break_even_dollars, :float
      attribute :estimated_net_profit_percent, :float
      attribute :estimated_net_profit_dollars, :float
      attribute :estimated_gross_margin_percent, :float
      attribute :estimated_gross_margin_dollars, :float
      attribute :estimated_labor_hours, :float
      attribute :estimated_labor_cost_per_hour, :float
      attribute :estimated_realize_rate, :float
      attribute :estimated_equipment_cost, :float
      attribute :estimated_labor_cost, :float
      attribute :estimated_material_cost, :float
      attribute :estimated_other_cost, :float
      attribute :estimated_sub_cost, :float
      attribute :actual_cost_dollars, :float
      attribute :actual_gross_margin_percent, :float
      attribute :actual_gross_margin_dollars, :float
      attribute :actual_labor_hours, :float
      attribute :actual_labor_cost_per_hour, :float
      attribute :actual_earned_revenue, :float
      attribute :invoice_note, :string
      attribute :percent_complete, :float
      attribute :renewal_date, :datetime
      attribute :modified_by_user_id, :integer
      attribute :modified_by_user_name, :string
      attribute :modified_date, :datetime
      attribute :override_payment_terms_id, :integer
      attribute :override_payment_terms, :string
      attribute :retainage_percent, :float
      attribute :estimator_notes, :string
      attribute :electronic_payments_override_convenience_fee, :float
      attribute :opportunity_canceled_reason_id, :integer
      attribute :opportunity_canceled_reason, :string
      attribute :retainage_maturity_date, :datetime
      attribute :district_name, :string
      attribute :job_status_name, :string
      attribute :region_name, :string
      attribute :payment_terms_id, :integer
      attribute :billing_company_id, :integer
      attribute :billing_company_name, :string
      attribute :branch_code, :string
      attribute :property_name, :string
      attribute :division_code, :string
      attribute :taxable_percent, :float
      attribute :taxable_amount, :float
      attribute :tm_per_service_taxable_amount, :float
      attribute :actual_cost_sub, :float
      attribute :actual_cost_material, :float
      attribute :actual_cost_labor, :float
      attribute :complete_date, :datetime
      attribute :opportunity_revisions, ArrayOf.new(AspireCloudDomainTenantOpportunitiesODataModelOpportunityRevision)
      attribute :opportunity_billings, ArrayOf.new(AspireCloudDomainTenantOpportunitiesODataModelOpportunityBilling)
      attribute :schedule_of_value_groups, ArrayOf.new(AspireCloudDomainTenantOpportunitiesODataModelScheduleOfValueGroup)
      attribute :opportunity_stage_id, :integer
      attribute :opportunity_stage, :string
      attribute :opportunity_stage_name, :string
      attribute :opportunity_status, :string

      validates :opportunity_id, presence: true, numericality: true
      validates :property_id, numericality: true, allow_nil: true
      validates :opportunity_status_id, numericality: true, allow_nil: true
      validates :opportunity_status_name, length: (0..100), allow_blank: true
      validates :sales_rep_contact_id, numericality: true, allow_nil: true
      validates :sales_rep_contact_name, length: (0..101), allow_blank: true
      validates :operations_manager_contact_id, numericality: true, allow_nil: true
      validates :operations_manager_contact_name, length: (0..101), allow_blank: true
      validates :division_id, numericality: true, allow_nil: true
      validates :division_name, length: (0..50), allow_blank: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :sales_type_id, numericality: true, allow_nil: true
      validates :sales_type_name, length: (0..30), allow_blank: true
      validates :template_opportunity_id, numericality: true, allow_nil: true
      validates :lead_source_id, numericality: true, allow_nil: true
      validates :lead_source_name, length: (0..75), allow_blank: true
      validates :master_opportunity_id, numericality: true, allow_nil: true
      validates :billing_address_id, numericality: true, allow_nil: true
      validates :billing_address_line1, length: (0..75), allow_blank: true
      validates :billing_address_line2, length: (0..75), allow_blank: true
      validates :billing_address_city, length: (0..50), allow_blank: true
      validates :billing_address_state_province_code, length: (0..3), allow_blank: true
      validates :billing_address_zip_code, length: (0..15), allow_blank: true
      validates :opportunity_type, length: (0..10), allow_blank: true
      validates :master_sequence_num, numericality: true, allow_nil: true
      validates :opportunity_name, length: (0..200), allow_blank: true
      validates :proposal_description1, length: (0..**********), allow_blank: true
      validates :probability, numericality: true, allow_nil: true
      validates :invoice_type, length: (0..35), allow_blank: true
      validates :customer_contract_num, length: (0..40), allow_blank: true
      validates :customer_po_num, length: (0..20), allow_blank: true
      validates :budgeted_dollars, numericality: true, allow_nil: true
      validates :estimated_dollars, numericality: true, allow_nil: true
      validates :state_tax_percent, numericality: true, allow_nil: true
      validates :federal_tax_percent, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :approved_user_id, numericality: true, allow_nil: true
      validates :approved_user_name, length: (0..101), allow_blank: true
      validates :proposed_user_id, numericality: true, allow_nil: true
      validates :proposed_user_name, length: (0..101), allow_blank: true
      validates :closed_user_id, numericality: true, allow_nil: true
      validates :closed_user_name, length: (0..101), allow_blank: true
      validates :competitor_id, numericality: true, allow_nil: true
      validates :competitor_name, length: (0..100), allow_blank: true
      validates :lost_reason, length: (0..50), allow_blank: true
      validates :billing_contact_id, numericality: true, allow_nil: true
      validates :billing_contact_name, length: (0..101), allow_blank: true
      validates :proposal_description2, length: (0..**********), allow_blank: true
      validates :opportunity_number, numericality: true, allow_nil: true
      validates :estimated_cost_dollars, numericality: true, allow_nil: true
      validates :estimated_overhead_dollars, numericality: true, allow_nil: true
      validates :estimated_break_even_dollars, numericality: true, allow_nil: true
      validates :estimated_net_profit_percent, numericality: true, allow_nil: true
      validates :estimated_net_profit_dollars, numericality: true, allow_nil: true
      validates :estimated_gross_margin_percent, numericality: true, allow_nil: true
      validates :estimated_gross_margin_dollars, numericality: true, allow_nil: true
      validates :estimated_labor_hours, numericality: true, allow_nil: true
      validates :estimated_labor_cost_per_hour, numericality: true, allow_nil: true
      validates :estimated_realize_rate, numericality: true, allow_nil: true
      validates :estimated_equipment_cost, numericality: true, allow_nil: true
      validates :estimated_labor_cost, numericality: true, allow_nil: true
      validates :estimated_material_cost, numericality: true, allow_nil: true
      validates :estimated_other_cost, numericality: true, allow_nil: true
      validates :estimated_sub_cost, numericality: true, allow_nil: true
      validates :actual_cost_dollars, numericality: true, allow_nil: true
      validates :actual_gross_margin_percent, numericality: true, allow_nil: true
      validates :actual_gross_margin_dollars, numericality: true, allow_nil: true
      validates :actual_labor_hours, numericality: true, allow_nil: true
      validates :actual_labor_cost_per_hour, numericality: true, allow_nil: true
      validates :actual_earned_revenue, numericality: true, allow_nil: true
      validates :invoice_note, length: (0..**********), allow_blank: true
      validates :percent_complete, numericality: true, allow_nil: true
      validates :modified_by_user_id, numericality: true, allow_nil: true
      validates :modified_by_user_name, length: (0..101), allow_blank: true
      validates :override_payment_terms_id, numericality: true, allow_nil: true
      validates :override_payment_terms, length: (0..20), allow_blank: true
      validates :retainage_percent, numericality: true, allow_nil: true
      validates :estimator_notes, length: (0..65535), allow_blank: true
      validates :electronic_payments_override_convenience_fee, numericality: true, allow_nil: true
      validates :opportunity_canceled_reason_id, numericality: true, allow_nil: true
      validates :opportunity_canceled_reason, length: (0..100), allow_blank: true
      validates :district_name, length: (0..200), allow_blank: true
      validates :job_status_name, length: (0..100), allow_blank: true
      validates :region_name, length: (0..250), allow_blank: true
      validates :payment_terms_id, numericality: true, allow_nil: true
      validates :billing_company_id, numericality: true, allow_nil: true
      validates :billing_company_name, length: (0..100), allow_blank: true
      validates :branch_code, length: (0..6), allow_blank: true
      validates :property_name, length: (0..100), allow_blank: true
      validates :division_code, length: (0..6), allow_blank: true
      validates :taxable_percent, numericality: true, allow_nil: true
      validates :taxable_amount, numericality: true, allow_nil: true
      validates :tm_per_service_taxable_amount, presence: true, numericality: true
      validates :actual_cost_sub, numericality: true, allow_nil: true
      validates :actual_cost_material, numericality: true, allow_nil: true
      validates :actual_cost_labor, numericality: true, allow_nil: true
      validates :opportunity_stage_id, numericality: true, allow_nil: true
      validates :opportunity_stage, length: (0..50), allow_blank: true
      validates :opportunity_stage_name, length: (0..100), allow_blank: true
      validates :opportunity_status, length: (0..20), allow_blank: true

      def attributes
        {
          "OpportunityID" => nil,
          "PropertyID" => nil,
          "OpportunityStatusID" => nil,
          "OpportunityStatusName" => nil,
          "SalesRepContactID" => nil,
          "SalesRepContactName" => nil,
          "OperationsManagerContactID" => nil,
          "OperationsManagerContactName" => nil,
          "DivisionID" => nil,
          "DivisionName" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "SalesTypeID" => nil,
          "SalesTypeName" => nil,
          "TemplateOpportunityID" => nil,
          "LeadSourceID" => nil,
          "LeadSourceName" => nil,
          "MasterOpportunityID" => nil,
          "BillingAddressID" => nil,
          "BillingAddressLine1" => nil,
          "BillingAddressLine2" => nil,
          "BillingAddressCity" => nil,
          "BillingAddressStateProvinceCode" => nil,
          "BillingAddressZipCode" => nil,
          "OpportunityType" => nil,
          "MasterSequenceNum" => nil,
          "OpportunityName" => nil,
          "ProposalDescription1" => nil,
          "StartDate" => nil,
          "EndDate" => nil,
          "BidDueDate" => nil,
          "CreatedDateTime" => nil,
          "ApprovedDate" => nil,
          "ProposedDate" => nil,
          "WonDate" => nil,
          "LostDate" => nil,
          "Probability" => nil,
          "AnticipatedCloseDate" => nil,
          "InvoiceType" => nil,
          "CustomerContractNum" => nil,
          "CustomerPONum" => nil,
          "BudgetedDollars" => nil,
          "EstimatedDollars" => nil,
          "StateTaxPercent" => nil,
          "FederalTaxPercent" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "ApprovedUserID" => nil,
          "ApprovedUserName" => nil,
          "ProposedUserID" => nil,
          "ProposedUserName" => nil,
          "ClosedUserID" => nil,
          "ClosedUserName" => nil,
          "CompetitorID" => nil,
          "CompetitorName" => nil,
          "LostReason" => nil,
          "BillingContactID" => nil,
          "BillingContactName" => nil,
          "ProposalDescription2" => nil,
          "OpportunityNumber" => nil,
          "EstimatedCostDollars" => nil,
          "EstimatedOverheadDollars" => nil,
          "EstimatedBreakEvenDollars" => nil,
          "EstimatedNetProfitPercent" => nil,
          "EstimatedNetProfitDollars" => nil,
          "EstimatedGrossMarginPercent" => nil,
          "EstimatedGrossMarginDollars" => nil,
          "EstimatedLaborHours" => nil,
          "EstimatedLaborCostPerHour" => nil,
          "EstimatedRealizeRate" => nil,
          "EstimatedEquipmentCost" => nil,
          "EstimatedLaborCost" => nil,
          "EstimatedMaterialCost" => nil,
          "EstimatedOtherCost" => nil,
          "EstimatedSubCost" => nil,
          "ActualCostDollars" => nil,
          "ActualGrossMarginPercent" => nil,
          "ActualGrossMarginDollars" => nil,
          "ActualLaborHours" => nil,
          "ActualLaborCostPerHour" => nil,
          "ActualEarnedRevenue" => nil,
          "InvoiceNote" => nil,
          "PercentComplete" => nil,
          "RenewalDate" => nil,
          "ModifiedByUserID" => nil,
          "ModifiedByUserName" => nil,
          "ModifiedDate" => nil,
          "OverridePaymentTermsID" => nil,
          "OverridePaymentTerms" => nil,
          "RetainagePercent" => nil,
          "EstimatorNotes" => nil,
          "ElectronicPaymentsOverrideConvenienceFee" => nil,
          "OpportunityCanceledReasonID" => nil,
          "OpportunityCanceledReason" => nil,
          "RetainageMaturityDate" => nil,
          "DistrictName" => nil,
          "JobStatusName" => nil,
          "RegionName" => nil,
          "PaymentTermsID" => nil,
          "BillingCompanyID" => nil,
          "BillingCompanyName" => nil,
          "BranchCode" => nil,
          "PropertyName" => nil,
          "DivisionCode" => nil,
          "TaxablePercent" => nil,
          "TaxableAmount" => nil,
          "TMPerServiceTaxableAmount" => nil,
          "ActualCostSub" => nil,
          "ActualCostMaterial" => nil,
          "ActualCostLabor" => nil,
          "CompleteDate" => nil,
          "OpportunityRevisions" => nil,
          "OpportunityBillings" => nil,
          "ScheduleOfValueGroups" => nil,
          "OpportunityStageID" => nil,
          "OpportunityStage" => nil,
          "OpportunityStageName" => nil,
          "OpportunityStatus" => nil
        }
      end
    end

    class AspireCloudDomainTenantPurchaseReceiptODataModelReceipt < Base
      attribute :receipt_id, :integer
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :branch_code, :string
      attribute :vendor_id, :integer
      attribute :receipt_number, :integer
      attribute :vendor_invoice_num, :string
      attribute :vendor_invoice_date, :datetime
      attribute :receipt_status_id, :integer
      attribute :receipt_status_name, :string
      attribute :work_ticket_id, :integer
      attribute :work_ticket_number, :integer
      attribute :created_date_time, :datetime
      attribute :created_by_user_id, :integer
      attribute :created_by_user_name, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :received_date, :datetime
      attribute :received_user_id, :integer
      attribute :received_by_user_name, :string
      attribute :approved_date, :datetime
      attribute :approved_user_id, :integer
      attribute :approved_by_user_name, :string
      attribute :receipt_note, :string
      attribute :receipt_total_cost, :float
      attribute :sync_date, :datetime
      attribute :sync_error, :string
      attribute :purchase_address_location_name, :string
      attribute :shipped_to_address_id, :integer
      attribute :is_purchase_credit, :boolean
      attribute :master_receipt_id, :integer
      attribute :revision_number, :integer
      attribute :job_inventory, :boolean
      attribute :inventory_location_id, :integer
      attribute :opportunity_id, :integer
      attribute :opportunity_number, :integer
      attribute :receipt_items, ArrayOf.new(AspireCloudDomainTenantPurchaseReceiptODataModelReceiptItem)
      attribute :receipt_extra_costs, ArrayOf.new(AspireCloudDomainTenantPurchaseReceiptODataModelReceiptExtraCost)

      validates :receipt_id, presence: true, numericality: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :branch_code, length: (0..6), allow_blank: true
      validates :vendor_id, numericality: true, allow_nil: true
      validates :receipt_number, numericality: true, allow_nil: true
      validates :vendor_invoice_num, length: (0..50), allow_blank: true
      validates :receipt_status_id, numericality: true, allow_nil: true
      validates :receipt_status_name, length: (0..50), allow_blank: true
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :work_ticket_number, numericality: true, allow_nil: true
      validates :created_by_user_id, numericality: true, allow_nil: true
      validates :created_by_user_name, length: (0..101), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true
      validates :received_user_id, numericality: true, allow_nil: true
      validates :received_by_user_name, length: (0..101), allow_blank: true
      validates :approved_user_id, numericality: true, allow_nil: true
      validates :approved_by_user_name, length: (0..101), allow_blank: true
      validates :receipt_note, length: (0..**********), allow_blank: true
      validates :receipt_total_cost, numericality: true, allow_nil: true
      validates :sync_error, length: (0..**********), allow_blank: true
      validates :purchase_address_location_name, length: (0..75), allow_blank: true
      validates :shipped_to_address_id, numericality: true, allow_nil: true
      validates :master_receipt_id, numericality: true, allow_nil: true
      validates :revision_number, numericality: true, allow_nil: true
      validates :inventory_location_id, numericality: true, allow_nil: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :opportunity_number, numericality: true, allow_nil: true

      def attributes
        {
          "ReceiptID" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "BranchCode" => nil,
          "VendorID" => nil,
          "ReceiptNumber" => nil,
          "VendorInvoiceNum" => nil,
          "VendorInvoiceDate" => nil,
          "ReceiptStatusID" => nil,
          "ReceiptStatusName" => nil,
          "WorkTicketID" => nil,
          "WorkTicketNumber" => nil,
          "CreatedDateTime" => nil,
          "CreatedByUserID" => nil,
          "CreatedByUserName" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "ReceivedDate" => nil,
          "ReceivedUserID" => nil,
          "ReceivedByUserName" => nil,
          "ApprovedDate" => nil,
          "ApprovedUserID" => nil,
          "ApprovedByUserName" => nil,
          "ReceiptNote" => nil,
          "ReceiptTotalCost" => nil,
          "SyncDate" => nil,
          "SyncError" => nil,
          "PurchaseAddressLocationName" => nil,
          "ShippedToAddressID" => nil,
          "IsPurchaseCredit" => nil,
          "MasterReceiptID" => nil,
          "RevisionNumber" => nil,
          "JobInventory" => nil,
          "InventoryLocationID" => nil,
          "OpportunityID" => nil,
          "OpportunityNumber" => nil,
          "ReceiptItems" => nil,
          "ReceiptExtraCosts" => nil
        }
      end
    end

    class AspireCloudExternalApiReceiptsModelReceiptPost < Base
      attribute :receipt_id, :integer
      attribute :branch_id, :integer
      attribute :vendor_id, :integer
      attribute :vendor_invoice_num, :string
      attribute :vendor_invoice_date, :datetime
      attribute :work_ticket_id, :integer
      attribute :receipt_note, :string
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string
      attribute :inventory_location_id, :integer
      attribute :receipt_total_cost, :float
      attribute :receipt_items, ArrayOf.new(AspireCloudExternalApiReceiptsModelReceiptItemModel)
      attribute :receipt_extra_costs, ArrayOf.new(AspireCloudExternalApiReceiptsModelReceiptExtraCost)

      validates :receipt_id, presence: true, numericality: true
      validates :branch_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :vendor_id, presence: true, numericality: true, inclusion: { in: (1..**********) }
      validates :work_ticket_id, numericality: true, allow_nil: true
      validates :inventory_location_id, numericality: true, allow_nil: true
      validates :receipt_total_cost, presence: true, numericality: true

      def attributes
        {
          "ReceiptID" => nil,
          "BranchID" => nil,
          "VendorID" => nil,
          "VendorInvoiceNum" => nil,
          "VendorInvoiceDate" => nil,
          "WorkTicketID" => nil,
          "ReceiptNote" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil,
          "InventoryLocationID" => nil,
          "ReceiptTotalCost" => nil,
          "ReceiptItems" => nil,
          "ReceiptExtraCosts" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoicesODataModelInvoiceOpportunity < Base
      attribute :invoice_opportunity_id, :integer
      attribute :invoice_type, :string
      attribute :opportunity_id, :integer
      attribute :opportunity_number, :integer
      attribute :amount, :float
      attribute :taxable_amount, :float
      attribute :state_tax_percent, :float
      attribute :state_tax_amount, :float
      attribute :federal_tax_percent, :float
      attribute :federal_tax_amount, :float
      attribute :description, :string
      attribute :total_amount, :float
      attribute :orig_amount, :float
      attribute :orig_total_amount, :float
      attribute :invoice_adjustment_type_id, :integer
      attribute :invoice_adjustment_type_name, :string
      attribute :retainage_percent, :float
      attribute :opportunity_name, :string
      attribute :customer_po_number, :string
      attribute :customer_contract_number, :string
      attribute :division_name, :string
      attribute :invoice_opportunity_services, ArrayOf.new(AspireCloudDomainTenantInvoicesODataModelInvoiceOpportunityService)

      validates :invoice_opportunity_id, presence: true, numericality: true
      validates :invoice_type, length: (0..35), allow_blank: true
      validates :opportunity_id, numericality: true, allow_nil: true
      validates :opportunity_number, numericality: true, allow_nil: true
      validates :amount, numericality: true, allow_nil: true
      validates :taxable_amount, numericality: true, allow_nil: true
      validates :state_tax_percent, numericality: true, allow_nil: true
      validates :state_tax_amount, numericality: true, allow_nil: true
      validates :federal_tax_percent, numericality: true, allow_nil: true
      validates :federal_tax_amount, numericality: true, allow_nil: true
      validates :description, length: (0..**********), allow_blank: true
      validates :total_amount, numericality: true, allow_nil: true
      validates :orig_amount, numericality: true, allow_nil: true
      validates :orig_total_amount, numericality: true, allow_nil: true
      validates :invoice_adjustment_type_id, numericality: true, allow_nil: true
      validates :invoice_adjustment_type_name, length: (0..50), allow_blank: true
      validates :retainage_percent, numericality: true, allow_nil: true

      def attributes
        {
          "InvoiceOpportunityID" => nil,
          "InvoiceType" => nil,
          "OpportunityID" => nil,
          "OpportunityNumber" => nil,
          "Amount" => nil,
          "TaxableAmount" => nil,
          "StateTaxPercent" => nil,
          "StateTaxAmount" => nil,
          "FederalTaxPercent" => nil,
          "FederalTaxAmount" => nil,
          "Description" => nil,
          "TotalAmount" => nil,
          "OrigAmount" => nil,
          "OrigTotalAmount" => nil,
          "InvoiceAdjustmentTypeID" => nil,
          "InvoiceAdjustmentTypeName" => nil,
          "RetainagePercent" => nil,
          "OpportunityName" => nil,
          "CustomerPONumber" => nil,
          "CustomerContractNumber" => nil,
          "DivisionName" => nil,
          "InvoiceOpportunityServices" => nil
        }
      end
    end

    class AspireCloudDomainTenantInvoicesODataModelInvoice < Base
      attribute :invoice_number, :integer
      attribute :invoice_id, :integer
      attribute :invoice_batch_id, :integer
      attribute :invoice_batch_number, :integer
      attribute :invoice_accounting_period, :string
      attribute :branch_id, :integer
      attribute :branch_name, :string
      attribute :branch_code, :string
      attribute :property_id, :integer
      attribute :property_name, :string
      attribute :primary_contact_id, :integer
      attribute :primary_contact_name, :string
      attribute :billing_contact_id, :integer
      attribute :billing_contact_name, :string
      attribute :invoice_date, :datetime
      attribute :amount, :float
      attribute :invoice_address_id, :integer
      attribute :address_line1, :string
      attribute :address_line2, :string
      attribute :city, :string
      attribute :state_province_code, :string
      attribute :zip_code, :string
      attribute :amount_remaining, :float
      attribute :orig_amount, :float
      attribute :payment_terms_id, :integer
      attribute :payment_terms_name, :string
      attribute :invoice_note, :string
      attribute :email_status, :string
      attribute :due_date, :datetime
      attribute :report_layout_id, :integer
      attribute :report_layout_name, :string
      attribute :company_id, :integer
      attribute :company_name, :string
      attribute :tax_jurisdiction_id, :integer
      attribute :tax_jurisdiction_name, :string
      attribute :invoice_number_prefix, :string
      attribute :last_modified_date_time, :datetime
      attribute :last_modified_by_user_id, :integer
      attribute :last_modified_by_user_name, :string
      attribute :completed_date_time, :datetime
      attribute :invoice_opportunities, ArrayOf.new(AspireCloudDomainTenantInvoicesODataModelInvoiceOpportunity)

      validates :invoice_number, presence: true, numericality: true
      validates :invoice_id, presence: true, numericality: true
      validates :invoice_batch_id, numericality: true, allow_nil: true
      validates :invoice_batch_number, numericality: true, allow_nil: true
      validates :invoice_accounting_period, length: (0..55), allow_blank: true
      validates :branch_id, numericality: true, allow_nil: true
      validates :branch_name, length: (0..50), allow_blank: true
      validates :branch_code, length: (0..6), allow_blank: true
      validates :property_id, numericality: true, allow_nil: true
      validates :property_name, length: (0..100), allow_blank: true
      validates :primary_contact_id, numericality: true, allow_nil: true
      validates :primary_contact_name, length: (0..101), allow_blank: true
      validates :billing_contact_id, numericality: true, allow_nil: true
      validates :billing_contact_name, length: (0..101), allow_blank: true
      validates :invoice_date, presence: true
      validates :amount, numericality: true, allow_nil: true
      validates :invoice_address_id, numericality: true, allow_nil: true
      validates :address_line1, length: (0..75), allow_blank: true
      validates :address_line2, length: (0..75), allow_blank: true
      validates :city, length: (0..50), allow_blank: true
      validates :state_province_code, length: (0..3), allow_blank: true
      validates :zip_code, length: (0..15), allow_blank: true
      validates :amount_remaining, numericality: true, allow_nil: true
      validates :orig_amount, numericality: true, allow_nil: true
      validates :payment_terms_id, numericality: true, allow_nil: true
      validates :payment_terms_name, length: (0..20), allow_blank: true
      validates :invoice_note, length: (0..**********), allow_blank: true
      validates :email_status, length: (0..10), allow_blank: true
      validates :report_layout_id, numericality: true, allow_nil: true
      validates :report_layout_name, length: (0..50), allow_blank: true
      validates :company_id, numericality: true, allow_nil: true
      validates :company_name, length: (0..100), allow_blank: true
      validates :tax_jurisdiction_id, numericality: true, allow_nil: true
      validates :tax_jurisdiction_name, length: (0..50), allow_blank: true
      validates :invoice_number_prefix, length: (0..20), allow_blank: true
      validates :last_modified_by_user_id, numericality: true, allow_nil: true
      validates :last_modified_by_user_name, length: (0..101), allow_blank: true

      def attributes
        {
          "InvoiceNumber" => nil,
          "InvoiceID" => nil,
          "InvoiceBatchID" => nil,
          "InvoiceBatchNumber" => nil,
          "InvoiceAccountingPeriod" => nil,
          "BranchID" => nil,
          "BranchName" => nil,
          "BranchCode" => nil,
          "PropertyID" => nil,
          "PropertyName" => nil,
          "PrimaryContactID" => nil,
          "PrimaryContactName" => nil,
          "BillingContactID" => nil,
          "BillingContactName" => nil,
          "InvoiceDate" => nil,
          "Amount" => nil,
          "InvoiceAddressID" => nil,
          "AddressLine1" => nil,
          "AddressLine2" => nil,
          "City" => nil,
          "StateProvinceCode" => nil,
          "ZipCode" => nil,
          "AmountRemaining" => nil,
          "OrigAmount" => nil,
          "PaymentTermsID" => nil,
          "PaymentTermsName" => nil,
          "InvoiceNote" => nil,
          "EmailStatus" => nil,
          "DueDate" => nil,
          "ReportLayoutID" => nil,
          "ReportLayoutName" => nil,
          "CompanyID" => nil,
          "CompanyName" => nil,
          "TaxJurisdictionID" => nil,
          "TaxJurisdictionName" => nil,
          "InvoiceNumberPrefix" => nil,
          "LastModifiedDateTime" => nil,
          "LastModifiedByUserID" => nil,
          "LastModifiedByUserName" => nil,
          "CompletedDateTime" => nil,
          "InvoiceOpportunities" => nil
        }
      end
    end
  end
end
