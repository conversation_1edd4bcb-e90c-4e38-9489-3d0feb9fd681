# frozen_string_literal: true

# This file was generated by a script. Direct modifications are discouraged.
# source: lib/generators/swagger_client/swagger_client_generator.rb
# command: bin/rails generate aspire --schema_path=spec/support/aspire.openapi.json

require_relative "./models"

module AspireClient
  module Resources
    def self.response_type(resource)
      RESOURCE_MAP.fetch(resource).const_get(:RESPONSE_TYPE_MAPPING)
    end

    class Base
      attr_reader :client

      def initialize(client)
        @client = client
      end
    end

    class Activities < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantActivitiesODataModelActivity

      def get(query: {})
        path = "/Activities"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantActivitiesODataModelActivity.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ActivityCategories < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantActivityCategoriesODataModelActivityCategory

      def get(query: {})
        path = "/ActivityCategories"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantActivityCategoriesODataModelActivityCategory.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ActivityCommentHistories < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantActivitiesODataModelActivityCommentHistory

      def get(query: {})
        path = "/ActivityCommentHistories"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantActivitiesODataModelActivityCommentHistory.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Addresses < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantAddressesODataModelAddress

      def get(query: {})
        path = "/Addresses"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantAddressesODataModelAddress.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Attachments < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantAttachmentsODataModelAttachment

      def get(query: {})
        path = "/Attachments"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantAttachmentsODataModelAttachment.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Attachments"
        request_body = Models::AspireCloudExternalApiAttachmentsModelsAttachmentUploadRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def new_link(query: {})
        path = "/Attachments/NewLink"
        result = client.get(path, query)
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def attachment_file_data(query: {})
        path = "/Attachments/AttachmentFileData"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudExternalApiAttachmentsModelsAttachmentFileData.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class AttachmentTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantAttachmentTypesAttachmentType

      def get(query: {})
        path = "/AttachmentTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantAttachmentTypesAttachmentType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Authorization < Base
      def post(query: {}, **kwargs)
        path = "/Authorization"
        request_body = Models::AspireCloudDomainConfigExternalApiUsersApiAuthenticationRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudDomainConfigExternalApiUsersApiAuthenticationResult.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def refresh_token(query: {}, **kwargs)
        path = "/Authorization/RefreshToken"
        request_body = Models::AspireCloudDomainConfigExternalApiUsersApiRefreshTokenRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudDomainConfigExternalApiUsersApiAuthenticationResult.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class BankDeposits < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantBankDepositsODataModelBankDeposit

      def get(query: {})
        path = "/BankDeposits"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantBankDepositsODataModelBankDeposit.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Branches < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantBranchesODataModelBranch

      def get(query: {})
        path = "/Branches"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantBranchesODataModelBranch.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class CatalogItemCategories < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantCatalogItemCategoriesODataModelCatalogItemCategory

      def get(query: {})
        path = "/CatalogItemCategories"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantCatalogItemCategoriesODataModelCatalogItemCategory.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class CatalogItems < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantCatalogItemsODataModelCatalogItem

      def get(query: {})
        path = "/CatalogItems"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantCatalogItemsODataModelCatalogItem.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/CatalogItems"
        request_body = Models::AspireCloudExternalApiCatalogItemsModelRequestCatalogItemInsert.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/CatalogItems"
        request_body = Models::AspireCloudExternalApiCatalogItemsModelRequestCatalogItemUpdate.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Certifications < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantCertificationsODataModelCertification

      def get(query: {})
        path = "/Certifications"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantCertificationsODataModelCertification.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class CertificationTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantCertificationTypesODataModelCertificationType

      def get(query: {})
        path = "/CertificationTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantCertificationTypesODataModelCertificationType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ClockTimes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantClockTimesODataModelClockTime

      def get(query: {})
        path = "/ClockTimes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantClockTimesODataModelClockTime.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/ClockTimes"
        request_body = Models::AspireCloudExternalApiClockTimesModelClockTimeInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Companies < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantCompaniesODataModelCompany

      def get(query: {})
        path = "/Companies"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantCompaniesODataModelCompany.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Companies"
        request_body = Models::AspireCloudExternalApiCompaniesModelRequestCompanyInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiCompaniesModelResponseCompanyResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/Companies"
        request_body = Models::AspireCloudExternalApiCompaniesModelRequestCompanyUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiCompaniesModelResponseCompanyResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Contacts < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantContactsODataModelContact

      def post(query: {}, **kwargs)
        path = "/Contacts"
        request_body = Models::AspireCloudExternalApiContactsModelContactInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/Contacts"
        request_body = Models::AspireCloudExternalApiContactsModelContactUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def get(query: {})
        path = "/Contacts"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantContactsODataModelContact.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ContactTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantContactsODataModelContactType

      def get(query: {})
        path = "/ContactTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantContactsODataModelContactType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class DivisionIntegrationCodes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantDivisionIntegrationCodesODataModelDivisionIntegrationCode

      def get(query: {})
        path = "/DivisionIntegrationCodes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantDivisionIntegrationCodesODataModelDivisionIntegrationCode.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Divisions < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantDivisionsODataModelDivision

      def get(query: {})
        path = "/Divisions"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantDivisionsODataModelDivision.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EmployeeIncidents < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEmployeeIncidentsODataModelEmployeeIncident

      def get(query: {})
        path = "/EmployeeIncidents"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEmployeeIncidentsODataModelEmployeeIncident.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EmployeeIncidentTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEmployeeIncidentsODataModelEmployeeIncidentType

      def get(query: {})
        path = "/EmployeeIncidentTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEmployeeIncidentsODataModelEmployeeIncidentType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentClasses < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentClassODataModelEquipmentClass

      def get(query: {})
        path = "/EquipmentClasses"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentClassODataModelEquipmentClass.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentDisposalReasons < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentDisposalReasonsODataModelEquipmentDisposalReason

      def get(query: {})
        path = "/EquipmentDisposalReasons"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentDisposalReasonsODataModelEquipmentDisposalReason.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentManufacturers < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentODataModelEquipmentManufacturer

      def get(query: {})
        path = "/EquipmentManufacturers"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentODataModelEquipmentManufacturer.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentModels < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentODataModelEquipmentModel

      def get(query: {})
        path = "/EquipmentModels"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentODataModelEquipmentModel.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentModelServiceSchedules < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentModelServiceSchedulesODataModelEquipmentModelServiceSchedule

      def get(query: {})
        path = "/EquipmentModelServiceSchedules"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentModelServiceSchedulesODataModelEquipmentModelServiceSchedule.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentReadingLogs < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentReadingLogODataModelEquipmentReadingLog

      def post(query: {}, **kwargs)
        path = "/EquipmentReadingLogs"
        request_body = Models::AspireCloudExternalApiEquipmentReadingLogsModelEquipmentReadingLogInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/EquipmentReadingLogs"
        request_body = Models::AspireCloudExternalApiEquipmentReadingLogsModelEquipmentReadingLogUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def get(query: {})
        path = "/EquipmentReadingLogs"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentReadingLogODataModelEquipmentReadingLog.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentRequestedServices < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentRequestedServicesODataModelEquipmentRequestedService

      def get(query: {})
        path = "/EquipmentRequestedServices"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentRequestedServicesODataModelEquipmentRequestedService.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Equipments < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentODataModelEquipment

      def get(query: {})
        path = "/Equipments"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentODataModelEquipment.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentServiceLogs < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentServiceLogsODataModelEquipmentServiceLog

      def get(query: {})
        path = "/EquipmentServiceLogs"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentServiceLogsODataModelEquipmentServiceLog.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentServiceTags < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentServiceTagsODataModelEquipmentServiceTag

      def get(query: {})
        path = "/EquipmentServiceTags"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentServiceTagsODataModelEquipmentServiceTag.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class EquipmentSizes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantEquipmentSizesODataModelEquipmentSize

      def get(query: {})
        path = "/EquipmentSizes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantEquipmentSizesODataModelEquipmentSize.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class InventoryLocations < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantInventoryLocationsODataModelInventoryLocation

      def get(query: {})
        path = "/InventoryLocations"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantInventoryLocationsODataModelInventoryLocation.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class InvoiceBatches < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantInvoiceBatchesODataModelInvoiceBatch

      def get(query: {})
        path = "/InvoiceBatches"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantInvoiceBatchesODataModelInvoiceBatch.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class InvoiceRevenues < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantInvoiceRevenuesODataModelInvoiceRevenue

      def get(query: {})
        path = "/InvoiceRevenues"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantInvoiceRevenuesODataModelInvoiceRevenue.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Invoices < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantInvoicesODataModelInvoice

      def get(query: {})
        path = "/Invoices"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantInvoicesODataModelInvoice.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class InvoiceTaxes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantInvoiceTaxesODataModelInvoiceTax

      def get(query: {})
        path = "/InvoiceTaxes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantInvoiceTaxesODataModelInvoiceTax.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Issues < Base
      def post(query: {}, **kwargs)
        path = "/Issues"
        request_body = Models::AspireCloudExternalApiActivitiesModelsIssueRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ItemAllocations < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantItemAllocationsODataModelItemAllocation

      def get(query: {})
        path = "/ItemAllocations"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantItemAllocationsODataModelItemAllocation.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/ItemAllocations"
        request_body = Models::AspireCloudExternalApiInventoryModelsAllocateFromInventoryPostRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/ItemAllocations"
        request_body = Models::AspireCloudExternalApiInventoryModelsAllocateFromInventoryPutRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Localities < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantCountyODataModelCounty

      def get(query: {})
        path = "/Localities"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantCountyODataModelCounty.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Localities"
        request_body = Models::AspireCloudExternalApiCountiesModelLocalityInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/Localities"
        request_body = Models::AspireCloudExternalApiCountiesModelLocalityUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ObjectCodes < Base
      RESPONSE_TYPE_MAPPING = String

      def get(query: {})
        path = "/ObjectCodes"
        result = client.get(path, query)
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Opportunities < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunity

      def get(query: {})
        path = "/Opportunities"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunity.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Opportunities"
        request_body = Models::AspireCloudExternalApiOpportunitiesModelOpportunityInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class OpportunityServiceGroups < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantOpportunityServiceGroupsODataModelOpportunityServiceGroup

      def get(query: {})
        path = "/OpportunityServiceGroups"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantOpportunityServiceGroupsODataModelOpportunityServiceGroup.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class OpportunityServiceItems < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunityServiceItem

      def get(query: {})
        path = "/OpportunityServiceItems"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunityServiceItem.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class OpportunityServiceKitItems < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunityServiceKitItem

      def get(query: {})
        path = "/OpportunityServiceKitItems"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunityServiceKitItem.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class OpportunityServices < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantOpportunityServicesODataModelOpportunityService

      def get(query: {})
        path = "/OpportunityServices"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantOpportunityServicesODataModelOpportunityService.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class OpportunityTags < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunityTag

      def get(query: {})
        path = "/OpportunityTags"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantOpportunitiesODataModelOpportunityTag.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/OpportunityTags"
        request_body = Models::AspireCloudExternalApiOpportunityTagsModelRequestOpportunityTagInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def delete(query: {}, id:)
        path = ["", "OpportunityTags", id].join("/")
        raise "not implemented"
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PartialPayments < Base
      def post(query: {}, **kwargs)
        path = "/PartialPayments"
        request_body = Models::AspireCloudExternalApiPartialPaymentsModelPartialPaymentInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PayCodes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPayCodesODataModelPayCodeModel

      def get(query: {})
        path = "/PayCodes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPayCodesODataModelPayCodeModel.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/PayCodes"
        request_body = Models::AspireCloudExternalApiPayCodesModelRequestPayCodeInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiPayCodesModelResponsePayCodeResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/PayCodes"
        request_body = Models::AspireCloudExternalApiPayCodesModelRequestPayCodeUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiPayCodesModelResponsePayCodeResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PaymentCategories < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPaymentCategoriesODataModelPaymentCategory

      def get(query: {})
        path = "/PaymentCategories"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPaymentCategoriesODataModelPaymentCategory.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Payments < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPaymentsODataModelPayment

      def get(query: {})
        path = "/Payments"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPaymentsODataModelPayment.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PaymentTerms < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPaymentTermsNSODataModelPaymentTerms

      def get(query: {})
        path = "/PaymentTerms"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPaymentTermsNSODataModelPaymentTerms.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PayRateOverridePayCodes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPayCodesODataModelPayRateOverridePayCode

      def get(query: {})
        path = "/PayRateOverridePayCodes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPayCodesODataModelPayRateOverridePayCode.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/PayRateOverridePayCodes"
        request_body = Models::AspireCloudExternalApiPayRateOverridePayCodesModelPayRateOverridePayCodeRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/PayRateOverridePayCodes"
        request_body = Models::AspireCloudExternalApiPayRateOverridePayCodesModelPayRateOverridePayCodeRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PayRates < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPayRatesODataModelPayRate

      def get(query: {})
        path = "/PayRates"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPayRatesODataModelPayRate.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/PayRates"
        request_body = Models::AspireCloudExternalApiPayRatesModelRequestPayRateInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/PayRates"
        request_body = Models::AspireCloudExternalApiPayRatesModelRequestPayRateUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PaySchedules < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPaySchedulesODataModelPaySchedule

      def get(query: {})
        path = "/PaySchedules"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPaySchedulesODataModelPaySchedule.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/PaySchedules"
        request_body = Models::AspireCloudExternalApiPaySchedulesModelRequestPayScheduleInsert.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiPaySchedulesModelResponsePayScheduleResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/PaySchedules"
        request_body = Models::AspireCloudExternalApiPaySchedulesModelRequestPayScheduleUpdate.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiPaySchedulesModelResponsePayScheduleResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Properties < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertiesODataModelProperty

      def get(query: {})
        path = "/Properties"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertiesODataModelProperty.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Properties"
        request_body = Models::AspireCloudExternalApiPropertyModelsPropertyRequestModel.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/Properties"
        request_body = Models::AspireCloudExternalApiPropertyModelsPropertyUpdateRequestModel.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyAvailabilities < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertyAvailabilitiesODataModelPropertyAvailability

      def get(query: {})
        path = "/PropertyAvailabilities"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertyAvailabilitiesODataModelPropertyAvailability.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/PropertyAvailabilities"
        request_body = Models::AspireCloudExternalApiPropertyAvailabilityModelsPropertyAvailabilityRequestModel.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyContacts < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertiesODataModelPropertyContactDataModel

      def get(query: {})
        path = "/PropertyContacts"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertiesODataModelPropertyContactDataModel.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/PropertyContacts"
        request_body = Models::AspireCloudExternalApiPropertyContactsModelsPropertyContactRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/PropertyContacts"
        request_body = Models::AspireCloudExternalApiPropertyContactsModelsPropertyContactRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyCustomFieldDefinitions < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantTableExtenderODataModelPropertyTableExtenderHeader

      def get(query: {})
        path = "/PropertyCustomFieldDefinitions"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantTableExtenderODataModelPropertyTableExtenderHeader.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyCustomFields < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertyCustomFieldODataModelTableExtenderDetail

      def post(query: {}, **kwargs)
        path = "/PropertyCustomFields"
        request_body = Models::AspireCloudExternalApiPropertyCustomFieldsModelsPropertyCustomFieldInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/PropertyCustomFields"
        request_body = Models::AspireCloudExternalApiPropertyCustomFieldsModelsPropertyCustomFieldUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def get(query: {})
        path = "/PropertyCustomFields"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertyCustomFieldODataModelTableExtenderDetail.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyGroups < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertyGroupsODataModelPropertyGroup

      def get(query: {})
        path = "/PropertyGroups"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertyGroupsODataModelPropertyGroup.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyStatuses < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertyStatusesODataModelPropertyStatus

      def get(query: {})
        path = "/PropertyStatuses"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertyStatusesODataModelPropertyStatus.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class PropertyTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPropertyTypesODataModelPropertyType

      def get(query: {})
        path = "/PropertyTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPropertyTypesODataModelPropertyType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ProspectRatings < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantProspectRatingsODataModelProspectRating

      def get(query: {})
        path = "/ProspectRatings"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantProspectRatingsODataModelProspectRating.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Receipts < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantPurchaseReceiptODataModelReceipt

      def get(query: {})
        path = "/Receipts"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantPurchaseReceiptODataModelReceipt.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Receipts"
        request_body = Models::AspireCloudExternalApiReceiptsModelReceiptPost.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiReceiptsModelResponseReceiptResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def approve(query: {}, **kwargs)
        path = "/Receipts/Approve"
        request_body = Models::AspireCloudExternalApiReceiptsModelRequestApproveReceiptRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def receive(query: {}, **kwargs)
        path = "/Receipts/Receive"
        request_body = Models::AspireCloudExternalApiReceiptsModelRequestReceiveReceiptRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ReceiptStatuses < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantReceiptStatusesODataModelReceiptStatusModel

      def get(query: {})
        path = "/ReceiptStatuses"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantReceiptStatusesODataModelReceiptStatusModel.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Regions < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantRegionsODataModelRegion

      def get(query: {})
        path = "/Regions"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantRegionsODataModelRegion.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class RevenueVariances < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantRevenueVariancesODataModelRevenueVariance

      def get(query: {})
        path = "/RevenueVariances"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantRevenueVariancesODataModelRevenueVariance.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Roles < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantRolesODataModelRole

      def get(query: {})
        path = "/Roles"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantRolesODataModelRole.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Routes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantRoutesODataModelRoute

      def get(query: {})
        path = "/Routes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantRoutesODataModelRoute.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class SalesTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantSalesTypesODataModelSalesType

      def get(query: {})
        path = "/SalesTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantSalesTypesODataModelSalesType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Services < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantServicesODataModelService

      def get(query: {})
        path = "/Services"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantServicesODataModelService.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ServiceTaxOverrides < Base
      def post(query: {}, **kwargs)
        path = "/ServiceTaxOverrides"
        request_body = Models::AspireCloudExternalApiServiceTaxOverridesModelRequestServiceTaxOverrideInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/ServiceTaxOverrides"
        request_body = Models::AspireCloudExternalApiServiceTaxOverridesModelRequestServiceTaxOverrideUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ServiceTypeIntegrationCodes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantServiceTypeIntegrationCodesODataModelServiceTypeIntegrationCode

      def get(query: {})
        path = "/ServiceTypeIntegrationCodes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantServiceTypeIntegrationCodesODataModelServiceTypeIntegrationCode.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class ServiceTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantServiceTypesODataModelServiceType

      def get(query: {})
        path = "/ServiceTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantServiceTypesODataModelServiceType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Tags < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantTagsODataModelTag

      def get(query: {})
        path = "/Tags"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantTagsODataModelTag.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class TakeoffGroups < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantTakeOffODataModelTakeoffGroup

      def get(query: {})
        path = "/TakeoffGroups"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantTakeOffODataModelTakeoffGroup.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class TakeoffItems < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantTakeOffODataModelTakeoffItem

      def get(query: {})
        path = "/TakeoffItems"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantTakeOffODataModelTakeoffItem.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Tasks < Base
      def post(query: {}, **kwargs)
        path = "/Tasks"
        request_body = Models::AspireCloudExternalApiActivitiesModelsTaskRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class TaxEntities < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantTaxEntitiesODataModelTaxEntity

      def get(query: {})
        path = "/TaxEntities"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantTaxEntitiesODataModelTaxEntity.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/TaxEntities"
        request_body = Models::AspireCloudExternalApiTaxEntitiesModelsTaxEntityInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/TaxEntities"
        request_body = Models::AspireCloudExternalApiTaxEntitiesModelsTaxEntityUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class TaxJurisdictions < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantTaxJurisdictionsODataModelTaxJurisdiction

      def get(query: {})
        path = "/TaxJurisdictions"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantTaxJurisdictionsODataModelTaxJurisdiction.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/TaxJurisdictions"
        request_body = Models::AspireCloudExternalApiTaxJurisdictionsModelTaxJurisdictionInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/TaxJurisdictions"
        request_body = Models::AspireCloudExternalApiTaxJurisdictionsModelTaxJurisdictionUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class UnitTypes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantUnitTypesODataModelUnitType

      def get(query: {})
        path = "/UnitTypes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantUnitTypesODataModelUnitType.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Users < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantUsersODataModelUser

      def get(query: {})
        path = "/Users"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantUsersODataModelUser.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Users"
        request_body = Models::AspireCloudExternalApiUsersModelsUserInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/Users"
        request_body = Models::AspireCloudExternalApiUsersModelsUserUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Vendors < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantVendorsODataModelVendor

      def get(query: {})
        path = "/Vendors"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantVendorsODataModelVendor.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/Vendors"
        request_body = Models::AspireCloudExternalApiVendorsModelRequestVendorInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiVendorsModelResponseVendorResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def put(query: {}, **kwargs)
        path = "/Vendors"
        request_body = Models::AspireCloudExternalApiVendorsModelRequestVendorUpdateRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        Models::AspireCloudExternalApiVendorsModelResponseVendorResponse.new(result.body)
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class Version < Base
      def get_api_version(query: {})
        path = "/Version/GetApiVersion"
        result = client.get(path, query)
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkersComps < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkersCompODataModelWorkersComp

      def get(query: {})
        path = "/WorkersComps"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkersCompODataModelWorkersComp.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketCanceledReasons < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketCanceledReasonsODataModelWorkTicketCanceledReason

      def get(query: {})
        path = "/WorkTicketCanceledReasons"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketCanceledReasonsODataModelWorkTicketCanceledReason.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketItems < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketsODataModelWorkTicketItemModel

      def get(query: {})
        path = "/WorkTicketItems"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketsODataModelWorkTicketItemModel.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketRevenues < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketRevenuesODataModelWorkTicketRevenueModel

      def get(query: {})
        path = "/WorkTicketRevenues"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketRevenuesODataModelWorkTicketRevenueModel.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTickets < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketsODataModelWorkTicket

      def get(query: {})
        path = "/WorkTickets"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketsODataModelWorkTicket.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def create_as_needed_work_tickets(query: {}, **kwargs)
        path = "/WorkTickets/CreateAsNeededWorkTickets"
        request_body = Models::AspireCloudExternalApiWorkTicketsCreateAsNeededWorkTicketRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketStatus < Base
      def mark_work_ticket_as_reviewed(query: {}, **kwargs)
        path = "/WorkTicketStatus/MarkWorkTicketAsReviewed"
        request_body = Models::AspireCloudExternalApiWorkTicketStatusModelMarkWorkTicketAsReviewedRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketTimes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketTimesODataModelWorkTicketTime

      def get(query: {})
        path = "/WorkTicketTimes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketTimesODataModelWorkTicketTime.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end

      def post(query: {}, **kwargs)
        path = "/WorkTicketTimes"
        request_body = Models::AspireCloudExternalApiWorkTicketTimesModelWorkTicketTimeInsertRequest.new(kwargs)

        if request_body.invalid?
          raise AspireClient::Error,
            "Invalid request: #{request_body.errors.map(&:full_message).join("\n")}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
        result.body
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketVisitNotes < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketVisitsODataModelWorkTicketVisitNote

      def get(query: {})
        path = "/WorkTicketVisitNotes"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketVisitsODataModelWorkTicketVisitNote.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    class WorkTicketVisits < Base
      RESPONSE_TYPE_MAPPING = Models::AspireCloudDomainTenantWorkTicketVisitsODataModelWorkTicketVisit

      def get(query: {})
        path = "/WorkTicketVisits"
        result = client.get(path, query)
        result.body.map do |data|
          Models::AspireCloudDomainTenantWorkTicketVisitsODataModelWorkTicketVisit.new(data)
        end
      rescue => e
        raise AspireClient::Error.new(e)
      end
    end

    RESOURCE_MAP = {
      activities: Activities,
      activity_categories: ActivityCategories,
      activity_comment_histories: ActivityCommentHistories,
      addresses: Addresses,
      attachments: Attachments,
      attachment_types: AttachmentTypes,
      authorization: Authorization,
      bank_deposits: BankDeposits,
      branches: Branches,
      catalog_item_categories: CatalogItemCategories,
      catalog_items: CatalogItems,
      certifications: Certifications,
      certification_types: CertificationTypes,
      clock_times: ClockTimes,
      companies: Companies,
      contacts: Contacts,
      contact_types: ContactTypes,
      division_integration_codes: DivisionIntegrationCodes,
      divisions: Divisions,
      employee_incidents: EmployeeIncidents,
      employee_incident_types: EmployeeIncidentTypes,
      equipment_classes: EquipmentClasses,
      equipment_disposal_reasons: EquipmentDisposalReasons,
      equipment_manufacturers: EquipmentManufacturers,
      equipment_models: EquipmentModels,
      equipment_model_service_schedules: EquipmentModelServiceSchedules,
      equipment_reading_logs: EquipmentReadingLogs,
      equipment_requested_services: EquipmentRequestedServices,
      equipments: Equipments,
      equipment_service_logs: EquipmentServiceLogs,
      equipment_service_tags: EquipmentServiceTags,
      equipment_sizes: EquipmentSizes,
      inventory_locations: InventoryLocations,
      invoice_batches: InvoiceBatches,
      invoice_revenues: InvoiceRevenues,
      invoices: Invoices,
      invoice_taxes: InvoiceTaxes,
      issues: Issues,
      item_allocations: ItemAllocations,
      localities: Localities,
      object_codes: ObjectCodes,
      opportunities: Opportunities,
      opportunity_service_groups: OpportunityServiceGroups,
      opportunity_service_items: OpportunityServiceItems,
      opportunity_service_kit_items: OpportunityServiceKitItems,
      opportunity_services: OpportunityServices,
      opportunity_tags: OpportunityTags,
      partial_payments: PartialPayments,
      pay_codes: PayCodes,
      payment_categories: PaymentCategories,
      payments: Payments,
      payment_terms: PaymentTerms,
      pay_rate_override_pay_codes: PayRateOverridePayCodes,
      pay_rates: PayRates,
      pay_schedules: PaySchedules,
      properties: Properties,
      property_availabilities: PropertyAvailabilities,
      property_contacts: PropertyContacts,
      property_custom_field_definitions: PropertyCustomFieldDefinitions,
      property_custom_fields: PropertyCustomFields,
      property_groups: PropertyGroups,
      property_statuses: PropertyStatuses,
      property_types: PropertyTypes,
      prospect_ratings: ProspectRatings,
      receipts: Receipts,
      receipt_statuses: ReceiptStatuses,
      regions: Regions,
      revenue_variances: RevenueVariances,
      roles: Roles,
      routes: Routes,
      sales_types: SalesTypes,
      services: Services,
      service_tax_overrides: ServiceTaxOverrides,
      service_type_integration_codes: ServiceTypeIntegrationCodes,
      service_types: ServiceTypes,
      tags: Tags,
      takeoff_groups: TakeoffGroups,
      takeoff_items: TakeoffItems,
      tasks: Tasks,
      tax_entities: TaxEntities,
      tax_jurisdictions: TaxJurisdictions,
      unit_types: UnitTypes,
      users: Users,
      vendors: Vendors,
      version: Version,
      workers_comps: WorkersComps,
      work_ticket_canceled_reasons: WorkTicketCanceledReasons,
      work_ticket_items: WorkTicketItems,
      work_ticket_revenues: WorkTicketRevenues,
      work_tickets: WorkTickets,
      work_ticket_status: WorkTicketStatus,
      work_ticket_times: WorkTicketTimes,
      work_ticket_visit_notes: WorkTicketVisitNotes,
      work_ticket_visits: WorkTicketVisits
    }.with_indifferent_access.freeze
  end
end
