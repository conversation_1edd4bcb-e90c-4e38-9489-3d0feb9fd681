# frozen_string_literal: true

# This file was generated by a script. Direct modifications are discouraged.
# source: lib/generators/swagger_client/swagger_client_generator.rb
# command: bin/rails generate aspire --schema_path=spec/support/aspire.openapi.json

require_relative "./resources"

module AspireClient
  module ResourceMethods
    def activities
      Resources::Activities.new(client)
    end

    def activity_categories
      Resources::ActivityCategories.new(client)
    end

    def activity_comment_histories
      Resources::ActivityCommentHistories.new(client)
    end

    def addresses
      Resources::Addresses.new(client)
    end

    def attachments
      Resources::Attachments.new(client)
    end

    def attachment_types
      Resources::AttachmentTypes.new(client)
    end

    def authorization
      Resources::Authorization.new(client)
    end

    def bank_deposits
      Resources::BankDeposits.new(client)
    end

    def branches
      Resources::Branches.new(client)
    end

    def catalog_item_categories
      Resources::CatalogItemCategories.new(client)
    end

    def catalog_items
      Resources::CatalogItems.new(client)
    end

    def certifications
      Resources::Certifications.new(client)
    end

    def certification_types
      Resources::CertificationTypes.new(client)
    end

    def clock_times
      Resources::ClockTimes.new(client)
    end

    def companies
      Resources::Companies.new(client)
    end

    def contacts
      Resources::Contacts.new(client)
    end

    def contact_types
      Resources::ContactTypes.new(client)
    end

    def division_integration_codes
      Resources::DivisionIntegrationCodes.new(client)
    end

    def divisions
      Resources::Divisions.new(client)
    end

    def employee_incidents
      Resources::EmployeeIncidents.new(client)
    end

    def employee_incident_types
      Resources::EmployeeIncidentTypes.new(client)
    end

    def equipment_classes
      Resources::EquipmentClasses.new(client)
    end

    def equipment_disposal_reasons
      Resources::EquipmentDisposalReasons.new(client)
    end

    def equipment_manufacturers
      Resources::EquipmentManufacturers.new(client)
    end

    def equipment_models
      Resources::EquipmentModels.new(client)
    end

    def equipment_model_service_schedules
      Resources::EquipmentModelServiceSchedules.new(client)
    end

    def equipment_reading_logs
      Resources::EquipmentReadingLogs.new(client)
    end

    def equipment_requested_services
      Resources::EquipmentRequestedServices.new(client)
    end

    def equipments
      Resources::Equipments.new(client)
    end

    def equipment_service_logs
      Resources::EquipmentServiceLogs.new(client)
    end

    def equipment_service_tags
      Resources::EquipmentServiceTags.new(client)
    end

    def equipment_sizes
      Resources::EquipmentSizes.new(client)
    end

    def inventory_locations
      Resources::InventoryLocations.new(client)
    end

    def invoice_batches
      Resources::InvoiceBatches.new(client)
    end

    def invoice_revenues
      Resources::InvoiceRevenues.new(client)
    end

    def invoices
      Resources::Invoices.new(client)
    end

    def invoice_taxes
      Resources::InvoiceTaxes.new(client)
    end

    def issues
      Resources::Issues.new(client)
    end

    def item_allocations
      Resources::ItemAllocations.new(client)
    end

    def localities
      Resources::Localities.new(client)
    end

    def object_codes
      Resources::ObjectCodes.new(client)
    end

    def opportunities
      Resources::Opportunities.new(client)
    end

    def opportunity_service_groups
      Resources::OpportunityServiceGroups.new(client)
    end

    def opportunity_service_items
      Resources::OpportunityServiceItems.new(client)
    end

    def opportunity_service_kit_items
      Resources::OpportunityServiceKitItems.new(client)
    end

    def opportunity_services
      Resources::OpportunityServices.new(client)
    end

    def opportunity_tags
      Resources::OpportunityTags.new(client)
    end

    def partial_payments
      Resources::PartialPayments.new(client)
    end

    def pay_codes
      Resources::PayCodes.new(client)
    end

    def payment_categories
      Resources::PaymentCategories.new(client)
    end

    def payments
      Resources::Payments.new(client)
    end

    def payment_terms
      Resources::PaymentTerms.new(client)
    end

    def pay_rate_override_pay_codes
      Resources::PayRateOverridePayCodes.new(client)
    end

    def pay_rates
      Resources::PayRates.new(client)
    end

    def pay_schedules
      Resources::PaySchedules.new(client)
    end

    def properties
      Resources::Properties.new(client)
    end

    def property_availabilities
      Resources::PropertyAvailabilities.new(client)
    end

    def property_contacts
      Resources::PropertyContacts.new(client)
    end

    def property_custom_field_definitions
      Resources::PropertyCustomFieldDefinitions.new(client)
    end

    def property_custom_fields
      Resources::PropertyCustomFields.new(client)
    end

    def property_groups
      Resources::PropertyGroups.new(client)
    end

    def property_statuses
      Resources::PropertyStatuses.new(client)
    end

    def property_types
      Resources::PropertyTypes.new(client)
    end

    def prospect_ratings
      Resources::ProspectRatings.new(client)
    end

    def receipts
      Resources::Receipts.new(client)
    end

    def receipt_statuses
      Resources::ReceiptStatuses.new(client)
    end

    def regions
      Resources::Regions.new(client)
    end

    def revenue_variances
      Resources::RevenueVariances.new(client)
    end

    def roles
      Resources::Roles.new(client)
    end

    def routes
      Resources::Routes.new(client)
    end

    def sales_types
      Resources::SalesTypes.new(client)
    end

    def services
      Resources::Services.new(client)
    end

    def service_tax_overrides
      Resources::ServiceTaxOverrides.new(client)
    end

    def service_type_integration_codes
      Resources::ServiceTypeIntegrationCodes.new(client)
    end

    def service_types
      Resources::ServiceTypes.new(client)
    end

    def tags
      Resources::Tags.new(client)
    end

    def takeoff_groups
      Resources::TakeoffGroups.new(client)
    end

    def takeoff_items
      Resources::TakeoffItems.new(client)
    end

    def tasks
      Resources::Tasks.new(client)
    end

    def tax_entities
      Resources::TaxEntities.new(client)
    end

    def tax_jurisdictions
      Resources::TaxJurisdictions.new(client)
    end

    def unit_types
      Resources::UnitTypes.new(client)
    end

    def users
      Resources::Users.new(client)
    end

    def vendors
      Resources::Vendors.new(client)
    end

    def version
      Resources::Version.new(client)
    end

    def workers_comps
      Resources::WorkersComps.new(client)
    end

    def work_ticket_canceled_reasons
      Resources::WorkTicketCanceledReasons.new(client)
    end

    def work_ticket_items
      Resources::WorkTicketItems.new(client)
    end

    def work_ticket_revenues
      Resources::WorkTicketRevenues.new(client)
    end

    def work_tickets
      Resources::WorkTickets.new(client)
    end

    def work_ticket_status
      Resources::WorkTicketStatus.new(client)
    end

    def work_ticket_times
      Resources::WorkTicketTimes.new(client)
    end

    def work_ticket_visit_notes
      Resources::WorkTicketVisitNotes.new(client)
    end

    def work_ticket_visits
      Resources::WorkTicketVisits.new(client)
    end
  end
end
