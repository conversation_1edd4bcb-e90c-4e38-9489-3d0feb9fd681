# frozen_string_literal: true

module MoneyType
  module MoneyCoercion
    def self.to_money(input, &_block)
      return input if input.is_a?(::Money)

      input = JSON.parse(input) if input.is_a?(String)

      if input.respond_to?(:to_hash)
        begin
          hash = input.to_hash
          hash = Dry::Types["params.money_schema"].call(hash)
          ::Money.new(hash.fetch(:cents), hash.fetch(:currency_iso, "USD"))
        rescue KeyError => e
          Dry::Types::CoercionError.handle(e, &_block)
        end
      else
        raise Dry::Types::CoercionError, "#{input.inspect} is not convertible to Money"
      end
    end
  end

  Dry::Types.instance_exec do
    nominal = Dry::Types().Nominal(::Money)
    register("nominal.money") { nominal }
    register("params.money_schema") do
      types = Dry::Types()
      types::Hash.schema(
        cents: types::Integer,
        currency_iso: types::String.default(Money.default_currency.iso_code.freeze)
      ).with_key_transform(&:to_sym)
    end

    register("params.money") do
      nominal.constructor(MoneyCoercion.method(:to_money))
    end

    # essentially an alias of 'params.money'
    register("json.money") do
      nominal.constructor(MoneyCoercion.method(:to_money))
    end

    register("strict.money") do
      nominal.constrained(type: ::Money)
    end
  end

  Graphiti::Types.instance_exec do
    ParamsToMoney = Graphiti::Types.create(Money) do |input|
      input = Dry::Types["params.money"][input]
      Dry::Types["strict.money"][input] if input
    end

    MoneyToJson = Graphiti::Types.create(Money) do |input|
      input = Dry::Types["params.money"][input]
      Dry::Types["strict.money"][input].as_json if input
    end

    self[:money] = {
      params: ParamsToMoney,
      read: MoneyToJson,
      write: ParamsToMoney,
      kind: "record",
      description: "money"
    }
  end
end
