# frozen_string_literal: true

namespace :aspire do
  desc "Create a placeholder for a missing contact in Aspire integration"
  task :create_contact_placeholder, [:integration_id, :contact_id] => :environment do |_, args|
    if args[:integration_id].blank? || args[:contact_id].blank?
      puts "ERROR: Required parameters missing!"
      puts "Usage: rake aspire:create_contact_placeholder[integration_id,contact_id]"
      exit 1
    end

    integration_id = args[:integration_id]
    contact_id = args[:contact_id]

    begin
      integration = Integration.find(integration_id)

      unless integration.aspire?
        puts "ERROR: Integration #{integration_id} is not an Aspire integration!"
        exit 1
      end

      puts "Creating placeholder for contact ID: #{contact_id} in integration #{integration_id}..."

      Sync::Current.track(integration_id: integration.id) do
        integration.materializer.create_placeholder_contact(contact_id)
      end

      puts "Successfully created placeholder contact!"
      puts "You can now retry your sync operation."
    rescue => e
      puts "ERROR: #{e.message}"
      puts e.backtrace.first(5).join("\n")
      exit 1
    end
  end

  desc "Fix all missing contacts referenced in a failed sync job"
  task :fix_missing_contacts, [:integration_id] => :environment do |_, args|
    if args[:integration_id].blank?
      puts "ERROR: Required parameter missing!"
      puts "Usage: rake aspire:fix_missing_contacts[integration_id]"
      exit 1
    end

    integration_id = args[:integration_id]

    begin
      integration = Integration.find(integration_id)

      unless integration.aspire?
        puts "ERROR: Integration #{integration_id} is not an Aspire integration!"
        exit 1
      end

      puts "Scanning for missing contacts in integration #{integration_id}..."

      # Get recent failed Sidekiq jobs for this integration
      require "sidekiq/api"

      missing_contact_ids = Set.new

      # Check the dead set for failed jobs
      Sidekiq::DeadSet.new.each do |job|
        next unless job.klass == "Sync::SyncIntegrationJob" && job.args.first == integration_id

        if job.error_message =~ /cannot materialize aspire:contact:(\d+); no cache found/
          missing_contact_ids << $1
        end
      end

      # Check the retry set for failed jobs
      Sidekiq::RetrySet.new.each do |job|
        next unless job.klass == "Sync::SyncIntegrationJob" && job.args.first == integration_id

        if job.error_message =~ /cannot materialize aspire:contact:(\d+); no cache found/
          missing_contact_ids << $1
        end
      end

      if missing_contact_ids.empty?
        puts "No missing contacts found in failed jobs."
        exit 0
      end

      puts "Found #{missing_contact_ids.size} missing contacts: #{missing_contact_ids.to_a.join(', ')}"
      puts "Creating placeholders..."

      Sync::Current.track(integration_id: integration.id) do
        missing_contact_ids.each do |contact_id|
          puts "Creating placeholder for contact ID: #{contact_id}..."
          integration.materializer.create_placeholder_contact(contact_id)
        end
      end

      puts "Successfully created #{missing_contact_ids.size} placeholder contacts!"
      puts "You can now retry your sync operation or clear the Sidekiq retry/dead queues."
    rescue => e
      puts "ERROR: #{e.message}"
      puts e.backtrace.first(5).join("\n")
      exit 1
    end
  end
end
