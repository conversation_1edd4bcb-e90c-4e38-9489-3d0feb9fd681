# frozen_string_literal: true

namespace :postman do
  desc "Generate Postman collection for API endpoints"
  task generate: :environment do
    require "json"

    # Base details
    base_url = ENV["API_BASE_URL"] || "http://localhost:3000"

    collection = {
      info: {
        name: "Protiv v2",
        schema: "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
        description: "Collection for Protiv v2 API"
      },
      item: []
    }

    # Add Authentication folder with login example
    auth_folder = {
      name: "Authentication",
      description: "Authentication endpoints to get bearer token",
      item: [
        {
          name: "Login",
          request: {
            method: "POST",
            header: [
              {
                key: "Content-Type",
                value: "application/vnd.api+json"
              },
              {
                key: "Accept",
                value: "application/vnd.api+json"
              }
            ],
            url: {
              raw: "{{base_url}}/api/v2/session",
              host: ["{{base_url}}"],
              path: ["api", "v2", "session"]
            },
            body: {
              mode: "raw",
              raw: <<~JSON,
                {
                  "email": "{{email}}",
                  "password": "{{password}}"
                }
              JSON
              options: {
                raw: {
                  language: "json"
                }
              }
            },
            description: "Creates a session and returns a token for API authentication. You will need to extract the redirect URL and follow it to complete authentication."
          },
          response: [],
          event: [
            {
              listen: "test",
              script: {
                type: "text/javascript",
                exec: [
                  "var jsonData = pm.response.json();",
                  "if (jsonData && jsonData.data && jsonData.data.attributes && jsonData.data.attributes.redirect) {",
                  "    // Store the redirect URL for the next request",
                  "    pm.environment.set('redirect_url', jsonData.data.attributes.redirect);",
                  "    // Set up the next request automatically",
                  "    pm.execution.setNextRequest('Follow Redirect');",
                  "} else {",
                  "    console.error('Failed to find redirect URL in the response');",
                  "}"
                ]
              }
            }
          ]
        },
        {
          name: "Follow Redirect",
          request: {
            method: "GET",
            header: [],
            url: {
              raw: "{{redirect_url}}",
              host: ["{{redirect_url}}"]
            },
            description: "Follows the redirect URL to complete authentication and extract token"
          },
          response: [],
          event: [
            {
              listen: "test",
              script: {
                type: "text/javascript",
                exec: <<~JAVASCRIPT
                  // Get the full response body as text
                  let responseBody = pm.response.text();
                  let token = null;

                  // Regex to find the content of the script tag with id 'protiv-bootstrap'
                  let scriptTagMatch = responseBody.match(/<script\\s+id=['"]protiv-bootstrap['"]\\s*>([\\s\\S]*?)<\\/script>/i);

                  if (scriptTagMatch && scriptTagMatch[1]) {
                      let scriptContent = scriptTagMatch[1];
                  #{'    '}
                      // Regex to find the assignment to window._protiv_bootstrap and capture the JSON object
                      // This regex handles potential whitespace and assumes the object ends with a semicolon
                      let bootstrapDataMatch = scriptContent.match(/window\\._protiv_bootstrap\\s*=\\s*(\\{[\\s\\S]*?\\});?/i);
                  #{'    '}
                      if (bootstrapDataMatch && bootstrapDataMatch[1]) {
                          let bootstrapJsonString = bootstrapDataMatch[1];
                          try {
                              // Attempt to parse the captured string as JSON
                              let bootstrapData = JSON.parse(bootstrapJsonString);
                  #{'            '}
                              // Check if the 'account.authentication' key exists and extract the token
                              if (bootstrapData && bootstrapData.hasOwnProperty('account.authentication')) {
                                  token = bootstrapData['account.authentication'];
                                  // Store the token in collection variables
                                  pm.collectionVariables.set('auth_token', token);
                                  console.log('✅ Authentication token extracted and stored successfully in Collection Variables');
                              } else {
                                  console.error('❌ Key "account.authentication" not found in bootstrap data:', bootstrapData);
                              }
                          } catch (e) {
                              console.error('❌ Failed to parse bootstrap data JSON:', e);
                              console.error('Extracted JSON string:', bootstrapJsonString);
                          }
                      } else {
                          console.error('❌ Could not extract window._protiv_bootstrap assignment from script content');
                          console.error('Script content:', scriptContent);
                      }
                  } else {
                      console.error('❌ Could not find script tag with id="protiv-bootstrap" in the response body');
                  }

                  // Test to ensure the token was extracted
                  pm.test("Authentication token should be extracted", function () {
                      pm.expect(token).to.not.be.null;
                      pm.expect(token).to.be.a('string');
                      pm.expect(pm.collectionVariables.get('auth_token')).to.eql(token);
                  });
                JAVASCRIPT
              }
            }
          ]
        }
      ]
    }

    collection[:item].push(auth_folder)

    # Group by controller
    controller_endpoints = {}

    # Filter routes for API endpoints
    Rails.application.routes.routes.each do |route|
      path = route.path.spec.to_s
      next unless path.include?("/api/v2/")

      # Extract relevant information
      verb = route.verb.downcase.split("|").first.gsub(/[^a-z]/, "")
      next if verb.empty?

      controller = route.defaults[:controller]
      next unless controller&.start_with?("api/")

      action = route.defaults[:action]

      # Format the path for Postman (replace :param with {{param}})
      formatted_path = path.gsub(/\(\.:format\)/, "")
                           .gsub(/:([^\/]+)/, '{{\1}}')

      # Create the endpoint object
      endpoint = {
        name: "#{action.capitalize} #{path.split('/').last.gsub(/\(\.:format\)/, '')}",
        request: {
          method: verb.upcase,
          header: [
            {
              key: "Content-Type",
              value: "application/vnd.api+json"
            },
            {
              key: "Accept",
              value: "application/vnd.api+json"
            }
          ],
          url: {
            raw: "{{base_url}}#{formatted_path}",
            host: ["{{base_url}}"],
            path: formatted_path.slice(1..-1).split("/")
          }
        }
      }

      # Add authorization header placeholder
      if !["sessions#create", "password_resets#create", "accounts#create", "healthcheck"].include?("#{controller.split('/').last}##{action}")
        endpoint[:request][:header] << {
          key: "Authorization",
          value: "Bearer {{auth_token}}"
        }
      end

      # Add body template for POST, PUT, PATCH requests
      if %w[post put patch].include?(verb)
        endpoint[:request][:body] = {
          mode: "raw",
          raw: <<~JSON,
            {
              "data": {
                "type": "#{controller.split('/').last.singularize}",
                "attributes": {
            #{'      '}
                }
              }
            }
          JSON
          options: {
            raw: {
              language: "json"
            }
          }
        }
      end

      # Group by controller
      controller_key = controller.split("/").last
      controller_endpoints[controller_key] ||= []
      controller_endpoints[controller_key] << endpoint
    end

    # Create folders for each controller
    controller_endpoints.each do |controller, endpoints|
      collection[:item] << {
        name: controller.capitalize,
        item: endpoints
      }
    end

    # Add variables
    collection[:variable] = [
      {
        key: "base_url",
        value: base_url,
        type: "string"
      },
      {
        key: "auth_token",
        value: "",
        type: "string"
      },
      {
        key: "email",
        value: "<EMAIL>",
        type: "string",
        description: "Your user email"
      },
      {
        key: "password",
        value: "your_password",
        type: "string",
        description: "Your user password"
      }
    ]

    # Create README for how to use the collection
    readme_folder = {
      name: "README - How to Use",
      description: "## How to Authenticate\n\n1. First, set your email and password in the collection variables\n2. Run the \"Login\" request in the Authentication folder\n3. The collection will automatically follow the redirect and extract the token\n4. Once authenticated, the token will be stored in the `{{auth_token}}` variable\n5. All authenticated requests will use this token automatically\n\n## Troubleshooting\n\nIf authentication fails, check that:\n- Your email and password are correct\n- The API server is running\n- You're using the correct base URL",
      item: []
    }

    collection[:item].unshift(readme_folder)

    # Write to file
    File.write("tmp/protiv_v2_postman_collection.json", JSON.pretty_generate(collection))
    puts "Postman collection generated at tmp/protiv_v2_postman_collection.json"
  end
end
