# frozen_string_literal: true

# Helper for adding colors to output
def colorize(text, color_code)
  if $stdout.isatty
    "\e[#{color_code}m#{text}\e[0m"
  else
    text
  end
end

namespace :aspire_targeted_sync do
  SECTION_SEPARATOR = "\n" + "-" * 70 + "\n"

  desc "Connect to an Aspire company and disable auto_sync by default"
  task :connect, [:org_id, :org_name, :client_id, :secret, :sync_interval] => :environment do |_, args|
    args.with_defaults(sync_interval: "24") # Default sync interval in hours

    if args[:client_id].blank? || args[:secret].blank?
      puts colorize("ERROR: Required parameters missing!", 31)
      puts "Usage with existing organization: rake aspire_targeted_sync:connect[org_id,null,client_id,secret,sync_interval]"
      puts "Usage to create organization: rake aspire_targeted_sync:connect[null,org_name,client_id,secret,sync_interval]"
      puts "  org_id: ID of existing organization, or 'null' to create a new one"
      puts "  org_name: Name for the new organization (when org_id is 'null')"
      puts "  client_id: Aspire client ID"
      puts "  secret: Aspire client secret"
      puts "  sync_interval: Optional, interval in hours (default: 24)"
      exit 1
    end

    puts colorize("\n=== Aspire Integration Setup ===", 34)

    # Get or create organization
    if args[:org_id].to_s.downcase == "null"
      if args[:org_name].blank?
        puts colorize("ERROR: Organization name is required when creating a new organization!", 31)
        exit 1
      end

      # Create the organization
      organization = Organization.create!(name: args[:org_name])
      puts colorize("Created new organization: #{organization.name} (ID: #{organization.id})", 32)

      # Add default PayrollSchedule to the organization
      if defined?(AddPayrollScheduleToOrganization)
        AddPayrollScheduleToOrganization.new(
          organization: organization,
        ).execute!
        puts colorize("Created new default monthly PayrollSchedule", 32)
      end

      # Create a default user for the organization
      user = User.create!(
        name: "user 1",
        email: "<EMAIL>",
        password: "password",
        password_confirmation: "password"
      )

      # Make the user an admin
      user.update(admin: true)

      # Link the user to the organization with the admin role
      if defined?(AddUserToOrganization)
        AddUserToOrganization.new(
          user: user,
          organization: organization,
          role_type: :admin
        ).execute!
        puts colorize("Created default admin user:", 32)
      else
        # Fallback if service object isn't available
        OrganizationUser.create!(
          user: user,
          organization: organization,
          role: "admin"
        )
        puts colorize("Created default admin user:", 32)
      end

      puts colorize("  Email: <EMAIL>", 36)
      puts colorize("  Password: password", 36)
    else
      begin
        organization = Organization.find(args[:org_id])
        puts colorize("Using existing organization: #{organization.name} (ID: #{organization.id})", 36)
      rescue ActiveRecord::RecordNotFound
        puts colorize("ERROR: Organization with ID #{args[:org_id]} not found!", 31)
        exit 1
      end
    end

    # Create the integration
    integration = Integration.new(
      organization: organization,
      source: "aspire",
      client_id: args[:client_id],
      secret: args[:secret],
      sync_interval: args[:sync_interval].to_i.hours,
      auto_sync: false # Disable auto-sync by default
    )

    if integration.save
      puts colorize("Integration created successfully (ID: #{integration.id})", 32)
      puts colorize("Getting authentication token...", 36)

      integration.refresh_credential
      if integration.credential&.bearer_token.present?
        puts colorize("Successfully connected to Aspire!", 32)
        puts SECTION_SEPARATOR
        puts colorize("Integration Summary:", 34)
        puts "Integration ID: #{integration.id}"
        puts "Organization: #{organization.name} (ID: #{organization.id})"
        puts "Sync interval set to #{args[:sync_interval]} hours."
        puts colorize("IMPORTANT: auto_sync is set to FALSE. This integration will NOT sync automatically.", 33)
      else
        puts colorize("Failed to connect to Aspire. Check your client_id and secret.", 31)
        integration.destroy
        puts colorize("Integration was removed due to authentication failure.", 33)
      end
    else
      puts colorize("Failed to create integration: #{integration.errors.full_messages.join(', ')}", 31)
    end
  end

  desc "Sync a specific opportunity from Aspire with all its dependencies"
  task :sync_opportunity, [:integration_id, :opportunity_number] => :environment do |_, args|
    if args[:integration_id].blank? || args[:opportunity_number].blank?
      puts colorize("ERROR: Required parameters missing!", 31)
      puts "Usage: rake aspire_targeted_sync:sync_opportunity[integration_id,opportunity_number]"
      exit 1
    end

    integration = Integration.find(args[:integration_id])
    unless integration.aspire?
      puts colorize("ERROR: Integration #{integration.id} is not Aspire!", 31)
      exit 1
    end

    opportunity_number_to_sync = args[:opportunity_number]
    job_contacts_remote_ids = []

    puts colorize("\n\n=== Starting Sync for OpportunityNumber: #{opportunity_number_to_sync} ===", 34)

    # --- Step 1: Find or Create Job ---
    puts SECTION_SEPARATOR
    puts colorize("Step 1: Finding or Creating Job...", 36)
    job = integration.jobs.find_by(remote_reference: opportunity_number_to_sync)

    if job.nil?
      puts "Local Job not found. Triggering creation..."
      Sync::Current.track(integration_id: integration.id) do
        integration.cache_adapter.cache_jobs(
          ids: [opportunity_number_to_sync],
          segment_key: "OpportunityNumber"
        )
        numbered_opportunity_resource = Sync::Aspire::NumberedOpportunity.new(opportunity_number_to_sync)
        integration.materializer.send(:materialize_job, numbered_opportunity_resource)
      end
      job = integration.jobs.find_by(remote_reference: opportunity_number_to_sync)
    end

    unless job
      puts colorize("Failed to find/create Job. Aborting.", 31)
      exit 1
    end

    puts colorize("Found/Created Job ID #{job.id} (Name: #{job.name})", 32)

    # --- Step 2: Sync Catalog Items ---
    puts SECTION_SEPARATOR
    puts colorize("Step 2: Syncing Catalog Items (Optional)...", 36)

    begin
      Sync::Current.track(integration_id: integration.id) do
        if integration.adapter.respond_to?(:retrieve_catalog_items) &&
          integration.materializer.respond_to?(:materialize_catalog_items)
          integration.adapter.retrieve_catalog_items
          integration.materializer.materialize_catalog_items
          puts colorize("Catalog items sync complete.", 32)
        else
          puts "Catalog item sync methods not fully available."
        end
      end
    rescue => e
      puts colorize("Warning: Failed to sync catalog items: #{e.message}", 33)
    end

    # Helper method to ensure correct materialization
    def ensure_materialization(integration, job)
      Sync::Current.track(integration_id: integration.id) do
        milestone_count = integration.milestones.where(job_id: job.id).count
        item_count = integration.milestone_items.joins(:milestone).where(milestones: { job_id: job.id }).count
        time_count = integration.milestone_times.joins(:milestone).where(milestones: { job_id: job.id }).count

        puts "Object counts - Milestones: #{milestone_count}, Items: #{item_count}, Times: #{time_count}"

        # Only apply fix if we have milestones but missing items/times
        if milestone_count > 0 && (item_count == 0 || time_count == 0)
          puts colorize("=== APPLYING MATERIALIZATION FIX ===", 35)

          # Get the work ticket IDs
          milestone_ids = integration.milestones.where(job_id: job.id).pluck(:id)
          milestone_remote_slugs = integration.integration_records
                                              .where(record_type: "Milestone", record_id: milestone_ids)
                                              .pluck(:remote_slug)
          work_ticket_ids = milestone_remote_slugs.map { |slug| slug.split(":").last }

          # Find cache objects for work tickets
          item_caches = integration.sync_caches.cached
                                   .where(remote_resource: "work_ticket_item")
                                   .where("raw_data->>'WorkTicketID' IN (?)", work_ticket_ids)

          time_caches = integration.sync_caches.cached
                                   .where(remote_resource: "work_ticket_time")
                                   .where("raw_data->>'WorkTicketID' IN (?)", work_ticket_ids)

          allocation_caches = integration.sync_caches.cached
                                         .where(remote_resource: "item_allocation")
                                         .where("raw_data->>'WorkTicketID' IN (?)", work_ticket_ids)

          puts "Found objects to materialize - Items: #{item_caches.count}, Times: #{time_caches.count}, Allocations: #{allocation_caches.count}"

          # Materialize items
          if item_count == 0 && item_caches.any?
            materialize_count = 0
            item_caches.find_each do |cache|
              begin
                integration.materializer.send(:materialize_milestone_item, cache.to_resource)
                materialize_count += 1
                print "." if materialize_count % 10 == 0
              rescue => e
                print "E"
              end
            end
            puts "\nMaterialized #{materialize_count} milestone items"
          end

          # Materialize times
          if time_count == 0 && time_caches.any?
            materialize_count = 0
            time_caches.find_each do |cache|
              begin
                resource = cache.to_resource
                work_ticket_id = resource.work_ticket_id

                # Find milestone through direct lookup
                milestone = integration.milestones.joins(:integration_records)
                                       .where(integration_records: { remote_slug: "aspire:work_ticket:#{work_ticket_id}" })
                                       .first

                if milestone
                  integration.materializer.send(:materialize_milestone_time, resource)
                  materialize_count += 1
                  print "." if materialize_count % 20 == 0
                else
                  print "x"
                end
              rescue => e
                print "E"
              end
            end
            puts "\nMaterialized #{materialize_count} milestone times"
          end

          # Materialize allocations
          if allocation_caches.any?
            materialize_count = 0
            allocation_caches.find_each do |cache|
              begin
                integration.materializer.send(:materialize_item_allocation, cache.to_resource)
                materialize_count += 1
                print "." if materialize_count % 10 == 0
              rescue => e
                print "E"
              end
            end
            puts "\nMaterialized #{materialize_count} item allocations"
          end

          puts colorize("=== MATERIALIZATION COMPLETE ===", 35)
        end
      end
    end

    # --- Step 3: Sync Job Dependencies ---
    puts SECTION_SEPARATOR
    puts colorize("Step 3: Syncing Job Dependencies (WorkTickets, Times, Items)...", 36)

    milestone_sync_successful = false

    begin
      Sync::SyncAspireJobJob.new.perform(integration.id, job.id)
      milestone_sync_successful = true
      puts colorize("Successfully synced WorkTickets and related items!", 32)

      # Apply our materialization fix conditionally
      ensure_materialization(integration, job)

    rescue ActiveRecord::RecordInvalid => e
      if e.message.include?("Catalog item must exist")
        puts colorize("Warning: Item allocations failed due to missing catalog items.", 33)
        milestone_sync_successful = true
      else
        puts colorize("ERROR during milestone sync: #{e.message}", 31)
      end
    rescue => e
      if e.message.match?(/cannot materialize aspire:contact:\d+; no cache found/)
        # Extract the contact ID from the error message
        contact_id = e.message.match(/cannot materialize aspire:contact:(\d+)/)[1]
        puts colorize("Creating placeholder for missing contact: #{contact_id}", 33)

        # Create a placeholder contact record manually
        Sync::Current.track(integration_id: integration.id) do
          contact = AspireClient::Resources::Contacts::RESPONSE_TYPE_MAPPING.new
          contact.contact_id = contact_id
          contact.last_name = "<remote record missing>"
          contact.first_name = "<[contact:#{contact_id}]>"
          contact.home_address = {}
          contact.office_address = {}

          # Materialize the placeholder contact
          integration.materializer.send(:materialize_identity, contact)
          puts colorize("Placeholder contact created, retrying sync...", 32)

          # Try the job sync again
          begin
            Sync::SyncAspireJobJob.new.perform(integration.id, job.id)
            milestone_sync_successful = true
            puts colorize("Successfully synced WorkTickets and related items after creating placeholder!", 32)

            # Apply our materialization fix here too
            ensure_materialization(integration, job)

          rescue ActiveRecord::RecordInvalid => e2
            if e2.message.include?("Catalog item must exist")
              puts colorize("Warning: Item allocations failed due to missing catalog items.", 33)
              milestone_sync_successful = true
            else
              puts colorize("ERROR during retry: #{e2.message}", 31)
            end
          end
        end
      else
        puts colorize("ERROR during milestone sync: #{e.message}", 31)
        puts e.backtrace.first(5).join("\n")
      end
    end

    # --- Step 4: Syncing Clock Times ---
    if milestone_sync_successful
      puts SECTION_SEPARATOR
      puts colorize("Step 4: Syncing Clock Times for Job Contacts...", 36)

      Sync::Current.track(integration_id: integration.id) do
        milestone_times = integration.milestone_times
                                     .joins(:milestone)
                                     .where(milestones: { job_id: job.id })

        if milestone_times.any?
          start_field = MilestoneTime.column_names.include?("start_time") ? "start_time" : "started_at"
          end_field = MilestoneTime.column_names.include?("end_time") ? "end_time" : "ended_at"

          min_start = milestone_times.minimum(start_field)&.to_date || 3.months.ago.to_date
          max_end = milestone_times.maximum(end_field)&.to_date || Time.current.to_date

          buffered_start = min_start - 7.days
          buffered_end = max_end + 7.days
          odata_start = buffered_start.beginning_of_day.utc.iso8601(6)
          odata_end_exclusive = (buffered_end + 1.day).beginning_of_day.utc.iso8601(6)

          contact_remote_ids = milestone_times.map do |mt|
            mt.identity&.integration_records&.find_by(integration_id: integration.id)&.remote_slug&.split(":")&.last
          end.compact.uniq

          job_contacts_remote_ids = contact_remote_ids

          if contact_remote_ids.any?
            puts "Found #{contact_remote_ids.size} contacts: #{contact_remote_ids.join(', ')}"
            puts "Requesting clock times for period #{buffered_start} to #{buffered_end}"

            filters = [
              "ClockStart ge #{odata_start} and ClockStart lt #{odata_end_exclusive}",
              "ContactID in (#{contact_remote_ids.join(',')})"
            ]

            cache_adapter = integration.cache_adapter
            materializer = integration.materializer

            status_range = integration.sync_status_ranges.find_or_create_by!(
              resource: "clock_times",
              start_timestamp: buffered_start.beginning_of_day.utc,
              end_timestamp: (buffered_end + 1.day).beginning_of_day.utc
            )
            status_range.update!(last_synced_at: nil)

            all_caches = []

            begin
              all_caches = cache_adapter.cache(
                remote_resource: :clock_time,
                sync_status: status_range,
                filters: filters
              )
              puts "Fetched #{all_caches.size} total Sync::Cache objects."
            rescue => e
              puts colorize("Error during CacheAdapter#cache: #{e.message}", 31)
            end

            clock_time_caches = all_caches.select { |c| c.remote_resource.to_sym == :clock_time }
            puts "Filtered to #{clock_time_caches.size} :clock_time records"

            if clock_time_caches.any?
              begin
                materializer.materialize_clock_times(all_caches)
                puts "Materializer#materialize_caches complete."
              rescue => e
                puts "ERROR during materialization: #{e.message}"
                puts e.backtrace.first(10).join("\n")
              end

              identity_ids = Identity.joins(:integration_records)
                                     .where(integration_records: {
                                       integration_id: integration.id,
                                       remote_slug: job_contacts_remote_ids.map { |rid| "aspire:contact:#{rid}" }
                                     }).pluck(:id)

              clock_count = integration.clock_times.where(identity_id: identity_ids).count
              puts "Clock Times linked to job contacts: #{clock_count}"
            else
              puts "No clock time Sync::Cache objects to materialize."
            end
          else
            puts colorize("Warning: No contact remote IDs found.", 33)
          end
        else
          puts colorize("Warning: No milestone times found. Skipping clock time sync.", 33)
        end
      end
    else
      puts colorize("Milestone sync failed. Skipping clock time sync.", 31)
    end

    # --- Step 5: Ensure Schedule Visits are Materialized ---
    if milestone_sync_successful
      puts SECTION_SEPARATOR
      puts colorize("Step 5: Ensuring Schedule Visits are Materialized...", 36)

      Sync::Current.track(integration_id: integration.id) do
        # Get the work ticket IDs for this job
        milestone_ids = integration.milestones.where(job_id: job.id).pluck(:id)
        milestone_remote_slugs = integration.integration_records
                                            .where(record_type: "Milestone", record_id: milestone_ids)
                                            .pluck(:remote_slug)
        work_ticket_ids = milestone_remote_slugs.map { |slug| slug.split(":").last }

        # Find work_ticket_visit cache objects for these work tickets
        work_ticket_visit_caches = integration.sync_caches.cached
                                              .where(remote_resource: "work_ticket_visit")
                                              .where("raw_data->>'WorkTicketID' IN (?)", work_ticket_ids)

        if work_ticket_visit_caches.any?
          puts "Found #{work_ticket_visit_caches.count} work_ticket_visit caches"

          # Check current schedule visits count
          current_schedule_visits = integration.schedule_visits.joins(:milestone)
                                               .where(milestones: { job_id: job.id })
                                               .count

          puts "Current schedule visits count: #{current_schedule_visits}"

          if current_schedule_visits < work_ticket_visit_caches.count
            puts colorize("Materializing missing schedule visits...", 32)
            begin
              integration.materializer.materialize_schedule_visits(caches: work_ticket_visit_caches)

              # Count after materialization
              new_schedule_visits = integration.schedule_visits.joins(:milestone)
                                               .where(milestones: { job_id: job.id })
                                               .count

              puts colorize("Schedule Visits materialized: #{new_schedule_visits}", 32)
            rescue => e
              puts colorize("ERROR during schedule visit materialization: #{e.message}", 31)
              puts e.backtrace.first(5).join("\n")
            end
          else
            puts colorize("All schedule visits are already materialized.", 32)
          end
        else
          puts colorize("No work_ticket_visit caches found for this job.", 33)
        end
      end
    end

    # --- Step 6: Materializing Services ---
    if milestone_sync_successful
      puts SECTION_SEPARATOR
      puts colorize("Step 6: Materializing Services...", 36)

      Sync::Current.track(integration_id: integration.id) do
        # Get the work ticket IDs for this job
        milestone_ids = integration.milestones.where(job_id: job.id).pluck(:id)
        milestone_remote_slugs = integration.integration_records
                                            .where(record_type: "Milestone", record_id: milestone_ids)
                                            .pluck(:remote_slug)
        work_ticket_ids = milestone_remote_slugs.map { |slug| slug.split(":").last }

        # First, identify opportunity_service records related to our milestones
        puts "Looking up opportunity_service records for milestones..."

        # Join to opportunity_service through work_ticket (milestone) records
        opportunity_service_caches = []

        # Get opportunity IDs from work ticket data
        opportunity_ids = integration.sync_caches.cached
                                     .where(remote_resource: "work_ticket")
                                     .where("raw_data->>'WorkTicketID' IN (?)", work_ticket_ids)
                                     .map { |wt| wt.raw_data["OpportunityID"] }
                                     .compact
                                     .uniq

        if opportunity_ids.any?
          puts "Found #{opportunity_ids.size} opportunity IDs for this job's work tickets"
          puts "Opportunity IDs: #{opportunity_ids.inspect}"

          # Convert IDs to strings to match the JSONB text format
          opportunity_id_strings = opportunity_ids.map(&:to_s)

          # Find opportunity_service records for these opportunities
          opportunity_service_caches = integration.sync_caches.cached
                                                  .where(remote_resource: "opportunity_service")
                                                  .where("raw_data->>'OpportunityID' IN (?)", opportunity_id_strings)

          puts "Found #{opportunity_service_caches.count} opportunity_service records"

          # Extract service IDs we need
          service_ids = opportunity_service_caches.map { |cache| cache.raw_data["ServiceID"] }.compact.uniq

          if service_ids.any?
            # Convert to strings for JSONB comparison
            service_id_strings = service_ids.map(&:to_s)

            puts "Need to fetch/verify #{service_ids.size} services: #{service_ids.join(', ')}"

            # Get service caches for these IDs
            existing_service_caches = integration.sync_caches.cached
                                                 .where(remote_resource: "service")
                                                 .where("raw_data->>'ServiceID' IN (?)", service_id_strings)

            existing_service_ids = existing_service_caches.map { |cache| cache.raw_data["ServiceID"] }
            missing_service_ids = service_ids - existing_service_ids

            if missing_service_ids.any?
              puts "Missing services to fetch: #{missing_service_ids.join(', ')}"
              puts "Requesting missing services from API..."

              begin
                # Fetch missing services from the API
                integration.cache_adapter.cache(
                  remote_resource: :service,
                  ids: missing_service_ids
                )

                puts "Successfully fetched missing services"
              rescue => e
                puts colorize("Error fetching services: #{e.message}", 31)
              end
            end

            # Now materialize services and link them to milestones
            puts "Materializing services..."
            service_caches = integration.sync_caches.cached
                                        .where(remote_resource: "service")
                                        .where("raw_data->>'ServiceID' IN (?)", service_id_strings)

            if service_caches.any?
              materialize_count = 0
              error_count = 0

              # Materialize all services
              service_caches.find_each do |cache|
                begin
                  integration.materializer.send(:materialize_service, cache.to_resource)
                  materialize_count += 1
                  print "."
                rescue => e
                  error_count += 1
                  print "E"
                  puts "\nError materializing service #{cache.remote_primary_id}: #{e.message}"
                end
              end

              puts "\nMaterialized #{materialize_count} services, #{error_count} errors"

              # Verify integration records exist for all services
              puts "Verifying service integration records..."
              service_id_count = service_ids.size
              integration_record_count = integration.integration_records
                                                    .where(remote_slug: service_ids.map { |id| "aspire:service:#{id}" })
                                                    .count

              if integration_record_count < service_id_count
                puts colorize("Warning: Found only #{integration_record_count} integration records for #{service_id_count} services", 33)

                # Create missing integration records
                service_caches.each do |cache|
                  service_id = cache.raw_data["ServiceID"]
                  remote_slug = "aspire:service:#{service_id}"

                  unless integration.integration_records.exists?(remote_slug: remote_slug)
                    # Find the service by name
                    service_name = cache.raw_data["ServiceName"]
                    service = Service.find_by(name: service_name, organization_id: integration.organization_id)

                    if service
                      puts "Creating integration record for service #{service_name} (ID: #{service.id})"
                      integration.integration_records.create!(
                        record: service,
                        remote_slug: remote_slug
                      )
                    end
                  end
                end

                # Count again after fixes
                fixed_count = integration.integration_records
                                         .where(remote_slug: service_ids.map { |id| "aspire:service:#{id}" })
                                         .count
                puts colorize("Fixed integration records: now have #{fixed_count} of #{service_id_count}", 32)
              else
                puts colorize("Service integration records look good: #{integration_record_count} of #{service_id_count}", 32)
              end

              # Now link services to milestones
              puts "Linking services to milestones..."
              link_count = 0
              link_errors = 0

              opportunity_service_caches.find_each do |opp_service_cache|
                begin
                  service_id = opp_service_cache.raw_data["ServiceID"]
                  opportunity_id = opp_service_cache.raw_data["OpportunityID"]

                  # Find the service record
                  service_record = integration.integration_records
                                              .where(remote_slug: "aspire:service:#{service_id}")
                                              .first

                  # Find related work tickets for this opportunity
                  related_work_tickets = integration.sync_caches.cached
                                                    .where(remote_resource: "work_ticket")
                                                    .where("raw_data->>'OpportunityID' = ?", opportunity_id.to_s)

                  if service_record && related_work_tickets.any?
                    related_work_tickets.each do |wt_cache|
                      wt_id = wt_cache.raw_data["WorkTicketID"]
                      milestone_record = integration.integration_records
                                                    .where(remote_slug: "aspire:work_ticket:#{wt_id}")
                                                    .first

                      if milestone_record && milestone_record.record
                        milestone = milestone_record.record
                        if milestone.service_id != service_record.record_id
                          milestone.update(service_id: service_record.record_id)
                          link_count += 1
                          print "L"
                        end
                      end
                    end
                  end
                rescue => e
                  link_errors += 1
                  print "E"
                  puts "\nError linking service: #{e.message}"
                end
              end

              puts "\nLinked #{link_count} milestones to services, #{link_errors} errors"

              # Verify services are linked
              current_service_count = integration.milestones
                                                 .where(job_id: job.id)
                                                 .where.not(service_id: nil)
                                                 .distinct
                                                 .count(:service_id)

              puts colorize("Services linked to job's milestones: #{current_service_count}", 32)
            else
              puts colorize("No service caches found after fetching", 33)
            end
          else
            puts colorize("No service IDs found in opportunity_service records", 33)
          end
        else
          puts colorize("No opportunity IDs found for this job's work tickets", 33)
        end
      end
    end

    # --- Step 7: Materializing Item Allocations ---
    if milestone_sync_successful
      puts SECTION_SEPARATOR
      puts colorize("Step 7: Materializing Item Allocations...", 36)

      Sync::Current.track(integration_id: integration.id) do
        # Get the work ticket IDs for this job
        milestone_ids = integration.milestones.where(job_id: job.id).pluck(:id)
        milestone_remote_slugs = integration.integration_records
                                            .where(record_type: "Milestone", record_id: milestone_ids)
                                            .pluck(:remote_slug)
        work_ticket_ids = milestone_remote_slugs.map { |slug| slug.split(":").last }

        # Find item allocation cache objects for these work tickets
        allocation_caches = integration.sync_caches.cached
                                       .where(remote_resource: "item_allocation")
                                       .where("raw_data->>'WorkTicketID' IN (?)", work_ticket_ids)

        if allocation_caches.any?
          puts "Found #{allocation_caches.count} item allocation caches"

          # Output some details about the caches to help diagnose
          puts "Sample item allocation fields from cache:"
          sample_cache = allocation_caches.first
          data = sample_cache.raw_data
          puts "  WorkTicketID: #{data['WorkTicketID']}"
          puts "  ItemName: #{data['ItemName']}"
          puts "  CatalogItemID: #{data['CatalogItemID'].inspect}"
          puts "  ItemType: #{data['ItemType']}"
          puts "  ItemUnitCost: #{data['ItemUnitCost']}"

          # Check current item allocations count
          current_allocations_count = integration.item_allocations
                                                 .where(milestone_id: milestone_ids)
                                                 .count

          puts "Current item allocations count: #{current_allocations_count}"

          if current_allocations_count < allocation_caches.count
            puts colorize("Materializing missing item allocations...", 32)
            begin
              materialize_count = 0
              error_count = 0

              # First, create catalog items for allocations that don't have them
              catalog_items_by_name = {}

              allocation_caches.each do |cache|
                if cache.raw_data["CatalogItemID"].blank? && cache.raw_data["ItemName"].present?
                  item_name = cache.raw_data["ItemName"]
                  item_type = cache.raw_data["ItemType"] || "Material"

                  # Skip if we already created a catalog item for this name
                  next if catalog_items_by_name[item_name]

                  # Look for existing catalog item with this name
                  existing_item = CatalogItem.find_by(name: item_name, organization_id: integration.organization_id)

                  if existing_item
                    catalog_items_by_name[item_name] = existing_item.id
                  else
                    # Create a temporary catalog item
                    temp_item = CatalogItem.new(
                      name: item_name,
                      description: "Auto-created from item allocation without catalog item ID",
                      item_type: item_type,
                      organization_id: integration.organization_id,
                      purchase_unit: "Each",
                      allocation_unit: "Each"
                    )

                    if temp_item.save
                      puts "Created temporary catalog item for '#{item_name}'"
                      catalog_items_by_name[item_name] = temp_item.id
                    else
                      puts "Failed to create catalog item: #{temp_item.errors.full_messages.join(', ')}"
                    end
                  end
                end
              end

              puts "Created/found #{catalog_items_by_name.size} catalog items for allocations"

              # Now create the allocations
              allocation_caches.find_each do |cache|
                begin
                  # Manual approach to materialize item allocations without requiring catalog_item_id
                  remote_slug = "aspire:item_allocation:#{cache.raw_data['ItemAllocationID']}"
                  work_ticket_id = cache.raw_data["WorkTicketID"]

                  # Skip if already linked through integration records
                  next if integration.integration_records.exists?(remote_slug: remote_slug)

                  # Find the milestone
                  milestone_id = integration.integration_records
                                            .where(remote_slug: "aspire:work_ticket:#{work_ticket_id}")
                                            .first&.record_id

                  if milestone_id
                    # Create the item allocation without remote_slug
                    item_allocation = ItemAllocation.new(
                      milestone_id: milestone_id,
                      organization_id: integration.organization_id
                    )

                    # Try to find catalog item id
                    catalog_item_id = nil

                    if cache.raw_data["CatalogItemID"].present?
                      # Try to find by remote ID
                      catalog_remote_slug = "aspire:catalog_item:#{cache.raw_data['CatalogItemID']}"
                      catalog_record = integration.integration_records.find_by(remote_slug: catalog_remote_slug)
                      catalog_item_id = catalog_record&.record_id
                    elsif cache.raw_data["ItemName"].present? && catalog_items_by_name[cache.raw_data["ItemName"]]
                      # Use our temporary item
                      catalog_item_id = catalog_items_by_name[cache.raw_data["ItemName"]]
                    end

                    if catalog_item_id.nil?
                      error_count += 1
                      print "C" # Missing catalog item
                      next
                    end

                    # Set the fields
                    item_allocation.catalog_item_id = catalog_item_id
                    item_allocation.unit_cost = Money.from_amount(cache.raw_data["ItemUnitCost"] || 0)
                    item_allocation.total_cost = Money.from_amount(cache.raw_data["ItemTotalCost"] || 0)
                    item_allocation.item_quantity = cache.raw_data["ItemQuantity"]

                    if item_allocation.save
                      # Create integration record
                      integration.integration_records.create!(
                        record: item_allocation,
                        remote_slug: remote_slug
                      )

                      materialize_count += 1
                      print "."
                    else
                      error_count += 1
                      print "F"
                      puts "\nValidation errors: #{item_allocation.errors.full_messages.join(', ')}"
                    end
                  else
                    error_count += 1
                    print "M" # Missing milestone
                  end
                rescue => e
                  error_count += 1
                  print "E"
                  puts "\nError with #{cache.remote_primary_id}: #{e.message}"
                end
              end
              puts "\nMaterialized #{materialize_count} item allocations, #{error_count} errors"

              # Count after materialization
              new_allocations_count = integration.item_allocations
                                                 .where(milestone_id: milestone_ids)
                                                 .count

              puts colorize("Item allocations after materialization: #{new_allocations_count}", 32)
            rescue => e
              puts colorize("ERROR during item allocation materialization: #{e.message}", 31)
              puts e.backtrace.first(5).join("\n")
            end
          else
            puts colorize("All item allocations are already materialized.", 32)
          end
        else
          puts colorize("No item allocation caches found for this job.", 33)
        end
      end
    end

    # --- Step 8: Final Summary ---
    puts SECTION_SEPARATOR
    puts colorize("Step 8: Final Summary for OpportunityNumber #{opportunity_number_to_sync}", 34)

    job.reload

    puts "Job: #{job.name} (ID: #{job.id}, Status: #{job.status})"
    puts "Work Tickets (Milestones):     #{integration.milestones.where(job_id: job.id).count}"
    puts "Work Ticket Items:             #{integration.milestone_items.joins(:milestone).where(milestones: { job_id: job.id }).count}"
    puts "Milestone Times:               #{integration.milestone_times.joins(:milestone).where(milestones: { job_id: job.id }).count}"
    puts "Schedule Visits:               #{integration.schedule_visits.joins(:milestone).where(milestones: { job_id: job.id }).count}"
    puts "Services:                      #{integration.milestones.where(job_id: job.id).where.not(service_id: nil).distinct.count(:service_id)}"
    puts "Item Allocations:              #{integration.item_allocations.where(milestone_id: integration.milestones.where(job_id: job.id).pluck(:id)).count}"

    if job_contacts_remote_ids.any?
      identity_ids = Identity.joins(:integration_records)
                             .where(integration_records: {
                               integration_id: integration.id,
                               remote_slug: job_contacts_remote_ids.map { |rid| "aspire:contact:#{rid}" }
                             }).pluck(:id)

      if identity_ids.any?
        clock_time_count = integration.clock_times.where(identity_id: identity_ids).count
        puts "Clock Times (for job contacts): #{clock_time_count}"
      else
        puts "Clock Times: No matching identities found."
      end
    else
      puts "Clock Times: No contact IDs captured."
    end

    puts colorize("\n=== Sync Complete ===", 34)
  end
end
