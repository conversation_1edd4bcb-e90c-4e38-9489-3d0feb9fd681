# frozen_string_literal: true

namespace :jobs do
  desc "Fix job statuses using new logic (Sidekiq background jobs)"
  task fix_status: :environment do
    puts "🔄 Starting job status fix"

    # Process each Aspire integration
    Integration.where(source: "aspire").find_each do |integration|
      puts "\n📋 Processing integration: #{integration.organization.name} (ID: #{integration.id})"

      job_count = integration.organization.jobs.count
      puts "   Found #{job_count} jobs to process"

      if job_count > 0
        # Enqueue Sidekiq job to process this integration
        Sync::QueueJobStatusFixJobs.perform_async(integration.id)
        puts "   ✅ Sidekiq job queued for #{job_count} jobs"
      else
        puts "   ⚠️  No jobs found for this integration"
      end
    end

    puts "\n📊 Summary:"
    puts "   Sidekiq jobs queued for all Aspire integrations"
    puts "   Monitor Sidekiq dashboard and Rails logs for progress"
    puts "\n✅ Job status fix processing queued!"
  end

  desc "Fix job statuses synchronously (for small datasets)"
  task fix_status_sync: :environment do
    puts "🔄 Starting synchronous job status fix"

    fixed_count = 0
    error_count = 0
    total_count = 0

    # Process each Aspire integration
    Integration.where(source: "aspire").find_each do |integration|
      puts "\n📋 Processing integration: #{integration.organization.name} (ID: #{integration.id})"

      # Set sync context
      Sync::Current.track(integration_id: integration.id) do
        materializer = integration.materializer

        # Get all jobs for this integration
        jobs = integration.organization.jobs.includes(:milestones, :milestone_times)

        puts "   Found #{jobs.count} jobs to check"

        jobs.find_each do |job|
          total_count += 1

          begin
            # Find the numbered opportunity for this job
            numbered_opportunity = Sync::Aspire::NumberedOpportunity.new(job.remote_reference.to_i)

            # Get current status
            old_status = job.status

            # Force update the job status using new logic
            materializer.send(:materialize_job, numbered_opportunity, force_status_update: true)

            # Check if status changed
            job.reload
            if job.status != old_status
              puts "   ✅ Job #{job.id} (#{job.name}): #{old_status} → #{job.status}"
              fixed_count += 1
            end

          rescue => e
            puts "   ❌ Error processing job #{job.id}: #{e.message}"
            error_count += 1
          end
        end
      end
    end

    puts "\n📊 Summary:"
    puts "   Total jobs processed: #{total_count}"
    puts "   Jobs with status changes: #{fixed_count}"
    puts "   Errors: #{error_count}"
    puts "   Errors: #{error_count}"
    puts "\n✅ Job status fix completed!"
  end

  desc "Preview job status changes without applying them"
  task preview_status_fix: :environment do
    puts "🔍 Previewing job status changes"

    preview_count = 0
    total_count = 0
    limit = 10

    # Process each Aspire integration
    Integration.where(source: "aspire").find_each do |integration|
      puts "\n📋 Integration: #{integration.organization.name} (ID: #{integration.id})"

      # Set sync context
      Sync::Current.track(integration_id: integration.id) do
        materializer = integration.materializer

        # Get all jobs for this integration
        jobs = integration.organization.jobs.includes(:milestones, :milestone_times)

        if jobs.count > limit
          puts "   Found #{jobs.count} jobs, limiting preview to first #{limit}"
          jobs = jobs.limit(limit)
        else
          puts "   Found #{jobs.count} jobs to check"
        end

        jobs.find_each do |job|
          total_count += 1

          begin
            # Get opportunity data
            opportunity_caches = materializer.send(:sync_caches)
                                             .by_aspire_opportunity_number(job.remote_reference.to_i)
                                             .map(&:to_resource)

            if opportunity_caches.empty?
              puts "   ⚠️  Job #{job.id} (#{job.name}): No opportunity data found"
              next
            end

            # Sort opportunities by recency (same as materialize_job)
            sorted_opportunities = opportunity_caches.sort_by do |opportunity|
              opportunity.modified_date || opportunity.created_date_time
            end.reverse.lazy

            # Get opportunity details for debugging and status calculation
            opportunity_status = sorted_opportunities.filter_map(&:opportunity_status).first&.strip
            job_status_name = sorted_opportunities.filter_map(&:job_status_name).first&.strip
            has_costs = materializer.send(:has_opportunity_costs?, sorted_opportunities)
            has_attendances = materializer.send(:has_job_attendances?, job.id)

            # Calculate what the new status should be (simulating force_status_update: true)
            if opportunity_status == "Won"
              new_status = materializer.send(:map_aspire_job_status, job_status_name)
            else
              new_status = (has_costs || has_attendances) ? :in_progress : :pending
            end

            # Check if this would be updated during normal sync (without force)
            would_update_normally = job.new_record? || materializer.send(:should_update_job_status?, job, sorted_opportunities)

            # Check if it would change
            if job.status != new_status.to_s
              force_indicator = would_update_normally ? "" : " (force required)"
              puts "   📝 Job #{job.id} (#{job.name}): #{job.status} → #{new_status}#{force_indicator}"
              puts "      OpportunityStatus: #{opportunity_status || 'nil'}"
              puts "      JobStatusName: #{job_status_name || 'nil'}"
              puts "      Has costs: #{has_costs}, Has attendances: #{has_attendances}"
              puts "      Would update in normal sync: #{would_update_normally}"
              preview_count += 1
            else
              puts "   ✅ Job #{job.id} (#{job.name}): #{job.status} (no change)"
              puts "      OpportunityStatus: #{opportunity_status || 'nil'}"
              puts "      JobStatusName: #{job_status_name || 'nil'}"
              puts "      Has costs: #{has_costs}, Has attendances: #{has_attendances}"
              puts "      Would update in normal sync: #{would_update_normally}"
            end

          rescue => e
            puts "   ❌ Error previewing job #{job.id}: #{e.message}"
            puts "      #{e.backtrace.first}"
          end
        end
      end
    end

    puts "\n📊 Preview Summary:"
    puts "   Total jobs checked: #{total_count}"
    puts "   Jobs that would change: #{preview_count}"
    puts "\n💡 Run 'rails jobs:fix_status' to apply these changes"
  end

  desc "Process job status fixes for a specific integration with batch control"
  task :fix_status_integration, [:integration_id, :batch_size, :max_batches, :start_batch] => :environment do |t, args|
    integration_id = args[:integration_id]&.to_i
    batch_size = args[:batch_size]&.to_i || 1000
    max_batches = args[:max_batches]&.to_i
    start_batch = args[:start_batch]&.to_i || 1

    unless integration_id
      puts "❌ Error: integration_id is required"
      puts "Usage: rails jobs:fix_status_integration[integration_id,batch_size,max_batches,start_batch]"
      puts "Example: rails jobs:fix_status_integration[1,500,10,1]"
      exit 1
    end

    integration = Integration.find_by(id: integration_id)
    unless integration
      puts "❌ Error: Integration with ID #{integration_id} not found"
      exit 1
    end

    puts "🔄 Processing job status fixes for integration: #{integration.organization.name} (ID: #{integration_id})"
    puts "   Batch size: #{batch_size}"
    puts "   Max batches: #{max_batches || 'unlimited'}"
    puts "   Start batch: #{start_batch}"

    job_count = integration.organization.jobs.count
    puts "   Total jobs: #{job_count}"

    if job_count > 0
      # Enqueue Sidekiq job with specific parameters
      # Note: This job uses keyword arguments, which is different from FetchCatalogItemsForSignupJob
      Sync::QueueJobStatusFixJobs.perform_async(
        integration_id,
        batch_size: batch_size,
        max_batches: max_batches,
        start_batch: start_batch
      )
      puts "   ✅ Sidekiq job queued with custom parameters"
    else
      puts "   ⚠️  No jobs found for this integration"
    end
  end
end
