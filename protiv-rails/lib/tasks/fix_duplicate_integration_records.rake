# frozen_string_literal: true

namespace :integration_records do
  desc "Fix duplicate integration records by keeping the most recent one"
  task fix_duplicates: :environment do
    # Find all duplicate integration_id, remote_slug combinations
    duplicates = IntegrationRecord.select("integration_id, remote_slug, COUNT(*) as count")
                                 .group("integration_id, remote_slug")
                                 .having("COUNT(*) > 1")
                                 .order("count DESC")

    puts "Found #{duplicates.count} duplicate integration_id, remote_slug combinations"

    duplicates.each do |duplicate|
      integration_id = duplicate.integration_id
      remote_slug = duplicate.remote_slug
      count = duplicate.count

      puts "Processing #{count} duplicates for integration_id=#{integration_id}, remote_slug=#{remote_slug}"

      # Get all records for this combination
      records = IntegrationRecord.where(integration_id: integration_id, remote_slug: remote_slug)
                                .order(updated_at: :desc)

      # Keep the most recently updated record
      keeper = records.first

      # Delete all other records
      to_delete = records.offset(1)
      delete_count = to_delete.count

      puts "  Keeping record ID #{keeper.id} (record_type=#{keeper.record_type}, record_id=#{keeper.record_id})"
      puts "  Deleting #{delete_count} duplicate records"

      # Delete the duplicates
      to_delete.destroy_all
    end

    puts "Finished fixing duplicate integration records"
  end

  desc "Find and report on duplicate integration records without deleting them"
  task report_duplicates: :environment do
    # Find all duplicate integration_id, remote_slug combinations
    duplicates = IntegrationRecord.select("integration_id, remote_slug, COUNT(*) as count")
                                 .group("integration_id, remote_slug")
                                 .having("COUNT(*) > 1")
                                 .order("count DESC")

    puts "Found #{duplicates.count} duplicate integration_id, remote_slug combinations"

    duplicates.each do |duplicate|
      integration_id = duplicate.integration_id
      remote_slug = duplicate.remote_slug
      count = duplicate.count

      puts "Found #{count} duplicates for integration_id=#{integration_id}, remote_slug=#{remote_slug}"

      # Get all records for this combination
      records = IntegrationRecord.where(integration_id: integration_id, remote_slug: remote_slug)
                                .order(updated_at: :desc)

      records.each do |record|
        puts "  ID: #{record.id}, record_type: #{record.record_type}, record_id: #{record.record_id}, created_at: #{record.created_at}, updated_at: #{record.updated_at}"
      end
    end
  end
end
