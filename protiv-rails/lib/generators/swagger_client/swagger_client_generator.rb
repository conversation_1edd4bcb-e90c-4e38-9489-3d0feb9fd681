# frozen_string_literal: true

require "rails/generators"

##
# we could not get any of the existing swagger client generators to work with the schema definition. So, we have a custom generator that looks like this.
# It's basically just mapping every possible value from Aspire into a into a Rails slash Ruby type thing
class SwaggerClientGenerator < Rails::Generators::NamedBase
  source_root File.expand_path("templates", __dir__)

  class_option :schema_path, type: :string

  def load_schema
    raise "schema_path is required" unless @options[:schema_path].present?

    raw_schema = File.read(Rails.root.join(@options[:schema_path]))
    @schema = JSON.parse(raw_schema)
    build_metadata
  end

  def normalize_name
    @underscore_module_name = [name, "client"].join("_").underscore
    @constant_module_name = @underscore_module_name.camelcase
    @base_module_name =
    @module_path = "lib/#{@underscore_module_name}"
  end

  def create_resources_module_file
    create_file "#{@module_path}/resources.rb" do
      content = <<~RUBY
      require_relative "./models"

      module #{@constant_module_name}
        module Resources
          def self.response_type(resource)
            RESOURCE_MAP.fetch(resource).const_get(:RESPONSE_TYPE_MAPPING)
          end

          class Base
            attr_reader :client

            def initialize(client)
              @client = client
            end
          end
      RUBY

      resources.each_value do |resource|
        content << "\n"
        content << optimize_indentation(resource_class_def(resource), 4)
      end

      content << "\n"
      resource_map_string = +"RESOURCE_MAP = {\n"


      resource_map_string << resources.each_value.map do |resource|
        "  #{resource.class_name.underscore}: #{resource.class_name}"
      end.join(",\n")

      resource_map_string << "\n}.with_indifferent_access.freeze"

      content << optimize_indentation(resource_map_string, 4)

      content << "  end\n"
      content << "end\n"
      content
    end
  end

  def create_resource_methods_module_file
    create_file "#{@module_path}/resource_methods.rb" do
      content = <<~RUBY
      require_relative "./resources"

      module #{@constant_module_name}
        module ResourceMethods
      RUBY

      resources.each_value.with_index do |resource, index|
        content << "\n" unless index == 0
        content << optimize_indentation(<<~RUBY, 4)
          def #{resource.class_name.underscore}
            Resources::#{resource.class_name}.new(client)
          end
        RUBY
      end

      content << "  end\n"
      content << "end\n"
      content
    end
  end

  # order matters here; the refs must be inserted into the schema_models hash
  # in `create_resources_module_file`.
  def instantiate_model_references
    loop do
      # surprisingly takes 4 loops to get everything for aspire
      count_before = schema_models.length

      keys = schema_models.keys
      keys.each do |key|
        model = schema_models[key]
        model.properties.map do |k, property|
          property["$ref"] || property.dig("items", "$ref")
        end.compact.uniq.each do |ref|
          schema_models[ref]
        end
      end

      break if schema_models.length == count_before
    end
  end

  def create_models_module_file
    create_file "#{@module_path}/models.rb" do
      content = <<~RUBY
      module #{@constant_module_name}
        module Models
          class ArrayOf
            def initialize(wrapped)
              @wrapped = wrapped
            end

            def assert_valid_value(arg)
              return unless arg
              raise "invalid value" unless arg.is_a?(Array)

              arg.each do |element|
                @wrapped.assert_valid_value(element)
              end
            end

            def cast(arg)
              arg&.map { |element| @wrapped.cast(element) }
            end
          end

          class Base
            include ActiveModel::Model
            include ActiveModel::Attributes
            include ActiveModel::Serialization

            def initialize(attributes = {})
              attributes ||= {}

              if attributes.is_a?(Array)
                super(attributes.map { |inner| inner.transform_keys { |k| k.to_s.underscore } })
              else
                super(attributes.transform_keys { |k| k.to_s.underscore })
              end
            end

            def self.assert_valid_value(*)
            end

            def self.cast(attrs)
              new(attrs)
            end

            def as_json(options = nil)
              serializable_hash.as_json(options)
            end

            private

            def read_attribute_for_serialization(attr)
              attr = attr.underscore
              res = super(attr)
              if res.is_a?(Base)
                res&.serializable_hash
              elsif @attributes[attr].type.is_a?(ArrayOf)
                res&.map { |x| x.serializable_hash }
              else
                res
              end
            end
          end
      RUBY

      ordered_schema_models.each do |model|
        content << "\n"
        content << optimize_indentation(model_named_class_def(model), 4)
      end
      content << "  end\nend\n"
      content
    end
  end

  def create_client_file
    create_file "#{@module_path}.rb", <<~RUBY
    require_relative "./#{@underscore_module_name}/resource_methods"
    require Rails.root.join("app", "services", "rate_limiter")

    module #{@constant_module_name}
      DEFAULT_URL = #{production_url.inspect}

      class Error < StandardError
        def initialize(inner)
          # FIXME: this is not ideal
          if Rails.env.test?
            if inner.is_a?(VCR::Errors::Error)
              puts inner
              raise inner
            end
          end

          super
        end
      end

      class Client
        attr_reader :token, :url, :client_id

        def initialize(token: nil, url: nil, client_id: nil)
          @token = token
          @url = url || DEFAULT_URL
          @client_id = client_id
        end

        def client
          @client ||= Faraday.new(url: url) do |builder|
            if @token.present?
              builder.request :authorization, "Bearer", @token
            end

            builder.response :logger, nil, { headers: false, bodies: { request: false, response: false } }
            builder.request :json
            builder.response :json
            builder.response :raise_error

            if client_id
              # 1200 requests per minute (20 requests per second)
              builder.request :rate_limiter,
                name: ["_aspire_rate_limit", client_id].join(":"),
                max_count: 200,
                period: 10
            end
          end
        end

        def token=(token)
          @client = nil
          @token = token
        end

        include ResourceMethods
      end
    end
    RUBY
  end

  private

  def prepend_comment_header(data)
    raise "expected data to be a string" unless data.is_a?(String)

    "#{frozen_string_literal_comment}\n\n#{generator_warning}\n#{data}"
  end

  def frozen_string_literal_comment
    "# frozen_string_literal: true"
  end

  def create_file(destination, *args, &block)
    if block
      wrapped = -> () { prepend_comment_header(block.call) }

      super(destination, *args, &wrapped)
    else
      data = prepend_comment_header(args.shift)

      super(destination, data, *args)
    end
  end

  def generator_warning
    path = Pathname.new(__FILE__).relative_path_from(Rails.root)
    @generator_warning ||= <<~COMMENT
    # This file was generated by a script. Direct modifications are discouraged.
    # source: #{path}
    # command: bin/rails generate #{ARGV.join(' ')}
    COMMENT
  end

  def resource_class_def(resource)
    content = "class #{resource.class_name} < Base\n"

    if (get = resource.requests[:get])
      items_ref = get.dig(:response_type, "schema", "items", "$ref")
      schema_ref = get.dig(:response_type, "schema", "$ref")

      if items_ref && schema_ref && items_ref != schema_ref
        raise "items_ref unexpectedly does not match schema_ref"
      end

      primitive_items = get.dig(:response_type, "schema", "items", "type")

      case primitive_items
      when "string"
        items_ref = "String"
      when nil
        nil
      else
        raise "unexpected primitive_items value #{primitive_items}"
      end

      ref = items_ref || schema_ref || primitive_items

      raise "no type mapping found" unless ref

      unless ref == "String"
        ref = "Models::#{schema_models[ref].class_name}"
      end

      content << optimize_indentation(<<~RUBY, 2)
        RESPONSE_TYPE_MAPPING = #{ref}
      RUBY
      content << "\n"
    end

    resource.requests.each_with_index do |(method_name, request_config), index|
      content << "\n" unless index == 0
      content << optimize_indentation(resource_class_request_def(method_name, request_config, resource), 2)
    end
    content << "end"
    content
  end

  def resource_class_request_def(method_name, request_config, resource)
    path_config = path_builder_def(request_config[:path], request_config[:path_parameters])

    request_model = if (ref = request_config.dig(:request_body, "schema", "$ref"))
                      schema_models[ref]
    end

    content = "def #{method_name}(query: {}"

    if path_config[:additional_kwargs]
      content << ", #{path_config[:additional_kwargs]}"
    end

    content << ", **kwargs" if request_model

    content << ")\n"
    content << optimize_indentation("path = #{path_config[:content]}", 2)

    case request_config[:request_method]
    when "get"
      content << optimize_indentation("result = client.get(path, query)", 2)
    when "post"
      raise "unconfigured request body" unless request_model
      content << optimize_indentation(<<~RUBY, 2)
        request_body = Models::#{request_model.class_name}.new(kwargs)

        if request_body.invalid?
          raise #{@constant_module_name}::Error,
            "Invalid request: \#{request_body.errors.map(&:full_message).join("\\n")\}"
        end

        result = client.post(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
      RUBY
    when "put"
      raise "unconfigured request body" unless request_model
      content << optimize_indentation(<<~RUBY, 2)
        request_body = Models::#{request_model.class_name}.new(kwargs)

        if request_body.invalid?
          raise #{@constant_module_name}::Error,
            "Invalid request: \#{request_body.errors.map(&:full_message).join("\\n")\}"
        end

        result = client.put(path) do |req|
          req.body = request_body.serializable_hash.as_json
        end
      RUBY
    when "delete"
      content << optimize_indentation("raise \"not implemented\"", 2)
    else
      raise "request method not implemented for #{request_config}"
    end

    content << optimize_indentation(map_result_def(request_config[:response_type]), 2)
    content << optimize_indentation("rescue => e", 0)
    content << optimize_indentation("  raise #{@constant_module_name}::Error.new(e)", 2)
    content << "end"
    content
  end

  def model_named_class_def(model_config)
    class_name = model_config.class_name

    content = "class #{class_name} < Base\n"
    model_config.properties.each do |key, property|
      content << optimize_indentation(model_define_active_model_attribute(key, property), 2)
    end
    content << "\n"
    model_config.properties.each do |key, property|
      if (validation = model_define_active_model_validation(key, property))
        content << optimize_indentation(validation, 2)
      end
    end
    content << "\n"
    content << optimize_indentation(model_attributes_method_def(model_config), 2)
    content << "end"
    content
  end

  def model_define_active_model_attribute(key, property)
    content = "attribute #{key.underscore.to_sym.inspect}"
    attr_type = case property["type"]
    when "integer"
                  ":integer"
    when "string"
                  case property["format"]
                  when "date-time", "date-span" # FIXME: I actually have no idea what they mean by 'date-span'
                    ":datetime"
                  when nil, "email"
                    ":string"
                  else
                    raise "unknown format #{property['format']}"
                  end
    when "boolean"
                  ":boolean"
    when "number"
                  if %w[double float].include?(property["format"])
                    ":float"
                  else
                    raise "unknown format #{property['format']}"
                  end
    when "array"
                  if (ref = property.dig("items", "$ref"))
                    model_class = schema_models[ref].class_name
                    "ArrayOf.new(#{model_class})"
                  else
                    attr_comment = "#{property}"
                    nil
                  end
    else
                  if property["$ref"]
                    schema_models[property["$ref"]].class_name
                  else
                    raise "not implemented"
                  end
    end

    if attr_type
      content << ", #{attr_type}"
    end

    if attr_comment
      content << " # #{attr_comment}"
    end

    content
  end

  def model_define_active_model_validation(key, original_property)
    property = original_property.deep_dup
    prop_type = property.delete("type")
    nullable = property.delete("nullable")
    format = property.delete("format")
    description = property.delete("description")
    readonly = property.delete("readOnly")

    content = []
    content << "presence: true" unless nullable || prop_type == "boolean"

    case prop_type
    when "integer"
      raise "not implemented #{key} #{original_property.inspect}" unless format == "int32"

      max = property.delete("maximum")
      min = property.delete("minimum")

      content << "numericality: true"

      if max || min
        content << "inclusion: { in: (#{min}..#{max}) }"
      end

      content << "allow_nil: true" if nullable
    when "string"
      case format
      when "date-time", "date-span"
        # nothing? # FIXME
      when nil
        # nothing
      when "email"
        # FIXME: should we handle this?
      else
        raise "unknown format #{format}"
      end

      max_length = property.delete("maxLength")
      min_length = property.delete("minLength")

      if max_length || min_length
        content << "length: (#{min_length}..#{max_length})"
      end

      content << "allow_blank: true" if nullable && content.any?
    when "boolean"
      # nothing
    when "number"
      if %w[double float].include?(format)
        max = property.delete("maximum")
        min = property.delete("minimum")

        if max || min
          inner = []
          inner << "less_than_or_equal_to: #{max.to_f}" if max
          inner << "greater_than_or_equal_to: #{min.to_f}" if min
          content << "numericality: { #{inner.join(', ')} }"
        else
          content << "numericality: true"
        end
        content << "allow_nil: true" if nullable
      else
        raise "unknown format #{property['format']}"
      end
    when "array"
      items = property.delete("items")
      min_items = property.delete("minItems")
      max_items = property.delete("maxItems")

      if min_items || max_items
        # FIXME: test that this works on arrays
        content << "length: (#{min_items}..#{max_items})"
      end
      # FIXME ?
      # if (ref = property.dig("items", "$ref"))
      #   model_class = schema_models[ref].class_name
      #   "ArrayOf.new(#{model_class})"
      # else
      #   attr_comment = "#{property}"
      #   nil
      # end
    else
      # raise "not imeplemented #{property.inspect}"
      if property["$ref"]
        property.delete("$ref")
      else
        raise "not implemented"
      end
    end

    raise "additional keys: #{key} #{property} (#{original_property})" if property.any?

    if content.any?
      content = ["validates #{key.underscore.to_sym.inspect}", *content]
      content = content.join(", ")
      content << "# #{description}" if description
      content
    end
  end

  def model_attributes_method_def(model_config)
    # attributes method for ActiveModel

    content = +"def attributes\n"
    content << optimize_indentation("{", 2)
    last_prop = model_config.properties.keys.last

    model_config.properties.each_key do |key|
      line = "#{key.inspect} => nil"
      line << "," unless key == last_prop
      content << optimize_indentation(line, 4)
    end

    content << optimize_indentation("}", 2)
    content << "end"
    content
  end

  def is_primitive?(type)
    ["integer", "string", "boolean", "number"].include?(type)
  end

  def map_result_def(response_type_config)
    return "result.body" unless response_type_config

    response_schema = response_type_config.fetch("schema")
    if (type = response_schema["type"])
      case type
      when "array"
        map_result_array_def(response_schema)
      when "string", "integer", "boolean"
        "result.body"
      else
        raise "eh? #{type}"
      end
    elsif (ref_type = response_schema["$ref"])
      class_name = schema_models[ref_type].class_name
      "Models::#{class_name}.new(result.body)"
    else
      raise "not implemented"
    end
  end

  def map_result_array_def(response_schema)
    if (item_type = response_schema.dig("items", "type"))
      case item_type
      when "string"
        "result.body"
      else
        raise "not implemented #{item_type}"
      end
    elsif (ref_type = response_schema.dig("items", "$ref"))
      class_name = schema_models[ref_type].class_name
      <<~RUBY
      result.body.map do |data|
        Models::#{class_name}.new(data)
      end
      RUBY
    else
      raise "not implemented"
    end
  end

  def path_builder_def(path, path_parameters)
    if path_parameters.any?
      kwargs = path_parameters.map { |x| "#{x["name"]}:" }.join(", ")

      segments = path.split("/").map do |segment|
        if (match = segment.match(/\A\{(.*)\}\Z/))
          match[1]
        else
          segment.inspect
        end
      end

      content = +"["

      last_index = segments.length - 1
      segments.each.with_index do |segment, index|
        content << segment

        unless index == last_index
          content << ", "
        end
      end

      content << "].join(\"\/\")"

      { content: content, additional_kwargs: kwargs }
    else
      { content: path.inspect }
    end
  end

  def schema_paths
    @schema.fetch("paths")
  end

  def build_metadata
    @resources = nil

    schema_paths.each do |path, endpoints|
      # puts path

      endpoints.each do |request_method, endpoint_config|
        tags = endpoint_config.fetch("tags")
        raise "tags #{tags}" unless tags.count == 1
        resource = resources[tags[0]]
        response_type = endpoint_config.dig("responses", "200", "content", "application/json")

        request_body = if endpoint_config["requestBody"]
          endpoint_config["requestBody"].dig("content", "application/json")
        end

        resource.add_request(
          path: path,
          request_method: request_method,
          parameters: endpoint_config["parameters"],
          request_body: request_body,
          response_type: response_type # optional
        )
        # puts "#{resource} #{path} #{request_method} has_parameters=#{endpoint_config.key?("parameters")} has_body=#{endpoint_config.key?('requestBody')}"
      end
    end
  end

  def resources
    @resources ||= Hash.new { |hash, key| hash[key] = Resource.new(name: key) }
  end

  class Resource
    def initialize(name:)
      @name = name
    end

    attr_reader :name

    def class_name
      name
    end

    def add_request(
      path:,
      request_method:,
      parameters:,
      request_body:,
      response_type: nil
    )
      path_parameters = []
      query_parameters = []
      header_parameters = []

      parameters&.each do |parameter|
        if parameter["in"] == "path"
          path_parameters << parameter
        elsif parameter["in"] == "query"
          query_parameters << parameter
        elsif parameter["in"] == "header"
          header_parameters << parameter
        else
          raise "not implemented #{parameter}"
        end
      end

      ident_segments = path.split("/")

      ident_segments.reject! { |x| x.match?(/\A\{.*\}\Z/) }
      ident_segments.shift if ident_segments.first.empty?
      ident_segments.shift if ident_segments.first == @name
      ident_segments.unshift(request_method) if ident_segments.empty?
      identifier = ident_segments.map(&:underscore).join("_").to_sym

      requests[identifier] = {
        path:,
        request_method:,
        query_parameters:,
        path_parameters:,
        header_parameters:,
        request_body:,
        response_type:
      }
    end

    def requests
      @requests ||= {}
    end

    attr_reader :name
  end

  # FIXME: this may not apply universally
  def production_url
    server = @schema.fetch("servers").find { |server| server["description"] == "production" }
    raise "no production url found" unless server
    server.fetch("url")
  end

  def schema_models
    @schema_models ||= Hash.new { |hash, key| hash[key] = Model.new(key, @schema) }
  end

  def ordered_schema_models
    # topological sort by reference dependencies
    all_models = schema_models.values
    seen_keys = Set.new

    resolved = []

    loop do
      now_resolved, all_models = all_models.partition { |model| model.references.empty? }

      puts "now_resolved = #{now_resolved.count}, all_models = #{all_models.count}"

      now_resolved.each do |model|
        seen_keys << model.key
        resolved << model
      end

      all_models.each do |model|
        model.instance_exec { @references -= seen_keys }
      end

      break if all_models.empty?
    end

    resolved
  end

  class Model
    attr_reader :key, :definition, :object_name

    def initialize(key, schema)
      puts "model: #{key}"
      @key = key
      keys = key.split("/")
      keys.shift if key.first == "#"
      @definition = schema.dig(*keys)
      @object_name = keys.last
    end

    def reset_references
      @references = nil
    end

    def references
      @references ||= find_references(properties)
    end

    def find_references(value, references = Set.new)
      if value.is_a?(Hash)
        value.each do |key, v|
          if key == "$ref" && v.is_a?(String)
            references << v
          else
            find_references(v, references)
          end
        end
      elsif value.is_a?(Array)
        value.each do |element|
          find_references(element, references)
        end
      end

      references
    end

    def properties
      @definition["properties"]
    end

    def class_name
      @class_name ||= @object_name.tr(".", "").camelize
    end
  end
end
