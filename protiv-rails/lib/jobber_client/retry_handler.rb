# frozen_string_literal: true

module <PERSON>berClient
  ##
  # Service for implementing intelligent retry logic with exponential backoff.
  #
  # This handler catches THROTTLED errors and implements retry logic,
  # calculates wait times based on throttle status, and provides
  # configurable retry limits and strategies.
  class RetryHandler
    class MaxRetriesExceeded < StandardError
      attr_reader :attempts, :last_error, :total_wait_time

      def initialize(message, attempts: nil, last_error: nil, total_wait_time: nil)
        super(message)
        @attempts = attempts
        @last_error = last_error
        @total_wait_time = total_wait_time
      end
    end

    # Retry configuration
    DEFAULT_MAX_RETRIES = 3
    DEFAULT_INITIAL_BACKOFF = 1.0
    DEFAULT_BACKOFF_MULTIPLIER = 2.0
    DEFAULT_MAX_BACKOFF = 60.0
    DEFAULT_JITTER_FACTOR = 0.1

    # Error patterns that should trigger retries
    RETRYABLE_ERRORS = [
      OAuth2::Error,
      Net::ReadTimeout,
      Net::OpenTimeout,
      Errno::ECONNRESET,
      Errno::ECONNREFUSED,
      SocketError
    ].freeze

    attr_reader :max_retries, :initial_backoff, :backoff_multiplier, :max_backoff, :jitter_factor

    ##
    # Initializes a new RetryHandler instance.
    #
    # @param max_retries [Integer] Maximum number of retry attempts
    # @param initial_backoff [Float] Initial backoff time in seconds
    # @param backoff_multiplier [Float] Multiplier for exponential backoff
    # @param max_backoff [Float] Maximum backoff time in seconds
    # @param jitter_factor [Float] Jitter factor to add randomness (0.0-1.0)
    def initialize(max_retries: DEFAULT_MAX_RETRIES,
                   initial_backoff: DEFAULT_INITIAL_BACKOFF,
                   backoff_multiplier: DEFAULT_BACKOFF_MULTIPLIER,
                   max_backoff: DEFAULT_MAX_BACKOFF,
                   jitter_factor: DEFAULT_JITTER_FACTOR)
      @max_retries = max_retries
      @initial_backoff = initial_backoff
      @backoff_multiplier = backoff_multiplier
      @max_backoff = max_backoff
      @jitter_factor = jitter_factor
    end



    ##
    # Executes a block with adaptive retry logic that can adjust batch size on throttling errors.
    #
    # This method detects when throttling errors are due to cost exceeding maximum available
    # and dynamically reduces the batch size between retry attempts until the request fits
    # within the available cost budget.
    #
    # @param query [String] GraphQL query being executed
    # @param variables [Hash] GraphQL variables (must include batch size parameter)
    # @param throttle_tracker [ThrottleStatusTracker, nil] Throttle status tracker
    # @param cost_calculator [QueryCostCalculator, nil] Cost calculator for batch size optimization
    # @param batch_size_param [Symbol] Parameter name for batch size (default: :first)
    # @param min_batch_size [Integer] Minimum allowed batch size (default: 1)
    # @yield [Hash] Block to execute with retry logic, receives updated variables
    # @return [Object] Result of the block execution
    # @raise [MaxRetriesExceeded] If max retries exceeded
    def with_adaptive_retry(query:, variables: {}, throttle_tracker: nil, cost_calculator: nil,
                           batch_size_param: :first, min_batch_size: 1, &block)
      attempt = 0
      total_wait_time = 0.0
      last_error = nil
      current_variables = variables.dup
      original_batch_size = current_variables[batch_size_param]

      # Initialize cost calculator if not provided
      cost_calculator ||= JobberClient::QueryCostCalculator.new

      loop do
        begin
          return yield(current_variables)
        rescue => error
          attempt += 1
          last_error = error

          # Check if error is retryable
          unless retryable_error?(error)
            Rails.logger.error("Non-retryable error encountered: #{error.class} - #{error.message}")
            raise error
          end

          # Check if we've exceeded max retries
          if attempt > max_retries
            raise MaxRetriesExceeded.new(
              "Max retries (#{max_retries}) exceeded. Last error: #{error.message}",
              attempts: attempt,
              last_error: last_error,
              total_wait_time: total_wait_time
            )
          end

          # Handle throttling errors with batch size adjustment
          if throttling_error?(error) && throttle_tracker && current_variables[batch_size_param]
            adjustment_result = adjust_batch_size_for_throttling(
              query: query,
              variables: current_variables,
              error: error,
              throttle_tracker: throttle_tracker,
              cost_calculator: cost_calculator,
              batch_size_param: batch_size_param,
              min_batch_size: min_batch_size,
              attempt: attempt
            )

            current_variables = adjustment_result[:variables]

            # If batch size couldn't be reduced further, use regular retry logic
            unless adjustment_result[:adjusted]
              wait_time = calculate_wait_time(attempt, error, throttle_tracker)
              total_wait_time += wait_time
              log_retry_attempt(attempt, error, wait_time, total_wait_time)
              sleep(wait_time) if wait_time > 0
              next
            end

            # Log the batch size adjustment
            log_batch_size_adjustment(
              attempt: attempt,
              original_batch_size: original_batch_size,
              new_batch_size: current_variables[batch_size_param],
              reason: adjustment_result[:reason]
            )

            # Continue immediately with reduced batch size (no wait time)
            next
          end

          # Calculate wait time for non-throttling errors or when adjustment isn't possible
          wait_time = calculate_wait_time(attempt, error, throttle_tracker)
          total_wait_time += wait_time

          # Log retry attempt
          log_retry_attempt(attempt, error, wait_time, total_wait_time)

          # Wait before retry
          sleep(wait_time) if wait_time > 0
        end
      end
    end

    ##
    # Executes a GraphQL request with adaptive retry logic that adjusts batch size on throttling.
    #
    # This is a convenience method that combines GraphQL error handling with adaptive retry logic.
    #
    # @param query [String] GraphQL query being executed
    # @param variables [Hash] GraphQL variables (must include batch size parameter)
    # @param throttle_tracker [ThrottleStatusTracker, nil] Throttle status tracker
    # @param cost_calculator [QueryCostCalculator, nil] Cost calculator for batch size optimization
    # @param batch_size_param [Symbol] Parameter name for batch size (default: :first)
    # @param min_batch_size [Integer] Minimum allowed batch size (default: 1)
    # @yield [Hash] Block to execute with retry logic, receives updated variables
    # @return [Object] Result of the block execution
    # @raise [MaxRetriesExceeded] If max retries exceeded
    def with_adaptive_graphql_retry(query:, variables: {}, throttle_tracker: nil, cost_calculator: nil,
                                   batch_size_param: :first, min_batch_size: 1, &block)
      with_adaptive_retry(
        query: query,
        variables: variables,
        throttle_tracker: throttle_tracker,
        cost_calculator: cost_calculator,
        batch_size_param: batch_size_param,
        min_batch_size: min_batch_size
      ) do |current_variables|
        begin
          yield(current_variables)
        rescue => error
          # Extract additional context for GraphQL errors
          if graphql_error?(error)
            handle_graphql_error(error, query, current_variables)
          end
          raise error
        end
      end
    end

    ##
    # Checks if an error is retryable.
    #
    # @param error [Exception] Error to check
    # @return [Boolean] True if error is retryable
    def retryable_error?(error)
      # Check for explicit retryable error types
      return true if RETRYABLE_ERRORS.any? { |klass| error.is_a?(klass) }

      # Check for throttling errors
      return true if throttling_error?(error)

      # Check for temporary server errors
      return true if temporary_server_error?(error)

      false
    end

    ##
    # Checks if an error indicates throttling.
    #
    # @param error [Exception] Error to check
    # @return [Boolean] True if error indicates throttling
    def throttling_error?(error)
      return false unless error.respond_to?(:message)

      message = error.message.downcase
      message.include?("throttled") ||
      message.include?("rate limit") ||
      message.include?("too many requests")
    end

    ##
    # Checks if an error is a temporary server error.
    #
    # @param error [Exception] Error to check
    # @return [Boolean] True if error is temporary
    def temporary_server_error?(error)
      return false unless error.respond_to?(:response)

      response = error.response
      return false unless response&.respond_to?(:status)

      # 5xx errors are generally retryable
      status = response.status.to_i
      status >= 500 && status < 600
    end

    private

    ##
    # Calculates wait time for retry attempt.
    #
    # @param attempt [Integer] Current attempt number
    # @param error [Exception] Error that triggered retry
    # @param throttle_tracker [ThrottleStatusTracker, nil] Throttle status tracker
    # @return [Float] Wait time in seconds
    def calculate_wait_time(attempt, error, throttle_tracker)
      # For throttling errors, use throttle-aware wait time
      if throttling_error?(error) && throttle_tracker
        throttle_wait_time = calculate_throttle_wait_time(error, throttle_tracker)
        return throttle_wait_time if throttle_wait_time > 0
      end

      # Use exponential backoff for other errors
      base_wait_time = initial_backoff * (backoff_multiplier ** (attempt - 1))

      # Apply maximum backoff limit
      base_wait_time = [base_wait_time, max_backoff].min

      # Add jitter to prevent thundering herd
      jitter = base_wait_time * jitter_factor * (rand - 0.5) * 2
      wait_time = base_wait_time + jitter

      [wait_time, 0].max
    end

    ##
    # Calculates wait time for throttling errors.
    #
    # @param error [Exception] Throttling error
    # @param throttle_tracker [ThrottleStatusTracker] Throttle status tracker
    # @return [Float] Wait time in seconds
    def calculate_throttle_wait_time(error, throttle_tracker)
      # Try to extract wait time from error response
      if error.respond_to?(:response) && error.response
        retry_after = extract_retry_after_header(error.response)
        return retry_after if retry_after > 0
      end

      # Use throttle tracker to calculate wait time
      current_status = throttle_tracker.current_status
      if current_status
        restore_rate = current_status[:restore_rate] || 500
        available = current_status[:currently_available]
        needed = [1000, available * 0.1].max # Need at least 10% of current or 1000

        return throttle_tracker.calculate_wait_time(needed_cost: needed)
      end

      # Fallback to default throttle wait time
      30.0
    end

    ##
    # Extracts Retry-After header from response.
    #
    # @param response [Object] HTTP response object
    # @return [Float] Retry after time in seconds
    def extract_retry_after_header(response)
      return 0.0 unless response.respond_to?(:headers)

      retry_after = response.headers["Retry-After"] || response.headers["retry-after"]
      return 0.0 unless retry_after

      # Handle both seconds and HTTP date format
      if retry_after.match?(/^\d+$/)
        retry_after.to_f
      else
        # Try to parse as HTTP date
        begin
          retry_time = Time.parse(retry_after)
          [retry_time - Time.current, 0].max
        rescue
          0.0
        end
      end
    end

    ##
    # Checks if an error is a GraphQL error.
    #
    # @param error [Exception] Error to check
    # @return [Boolean] True if error is GraphQL-related
    def graphql_error?(error)
      error.respond_to?(:response) &&
      error.response&.respond_to?(:body) &&
      error.response.body&.include?("errors")
    end

    ##
    # Handles GraphQL-specific error processing.
    #
    # @param error [Exception] GraphQL error
    # @param query [String] GraphQL query
    # @param variables [Hash] GraphQL variables
    # @return [void]
    def handle_graphql_error(error, query, variables)
      begin
        response_body = error.response.body
        parsed_response = JSON.parse(response_body)

        if parsed_response["errors"]
          parsed_response["errors"].each do |gql_error|
            Rails.logger.error("GraphQL Error: #{gql_error['message']}")

            # Log additional context for debugging
            if gql_error["extensions"]
              Rails.logger.error("GraphQL Error Extensions: #{gql_error['extensions']}")
            end
          end
        end
      rescue JSON::ParserError
        Rails.logger.error("Failed to parse GraphQL error response: #{response_body}")
      end
    end

    ##
    # Logs retry attempt information.
    #
    # @param attempt [Integer] Current attempt number
    # @param error [Exception] Error that triggered retry
    # @param wait_time [Float] Wait time before retry
    # @param total_wait_time [Float] Total wait time so far
    # @return [void]
    def log_retry_attempt(attempt, error, wait_time, total_wait_time)
      Rails.logger.warn({
        event: "jobber_retry_attempt",
        attempt: attempt,
        max_retries: max_retries,
        error_class: error.class.name,
        error_message: error.message,
        wait_time: wait_time,
        total_wait_time: total_wait_time,
        retryable: retryable_error?(error),
        throttling_error: throttling_error?(error)
      }.to_json)
    end

    ##
    # Adjusts batch size for throttling errors to fit within available cost budget.
    #
    # @param query [String] GraphQL query being executed
    # @param variables [Hash] Current GraphQL variables
    # @param error [Exception] Throttling error that occurred
    # @param throttle_tracker [ThrottleStatusTracker] Throttle status tracker
    # @param cost_calculator [QueryCostCalculator] Cost calculator
    # @param batch_size_param [Symbol] Parameter name for batch size
    # @param min_batch_size [Integer] Minimum allowed batch size
    # @param attempt [Integer] Current retry attempt number
    # @return [Hash] Adjustment result with :variables, :adjusted, and :reason
    def adjust_batch_size_for_throttling(query:, variables:, error:, throttle_tracker:,
                                        cost_calculator:, batch_size_param:, min_batch_size:, attempt:)
      current_batch_size = variables[batch_size_param]

      # Always use efficient cost calculation for throttling errors
      new_batch_size = calculate_reduced_batch_size(
        query: query,
        variables: variables,
        throttle_tracker: throttle_tracker,
        cost_calculator: cost_calculator,
        batch_size_param: batch_size_param,
        min_batch_size: min_batch_size,
        attempt: attempt,
        error: error
      )

      if new_batch_size < current_batch_size && new_batch_size >= min_batch_size
        adjusted_variables = variables.dup
        adjusted_variables[batch_size_param] = new_batch_size

        return {
          variables: adjusted_variables,
          adjusted: true,
          reason: "efficient_cost_calculation"
        }
      end

      # Cannot adjust batch size further
      {
        variables: variables,
        adjusted: false,
        reason: "minimum_batch_size_reached"
      }
    end



    ##
    # Calculates reduced batch size based on available cost budget.
    #
    # This method implements an optimized approach that calculates the average cost per item
    # from the failed request and determines the optimal batch size in one calculation
    # instead of repeatedly halving the limit.
    #
    # @param query [String] GraphQL query
    # @param variables [Hash] Current variables
    # @param throttle_tracker [ThrottleStatusTracker] Throttle status tracker
    # @param cost_calculator [QueryCostCalculator] Cost calculator
    # @param batch_size_param [Symbol] Parameter name for batch size
    # @param min_batch_size [Integer] Minimum allowed batch size
    # @param attempt [Integer] Current retry attempt number
    # @param error [Exception, nil] The throttling error that occurred (optional)
    # @return [Integer] New batch size
    def calculate_reduced_batch_size(query:, variables:, throttle_tracker:, cost_calculator:,
                                    batch_size_param:, min_batch_size:, attempt:, error: nil)
      current_batch_size = variables[batch_size_param]

      # Get available cost from throttle tracker or error response
      available_cost = get_available_cost(throttle_tracker, error)
      return min_batch_size if available_cost <= 0

      # Try to extract actual cost from the error response for more accurate calculation
      actual_cost = extract_actual_cost_from_error(error)

      if actual_cost && actual_cost > 0
        # Calculate average cost per item from the failed request
        avg_cost_per_item = actual_cost.to_f / current_batch_size

        # Apply safety margin (configurable, default 25%)
        safety_margin = calculate_safety_margin(attempt)

        # Calculate optimal batch size that fits within available budget
        optimal_batch_size = calculate_optimal_batch_size_from_avg_cost(
          available_cost: available_cost,
          avg_cost_per_item: avg_cost_per_item,
          safety_margin: safety_margin,
          min_batch_size: min_batch_size
        )

        Rails.logger.info({
          event: "jobber_optimal_batch_calculation",
          actual_cost: actual_cost,
          current_batch_size: current_batch_size,
          avg_cost_per_item: avg_cost_per_item.round(2),
          available_cost: available_cost,
          safety_margin: safety_margin,
          optimal_batch_size: optimal_batch_size,
          method: "cost_extraction"
        }.to_json)

        return optimal_batch_size if optimal_batch_size < current_batch_size
      end

      # Fallback to estimation-based calculation if cost extraction fails
      cost_per_item = estimate_cost_per_item(query, variables, cost_calculator, batch_size_param)

      # Apply safety margin and attempt-based reduction
      safety_margin = calculate_safety_margin(attempt)
      safe_budget = (available_cost * (1 - safety_margin)).floor
      max_safe_batch = (safe_budget / cost_per_item).floor

      # Ensure we don't exceed current batch size and respect minimum
      new_batch_size = [max_safe_batch, min_batch_size].max
      new_batch_size = [new_batch_size, current_batch_size - 1].min # Always reduce by at least 1

      Rails.logger.info({
        event: "jobber_fallback_batch_calculation",
        estimated_cost_per_item: cost_per_item,
        available_cost: available_cost,
        safety_margin: safety_margin,
        new_batch_size: new_batch_size,
        method: "estimation_fallback"
      }.to_json)

      new_batch_size
    end

    ##
    # Gets available cost from throttle tracker or error response.
    #
    # @param throttle_tracker [ThrottleStatusTracker] Throttle status tracker
    # @param error [Exception, nil] The throttling error (optional)
    # @return [Integer] Available cost
    def get_available_cost(throttle_tracker, error = nil)
      # Try to get fresh throttle status from error response first
      if error
        throttle_status = extract_throttle_status_from_error(error)
        if throttle_status && throttle_status[:currently_available]
          return throttle_status[:currently_available]
        end
      end

      # Fallback to throttle tracker
      return 1000 unless throttle_tracker&.current_status # Fallback value

      status = throttle_tracker.current_status
      status[:currently_available] || 0
    end

    ##
    # Extracts throttle status from error response.
    #
    # @param error [Exception] The throttling error
    # @return [Hash, nil] Throttle status if extractable, nil otherwise
    def extract_throttle_status_from_error(error)
      return nil unless error

      # Try to extract from error response body
      if error.respond_to?(:response) && error.response&.respond_to?(:body)
        begin
          response_body = error.response.body
          if response_body.is_a?(String)
            parsed_response = JSON.parse(response_body)

            # Check for Jobber API format: extensions.cost.throttleStatus
            if parsed_response.dig("extensions", "cost", "throttleStatus")
              throttle_status = parsed_response["extensions"]["cost"]["throttleStatus"]
              return {
                currently_available: throttle_status["currentlyAvailable"],
                maximum_available: throttle_status["maximumAvailable"],
                restore_rate: throttle_status["restoreRate"],
                updated_at: Time.current
              }
            end
          end
        rescue JSON::ParserError
          # Ignore JSON parsing errors
        end
      end

      nil
    end

    ##
    # Extracts actual cost from throttling error response.
    #
    # Handles the actual Jobber API response format:
    # {
    #   "errors": [{"message": "Throttled", "extensions": {"code": "THROTTLED"}}],
    #   "extensions": {
    #     "cost": {
    #       "requestedQueryCost": 1860006,
    #       "actualQueryCost": 0,
    #       "throttleStatus": {...}
    #     }
    #   }
    # }
    #
    # @param error [Exception, nil] The throttling error
    # @return [Integer, nil] Requested cost if extractable, nil otherwise
    def extract_actual_cost_from_error(error)
      return nil unless error

      # Handle JobberClient::ThrottledError specifically
      if error.is_a?(JobberClient::ThrottledError) && error.response_data
        response_data = error.response_data

        # Extract requested query cost from extensions
        requested_cost = response_data.dig("extensions", "cost", "requestedQueryCost")
        return requested_cost
      end

      # Try to extract from error response body (for other error types)
      if error.respond_to?(:response) && error.response&.respond_to?(:body)
        begin
          response_body = error.response.body
          if response_body.is_a?(String)
            parsed_response = JSON.parse(response_body)

            # Check if this is a throttling error
            if parsed_response["errors"]&.any? { |err| err["message"] == "Throttled" }
              # Extract requested query cost from extensions
              return parsed_response.dig("extensions", "cost", "requestedQueryCost")
            end
          end
        rescue JSON::ParserError
          # Ignore JSON parsing errors
        end
      end

      # Try to extract from error object if it's already parsed
      if error.respond_to?(:dig)
        if error["errors"]&.any? { |err| err["message"] == "Throttled" }
          return error.dig("extensions", "cost", "requestedQueryCost")
        end
      end

      nil
    end

    ##
    # Calculates safety margin based on attempt number.
    #
    # @param attempt [Integer] Current retry attempt number
    # @return [Float] Safety margin (0.0 to 1.0)
    def calculate_safety_margin(attempt)
      # Start with 25% safety margin, increase with each attempt for more conservative approach
      base_margin = 0.25
      attempt_penalty = (attempt - 1) * 0.05 # Add 5% per retry attempt

      # Cap at 40% maximum safety margin
      [base_margin + attempt_penalty, 0.4].min
    end

    ##
    # Calculates optimal batch size from average cost per item.
    #
    # @param available_cost [Integer] Available cost budget
    # @param avg_cost_per_item [Float] Average cost per item
    # @param safety_margin [Float] Safety margin to apply
    # @param min_batch_size [Integer] Minimum allowed batch size
    # @return [Integer] Optimal batch size
    def calculate_optimal_batch_size_from_avg_cost(available_cost:, avg_cost_per_item:,
                                                  safety_margin:, min_batch_size:)
      # Apply safety margin to available cost
      safe_available_cost = available_cost * (1 - safety_margin)

      # Calculate optimal batch size
      optimal_batch = (safe_available_cost / avg_cost_per_item).floor

      # Ensure we respect minimum batch size
      [optimal_batch, min_batch_size].max
    end

    ##
    # Estimates cost per item using the cost calculator (fallback method).
    #
    # @param query [String] GraphQL query
    # @param variables [Hash] Current variables
    # @param cost_calculator [QueryCostCalculator] Cost calculator
    # @param batch_size_param [Symbol] Parameter name for batch size
    # @return [Integer] Estimated cost per item
    def estimate_cost_per_item(query, variables, cost_calculator, batch_size_param)
      # Calculate cost per item using single item estimation
      single_item_variables = variables.dup
      single_item_variables[batch_size_param] = 1

      begin
        cost_info = cost_calculator.estimate_cost(query, single_item_variables)
        cost_info[:estimated_cost] || cost_info[:base_cost] || 10
      rescue => e
        Rails.logger.warn("Failed to calculate cost per item: #{e.message}")
        10 # Fallback cost estimate
      end
    end

    ##
    # Logs batch size adjustment information.
    #
    # @param attempt [Integer] Current attempt number
    # @param original_batch_size [Integer] Original batch size
    # @param new_batch_size [Integer] New batch size
    # @param reason [String] Reason for adjustment
    # @return [void]
    def log_batch_size_adjustment(attempt:, original_batch_size:, new_batch_size:, reason:)
      Rails.logger.info({
        event: "jobber_batch_size_adjustment",
        attempt: attempt,
        original_batch_size: original_batch_size,
        new_batch_size: new_batch_size,
        reduction_percentage: ((original_batch_size - new_batch_size).to_f / original_batch_size * 100).round(2),
        reason: reason
      }.to_json)
    end
  end
end
