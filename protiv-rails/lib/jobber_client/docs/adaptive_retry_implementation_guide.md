# Adaptive Retry Implementation Guide

This guide explains how to implement adaptive retry functionality for new GraphQL queries in the JobberClient.

## Overview

Adaptive retry is a system that automatically detects throttling errors and reduces batch size between retry attempts, creating a feedback loop that optimizes query cost for available budget. Instead of waiting and retrying with the same parameters that caused throttling, it immediately retries with a smaller batch size.

## What's Already Implemented

All fetch methods now automatically use pagination and adaptive retry:

### Jobs
- `fetch_jobs()` - Automatically uses pagination and adaptive retry

### Users
- `fetch_users()` - Automatically uses pagination and adaptive retry

### Visits
- `fetch_visits(job_id: "...")` - Automatically uses pagination and adaptive retry

### Products/Services
- `fetch_products_or_services()` - Automatically uses pagination and adaptive retry

### Clients
- `fetch_clients()` - Automatically uses pagination and adaptive retry

### Timesheet Entries
- `fetch_timesheet_entries()` - Automatically uses pagination and adaptive retry

### Properties
- `fetch_properties()` - Automatically uses pagination and adaptive retry

### Line Items
- `fetch_line_items()` - Automatically uses pagination and adaptive retry

### Sync Adapter
- `retrieve_jobs()` - Automatically uses pagination and adaptive retry
- `retrieve_users()` - Automatically uses pagination and adaptive retry
- `retrieve_visits()` - Automatically uses pagination and adaptive retry
- `retrieve_clients()` - Automatically uses pagination and adaptive retry
- All other retrieve methods automatically use pagination and adaptive retry

## Implementation Steps for New Queries

All fetch methods now automatically use pagination and adaptive retry. For new queries, follow this simplified pattern:

### Step 1: Create the Main Fetch Method

```ruby
def fetch_new_resource(limit: 100, batch_size: :auto, filter: nil, sort: nil)
  variables_base = {
    filter: filter,
    sort: sort
  }.compact

  pagination_manager.fetch_all_paginated_with_adaptive_retry(
    query_method: :new_resource_query,
    variables_base: variables_base,
    data_path: %w[data newResources edges],
    page_info_path: %w[data newResources pageInfo],
    batch_size: batch_size,
    max_items: limit,
    min_batch_size: configuration.adaptive_retry.default_min_batch_size
  )
end
```

### Step 2: Create the Query Method

Ensure you have a query method that returns the GraphQL query string:

```ruby
def new_resource_query
  <<~GRAPHQL
    query GetNewResource($first: Int, $after: String, $filter: NewResourceFilter, $sort: [NewResourceSort!]) {
      newResources(first: $first, after: $after, filter: $filter, sort: $sort) {
        edges {
          node {
            id
            name
            # other fields
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  GRAPHQL
end
```

### Step 3: Update Documentation

Add the new methods to the API documentation and usage examples.

## Key Requirements for Adaptive Retry

### 1. Query Must Support Pagination
The GraphQL query must accept `first` and `after` parameters:

```graphql
query GetNewResource($first: Int, $after: String) {
  newResources(first: $first, after: $after) {
    edges {
      node {
        # fields
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
```

### 2. Batch Size Parameter
The query variables must include a batch size parameter (usually `:first`). If your query uses a different parameter name, specify it:

```ruby
retry_handler.with_adaptive_graphql_retry(
  # ... other params
  batch_size_param: :limit, # If your query uses 'limit' instead of 'first'
  # ...
)
```

### 3. Cost Calculation Support
The query should be compatible with the `QueryCostCalculator`. Complex nested queries may need cost calculation adjustments.

## Configuration

Adaptive retry behavior is controlled by configuration:

```yaml
# config/jobber_rate_limiting.yml
development:
  adaptive_retry:
    enabled: true
    default_min_batch_size: 5
    safety_margin_start: 0.8
    safety_margin_minimum: 0.3
    enable_for_jobs: true
    enable_for_pagination: true
```

## Testing

When implementing adaptive retry for new queries:

1. **Test with throttling scenarios**: Simulate high-cost queries that trigger throttling
2. **Verify batch size reduction**: Ensure the system reduces batch size appropriately
3. **Check minimum batch size handling**: Test behavior when minimum batch size is reached
4. **Monitor performance**: Compare performance against traditional retry methods

## Common Patterns

### For Simple Resources
Use the standard pattern shown above for resources that follow typical GraphQL pagination patterns.

### For Complex Resources with Filters
Include filter parameters in the `variables_base`:

```ruby
def fetch_all_filtered_resource_with_adaptive_retry(filter_id:, batch_size: :auto, max_items: nil, min_batch_size: 5)
  fetch_all_paginated_with_adaptive_retry(
    query_method: :filtered_resource_query,
    variables_base: { filterId: filter_id }, # Include filters in base variables
    data_path: %w[data filteredResources edges],
    page_info_path: %w[data filteredResources pageInfo],
    batch_size: batch_size,
    max_items: max_items,
    min_batch_size: min_batch_size
  )
end
```

### For Non-Paginated Resources
For resources that don't support pagination, you can still use adaptive retry for the single request:

```ruby
def fetch_single_resource_with_adaptive_retry(resource_id:)
  # Note: No batch size parameter for non-paginated queries
  retry_handler.with_adaptive_graphql_retry(
    query: single_resource_query,
    variables: { resourceId: resource_id },
    throttle_tracker: throttle_tracker,
    cost_calculator: cost_calculator,
    batch_size_param: nil, # No batch size adjustment possible
    min_batch_size: 1
  ) do |current_variables|
    graphql_request(
      query: single_resource_query,
      variables: current_variables,
      enable_retries: false
    )
  end
end
```

## Benefits

- **Faster recovery**: No waiting between retries when batch size can be reduced
- **Better throughput**: Finds optimal batch size for current conditions
- **Automatic optimization**: Adapts to changing API cost constraints
- **Backward compatibility**: Existing code continues to work unchanged
