# frozen_string_literal: true

# Test script to verify real Jobber cache integration with Hash data
# This script tests the actual caching functionality

puts "=== Testing Real Jobber Cache Integration ==="
puts

# Test with mock Hash data that simulates what Jobber API returns
mock_job_data = {
  'id' => 'job_test_123',
  'title' => 'Test Caching Job',
  'jobNumber' => 'JOB-001',
  'updatedAt' => Time.current.iso8601,
  'status' => 'ACTIVE'
}

puts "Testing Record creation with Hash data..."
record = Sync::Jobber::Record.new(mock_job_data, remote_resource: :job)

puts "Record ID: #{record.id}"
puts "Record title: #{record.title}"
puts "Record job_number: #{record.job_number}"
puts "Record updated_at: #{record.updated_at}"
puts "Record source: #{record.source}"
puts "Record remote_resource: #{record.remote_resource}"

puts "\nTesting as_json conversion..."
json_data = record.as_json
puts "JSON data class: #{json_data.class}"
puts "JSON data keys: #{json_data.keys}"

puts "\nTesting dependency management..."
record.add_dependency(:client, 'client_456')
record.add_reverse_dependency(:visit, 'visit_789')
record.add_embedded_dependency(:line_item, 'item_101', { 'name' => 'Test Item' })

puts "Dependencies: #{record.dependencies}"
puts "Reverse dependencies: #{record.reverse_dependencies}"
puts "Embedded dependencies: #{record.embedded_dependencies}"

puts "\n=== Real Cache Integration Test Completed Successfully ==="
