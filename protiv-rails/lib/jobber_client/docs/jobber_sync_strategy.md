# Jobber Sync Strategy Implementation

## Overview

This document outlines the sync strategy implemented for Jobber integration based on the API's filtering capabilities.

## API Filtering Limitations

The Jobber GraphQL API has limited support for `updated_at` filtering. Most entities only support filtering by creation date or other specific date fields, not by last modification date.

## Entity-Specific Sync Strategies

| Entity | Supports updated_at Filter | Sync Strategy | Implementation |
|--------|---------------------------|---------------|----------------|
| **Clients** | ✅ Yes | Incremental Sync | `filter: { updatedAt: { after: date.iso8601 } }` |
| **TimeSheetEntries** | ✅ Yes | Incremental Sync | `filter: { updatedAt: { after: date.iso8601 } }` |
| **Jobs** | ❌ No | Full Sync | `updated_records_filter: nil` |
| **Visits** | ❌ No | Full Sync | `updated_records_filter: nil` |
| **Users** | ❌ No | Full Sync | `updated_records_filter: nil` |
| **Properties** | ❌ No | Full Sync | `updated_records_filter: nil` |
| **Products** | ❌ No | Full Sync | `updated_records_filter: nil` |
| **LineItems** | ❌ No | Full Sync | `updated_records_filter: nil` |

## Implementation Details

### Full Sync Entities
For entities that don't support `updated_at` filtering:
- Set `updated_records_filter: nil` in retrieve methods
- Cache adapter will perform full sync every time
- All records are fetched and cached on each sync operation

### Incremental Sync Entities
For entities that support `updated_at` filtering:
- Use `updated_records_filter: ->(date) { { filter: { updatedAt: { after: date.iso8601 } } } }`
- Cache adapter applies 5-minute safety overlap (`SYNC_OVERLAP_SAFETY`)
- Only records modified since last sync (minus overlap) are fetched

## Code Changes Made

### 1. Updated retrieve_jobs method
```ruby
def retrieve_jobs(limit: DEFAULT_FETCH_LIMIT, filter: nil, sort: nil, search_term: nil, **options)
  _retrieve(
    remote_resource: :job,
    limit: limit,
    filter: filter,
    sort: sort,
    search_term: search_term,
    updated_records_filter: nil, # Jobs don't support updated_at filtering - full sync only
    **options
  )
end
```

### 2. Updated retrieve_clients method
```ruby
def retrieve_clients(limit: 100, filter: nil, sort: nil, search_term: nil, **options)
  _retrieve(
    remote_resource: :client,
    limit: limit,
    filter: filter,
    sort: sort,
    search_term: search_term,
    updated_records_filter: ->(date) { { filter: { updatedAt: { after: date.iso8601 } } } },
    **options
  )
end
```

### 3. Similar updates for all other retrieve methods
Each method now explicitly documents whether it supports incremental sync and implements the appropriate strategy.

## Benefits

1. **Efficiency**: Entities that support incremental sync (Clients, TimeSheetEntries) only fetch changed records
2. **Reliability**: Entities without incremental support use full sync to ensure data consistency
3. **Clarity**: Each method clearly documents its sync strategy
4. **Maintainability**: Easy to update if Jobber adds more filtering capabilities

## Performance Considerations

- **Full sync entities** will have higher API usage and longer sync times
- **Incremental sync entities** will be much more efficient after initial sync
- Consider sync frequency based on entity update patterns
- Monitor API rate limits for full sync operations

## Future Improvements

If Jobber adds `updated_at` filtering support for more entities:
1. Update the corresponding retrieve method
2. Change `updated_records_filter: nil` to the incremental filter
3. Update this documentation

## Testing

The implementation can be tested with:
```ruby
integration = Integration.find(1)
sjca = Sync::Jobber::CacheAdapter.new(integration)

# Test full sync (jobs)
sjca.cache_jobs

# Test incremental sync (clients)
sjca.cache_clients
```
