# frozen_string_literal: true

# Test script to verify the cache_all fix

puts "=== Testing Cache All Fix ==="
puts

# Test the resource mapping
puts "Testing resource mappings..."

integration = Integration.find(1)
adapter = integration.adapter

# Test local_resource_for mappings
puts "user -> #{adapter.local_resource_for(:user)}"
puts "job -> #{adapter.local_resource_for(:job)}"
puts "visit -> #{adapter.local_resource_for(:visit)}"
puts "product_or_service -> #{adapter.local_resource_for(:product_or_service)}"
puts "timesheet_entry -> #{adapter.local_resource_for(:timesheet_entry)}"
puts "property -> #{adapter.local_resource_for(:property)}"
puts "line_item -> #{adapter.local_resource_for(:line_item)}"

puts "\nTesting retrieve method existence..."

# Test if the retrieve methods exist
methods_to_test = [
  'retrieve_identities',
  'retrieve_jobs', 
  'retrieve_milestones',
  'retrieve_catalog_items',
  'retrieve_milestone_times',
  'retrieve_properties',
  'retrieve_milestone_items'
]

methods_to_test.each do |method|
  if adapter.respond_to?(method)
    puts "✓ #{method} exists"
  else
    puts "✗ #{method} missing"
  end
end

puts "\n=== Test completed ==="
