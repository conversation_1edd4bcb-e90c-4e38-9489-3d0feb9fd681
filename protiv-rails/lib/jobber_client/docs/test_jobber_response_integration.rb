# frozen_string_literal: true

# Test script to verify the retry handler correctly extracts cost from actual Jobber API responses

class MockJobberError < StandardError
  attr_reader :response

  def initialize(message, response_body)
    super(message)
    @response = MockResponse.new(response_body)
  end
end

class MockResponse
  attr_reader :body

  def initialize(body)
    @body = body.is_a?(String) ? body : body.to_json
  end
end

class MockThrottleTracker
  attr_reader :current_status

  def initialize(status = {})
    @current_status = {
      currently_available: 10000,
      maximum_available: 50000,
      restore_rate: 500,
      updated_at: Time.now
    }.merge(status)
  end
end

class JobberResponseIntegrationTest
  def self.test_cost_extraction
    puts "=== Testing Cost Extraction from Actual Jobber Response ==="
    puts

    # Create actual Jobber API response
    jobber_response = {
      "errors" => [
        {
          "message" => "Throttled",
          "extensions" => {
            "code" => "THROTTLED",
            "documentation" => "https://developer.getjobber.com/docs/using_jobbers_api/api_rate_limits"
          }
        }
      ],
      "extensions" => {
        "cost" => {
          "requestedQueryCost" => 1860006,
          "actualQueryCost" => 0,
          "throttleStatus" => {
            "maximumAvailable" => 10000,
            "currentlyAvailable" => 8500,
            "restoreRate" => 500
          }
        }
      }
    }

    # Create mock error with Jobber response
    error = MockJobberError.new("Throttled", jobber_response)
    
    # Test cost extraction
    extracted_cost = extract_cost_from_error(error)
    throttle_status = extract_throttle_status_from_error(error)
    
    puts "Test Results:"
    puts "- Original request limit: 1000"
    puts "- Extracted requested cost: #{extracted_cost}"
    puts "- Available cost from response: #{throttle_status[:currently_available]}"
    puts "- Maximum available: #{throttle_status[:maximum_available]}"
    puts "- Restore rate: #{throttle_status[:restore_rate]}"
    puts

    if extracted_cost && throttle_status[:currently_available]
      # Calculate optimal batch size
      original_limit = 1000
      available_cost = throttle_status[:currently_available]
      safety_margin = 0.25

      avg_cost_per_item = extracted_cost.to_f / original_limit
      safe_available_cost = available_cost * (1 - safety_margin)
      optimal_batch_size = (safe_available_cost / avg_cost_per_item).floor

      puts "Optimal Batch Size Calculation:"
      puts "- Average cost per item: #{avg_cost_per_item.round(2)}"
      puts "- Safe available cost (75%): #{safe_available_cost.to_i}"
      puts "- Optimal batch size: #{optimal_batch_size}"
      puts "- Expected cost: #{(optimal_batch_size * avg_cost_per_item).to_i}"
      puts "- Fits in budget: #{(optimal_batch_size * avg_cost_per_item) <= available_cost}"
      puts

      # Compare with halving approach
      halved_once = (original_limit * 0.5).ceil
      halved_twice = (halved_once * 0.5).ceil
      halved_thrice = (halved_twice * 0.5).ceil

      puts "Comparison with Halving Approach:"
      puts "- 1st halving: #{halved_once} (cost: #{(halved_once * avg_cost_per_item).to_i})"
      puts "- 2nd halving: #{halved_twice} (cost: #{(halved_twice * avg_cost_per_item).to_i})"
      puts "- 3rd halving: #{halved_thrice} (cost: #{(halved_thrice * avg_cost_per_item).to_i})"
      puts "- Optimal approach: #{optimal_batch_size} (cost: #{(optimal_batch_size * avg_cost_per_item).to_i})"
      puts

      if optimal_batch_size > halved_thrice
        improvement = ((optimal_batch_size - halved_thrice).to_f / halved_thrice * 100).round(2)
        puts "✅ Optimization successful: #{improvement}% throughput improvement"
      else
        puts "⚠️  Optimization result: Similar to halving approach"
      end
    else
      puts "❌ Failed to extract cost or throttle status"
    end
  end

  def self.test_edge_cases
    puts "\n=== Testing Edge Cases ==="
    puts

    test_cases = [
      {
        name: "Missing cost information",
        response: { "errors" => [{ "message" => "Throttled" }] }
      },
      {
        name: "Malformed JSON",
        response: "invalid json"
      },
      {
        name: "Different cost structure",
        response: {
          "extensions" => {
            "cost" => 500000  # Simple number instead of object
          }
        }
      }
    ]

    test_cases.each do |test_case|
      puts "Testing: #{test_case[:name]}"
      
      begin
        error = MockJobberError.new("Test error", test_case[:response])
        extracted_cost = extract_cost_from_error(error)
        puts "- Extracted cost: #{extracted_cost || 'None'}"
      rescue => e
        puts "- Error: #{e.message}"
      end
      
      puts
    end
  end

  private

  def self.extract_cost_from_error(error)
    return nil unless error

    if error.respond_to?(:response) && error.response&.respond_to?(:body)
      begin
        response_body = error.response.body
        if response_body.is_a?(String)
          parsed_response = JSON.parse(response_body)
          
          # Check for Jobber API format
          if parsed_response.dig("extensions", "cost", "requestedQueryCost")
            return parsed_response["extensions"]["cost"]["requestedQueryCost"].to_i
          end
          
          # Fallback for simple cost format
          if parsed_response.dig("extensions", "cost")
            cost = parsed_response["extensions"]["cost"]
            return cost.is_a?(Hash) ? nil : cost.to_i
          end
        end
      rescue JSON::ParserError
        # Ignore JSON parsing errors
      end
    end

    nil
  end

  def self.extract_throttle_status_from_error(error)
    return {} unless error

    if error.respond_to?(:response) && error.response&.respond_to?(:body)
      begin
        response_body = error.response.body
        if response_body.is_a?(String)
          parsed_response = JSON.parse(response_body)
          
          if parsed_response.dig("extensions", "cost", "throttleStatus")
            throttle_status = parsed_response["extensions"]["cost"]["throttleStatus"]
            return {
              currently_available: throttle_status["currentlyAvailable"],
              maximum_available: throttle_status["maximumAvailable"],
              restore_rate: throttle_status["restoreRate"]
            }
          end
        end
      rescue JSON::ParserError
        # Ignore JSON parsing errors
      end
    end

    {}
  end
end

# Run the tests
if __FILE__ == $0
  require 'json'
  require 'time'
  
  JobberResponseIntegrationTest.test_cost_extraction
  JobberResponseIntegrationTest.test_edge_cases
end
