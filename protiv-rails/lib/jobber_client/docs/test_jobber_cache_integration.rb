# frozen_string_literal: true

# Test script to verify Jobber adapter cache integration
# This script tests the phase 2 implementation of Jobber caching

class JobberCacheIntegrationTest
  def self.run_tests
    puts "=== Testing Jobber Cache Integration ==="
    puts

    test_adapter_methods
    test_record_methods
    test_cache_adapter_integration
    test_incremental_sync

    puts "=== All tests completed ==="
  end

  def self.test_adapter_methods
    puts "Testing Adapter Methods..."

    # Create a mock integration
    mock_integration = OpenStruct.new(id: 1, source: 'jobber')
    adapter = Sync::Jobber::Adapter.new(mock_integration)

    # Test source method
    assert_equal 'jobber', adapter.source
    puts "✓ source method works"

    # Test remote_helper_models method
    assert_equal [], adapter.remote_helper_models
    puts "✓ remote_helper_models method works"

    # Test local_resource_for method
    assert_equal :identity, adapter.local_resource_for(:user)
    assert_equal :job, adapter.local_resource_for(:job)
    assert_equal :milestone, adapter.local_resource_for(:visit)
    puts "✓ local_resource_for method works"

    # Test remote_resource_for method
    assert_equal :user, adapter.remote_resource_for(:identity)
    assert_equal :job, adapter.remote_resource_for(:job)
    assert_equal :visit, adapter.remote_resource_for(:milestone)
    puts "✓ remote_resource_for method works"

    puts "Adapter methods test completed ✓"
    puts
  end

  def self.test_record_methods
    puts "Testing Record Methods..."

    # Test with OpenStruct data
    mock_data = OpenStruct.new(
      id: 'job_123',
      title: 'Test Job',
      updated_at: Time.current
    )

    record = Sync::Jobber::Record.new(mock_data, remote_resource: :job)

    # Test basic methods
    assert_equal 'job_123', record.id
    assert_equal 'job_123', record.primary_id
    assert_equal 'jobber', record.source
    assert_equal :job, record.remote_resource
    puts "✓ Basic record methods work"

    # Test dependency methods
    record.add_dependency(:client, 'client_456')
    record.add_reverse_dependency(:visit, 'visit_789')
    record.add_embedded_dependency(:line_item, 'item_101', { name: 'Test Item' })

    assert_equal({ client: 'client_456' }, record.dependencies)
    assert_equal({ visit: 'visit_789' }, record.reverse_dependencies)
    assert_equal [[:line_item, 'item_101', { name: 'Test Item' }]], record.embedded_dependencies
    puts "✓ Dependency methods work"

    # Test as_json method
    json_data = record.as_json
    assert json_data.is_a?(Hash)
    puts "✓ as_json method works"

    # Test with Hash data (like what Jobber client returns)
    hash_data = {
      'id' => 'job_456',
      'title' => 'Hash Job',
      'updatedAt' => Time.current.iso8601
    }

    hash_record = Sync::Jobber::Record.new(hash_data, remote_resource: :job)

    # Test basic methods with Hash data
    assert_equal 'job_456', hash_record.id
    assert_equal 'job_456', hash_record.primary_id
    assert_equal 'Hash Job', hash_record.title
    puts "✓ Hash data access works"

    # Test dependency methods with Hash record
    hash_record.add_dependency(:client, 'client_789')
    assert_equal({ client: 'client_789' }, hash_record.dependencies)
    puts "✓ Hash record dependency methods work"

    puts "Record methods test completed ✓"
    puts
  end

  def self.test_cache_adapter_integration
    puts "Testing Cache Adapter Integration..."

    # This would require a more complex setup with actual database records
    # For now, we'll just verify the methods exist and are callable

    mock_integration = OpenStruct.new(id: 1, source: 'jobber')
    adapter = Sync::Jobber::Adapter.new(mock_integration)

    # Test that retrieve_remote method exists and is callable
    assert adapter.respond_to?(:retrieve_remote)
    puts "✓ retrieve_remote method exists"

    # Test that _retrieve method returns an enumerator
    # This would need mocking of the client and actual API calls
    puts "✓ Cache adapter integration methods exist"

    puts "Cache adapter integration test completed ✓"
    puts
  end

  def self.test_incremental_sync
    puts "Testing Incremental Sync Support..."

    # Create a mock sync status
    mock_sync_status = OpenStruct.new(last_synced_at: 1.hour.ago)

    # Create a mock data object
    mock_data = OpenStruct.new(id: 'job_123', title: 'Test Job')

    # Test that the Retrieve class handles sync_status properly
    mock_integration = OpenStruct.new(id: 1, source: 'jobber')
    adapter = Sync::Jobber::Adapter.new(mock_integration)

    retrieve = Sync::Jobber::Adapter::Retrieve.new(
      adapter,
      remote_resource: :job,
      sync_status: mock_sync_status,
      updated_records_filter: ->(date) { { updated_since: date } }
    )

    assert_equal mock_sync_status.last_synced_at, retrieve.last_synced_at
    assert_equal false, retrieve.initial_sync?
    puts "✓ Incremental sync methods work"

    puts "Incremental sync test completed ✓"
    puts
  end

  private

  def self.assert_equal(expected, actual)
    unless expected == actual
      raise "Assertion failed: expected #{expected.inspect}, got #{actual.inspect}"
    end
  end

  def self.assert(condition)
    unless condition
      raise "Assertion failed: expected truthy value"
    end
  end
end

# Run the tests if this file is executed directly
if __FILE__ == $0
  JobberCacheIntegrationTest.run_tests
end
