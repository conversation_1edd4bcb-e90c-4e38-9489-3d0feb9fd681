# Optimized Adaptive Retry Implementation

## Overview

This document describes the implementation of an optimized adaptive retry strategy for Jobber GraphQL API throttling. Instead of repeatedly halving the batch size through multiple retry attempts, this approach calculates the optimal batch size in a single step using actual cost data from the throttling error.

## Problem with Previous Approach

The previous adaptive retry strategy used a simple halving approach:

1. Request with limit 1000 → throttled (cost: 620006)
2. Retry with limit 500 → still throttled
3. Retry with limit 250 → still throttled  
4. Retry with limit 125 → success

This required **3 retry attempts** and resulted in **suboptimal throughput**.

## New Optimized Approach

The new approach calculates the optimal batch size in one step:

1. Request with limit 1000 → throttled (cost: 620006)
2. Calculate: `avg_cost = 620006 / 1000 = 620.006 per item`
3. Calculate: `safe_limit = (available_cost / (avg_cost * (1 + safety_margin))).floor`
4. Retry with optimal limit → success

This requires **1 retry attempt** and achieves **maximum safe throughput**.

## Implementation Details

### Key Components

#### 1. Cost Extraction (`extract_actual_cost_from_error`)

Extracts actual cost from Jobber API throttling responses:

**Primary Format (Jobber API):**
```json
{
  "errors": [{"message": "Throttled", "extensions": {"code": "THROTTLED"}}],
  "extensions": {
    "cost": {
      "requestedQueryCost": 1860006,
      "actualQueryCost": 0,
      "throttleStatus": {
        "maximumAvailable": 10000,
        "currentlyAvailable": 8500,
        "restoreRate": 500
      }
    }
  }
}
```

**Legacy Support:**
```ruby
# Fallback patterns for backward compatibility:
"GraphQL query cost 620006 exceeds available budget"
"Rate limit exceeded: cost: 450000"
"Throttled - Cost 123456 too high"
"Request failed with cost=789012"
```

#### 2. Optimal Batch Size Calculation (`calculate_optimal_batch_size_from_avg_cost`)

```ruby
def calculate_optimal_batch_size_from_avg_cost(available_cost:, avg_cost_per_item:, 
                                              safety_margin:, min_batch_size:)
  # Apply safety margin to available cost
  safe_available_cost = available_cost * (1 - safety_margin)
  
  # Calculate optimal batch size
  optimal_batch = (safe_available_cost / avg_cost_per_item).floor
  
  # Ensure we respect minimum batch size
  [optimal_batch, min_batch_size].max
end
```

#### 3. Safety Margin Calculation (`calculate_safety_margin`)

Progressive safety margin that increases with retry attempts:

```ruby
def calculate_safety_margin(attempt)
  base_margin = 0.25  # 25% base safety margin
  attempt_penalty = (attempt - 1) * 0.05  # +5% per retry
  [base_margin + attempt_penalty, 0.4].min  # Cap at 40%
end
```

### Integration Points

#### RetryHandler Updates

- Modified `calculate_reduced_batch_size` to accept `error` parameter
- Added cost extraction and optimal calculation logic
- Enhanced logging with calculation method details

#### QueryCostCalculator Enhancements

- Added `calculate_optimal_batch_size_from_actual_cost` method
- Included efficiency gain calculations
- Added comparison with halving strategy

## Performance Benefits

### Example Scenarios

#### Scenario 1: High-Cost Query
**Input:**
- Original limit: 1000
- Requested cost: 1860006 (from Jobber API)
- Available budget: 8500
- Safety margin: 25%

**Results:**
- **Optimized approach:** 3 items in 1 retry
- **Halving approach:** 125 items after 3 retries (would exceed budget)
- **Benefit:** Immediate optimal sizing, avoids budget overrun

#### Scenario 2: Moderate-Cost Query
**Input:**
- Original limit: 100
- Actual cost: 8500
- Available budget: 10000
- Safety margin: 20%

**Results:**
- **Optimized approach:** 94 items in 1 retry
- **Halving approach:** 50 items in multiple retries
- **Throughput improvement:** 88%

### Key Advantages

1. **Reduced API Calls:** Single retry instead of multiple attempts
2. **Maximum Throughput:** Calculates exact optimal batch size
3. **Better Resource Utilization:** Uses available cost budget efficiently
4. **Faster Recovery:** Immediate optimal sizing after throttling

## Configuration

### Safety Margins

- **Base margin:** 25% (configurable)
- **Attempt penalty:** +5% per retry attempt
- **Maximum margin:** 40% cap

### Fallback Behavior

If cost extraction fails, the system falls back to the original estimation-based approach:

```ruby
# Fallback to estimation-based calculation
cost_per_item = estimate_cost_per_item(query, variables, cost_calculator, batch_size_param)
safety_margin = calculate_safety_margin(attempt)
safe_budget = (available_cost * (1 - safety_margin)).floor
max_safe_batch = (safe_budget / cost_per_item).floor
```

## Logging and Monitoring

### Enhanced Logging

The implementation includes detailed logging for monitoring and debugging:

```json
{
  "event": "jobber_optimal_batch_calculation",
  "actual_cost": 8500,
  "current_batch_size": 100,
  "avg_cost_per_item": 85.0,
  "available_cost": 10000,
  "safety_margin": 0.2,
  "optimal_batch_size": 94,
  "method": "cost_extraction"
}
```

### Metrics Tracked

- Average cost per item accuracy
- Efficiency gains vs halving approach
- Cost extraction success rate
- Retry attempt reduction

## Error Handling

### Cost Extraction Failures

- Graceful fallback to estimation-based calculation
- Logging of extraction failures for pattern analysis
- Conservative safety margins when actual cost unavailable

### Edge Cases

- Minimum batch size enforcement
- Zero or negative cost handling
- Budget constraint validation

## Testing

Run the demonstration to see the implementation in action:

```bash
ruby protiv-rails/lib/jobber_client/docs/optimized_adaptive_retry_example.rb
```

This will show:
- Cost extraction from various error formats
- Optimal batch size calculations
- Performance comparisons with halving approach
- Efficiency gains and throughput improvements

## Future Enhancements

1. **Machine Learning:** Use historical cost data to improve predictions
2. **Dynamic Safety Margins:** Adjust based on API stability patterns
3. **Cost Prediction:** Proactive batch sizing before throttling occurs
4. **Multi-Query Optimization:** Coordinate batch sizes across concurrent requests
