# frozen_string_literal: true

# Test commands to verify cache_all is working

puts <<~COMMANDS
  # Test commands for Rails console to verify cache_all fix:
  
  # 1. Get your integration
  integration = Integration.find(1)
  
  # 2. Create cache adapter
  sjca = Sync::Jobber::CacheAdapter.new(integration)
  
  # 3. Test individual caching (should work)
  sjca.cache_users
  sjca.cache_jobs
  
  # 4. Test cache_all (should work now without NoMethodError)
  sjca.cache_all
  
  # 5. Check results
  puts "Users cached: #{Sync::Cache.jobber_users.count}"
  puts "Jobs cached: #{Sync::Cache.jobber_jobs.count}"
  puts "Clients cached: #{Sync::Cache.jobber_clients.count}"
  puts "Total Jobber caches: #{Sync::Cache.jobber.count}"
  
  # 6. View sample cached data
  puts "Sample user data:"
  puts Sync::Cache.jobber_users.first&.raw_data
  
  puts "Sample job data:"
  puts Sync::Cache.jobber_jobs.first&.raw_data
COMMANDS
