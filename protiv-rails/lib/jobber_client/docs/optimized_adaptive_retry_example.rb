# frozen_string_literal: true

# Example demonstrating the optimized adaptive retry strategy
# This shows how the new approach calculates optimal batch size in one step
# instead of repeatedly halving the limit.

# Standalone demonstration without requiring full Rails environment

class OptimizedAdaptiveRetryExample
  def self.demonstrate
    puts "=== Optimized Adaptive Retry Strategy Demo ==="
    puts

    # Simulate a throttling scenario
    original_limit = 1000
    actual_cost = 620006
    available_cost = 10000
    safety_margin = 0.25

    puts "Scenario:"
    puts "- Original request limit: #{original_limit}"
    puts "- Actual cost from API: #{actual_cost}"
    puts "- Available cost budget: #{available_cost}"
    puts "- Safety margin: #{(safety_margin * 100).to_i}%"
    puts

    # Calculate using the new optimized approach
    result = calculate_optimal_batch_size_from_actual_cost(
      available_cost: available_cost,
      actual_cost: actual_cost,
      failed_batch_size: original_limit,
      safety_margin: safety_margin
    )

    puts "New Optimized Approach:"
    puts "- Average cost per item: #{result[:avg_cost_per_item]}"
    puts "- Safe available cost: #{result[:safe_available_cost]}"
    puts "- Optimal batch size: #{result[:batch_size]}"
    puts "- Efficiency gain vs halving: #{result[:efficiency_gain]}%"
    puts

    # Compare with old halving approach
    puts "Old Halving Approach:"
    halved_once = (original_limit * 0.5).ceil
    halved_twice = (halved_once * 0.5).ceil
    halved_thrice = (halved_twice * 0.5).ceil

    puts "- After 1st retry: #{halved_once}"
    puts "- After 2nd retry: #{halved_twice}"
    puts "- After 3rd retry: #{halved_thrice}"
    puts

    # Calculate cost efficiency
    avg_cost = actual_cost.to_f / original_limit
    optimal_cost = result[:batch_size] * avg_cost
    halved_cost = halved_thrice * avg_cost

    puts "Cost Analysis:"
    puts "- Optimal approach cost: #{optimal_cost.to_i} (fits in budget: #{optimal_cost <= available_cost})"
    puts "- Halving approach cost: #{halved_cost.to_i} (fits in budget: #{halved_cost <= available_cost})"

    # Calculate the actual usable batch size for halving approach
    # Since halving approach would exceed budget, find the largest safe size
    max_safe_halving = (available_cost / avg_cost).floor
    actual_halving_batch = [halved_thrice, max_safe_halving].min

    puts "- Halving approach (budget-constrained): #{actual_halving_batch}"
    puts "- Throughput improvement: #{((result[:batch_size] - actual_halving_batch).to_f / actual_halving_batch * 100).round(2)}%"
    puts

    puts "=== Summary ==="
    puts "The optimized approach calculates the exact optimal batch size in one step,"
    puts "avoiding multiple retry attempts and maximizing throughput within the cost budget."
  end

  def self.demonstrate_realistic_scenario
    puts "\n=== Realistic Scenario Demo ==="
    puts

    # More realistic scenario where the benefit is clearer
    original_limit = 100
    actual_cost = 8500
    available_cost = 10000
    safety_margin = 0.2

    puts "Realistic Scenario:"
    puts "- Original request limit: #{original_limit}"
    puts "- Actual cost from API: #{actual_cost}"
    puts "- Available cost budget: #{available_cost}"
    puts "- Safety margin: #{(safety_margin * 100).to_i}%"
    puts

    # Calculate using the new optimized approach
    result = calculate_optimal_batch_size_from_actual_cost(
      available_cost: available_cost,
      actual_cost: actual_cost,
      failed_batch_size: original_limit,
      safety_margin: safety_margin
    )

    puts "New Optimized Approach:"
    puts "- Average cost per item: #{result[:avg_cost_per_item]}"
    puts "- Safe available cost: #{result[:safe_available_cost]}"
    puts "- Optimal batch size: #{result[:batch_size]}"
    puts

    # Compare with old halving approach
    puts "Old Halving Approach:"
    halved_once = (original_limit * 0.5).ceil
    puts "- After 1st retry: #{halved_once}"
    puts

    # Calculate cost efficiency
    avg_cost = actual_cost.to_f / original_limit
    optimal_cost = result[:batch_size] * avg_cost
    halved_cost = halved_once * avg_cost

    puts "Efficiency Comparison:"
    puts "- Optimal approach: #{result[:batch_size]} items (cost: #{optimal_cost.to_i})"
    puts "- Halving approach: #{halved_once} items (cost: #{halved_cost.to_i})"
    puts "- Throughput improvement: #{((result[:batch_size] - halved_once).to_f / halved_once * 100).round(2)}%"
    puts "- Requests saved: Instead of 3 retries, get optimal size in 1 calculation"
  end

  def self.demonstrate_jobber_api_scenario
    puts "\n=== Jobber API Scenario Demo ==="
    puts

    # Using actual Jobber API response data
    original_limit = 1000
    requested_cost = 1860006  # From actual Jobber response
    available_cost = 10000    # From throttleStatus.currentlyAvailable
    safety_margin = 0.25

    puts "Jobber API Scenario (from actual response):"
    puts "- Original request limit: #{original_limit}"
    puts "- Requested cost from API: #{requested_cost}"
    puts "- Available cost budget: #{available_cost}"
    puts "- Safety margin: #{(safety_margin * 100).to_i}%"
    puts

    # Calculate using the new optimized approach
    result = calculate_optimal_batch_size_from_actual_cost(
      available_cost: available_cost,
      actual_cost: requested_cost,
      failed_batch_size: original_limit,
      safety_margin: safety_margin
    )

    puts "New Optimized Approach:"
    puts "- Average cost per item: #{result[:avg_cost_per_item]}"
    puts "- Safe available cost: #{result[:safe_available_cost]}"
    puts "- Optimal batch size: #{result[:batch_size]}"
    puts

    # Compare with old halving approach
    puts "Old Halving Approach:"
    halved_once = (original_limit * 0.5).ceil
    halved_twice = (halved_once * 0.5).ceil
    halved_thrice = (halved_twice * 0.5).ceil
    puts "- After 1st retry: #{halved_once}"
    puts "- After 2nd retry: #{halved_twice}"
    puts "- After 3rd retry: #{halved_thrice}"
    puts

    # Calculate cost efficiency
    avg_cost = requested_cost.to_f / original_limit
    optimal_cost = result[:batch_size] * avg_cost

    puts "Efficiency Analysis:"
    puts "- Optimal approach: #{result[:batch_size]} items (cost: #{optimal_cost.to_i})"
    puts "- Fits in budget: #{optimal_cost <= available_cost}"
    puts "- Retry attempts needed: 1 (vs 3+ with halving)"
    puts "- Efficiency: Calculates exact optimal size instead of guessing"
  end

  def self.demonstrate_cost_extraction
    puts "\n=== Cost Extraction Demo ==="
    puts

    # Test actual Jobber API response format
    puts "Testing actual Jobber API response format:"
    jobber_response = {
      "errors" => [
        {
          "message" => "Throttled",
          "extensions" => {
            "code" => "THROTTLED",
            "documentation" => "https://developer.getjobber.com/docs/using_jobbers_api/api_rate_limits"
          }
        }
      ],
      "extensions" => {
        "cost" => {
          "requestedQueryCost" => 1860006,
          "actualQueryCost" => 0,
          "throttleStatus" => {
            "maximumAvailable" => 10000,
            "currentlyAvailable" => 10000,
            "restoreRate" => 500
          }
        }
      }
    }

    extracted_cost = extract_cost_from_jobber_response(jobber_response)
    throttle_status = extract_throttle_status_from_jobber_response(jobber_response)

    puts "Jobber API Response:"
    puts "- Extracted requested cost: #{extracted_cost}"
    puts "- Currently available: #{throttle_status[:currently_available]}"
    puts "- Maximum available: #{throttle_status[:maximum_available]}"
    puts "- Restore rate: #{throttle_status[:restore_rate]}"
    puts

    # Test legacy error message formats for backward compatibility
    puts "Testing legacy error message formats:"
    test_cases = [
      "GraphQL query cost 620006 exceeds available budget",
      "Rate limit exceeded: cost: 450000",
      "Throttled - Cost 123456 too high",
      "Request failed with cost=789012"
    ]

    test_cases.each_with_index do |error_message, index|
      puts "Legacy test #{index + 1}: #{error_message}"
      extracted_cost = extract_cost_from_message(error_message)
      puts "Extracted cost: #{extracted_cost || 'Not found'}"
      puts
    end
  end

  private

  def self.calculate_optimal_batch_size_from_actual_cost(available_cost:, actual_cost:,
                                                        failed_batch_size:, safety_margin: 0.25)
    return { batch_size: 1, reason: 'invalid_input' } if actual_cost <= 0 || failed_batch_size <= 0

    # Calculate average cost per item from actual data
    avg_cost_per_item = actual_cost.to_f / failed_batch_size

    # Apply safety margin to available cost
    safe_available_cost = available_cost * (1 - safety_margin)

    # Calculate optimal batch size
    optimal_batch = (safe_available_cost / avg_cost_per_item).floor

    # Ensure we respect minimum batch size
    final_batch_size = [optimal_batch, 1].max

    # Calculate efficiency gain compared to halving
    halved_batch_size = (failed_batch_size * 0.5).ceil
    efficiency_gain = halved_batch_size > 0 ?
      [((final_batch_size - halved_batch_size).to_f / halved_batch_size * 100).round(2), 0.0].max : 0.0

    {
      batch_size: final_batch_size,
      avg_cost_per_item: avg_cost_per_item.round(2),
      safe_available_cost: safe_available_cost.to_i,
      safety_margin: safety_margin,
      calculation_method: 'actual_cost_based',
      efficiency_gain: efficiency_gain
    }
  end

  def self.extract_cost_from_jobber_response(response)
    return nil unless response.is_a?(Hash)

    # Extract from Jobber API format: extensions.cost.requestedQueryCost
    response.dig("extensions", "cost", "requestedQueryCost")
  end

  def self.extract_throttle_status_from_jobber_response(response)
    return {} unless response.is_a?(Hash)

    # Extract from Jobber API format: extensions.cost.throttleStatus
    throttle_status = response.dig("extensions", "cost", "throttleStatus")
    return {} unless throttle_status

    {
      currently_available: throttle_status["currentlyAvailable"],
      maximum_available: throttle_status["maximumAvailable"],
      restore_rate: throttle_status["restoreRate"]
    }
  end

  def self.extract_cost_from_message(message)
    return nil unless message

    # Try different patterns to extract cost
    cost_match = message.match(/cost[:\s=]+(\d+)/i)
    return cost_match[1].to_i if cost_match

    nil
  end
end

# Run the demonstration
if __FILE__ == $0
  OptimizedAdaptiveRetryExample.demonstrate
  OptimizedAdaptiveRetryExample.demonstrate_realistic_scenario
  OptimizedAdaptiveRetryExample.demonstrate_jobber_api_scenario
  OptimizedAdaptiveRetryExample.demonstrate_cost_extraction
end
