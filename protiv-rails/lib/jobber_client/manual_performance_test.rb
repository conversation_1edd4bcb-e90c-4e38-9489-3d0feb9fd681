# frozen_string_literal: true

class JobberClient::ManualPerformanceTest
  def initialize(integration)
    @integration = integration
    @adapter = @integration.adapter
    credential = @integration.credential
    access_token = credential.bearer_token
    @client = JobberClient::Client.new(access_token, integration_id: integration.id, enable_rate_limiting: true)
  end

  def run(limit)
    batch_size = 50
    enable_pagination = true

    start_time = Time.current
    p "visits"
    p @adapter.retrieve_visits(job_id: "Z2lkOi8vSm9iYmVyL0pvYi8xMTYzODExNTI=", enable_pagination: false)
    p "jobs"
    p @adapter.retrieve_jobs(limit: limit, enable_pagination: false)
    p "users"
    p @adapter.retrieve_users(limit: limit, enable_pagination: false)
    p "clients"
    p @adapter.retrieve_clients(limit: limit)
    p "properties"
    p @adapter.retrieve_properties(limit: limit)
    p "line items"
    p @adapter.retrieve_line_items(limit: limit)
    p "timesheet entries"
    p @adapter.retrieve_timesheet_entries(limit: limit)
    p "products"
    p @adapter.retrieve_products_or_services(limit: limit)

    Time.current - start_time
  end

  def run_client_tests(limit)
    start_time = Time.current

    p "jobs"
    p @client.fetch_jobs(limit:)
    p "visits"
    p @client.fetch_visits(job_id: "Z2lkOi8vSm9iYmVyL0pvYi8xMTYzODExNTI=")
    p "users"
    p @client.fetch_users(limit:)
    p "clients"
    p @client.fetch_clients(limit:)
    p "properties"
    p @client.fetch_properties(limit:, client_id: "Z2lkOi8vSm9iYmVyL0NsaWVudC8xMTAxNjk2MzM=")
    p "line items"
    p @client.fetch_line_items(limit:, job_id: "Z2lkOi8vSm9iYmVyL0pvYi8xMTYzODExNTI=")
    p "timesheet entries"
    p @client.fetch_timesheet_entries(limit:)
    p "products"
    p @client.fetch_products_or_services(limit:)

    Time.current - start_time
  end
end
