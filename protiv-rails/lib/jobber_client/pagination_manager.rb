# frozen_string_literal: true

module JobberClient
  ##
  # Service for handling automatic pagination for large datasets.
  #
  # This manager breaks large queries into optimal batch sizes, implements
  # cursor-based pagination using pageInfo, and manages progressive data
  # collection with error recovery.
  #
  # @example Basic usage
  #   paginator = JobberClient::PaginationManager.new(client)
  #   all_jobs = paginator.fetch_all_jobs_with_adaptive_retry(batch_size: :auto)
  #
  class PaginationManager
    class PaginationError < StandardError
      attr_reader :cursor, :batch_number, :total_fetched

      def initialize(message, cursor: nil, batch_number: nil, total_fetched: nil)
        super(message)
        @cursor = cursor
        @batch_number = batch_number
        @total_fetched = total_fetched
      end
    end

    # Default configuration
    DEFAULT_BATCH_SIZE = 10
    MIN_BATCH_SIZE = 10
    MAX_BATCH_SIZE = 100
    MAX_RETRIES = 3
    RETRY_DELAY = 2.0

    attr_reader :client, :rate_limiter, :cost_calculator, :throttle_tracker

    ##
    # Initializes a new PaginationManager instance.
    #
    # @param client [JobberClient::Client] Jobber API client
    # @param rate_limiter [JobberRateLimiter, nil] Rate limiter instance
    def initialize(client, rate_limiter: nil)
      @client = client
      @rate_limiter = rate_limiter
      @cost_calculator = QueryCostCalculator.new
      @throttle_tracker = ThrottleStatusTracker.new(
        integration_id: rate_limiter&.integration_id
      )
    end

    ##
    # Generic method for fetching all paginated data with adaptive retry.
    #
    # This method uses adaptive retry logic that automatically adjusts batch size
    # when throttling errors occur, creating a feedback loop that optimizes
    # query cost for available budget.
    #
    # @param query_method [Symbol] Method name for the query
    # @param variables_base [Hash] Base variables for the query
    # @param data_path [Array<String>] Path to data in response
    # @param page_info_path [Array<String>] Path to pageInfo in response
    # @param batch_size [Integer, Symbol] Batch size or :auto for automatic sizing
    # @param max_items [Integer, nil] Maximum number of items to fetch
    # @param min_batch_size [Integer] Minimum allowed batch size
    # @return [Array<Hash>] All fetched data
    def fetch_all_paginated_with_adaptive_retry(query_method:, variables_base:, data_path:, page_info_path:,
                                               batch_size: :auto, max_items: nil, min_batch_size: 5)
      all_data = []
      cursor = nil
      batch_number = 0
      learned_batch_size = nil  # Track learned optimal batch size

      # Create throttle tracker and cost calculator for adaptive retry
      throttle_tracker = JobberClient::ThrottleStatusTracker.new(
        integration_id: rate_limiter&.integration_id
      )
      cost_calculator = JobberClient::QueryCostCalculator.new

      loop do
        batch_number += 1

        begin
          # Determine optimal batch size, using learned size if available
          effective_batch_size = learned_batch_size || batch_size
          current_batch_size = determine_batch_size(
            query_method: query_method,
            variables_base: variables_base,
            requested_batch_size: effective_batch_size,
            remaining_items: max_items ? max_items - all_data.length : nil
          )

          batch_size_source = learned_batch_size ? "learned" : "calculated"
          Rails.logger.info("Fetching batch #{batch_number} with size #{current_batch_size} (#{batch_size_source}, adaptive retry enabled)")

          # Prepare variables for this batch
          variables = variables_base.merge(
            first: current_batch_size,
            after: cursor
          ).compact

          # Get the query
          query = client.send(query_method)

          # Track the final variables used for learning
          final_variables_used = nil

          # Execute query with adaptive retry
          response = client.retry_handler.with_adaptive_graphql_retry(
            query: query,
            variables: variables,
            throttle_tracker: throttle_tracker,
            cost_calculator: cost_calculator,
            batch_size_param: :first,
            min_batch_size: min_batch_size
          ) do |current_variables|
            # Capture the variables that are actually being used
            final_variables_used = current_variables.dup

            # Update rate limiter before making request
            if rate_limiter
              cost_estimate = cost_calculator.estimate_cost(query, current_variables)
              rate_limiter.check_cost_availability(estimated_cost: cost_estimate[:estimated_cost])
            end

            # Execute the actual request
            response = client.graphql_request(
              query: query,
              variables: current_variables
            )

            # Update rate limiter and throttle tracker with response
            if rate_limiter
              rate_limiter.update_from_response(response)
              throttle_status = response.dig("extensions", "cost", "throttleStatus")
              throttle_tracker.update_status(throttle_status) if throttle_status
            end

            response
          end

          # Learn from successful batch size if it was adjusted
          if final_variables_used && final_variables_used[:first] != current_batch_size
            learned_batch_size = final_variables_used[:first]
            Rails.logger.info("Learned optimal batch size: #{learned_batch_size} (was #{current_batch_size})")
          end

          # Extract data and page info
          batch_data = extract_data_from_response(response, data_path)
          page_info = extract_page_info_from_response(response, page_info_path)

          # Add batch data to results
          all_data.concat(batch_data)

          # Log progress with adaptive retry information
          log_adaptive_pagination_progress(batch_number, batch_data.length, all_data.length, throttle_tracker)

          # Check if we've reached max items
          if max_items && all_data.length >= max_items
            all_data = all_data.first(max_items)
            break
          end

          # Check if there's more data
          break unless page_info&.dig("hasNextPage")

          cursor = page_info["endCursor"]

        rescue JobberClient::RetryHandler::MaxRetriesExceeded => e
          Rails.logger.error("Adaptive retry pagination failed after max retries: #{e.message}")
          raise PaginationError.new(
            "Failed to fetch batch with adaptive retry: #{e.message}",
            cursor: cursor,
            batch_number: batch_number,
            total_fetched: all_data.length
          )
        rescue StandardError => e
          Rails.logger.error("Unexpected error in adaptive retry pagination: #{e.message}")
          raise PaginationError.new(
            "Unexpected pagination error: #{e.message}",
            cursor: cursor,
            batch_number: batch_number,
            total_fetched: all_data.length
          )
        end
      end

      Rails.logger.info("Adaptive retry pagination completed: fetched #{all_data.length} items in #{batch_number} batches")
      all_data
    end

    private

    ##
    # Determines optimal batch size for the current request.
    #
    # @param query_method [Symbol] Query method name
    # @param variables_base [Hash] Base variables
    # @param requested_batch_size [Integer, Symbol] Requested batch size
    # @param remaining_items [Integer, nil] Remaining items to fetch
    # @return [Integer] Optimal batch size
    def determine_batch_size(query_method:, variables_base:, requested_batch_size:, remaining_items:)
      # Handle explicit batch size
      if requested_batch_size.is_a?(Integer)
        batch_size = requested_batch_size
      else
        # Auto-calculate batch size
        batch_size = calculate_auto_batch_size(query_method, variables_base)
      end

      # Apply constraints
      batch_size = [batch_size, MIN_BATCH_SIZE].max
      batch_size = [batch_size, MAX_BATCH_SIZE].min

      # Limit to remaining items if specified
      if remaining_items && remaining_items > 0
        batch_size = [batch_size, remaining_items].min
      end

      batch_size
    end

    ##
    # Calculates automatic batch size based on available cost.
    #
    # @param query_method [Symbol] Query method name
    # @param variables_base [Hash] Base variables
    # @return [Integer] Calculated batch size
    def calculate_auto_batch_size(query_method, variables_base)
      return DEFAULT_BATCH_SIZE unless rate_limiter

      # Get current throttle status
      throttle_status = rate_limiter.get_current_throttle_status
      return DEFAULT_BATCH_SIZE unless throttle_status

      available_cost = throttle_status[:currently_available]
      maximum_available = throttle_status[:maximum_available]
      Rails.logger.info("Available cost: #{available_cost}, Maximum available: #{maximum_available}")

      # Get query template
      query = client.send(query_method)

      # Calculate optimal batch size
      optimal_size = cost_calculator.calculate_optimal_batch_size(
        available_cost,
        query,
        variables_base
      )

      # Check if requested cost would exceed maximum available
      cost_per_item = estimate_cost_per_item(query, variables_base)
      requested_cost = cost_per_item * optimal_size

      if requested_cost > maximum_available
        # Adjust batch size to fit within maximum available
        adjusted_size = (maximum_available / cost_per_item).floor
        optimal_size = [adjusted_size, MIN_BATCH_SIZE].max
        Rails.logger.warn("Adjusted batch size from #{optimal_size} to #{adjusted_size} due to maximum cost constraints")
      end

      # Apply throttle-based adjustments
      if throttle_tracker.throttling_risk?
        recommendation = throttle_tracker.recommend_batch_size(
          base_batch_size: optimal_size,
          cost_per_item: cost_per_item
        )
        optimal_size = recommendation[:batch_size]
      end

      optimal_size
    end

    ##
    # Estimates cost per item for batch size calculations.
    #
    # @param query [String] GraphQL query
    # @param variables_base [Hash] Base variables
    # @return [Integer] Estimated cost per item
    def estimate_cost_per_item(query, variables_base)
      single_item_variables = variables_base.merge(first: 1)
      cost_info = cost_calculator.estimate_cost(query, single_item_variables)
      cost_info[:base_cost]
    end



    ##
    # Extracts data from API response using specified path.
    #
    # @param response [Hash] API response
    # @param data_path [Array<String>] Path to data
    # @return [Array<Hash>] Extracted data
    def extract_data_from_response(response, data_path)
      data = response.dig(*data_path)
      return [] unless data

      # Handle both edges format and nodes format
      if data.is_a?(Array) && data.first&.key?("node")
        data.map { |edge| edge["node"] }
      else
        data.is_a?(Array) ? data : []
      end
    end

    ##
    # Extracts page info from API response.
    #
    # @param response [Hash] API response
    # @param page_info_path [Array<String>] Path to page info
    # @return [Hash, nil] Page info
    def extract_page_info_from_response(response, page_info_path)
      response.dig(*page_info_path)
    end

    ##
    # Logs pagination progress.
    #
    # @param batch_number [Integer] Current batch number
    # @param batch_size [Integer] Size of current batch
    # @param total_fetched [Integer] Total items fetched so far
    # @return [void]
    def log_pagination_progress(batch_number, batch_size, total_fetched)
      Rails.logger.debug("Pagination batch #{batch_number}: fetched #{batch_size} items (total: #{total_fetched})")
    end

    ##
    # Logs adaptive pagination progress with throttle status information.
    #
    # @param batch_number [Integer] Current batch number
    # @param batch_size [Integer] Size of current batch
    # @param total_fetched [Integer] Total items fetched so far
    # @param throttle_tracker [ThrottleStatusTracker] Throttle status tracker
    # @return [void]
    def log_adaptive_pagination_progress(batch_number, batch_size, total_fetched, throttle_tracker)
      throttle_info = throttle_tracker.status_summary

      Rails.logger.info({
        event: "adaptive_pagination_progress",
        batch_number: batch_number,
        batch_size: batch_size,
        total_fetched: total_fetched,
        throttle_status: throttle_info[:status],
        available_cost: throttle_info[:currently_available],
        utilization_percentage: throttle_info[:utilization_percentage]
      }.to_json)
    end
  end
end
