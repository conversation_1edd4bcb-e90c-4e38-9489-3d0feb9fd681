# frozen_string_literal: true

module JobberClient
  ##
  # Service for monitoring and responding to API throttle status in real-time.
  #
  # This tracker parses throttle status from API responses, tracks cost budget
  # across multiple requests, and implements circuit breaker pattern for
  # repeated throttling scenarios.
  #
  # @example Basic usage
  #   tracker = JobberClient::ThrottleStatusTracker.new
  #   tracker.update_status(throttle_status)
  #   wait_time = tracker.calculate_wait_time(needed_cost: 200)
  class ThrottleStatusTracker
    # Throttling event types
    THROTTLE_WARNING = :warning
    THROTTLE_CRITICAL = :critical
    THROTTLE_BLOCKED = :blocked

    # Threshold percentages for different alert levels
    WARNING_THRESHOLD = 0.3   # 30% of maximum available
    CRITICAL_THRESHOLD = 0.1  # 10% of maximum available
    BLOCKED_THRESHOLD = 0.05  # 5% of maximum available

    attr_reader :integration_id, :current_status

    ##
    # Initializes a new ThrottleStatusTracker instance.
    #
    # @param integration_id [String] Unique identifier for the integration
    def initialize(integration_id: nil)
      @integration_id = integration_id
      @current_status = nil
    end

    ##
    # Updates throttle status from API response.
    #
    # @param throttle_status [Hash] Throttle status from API response
    # @return [Symbol] Throttle level (:ok, :warning, :critical, :blocked)
    def update_status(throttle_status)
      @current_status = normalize_throttle_status(throttle_status)

      throttle_level = determine_throttle_level(@current_status)

      # Log throttling events
      log_throttle_event(throttle_level, @current_status)

      # Store status for other components
      store_status(@current_status) if integration_id

      throttle_level
    end

    ##
    # Calculates optimal wait time for a needed cost amount.
    #
    # @param needed_cost [Integer] Cost needed for next operation
    # @return [Float] Wait time in seconds
    def calculate_wait_time(needed_cost:)
      return 0.0 unless @current_status

      available = @current_status[:currently_available]
      restore_rate = @current_status[:restore_rate]

      return 0.0 if available >= needed_cost

      cost_deficit = needed_cost - available
      base_wait_time = cost_deficit.to_f / restore_rate

      # Add buffer based on throttle level
      buffer_multiplier = case determine_throttle_level(@current_status)
      when :blocked then 2.0
      when :critical then 1.5
      when :warning then 1.2
      else 1.0
      end

      (base_wait_time * buffer_multiplier).ceil
    end

    ##
    # Determines if a query with given cost should be allowed.
    #
    # @param estimated_cost [Integer] Estimated cost of the query
    # @return [Hash] Decision result with recommendation
    def should_allow_query?(estimated_cost:)
      return { allowed: true, reason: "no_status" } unless @current_status

      available = @current_status[:currently_available]
      throttle_level = determine_throttle_level(@current_status)

      case throttle_level
      when :blocked
        {
          allowed: false,
          reason: "blocked_threshold",
          wait_time: calculate_wait_time(needed_cost: estimated_cost),
          available_cost: available
        }
      when :critical
        if estimated_cost > available * 0.5 # Only allow small queries
          {
            allowed: false,
            reason: "critical_threshold_large_query",
            wait_time: calculate_wait_time(needed_cost: estimated_cost),
            available_cost: available
          }
        else
          { allowed: true, reason: "critical_threshold_small_query" }
        end
      when :warning
        if estimated_cost > available * 0.8 # Be more conservative
          {
            allowed: false,
            reason: "warning_threshold_large_query",
            wait_time: calculate_wait_time(needed_cost: estimated_cost),
            available_cost: available
          }
        else
          { allowed: true, reason: "warning_threshold_acceptable" }
        end
      else
        { allowed: true, reason: "sufficient_cost" }
      end
    end

    ##
    # Recommends optimal batch size based on current throttle status.
    #
    # @param base_batch_size [Integer] Desired batch size
    # @param cost_per_item [Integer] Estimated cost per item
    # @return [Hash] Batch size recommendation
    def recommend_batch_size(base_batch_size:, cost_per_item:)
      return { batch_size: base_batch_size, reason: "no_status" } unless @current_status

      available = @current_status[:currently_available]
      throttle_level = determine_throttle_level(@current_status)

      # Calculate maximum safe batch size
      safety_multiplier = case throttle_level
      when :blocked then 0.1
      when :critical then 0.3
      when :warning then 0.6
      else 0.8
      end

      safe_cost_budget = (available * safety_multiplier).floor
      max_safe_batch = (safe_cost_budget / cost_per_item).floor

      recommended_batch = [base_batch_size, max_safe_batch].min
      recommended_batch = [recommended_batch, 1].max # Ensure at least 1

      {
        batch_size: recommended_batch,
        reason: "throttle_level_#{throttle_level}",
        throttle_level: throttle_level,
        available_cost: available,
        safe_cost_budget: safe_cost_budget
      }
    end

    ##
    # Gets current throttle level.
    #
    # @return [Symbol] Current throttle level
    def current_throttle_level
      return :unknown unless @current_status

      determine_throttle_level(@current_status)
    end

    ##
    # Checks if status indicates throttling risk.
    #
    # @return [Boolean] True if there's throttling risk
    def throttling_risk?
      level = current_throttle_level
      [:warning, :critical, :blocked].include?(level)
    end

    ##
    # Gets status summary for monitoring.
    #
    # @return [Hash] Status summary
    def status_summary
      return { status: "unknown" } unless @current_status

      {
        status: current_throttle_level,
        currently_available: @current_status[:currently_available],
        maximum_available: @current_status[:maximum_available],
        restore_rate: @current_status[:restore_rate],
        utilization_percentage: calculate_utilization_percentage,
        updated_at: @current_status[:updated_at]
      }
    end

    private

    ##
    # Normalizes throttle status from API response.
    #
    # @param throttle_status [Hash] Raw throttle status from API
    # @return [Hash] Normalized throttle status
    def normalize_throttle_status(throttle_status)
      {
        maximum_available: throttle_status["maximumAvailable"].to_i,
        currently_available: throttle_status["currentlyAvailable"].to_i,
        restore_rate: throttle_status["restoreRate"].to_i,
        updated_at: Time.current
      }
    end

    ##
    # Determines throttle level based on available cost.
    #
    # @param status [Hash] Normalized throttle status
    # @return [Symbol] Throttle level
    def determine_throttle_level(status)
      return :unknown unless status

      available = status[:currently_available]
      maximum = status[:maximum_available]

      return :blocked if maximum == 0

      utilization = available.to_f / maximum

      case utilization
      when 0..BLOCKED_THRESHOLD
        :blocked
      when BLOCKED_THRESHOLD..CRITICAL_THRESHOLD
        :critical
      when CRITICAL_THRESHOLD..WARNING_THRESHOLD
        :warning
      else
        :ok
      end
    end

    ##
    # Calculates current cost utilization percentage.
    #
    # @return [Float] Utilization percentage
    def calculate_utilization_percentage
      return 0.0 unless @current_status

      available = @current_status[:currently_available].to_f
      maximum = @current_status[:maximum_available].to_f

      return 0.0 if maximum == 0

      ((maximum - available) / maximum * 100.0).round(2)
    end

    ##
    # Logs throttle events for monitoring.
    #
    # @param throttle_level [Symbol] Current throttle level
    # @param status [Hash] Current throttle status
    # @return [void]
    def log_throttle_event(throttle_level, status)
      return if throttle_level == :ok

      Rails.logger.warn({
        event: "jobber_throttle_status",
        integration_id: integration_id,
        throttle_level: throttle_level,
        currently_available: status[:currently_available],
        maximum_available: status[:maximum_available],
        restore_rate: status[:restore_rate],
        utilization_percentage: calculate_utilization_percentage
      }.to_json)
    end

    ##
    # Stores throttle status for other components.
    #
    # @param status [Hash] Throttle status to store
    # @return [void]
    def store_status(status)
      return unless redis

      key = "jobber_throttle_tracker:#{integration_id}:status"
      redis.setex(key, 120, status.to_json) # Cache for 2 minutes
    end

    ##
    # Returns Redis connection.
    #
    # @return [Redis, nil] Redis connection or nil if not available
    def redis
      Protiv::Application.redis
    rescue
      nil
    end
  end
end
