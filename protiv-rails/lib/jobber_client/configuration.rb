# frozen_string_literal: true

require "yaml"

module JobberClient
  ##
  # Configuration management for Jobber GraphQL API rate limiting.
  #
  # This class loads and provides access to rate limiting configuration
  # settings from the jobber_rate_limiting.yml file. It supports
  # environment-specific configurations and provides sensible defaults.
  #
  # @example Basic usage
  #   config = JobberClient::Configuration.new
  #   max_cost_percentage = config.rate_limiting.max_query_cost_percentage
  #   batch_size = config.batch_processing.default_batch_size
  #
  class Configuration
    class ConfigurationError < StandardError; end

    # Default configuration values
    DEFAULT_CONFIG = {
      "rate_limiting" => {
        "max_query_cost_percentage" => 80,
        "minimum_cost_threshold" => 500,
        "safety_margin" => 0.2
      },
      "batch_processing" => {
        "default_batch_size" => 50,
        "minimum_batch_size" => 10,
        "maximum_batch_size" => 100,
        "auto_batch_sizing" => true
      },
      "retry_settings" => {
        "maximum_retry_attempts" => 3,
        "initial_backoff" => 1.0,
        "backoff_multiplier" => 2.0,
        "maximum_backoff" => 60.0,
        "jitter_factor" => 0.1
      },
      "adaptive_retry" => {
        "enabled" => true,
        "default_min_batch_size" => 5,
        "safety_margin_start" => 0.25,
        "safety_margin_minimum" => 0.4
      },
      "circuit_breaker" => {
        "failure_threshold" => 5,
        "timeout" => 300,
        "enabled" => true
      },
      "query_optimization" => {
        "field_limits" => {
          "jobs" => {
            "line_items" => 20,
            "time_sheet_entries" => 50,
            "visits" => 10
          },
          "visits" => {
            "line_items" => 50,
            "time_sheet_entries" => 100
          }
        }
      },
      "monitoring" => {
        "log_cost_metrics" => true,
        "log_throttling_events" => true,
        "log_pagination_progress" => false,
        "log_level" => "info"
      },
      "caching" => {
        "throttle_status_ttl" => 60,
        "cost_estimation_ttl" => 300,
        "enabled" => true
      }
    }.freeze

    attr_reader :config, :environment

    ##
    # Initializes a new Configuration instance.
    #
    # @param environment [String, Symbol, nil] Environment to load config for
    # @param config_file [String, nil] Path to configuration file
    def initialize(environment: nil, config_file: nil)
      @environment = (environment || Rails.env || "development").to_s
      @config_file = config_file || default_config_file_path
      @config = load_configuration
    end

    ##
    # Returns rate limiting configuration.
    #
    # @return [OpenStruct] Rate limiting configuration
    def rate_limiting
      @rate_limiting ||= create_config_struct(@config["rate_limiting"])
    end

    ##
    # Returns batch processing configuration.
    #
    # @return [OpenStruct] Batch processing configuration
    def batch_processing
      @batch_processing ||= create_config_struct(@config["batch_processing"])
    end

    ##
    # Returns retry settings configuration.
    #
    # @return [OpenStruct] Retry settings configuration
    def retry_settings
      @retry_settings ||= create_config_struct(@config["retry_settings"])
    end

    ##
    # Returns circuit breaker configuration.
    #
    # @return [OpenStruct] Circuit breaker configuration
    def circuit_breaker
      @circuit_breaker ||= create_config_struct(@config["circuit_breaker"])
    end

    ##
    # Returns query optimization configuration.
    #
    # @return [OpenStruct] Query optimization configuration
    def query_optimization
      @query_optimization ||= create_config_struct(@config["query_optimization"])
    end

    ##
    # Returns monitoring configuration.
    #
    # @return [OpenStruct] Monitoring configuration
    def monitoring
      @monitoring ||= create_config_struct(@config["monitoring"])
    end

    ##
    # Returns caching configuration.
    #
    # @return [OpenStruct] Caching configuration
    def caching
      @caching ||= create_config_struct(@config["caching"])
    end

    ##
    # Returns adaptive retry configuration.
    #
    # @return [OpenStruct] Adaptive retry configuration
    def adaptive_retry
      @adaptive_retry ||= create_config_struct(@config["adaptive_retry"])
    end

    ##
    # Gets field limit for a specific resource and field.
    #
    # @param resource [String, Symbol] Resource name (e.g., 'jobs', 'visits')
    # @param field [String, Symbol] Field name (e.g., 'line_items', 'visits')
    # @return [Integer, nil] Field limit or nil if not configured
    def field_limit(resource, field)
      query_optimization.field_limits&.dig(resource.to_s, field.to_s)
    end

    ##
    # Checks if a feature is enabled.
    #
    # @param feature [Symbol] Feature to check (:circuit_breaker, :auto_batch_sizing, :caching, :adaptive_retry)
    # @return [Boolean] True if feature is enabled
    def feature_enabled?(feature)
      case feature
      when :circuit_breaker
        circuit_breaker.enabled
      when :auto_batch_sizing
        batch_processing.auto_batch_sizing
      when :caching
        caching.enabled
      when :adaptive_retry
        adaptive_retry.enabled
      else
        false
      end
    end

    ##
    # Returns configuration as a hash.
    #
    # @return [Hash] Complete configuration hash
    def to_h
      @config.dup
    end

    ##
    # Reloads configuration from file.
    #
    # @return [void]
    def reload!
      @config = load_configuration
      clear_cached_structs
    end

    ##
    # Validates configuration values.
    #
    # @return [Array<String>] Array of validation errors (empty if valid)
    def validate
      errors = []

      # Validate rate limiting settings
      if rate_limiting.max_query_cost_percentage < 1 || rate_limiting.max_query_cost_percentage > 100
        errors << "max_query_cost_percentage must be between 1 and 100"
      end

      if rate_limiting.safety_margin < 0 || rate_limiting.safety_margin > 1
        errors << "safety_margin must be between 0.0 and 1.0"
      end

      # Validate batch processing settings
      if batch_processing.minimum_batch_size < 1
        errors << "minimum_batch_size must be at least 1"
      end

      if batch_processing.maximum_batch_size < batch_processing.minimum_batch_size
        errors << "maximum_batch_size must be greater than or equal to minimum_batch_size"
      end

      # Validate retry settings
      if retry_settings.maximum_retry_attempts < 0
        errors << "maximum_retry_attempts must be non-negative"
      end

      if retry_settings.jitter_factor < 0 || retry_settings.jitter_factor > 1
        errors << "jitter_factor must be between 0.0 and 1.0"
      end

      # Validate adaptive retry settings
      if adaptive_retry.default_min_batch_size < 1
        errors << "adaptive_retry default_min_batch_size must be at least 1"
      end

      if adaptive_retry.safety_margin_start < 0 || adaptive_retry.safety_margin_start > 1
        errors << "adaptive_retry safety_margin_start must be between 0.0 and 1.0"
      end

      if adaptive_retry.safety_margin_minimum < 0 || adaptive_retry.safety_margin_minimum > 1
        errors << "adaptive_retry safety_margin_minimum must be between 0.0 and 1.0"
      end

      errors
    end

    ##
    # Validates configuration and raises if invalid.
    #
    # @raise [ConfigurationError] If configuration is invalid
    # @return [void]
    def validate!
      errors = validate
      return if errors.empty?

      raise ConfigurationError, "Invalid configuration: #{errors.join(', ')}"
    end

    private

    ##
    # Loads configuration from YAML file.
    #
    # @return [Hash] Loaded configuration
    def load_configuration
      if File.exist?(@config_file)
        yaml_config = YAML.load_file(@config_file, aliases: true)
        env_config = yaml_config[environment] || yaml_config["default"] || {}
        merge_with_defaults(env_config)
      else
        Rails.logger.warn("Jobber rate limiting config file not found: #{@config_file}. Using defaults.")
        DEFAULT_CONFIG.dup
      end
    rescue Psych::SyntaxError => e
      Rails.logger.error("Invalid YAML in Jobber rate limiting config: #{e.message}")
      DEFAULT_CONFIG.dup
    end

    ##
    # Merges environment configuration with defaults.
    #
    # @param env_config [Hash] Environment-specific configuration
    # @return [Hash] Merged configuration
    def merge_with_defaults(env_config)
      deep_merge(DEFAULT_CONFIG, env_config)
    end

    ##
    # Performs deep merge of two hashes.
    #
    # @param base [Hash] Base hash
    # @param override [Hash] Override hash
    # @return [Hash] Merged hash
    def deep_merge(base, override)
      base.merge(override) do |key, base_val, override_val|
        if base_val.is_a?(Hash) && override_val.is_a?(Hash)
          deep_merge(base_val, override_val)
        else
          override_val
        end
      end
    end

    ##
    # Creates an OpenStruct from configuration hash.
    #
    # @param config_hash [Hash] Configuration hash
    # @return [OpenStruct] Configuration struct
    def create_config_struct(config_hash)
      return OpenStruct.new unless config_hash.is_a?(Hash)

      # Convert nested hashes to OpenStruct recursively
      converted = config_hash.transform_values do |value|
        if value.is_a?(Hash)
          create_config_struct(value)
        else
          value
        end
      end

      OpenStruct.new(converted)
    end

    ##
    # Returns default configuration file path.
    #
    # @return [String] Default config file path
    def default_config_file_path
      Rails.root.join("config", "jobber_rate_limiting.yml").to_s
    end

    ##
    # Clears cached configuration structs.
    #
    # @return [void]
    def clear_cached_structs
      @rate_limiting = nil
      @batch_processing = nil
      @retry_settings = nil
      @circuit_breaker = nil
      @query_optimization = nil
      @monitoring = nil
      @caching = nil
      @adaptive_retry = nil
    end
  end
end
