# frozen_string_literal: true

require "graphql"

module JobberClient
  ##
  # Service for estimating GraphQL query costs before execution.
  #
  # This calculator parses GraphQL queries to count fields and calculate
  # connection field costs based on pagination arguments. It helps prevent
  # throttling by providing cost estimates for batch size optimization.
  class QueryCostCalculator
    # Default values for cost calculation
    DEFAULT_FIRST_LAST = 100 # Jobber assumes 100 nodes if no first/last argument
    SAFETY_MARGIN = 0.2 # 20% safety margin for estimation accuracy
    MIN_BATCH_SIZE = 10 # Minimum batch size for queries

    ##
    # Estimates the cost of a GraphQL query.
    #
    # @param query [String] GraphQL query string
    # @param variables [Hash] GraphQL variables (default: {})
    # @return [Hash] Cost estimation details
    #   - :estimated_cost [Integer] Total estimated cost with safety margin
    #   - :base_cost [Integer] Base cost without safety margin
    #   - :field_count [Integer] Total number of fields in query
    #   - :connection_multiplier [Integer] Multiplier from connection fields
    #   - :safety_margin [Float] Applied safety margin percentage
    def estimate_cost(query, variables = {})
      begin
        document = GraphQL.parse(query)
        base_cost = calculate_query_cost(document, variables)

        {
          estimated_cost: apply_safety_margin(base_cost),
          base_cost: base_cost,
          field_count: count_total_fields(document),
          connection_multiplier: calculate_connection_multiplier(document, variables),
          safety_margin: SAFETY_MARGIN
        }
      rescue GraphQL::ParseError => e
        Rails.logger.error("Failed to parse GraphQL query: #{e.message}")
        # Return conservative estimate for unparseable queries
        {
          estimated_cost: 1000,
          base_cost: 800,
          field_count: 0,
          connection_multiplier: 1,
          safety_margin: SAFETY_MARGIN,
          error: e.message
        }
      end
    end

    ##
    # Calculates the optimal batch size for a given available cost budget.
    #
    # @param available_cost [Integer] Available cost budget
    # @param query_template [String] GraphQL query template
    # @param variables [Hash] Base variables for the query
    # @return [Integer] Optimal batch size
    def calculate_optimal_batch_size(available_cost, query_template, variables = {})
      # Calculate cost for a single item
      single_item_variables = variables.merge(first: 1)
      single_cost_info = estimate_cost(query_template, single_item_variables)
      base_cost_per_item = single_cost_info[:base_cost]

      return MIN_BATCH_SIZE if base_cost_per_item <= 0

      # Calculate max batch size with safety margin
      safety_budget = (available_cost * (1 - SAFETY_MARGIN)).floor
      max_batch = (safety_budget / base_cost_per_item).floor

      [max_batch, MIN_BATCH_SIZE].max
    end

    ##
    # Calculates optimal batch size from actual cost data (more accurate than estimation).
    #
    # This method uses actual cost information from a failed request to calculate
    # the average cost per item and determine the optimal batch size that fits
    # within the available budget with a safety margin.
    #
    # @param available_cost [Integer] Available cost budget
    # @param actual_cost [Integer] Actual cost from the failed request
    # @param failed_batch_size [Integer] Batch size that caused the failure
    # @param safety_margin [Float] Safety margin to apply (0.0 to 1.0)
    # @return [Hash] Calculation result with optimal batch size and details
    def calculate_optimal_batch_size_from_actual_cost(available_cost:, actual_cost:,
                                                     failed_batch_size:, safety_margin: SAFETY_MARGIN)
      return { batch_size: MIN_BATCH_SIZE, reason: "invalid_input" } if actual_cost <= 0 || failed_batch_size <= 0

      # Calculate average cost per item from actual data
      avg_cost_per_item = actual_cost.to_f / failed_batch_size

      # Apply safety margin to available cost
      safe_available_cost = available_cost * (1 - safety_margin)

      # Calculate optimal batch size
      optimal_batch = (safe_available_cost / avg_cost_per_item).floor

      # Ensure we respect minimum batch size
      final_batch_size = [optimal_batch, MIN_BATCH_SIZE].max

      {
        batch_size: final_batch_size,
        avg_cost_per_item: avg_cost_per_item.round(2),
        safe_available_cost: safe_available_cost.to_i,
        safety_margin: safety_margin,
        calculation_method: "actual_cost_based",
        efficiency_gain: calculate_efficiency_gain(failed_batch_size, final_batch_size)
      }
    end

    private

    ##
    # Calculates efficiency gain compared to simple halving strategy.
    #
    # @param original_batch_size [Integer] Original batch size that failed
    # @param optimal_batch_size [Integer] Calculated optimal batch size
    # @return [Float] Efficiency gain as percentage
    def calculate_efficiency_gain(original_batch_size, optimal_batch_size)
      # Compare with simple halving approach
      halved_batch_size = (original_batch_size * 0.5).ceil

      return 0.0 if halved_batch_size <= 0

      gain = ((optimal_batch_size - halved_batch_size).to_f / halved_batch_size * 100).round(2)
      [gain, 0.0].max # Only report positive gains
    end

    ##
    # Calculates the base cost of a GraphQL query.
    #
    # @param document [GraphQL::Language::Document] Parsed GraphQL document
    # @param variables [Hash] GraphQL variables
    # @return [Integer] Base cost without safety margin
    def calculate_query_cost(document, variables)
      total_cost = 0

      document.definitions.each do |definition|
        next unless definition.is_a?(GraphQL::Language::Nodes::OperationDefinition)

        total_cost += calculate_selection_set_cost(definition.selections, variables)
      end

      total_cost
    end

    ##
    # Calculates the cost of a selection set.
    #
    # @param selections [Array] GraphQL selection nodes
    # @param variables [Hash] GraphQL variables
    # @return [Integer] Cost of the selection set
    def calculate_selection_set_cost(selections, variables)
      cost = 0

      selections.each do |selection|
        case selection
        when GraphQL::Language::Nodes::Field
          cost += calculate_field_cost(selection, variables)
        when GraphQL::Language::Nodes::InlineFragment
          cost += calculate_selection_set_cost(selection.selections, variables)
        when GraphQL::Language::Nodes::FragmentSpread
          # For simplicity, assume fragment spreads add minimal cost
          cost += 1
        end
      end

      cost
    end

    ##
    # Calculates the cost of a single field.
    #
    # @param field [GraphQL::Language::Nodes::Field] GraphQL field node
    # @param variables [Hash] GraphQL variables
    # @return [Integer] Cost of the field
    def calculate_field_cost(field, variables)
      # Skip cost-free fields
      return 0 if cost_free_field?(field.name)

      base_cost = 1

      # Handle connection fields with pagination
      if connection_field?(field)
        multiplier = get_pagination_multiplier(field, variables)

        # For connection fields, calculate nested cost excluding pagination fields
        if field.selections
          nested_cost = 0
          field.selections.each do |selection|
            next if selection.is_a?(GraphQL::Language::Nodes::Field) &&
                    %w[pageInfo edges].include?(selection.name)

            if selection.is_a?(GraphQL::Language::Nodes::Field) && selection.name == "nodes"
              # For nodes, calculate the cost of each node's fields
              nested_cost += calculate_selection_set_cost(selection.selections, variables) if selection.selections
            else
              nested_cost += calculate_selection_set_cost([selection], variables)
            end
          end

          # If nested cost is 0, use 1 as minimum
          nested_cost = 1 if nested_cost == 0
          base_cost = multiplier * nested_cost
        else
          base_cost = multiplier
        end
      elsif field.selections
        # Regular field with nested selections
        base_cost += calculate_selection_set_cost(field.selections, variables)
      end

      base_cost
    end

    ##
    # Determines if a field is cost-free (edges, nodes, node).
    #
    # @param field_name [String] Name of the field
    # @return [Boolean] True if the field is cost-free
    def cost_free_field?(field_name)
      %w[edges nodes node].include?(field_name)
    end

    ##
    # Determines if a field is a connection field.
    #
    # @param field [GraphQL::Language::Nodes::Field] GraphQL field node
    # @return [Boolean] True if the field is a connection field
    def connection_field?(field)
      # Check if field name ends with common connection patterns
      return true if field.name =~ /Connection$/ || field.name == "users"

      # Check if field has first/last arguments
      has_pagination_args = field.arguments.any? { |arg| %w[first last].include?(arg.name) }

      # Check if field contains edges/nodes/pageInfo
      has_connection_structure = field.selections&.any? do |selection|
        selection.is_a?(GraphQL::Language::Nodes::Field) &&
        %w[edges nodes pageInfo].include?(selection.name)
      end

      has_pagination_args || has_connection_structure
    end

    ##
    # Gets the pagination multiplier for a connection field.
    #
    # @param field [GraphQL::Language::Nodes::Field] GraphQL field node
    # @param variables [Hash] GraphQL variables
    # @return [Integer] Pagination multiplier
    def get_pagination_multiplier(field, variables)
      # Look for first/last arguments
      field.arguments.each do |arg|
        if %w[first last].include?(arg.name)
          case arg.value
          when GraphQL::Language::Nodes::VariableIdentifier
            var_name = arg.value.name.to_sym
            var_value = variables[var_name] || variables[arg.value.name.to_s]
            return var_value || DEFAULT_FIRST_LAST
          when Integer
            return arg.value
          end
        end
      end

      DEFAULT_FIRST_LAST
    end

    ##
    # Counts the total number of fields in a GraphQL document.
    #
    # @param document [GraphQL::Language::Document] Parsed GraphQL document
    # @return [Integer] Total field count
    def count_total_fields(document)
      count = 0

      document.definitions.each do |definition|
        next unless definition.is_a?(GraphQL::Language::Nodes::OperationDefinition)

        count += count_selection_set_fields(definition.selections)
      end

      count
    end

    ##
    # Counts fields in a selection set.
    #
    # @param selections [Array] GraphQL selection nodes
    # @return [Integer] Field count in selection set
    def count_selection_set_fields(selections)
      count = 0

      selections.each do |selection|
        case selection
        when GraphQL::Language::Nodes::Field
          count += 1
          count += count_selection_set_fields(selection.selections) if selection.selections
        when GraphQL::Language::Nodes::InlineFragment
          count += count_selection_set_fields(selection.selections)
        when GraphQL::Language::Nodes::FragmentSpread
          count += 1 # Simplified counting for fragments
        end
      end

      count
    end

    ##
    # Calculates the connection multiplier for the entire query.
    #
    # @param document [GraphQL::Language::Document] Parsed GraphQL document
    # @param variables [Hash] GraphQL variables
    # @return [Integer] Overall connection multiplier
    def calculate_connection_multiplier(document, variables)
      max_multiplier = 1

      document.definitions.each do |definition|
        next unless definition.is_a?(GraphQL::Language::Nodes::OperationDefinition)

        multiplier = find_max_connection_multiplier(definition.selections, variables)
        max_multiplier = [max_multiplier, multiplier].max
      end

      max_multiplier
    end

    ##
    # Finds the maximum connection multiplier in a selection set.
    #
    # @param selections [Array] GraphQL selection nodes
    # @param variables [Hash] GraphQL variables
    # @return [Integer] Maximum connection multiplier
    def find_max_connection_multiplier(selections, variables)
      max_multiplier = 1

      selections.each do |selection|
        case selection
        when GraphQL::Language::Nodes::Field
          if connection_field?(selection)
            multiplier = get_pagination_multiplier(selection, variables)
            max_multiplier = [max_multiplier, multiplier].max
          end

          if selection.selections
            nested_multiplier = find_max_connection_multiplier(selection.selections, variables)
            max_multiplier = [max_multiplier, nested_multiplier].max
          end
        when GraphQL::Language::Nodes::InlineFragment
          nested_multiplier = find_max_connection_multiplier(selection.selections, variables)
          max_multiplier = [max_multiplier, nested_multiplier].max
        end
      end

      max_multiplier
    end

    ##
    # Applies safety margin to base cost.
    #
    # @param base_cost [Integer] Base cost without safety margin
    # @return [Integer] Cost with safety margin applied
    def apply_safety_margin(base_cost)
      (base_cost * (1 + SAFETY_MARGIN)).ceil
    end
  end
end
