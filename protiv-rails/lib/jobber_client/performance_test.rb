# frozen_string_literal: true

require "benchmark"
require "json"
require "csv"
require "logger"
require "fileutils"

##
# Performance testing suite for Jobber API data retrieval procedures.
#
# This class provides comprehensive metrics collection for different Jobber
# data retrieval strategies including pagination, adaptive retry, and various
# batch sizes. It measures execution time, error rates, throttling events,
# and API cost consumption.
#
# @example Basic usage
#   test = JobberClient::PerformanceTest.new(integration)
#   test.run_all_tests
#   test.generate_report
#
# @example Custom test configuration
#   test = JobberClient::PerformanceTest.new(integration)
#   test.test_jobs_retrieval(
#     strategies: [:pagination, :single_request],
#     batch_sizes: [10, 25, 50],
#     limits: [100, 500]
#   )
#
# <AUTHOR> Team
# @since 1.0.0
class JobberClient::PerformanceTest
  attr_reader :integration, :adapter, :results, :errors, :start_time

  ##
  # Initializes a new performance test instance.
  #
  # @param integration [Integration] Jobber integration instance
  # @param output_dir [String] Directory to save test results (default: tmp/jobber_performance)
  def initialize(integration, output_dir: "tmp/jobber_performance")
    @integration = integration
    @adapter = Sync::Jobber::Adapter.new(integration)
    @output_dir = output_dir
    @results = []
    @errors = []
    @start_time = Time.current

    # Ensure output directory exists
    FileUtils.mkdir_p(@output_dir)

    # Initialize logging
    setup_logging
  end

  ##
  # Runs comprehensive performance tests for all retrieve methods.
  #
  # @param strategies [Array<Symbol>] Test strategies (:pagination, :single_request)
  # @param batch_sizes [Array<Integer>] Batch sizes to test
  # @param limits [Array<Integer>] Record limits to test
  # @return [Hash] Summary of all test results
  def run_all_tests(strategies: [:pagination, :single_request],
                   batch_sizes: [10, 25, 50, 100],
                   limits: [50, 100, 250, 500])
    log_info("Starting comprehensive Jobber performance tests")
    log_info("Strategies: #{strategies}")
    log_info("Batch sizes: #{batch_sizes}")
    log_info("Limits: #{limits}")

    # Test each retrieve method
    test_jobs_retrieval(strategies: strategies, batch_sizes: batch_sizes, limits: limits)
    test_users_retrieval(strategies: strategies, batch_sizes: batch_sizes, limits: limits)
    test_visits_retrieval(strategies: strategies, batch_sizes: batch_sizes, limits: limits)
    test_clients_retrieval(strategies: strategies, limits: limits)
    test_properties_retrieval(strategies: strategies, limits: limits)
    test_line_items_retrieval(strategies: strategies, limits: limits)
    test_timesheet_entries_retrieval(strategies: strategies, limits: limits)
    test_products_retrieval(strategies: strategies, limits: limits)

    # Generate comprehensive report
    generate_report

    log_info("Performance tests completed. Results saved to #{@output_dir}")

    {
      total_tests: @results.length,
      total_errors: @errors.length,
      duration: Time.current - @start_time,
      output_dir: @output_dir
    }
  end

  ##
  # Tests jobs retrieval with different strategies and configurations.
  #
  # @param strategies [Array<Symbol>] Test strategies to use
  # @param batch_sizes [Array<Integer>] Batch sizes to test
  # @param limits [Array<Integer>] Record limits to test
  def test_jobs_retrieval(strategies: [:pagination, :single_request], batch_sizes: [25, 50], limits: [100, 250])
    log_info("Testing jobs retrieval performance")

    strategies.each do |strategy|
      case strategy
      when :pagination
        batch_sizes.each do |batch_size|
          limits.each do |limit|
            test_method_performance(
              method: :retrieve_jobs,
              strategy: :pagination,
              params: { limit: limit, enable_pagination: true },
              batch_size: batch_size,
              description: "Jobs pagination (batch: #{batch_size}, limit: #{limit})"
            )
          end
        end
      when :single_request
        limits.each do |limit|
          test_method_performance(
            method: :retrieve_jobs,
            strategy: :single_request,
            params: { limit: limit, enable_pagination: false },
            description: "Jobs single request (limit: #{limit})"
          )
        end

      end
    end
  end

  ##
  # Tests users retrieval with different strategies and configurations.
  def test_users_retrieval(strategies: [:pagination, :single_request], batch_sizes: [25, 50], limits: [100, 250])
    log_info("Testing users retrieval performance")

    strategies.each do |strategy|
      case strategy
      when :pagination
        batch_sizes.each do |batch_size|
          limits.each do |limit|
            test_method_performance(
              method: :retrieve_users,
              strategy: :pagination,
              params: { limit: limit, enable_pagination: true },
              batch_size: batch_size,
              description: "Users pagination (batch: #{batch_size}, limit: #{limit})"
            )
          end
        end
      when :single_request
        limits.each do |limit|
          test_method_performance(
            method: :retrieve_users,
            strategy: :single_request,
            params: { limit: limit, enable_pagination: false },
            description: "Users single request (limit: #{limit})"
          )
        end

      end
    end
  end

  ##
  # Tests visits retrieval with different strategies and configurations.
  def test_visits_retrieval(strategies: [:pagination, :single_request], batch_sizes: [25, 50], limits: [100, 250])
    log_info("Testing visits retrieval performance")

    # Get a sample job ID for testing visits
    sample_jobs = adapter.retrieve_jobs(limit: 1, enable_pagination: false)
    job_id = sample_jobs.first&.data&.dig("id") || sample_jobs.first&.data&.dig("encodedId")

    unless job_id
      log_error("No jobs found for visits testing - skipping visits tests")
      return
    end

    strategies.each do |strategy|
      case strategy
      when :pagination
        batch_sizes.each do |batch_size|
          limits.each do |limit|
            test_method_performance(
              method: :retrieve_visits,
              strategy: :pagination,
              params: { job_id: job_id, limit: limit, enable_pagination: true },
              batch_size: batch_size,
              description: "Visits pagination (batch: #{batch_size}, limit: #{limit})"
            )
          end
        end
      when :single_request
        limits.each do |limit|
          test_method_performance(
            method: :retrieve_visits,
            strategy: :single_request,
            params: { job_id: job_id, limit: limit, enable_pagination: false },
            description: "Visits single request (limit: #{limit})"
          )
        end

      end
    end
  end

  ##
  # Tests clients retrieval with different strategies.
  def test_clients_retrieval(strategies: [:single_request], limits: [50, 100])
    log_info("Testing clients retrieval performance")

    strategies.each do |strategy|
      limits.each do |limit|
        test_method_performance(
          method: :retrieve_clients,
          strategy: strategy,
          params: { limit: limit },
          description: "Clients #{strategy} (limit: #{limit})"
        )
      end
    end
  end

  ##
  # Tests properties retrieval with different strategies.
  def test_properties_retrieval(strategies: [:single_request], limits: [50, 100])
    log_info("Testing properties retrieval performance")

    strategies.each do |strategy|
      limits.each do |limit|
        test_method_performance(
          method: :retrieve_properties,
          strategy: strategy,
          params: { limit: limit },
          description: "Properties #{strategy} (limit: #{limit})"
        )
      end
    end
  end

  ##
  # Tests line items retrieval with different strategies.
  def test_line_items_retrieval(strategies: [:single_request], limits: [50, 100])
    log_info("Testing line items retrieval performance")

    strategies.each do |strategy|
      limits.each do |limit|
        test_method_performance(
          method: :retrieve_line_items,
          strategy: strategy,
          params: { limit: limit },
          description: "Line items #{strategy} (limit: #{limit})"
        )
      end
    end
  end

  ##
  # Tests timesheet entries retrieval with different strategies.
  def test_timesheet_entries_retrieval(strategies: [:single_request], limits: [50, 100])
    log_info("Testing timesheet entries retrieval performance")

    strategies.each do |strategy|
      limits.each do |limit|
        test_method_performance(
          method: :retrieve_timesheet_entries,
          strategy: strategy,
          params: { limit: limit },
          description: "Timesheet entries #{strategy} (limit: #{limit})"
        )
      end
    end
  end

  ##
  # Tests products/services retrieval with different strategies.
  def test_products_retrieval(strategies: [:single_request], limits: [50, 100])
    log_info("Testing products/services retrieval performance")

    strategies.each do |strategy|
      limits.each do |limit|
        test_method_performance(
          method: :retrieve_products_or_services,
          strategy: strategy,
          params: { limit: limit },
          description: "Products/services #{strategy} (limit: #{limit})"
        )
      end
    end
  end

  private

  ##
  # Sets up logging for performance tests.
  #
  # Creates a dedicated logger for performance test output with both
  # file and console output for comprehensive monitoring.
  #
  # @return [void]
  def setup_logging
    log_file = File.join(@output_dir, "performance_test.log")
    @logger = Logger.new(log_file)
    @logger.level = Logger::INFO
    @logger.formatter = proc do |severity, datetime, progname, msg|
      "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity}: #{msg}\n"
    end

    # Also log to console for immediate feedback
    @console_logger = Logger.new(STDOUT)
    @console_logger.level = Logger::INFO
    @console_logger.formatter = proc do |severity, datetime, progname, msg|
      "[#{datetime.strftime('%H:%M:%S')}] #{msg}\n"
    end
  end

  ##
  # Tests the performance of a specific adapter method.
  #
  # @param method [Symbol] The adapter method to test
  # @param strategy [Symbol] The testing strategy being used
  # @param params [Hash] Parameters to pass to the method
  # @param batch_size [Integer, nil] Batch size for pagination tests
  # @param description [String] Human-readable description of the test
  # @return [Hash] Test result metrics
  def test_method_performance(method:, strategy:, params:, batch_size: nil, description:)
    log_info("Running test: #{description}")

    result = {
      method: method,
      strategy: strategy,
      params: params,
      batch_size: batch_size,
      description: description,
      timestamp: Time.current,
      success: false,
      duration: 0,
      records_retrieved: 0,
      errors: [],
      throttling_events: 0,
      api_calls: 0,
      cost_metrics: {}
    }

    begin
      # Capture performance metrics
      start_time = Time.current

      # Execute the method with error handling
      records = adapter.send(method, **params)

      end_time = Time.current
      duration = end_time - start_time

      # Update result with success metrics
      result.merge!(
        success: true,
        duration: duration,
        records_retrieved: records&.length || 0,
        records_per_second: records&.length ? (records.length / duration).round(2) : 0
      )

      log_info("✓ #{description} completed: #{records&.length || 0} records in #{duration.round(2)}s")

    rescue => error
      result[:errors] << {
        class: error.class.name,
        message: error.message,
        backtrace: error.backtrace&.first(5)
      }

      log_error("✗ #{description} failed: #{error.class.name} - #{error.message}")
      @errors << result
    end

    @results << result
    result
  end

  ##
  # Generates comprehensive performance test reports.
  #
  # Creates multiple output formats including JSON, CSV, and human-readable
  # summary reports with performance metrics and recommendations.
  #
  # @return [void]
  def generate_report
    log_info("Generating performance test reports")

    # Generate JSON report
    generate_json_report

    # Generate CSV report
    generate_csv_report

    # Generate summary report
    generate_summary_report

    # Generate recommendations
    generate_recommendations_report
  end

  ##
  # Generates detailed JSON report with all test results.
  #
  # @return [void]
  def generate_json_report
    report_data = {
      test_run: {
        start_time: @start_time,
        end_time: Time.current,
        duration: Time.current - @start_time,
        integration_id: @integration.id
      },
      summary: {
        total_tests: @results.length,
        successful_tests: @results.count { |r| r[:success] },
        failed_tests: @errors.length,
        total_records_retrieved: @results.sum { |r| r[:records_retrieved] },
        average_duration: @results.map { |r| r[:duration] }.sum / @results.length.to_f
      },
      results: @results,
      errors: @errors
    }

    json_file = File.join(@output_dir, "performance_results.json")
    File.write(json_file, JSON.pretty_generate(report_data))
    log_info("JSON report saved to #{json_file}")
  end

  ##
  # Generates CSV report for easy analysis in spreadsheet applications.
  #
  # @return [void]
  def generate_csv_report
    csv_file = File.join(@output_dir, "performance_results.csv")

    CSV.open(csv_file, "w") do |csv|
      # Headers
      csv << [
        "Method", "Strategy", "Batch Size", "Limit", "Description",
        "Success", "Duration (s)", "Records Retrieved", "Records/Second",
        "Errors", "Timestamp"
      ]

      # Data rows
      @results.each do |result|
        csv << [
          result[:method],
          result[:strategy],
          result[:batch_size],
          result[:params][:limit],
          result[:description],
          result[:success],
          result[:duration].round(3),
          result[:records_retrieved],
          result[:records_per_second],
          result[:errors].length,
          result[:timestamp].strftime("%Y-%m-%d %H:%M:%S")
        ]
      end
    end

    log_info("CSV report saved to #{csv_file}")
  end

  ##
  # Generates human-readable summary report.
  #
  # @return [void]
  def generate_summary_report
    summary_file = File.join(@output_dir, "performance_summary.txt")

    File.open(summary_file, "w") do |file|
      file.puts "Jobber API Performance Test Summary"
      file.puts "=" * 50
      file.puts "Test Run: #{@start_time.strftime('%Y-%m-%d %H:%M:%S')} - #{Time.current.strftime('%H:%M:%S')}"
      file.puts "Duration: #{(Time.current - @start_time).round(2)} seconds"
      file.puts "Integration ID: #{@integration.id}"
      file.puts ""

      file.puts "Overall Results:"
      file.puts "- Total Tests: #{@results.length}"
      file.puts "- Successful: #{@results.count { |r| r[:success] }}"
      file.puts "- Failed: #{@errors.length}"
      file.puts "- Total Records Retrieved: #{@results.sum { |r| r[:records_retrieved] }}"
      file.puts ""

      # Performance by method
      methods = @results.group_by { |r| r[:method] }
      file.puts "Performance by Method:"
      methods.each do |method, results|
        successful_results = results.select { |r| r[:success] }
        next if successful_results.empty?

        avg_duration = successful_results.map { |r| r[:duration] }.sum / successful_results.length
        avg_records = successful_results.map { |r| r[:records_retrieved] }.sum / successful_results.length
        avg_rate = successful_results.map { |r| r[:records_per_second] }.sum / successful_results.length

        file.puts "  #{method}:"
        file.puts "    - Tests: #{results.length} (#{successful_results.length} successful)"
        file.puts "    - Avg Duration: #{avg_duration.round(3)}s"
        file.puts "    - Avg Records: #{avg_records.round(1)}"
        file.puts "    - Avg Rate: #{avg_rate.round(2)} records/second"
      end

      # Strategy comparison
      file.puts ""
      file.puts "Performance by Strategy:"
      strategies = @results.group_by { |r| r[:strategy] }
      strategies.each do |strategy, results|
        successful_results = results.select { |r| r[:success] }
        next if successful_results.empty?

        avg_duration = successful_results.map { |r| r[:duration] }.sum / successful_results.length
        avg_rate = successful_results.map { |r| r[:records_per_second] }.sum / successful_results.length

        file.puts "  #{strategy}:"
        file.puts "    - Tests: #{results.length} (#{successful_results.length} successful)"
        file.puts "    - Avg Duration: #{avg_duration.round(3)}s"
        file.puts "    - Avg Rate: #{avg_rate.round(2)} records/second"
      end

      # Errors
      if @errors.any?
        file.puts ""
        file.puts "Errors:"
        @errors.each_with_index do |error, index|
          file.puts "  #{index + 1}. #{error[:description]}"
          error[:errors].each do |err|
            file.puts "     #{err[:class]}: #{err[:message]}"
          end
        end
      end
    end

    log_info("Summary report saved to #{summary_file}")
  end

  ##
  # Generates performance recommendations based on test results.
  #
  # @return [void]
  def generate_recommendations_report
    recommendations_file = File.join(@output_dir, "recommendations.txt")

    File.open(recommendations_file, "w") do |file|
      file.puts "Jobber API Performance Recommendations"
      file.puts "=" * 50
      file.puts ""

      successful_results = @results.select { |r| r[:success] }

      if successful_results.any?
        # Find best performing strategy
        strategy_performance = successful_results.group_by { |r| r[:strategy] }
                                               .transform_values do |results|
          {
            avg_rate: results.map { |r| r[:records_per_second] }.sum / results.length,
            avg_duration: results.map { |r| r[:duration] }.sum / results.length,
            success_rate: results.length.to_f / @results.count { |r| r[:strategy] == results.first[:strategy] }
          }
        end

        best_strategy = strategy_performance.max_by { |_, metrics| metrics[:avg_rate] }
        file.puts "Best Overall Strategy: #{best_strategy[0]}"
        file.puts "  - Average Rate: #{best_strategy[1][:avg_rate].round(2)} records/second"
        file.puts "  - Average Duration: #{best_strategy[1][:avg_duration].round(3)} seconds"
        file.puts "  - Success Rate: #{(best_strategy[1][:success_rate] * 100).round(1)}%"
        file.puts ""

        # Method-specific recommendations
        file.puts "Method-Specific Recommendations:"
        methods = successful_results.group_by { |r| r[:method] }
        methods.each do |method, results|
          best_result = results.max_by { |r| r[:records_per_second] }
          file.puts "  #{method}:"
          file.puts "    - Best Configuration: #{best_result[:description]}"
          file.puts "    - Rate: #{best_result[:records_per_second]} records/second"
          file.puts "    - Duration: #{best_result[:duration].round(3)} seconds"
        end
      else
        file.puts "No successful tests to analyze for recommendations."
      end

      if @errors.any?
        file.puts ""
        file.puts "Issues to Address:"
        error_types = @errors.flat_map { |e| e[:errors] }.group_by { |e| e[:class] }
        error_types.each do |error_class, errors|
          file.puts "  - #{error_class}: #{errors.length} occurrences"
          file.puts "    Common message: #{errors.first[:message]}"
        end
      end
    end

    log_info("Recommendations saved to #{recommendations_file}")
  end

  ##
  # Logs informational messages to both file and console.
  #
  # @param message [String] Message to log
  # @return [void]
  def log_info(message)
    @logger&.info(message)
    @console_logger&.info(message)
  end

  ##
  # Logs error messages to both file and console.
  #
  # @param message [String] Error message to log
  # @return [void]
  def log_error(message)
    @logger&.error(message)
    @console_logger&.error(message)
  end
end
