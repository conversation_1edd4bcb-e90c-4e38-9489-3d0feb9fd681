# frozen_string_literal: true

module JobberClient
  ##
  # Module containing GraphQL queries for the Jobber API.
  #
  # This module provides all the GraphQL query strings needed to interact
  # with Jobber's API endpoints. Each method returns a properly formatted
  # GraphQL query string.
  module Queries
    ##
    # Schema introspection query for exploring the GraphQL schema.
    #
    # @return [String] GraphQL schema query
    def schema_query
      <<~GRAPHQL
        query GetSchema {
          __schema {
            types {
              name
              description
              fields {
                name
                type {
                  name
                  kind
                  ofType {
                    name
                    kind
                  }
                }
              }
            }
          }
        }
      GRAPHQL
    end

    ##
    # Jobs query with sorting, filtering, and pagination.
    # Matches Jobber API: filter, sort, searchTerm, pagination
    #
    # @return [String] GraphQL jobs query
    def jobs_query
      <<~GRAPHQL
        query GetJobs($filter: JobFilterAttributes, $searchTerm: String, $sort: [JobsSortInput!], $after: String, $before: String, $first: Int, $last: Int) {
          jobs(
            filter: $filter
            searchTerm: $searchTerm
            sort: $sort
            after: $after
            before: $before
            first: $first
            last: $last
          ) {
            edges {
              node {
                id
                client {
                  id
                  name
                }
                property {
                  id
                }
                lineItems {
                  nodes {
                    id
                    linkedProductOrService {
                      id
                      name
                    }
                  }
                }
                jobCosting {
                  id
                  expenseCost
                  labourCost
                  labourDuration
                  lineItemCost
                  profitAmount
                  profitPercentage
                  totalCost
                  totalRevenue
                }
                title
                billingType
                jobStatus
                jobNumber
                jobType
                total
                startAt
                endAt
                createdAt
                updatedAt
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Visits query with sorting, filtering, and pagination options.
    # Matches Jobber API: after, before, first, last, filter, sort, timezone
    #
    # @return [String] GraphQL visits query
    def visits_query
      <<~GRAPHQL
        query GetJobVisits(
          $after: String,
          $before: String,
          $first: Int,
          $last: Int,
          $filter: VisitFilterAttributes,
          $sort: [VisitsSortInput!],
          $timezone: Timezone,
          $jobId: EncodedId!
        ) {
          job(id: $jobId) {
            visits(
              after: $after,
              before: $before,
              first: $first,
              last: $last,
              filter: $filter,
              sort: $sort,
              timezone: $timezone
            ) {
              edges {
                node {
                  job {
                    id
                  }
                  client {
                    id
                  }
                  property {
                    id
                  }
                  assignedUsers {
                    nodes {
                      id
                    }
                  }
                  lineItems {
                    nodes {
                      id
                      totalCost
                    }
                  }
                  id
                  title
                  visitStatus
                  startAt
                  endAt
                  completedAt
                  createdAt
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        }
      GRAPHQL
    end

    def all_visits_query
      <<~GRAPHQL
        query GetAllVisits(
          $after: String,
          $before: String,
          $first: Int,
          $last: Int,
          $filter: VisitFilterAttributes,
          $sort: [VisitsSortInput!],
          $timezone: Timezone
        ) {
          visits(
            after: $after,
            before: $before,
            first: $first,
            last: $last,
            filter: $filter,
            sort: $sort,
            timezone: $timezone
          ) {
            edges {
              node {
                job {
                  id
                }
                client {
                  id
                }
                property {
                  id
                }
                assignedUsers {
                  nodes {
                    id
                  }
                }
                lineItems {
                  nodes {
                    id
                    totalCost
                  }
                }
                id
                title
                visitStatus
                startAt
                endAt
                completedAt
                createdAt
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Users query with filtering and pagination.
    # Matches Jobber API: filter, after, before, first, last (no sort parameter)
    #
    # @return [String] GraphQL users query
    def users_query
      <<~GRAPHQL
        query GetUsers(
          $filter: UsersFilterAttributes,
          $after: String,
          $before: String,
          $first: Int,
          $last: Int
        ) {
          users(
            filter: $filter,
            after: $after,
            before: $before,
            first: $first,
            last: $last
          ) {
            nodes {
              id
              name {
                first
                last
              }
              email {
                raw
              }
              phone {
                areaCode
                countryCode
                raw
                friendly
              }
              isAccountAdmin
              isAccountOwner
              status
              createdAt
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Clients query with filtering, sorting, and pagination.
    # Matches Jobber API: filter, searchTerm, sort, after, before, first, last
    #
    # @return [String] GraphQL clients query
    def clients_query
      <<~GRAPHQL
        query GetClients(
          $filter: ClientFilterAttributes,
          $searchTerm: String,
          $sort: ClientsSortInput,
          $after: String,
          $before: String,
          $first: Int,
          $last: Int
        ) {
          clients(
            filter: $filter,
            searchTerm: $searchTerm,
            sort: $sort,
            after: $after,
            before: $before,
            first: $first,
            last: $last
          ) {
            edges {
              node {
                id
                clientProperties {
                  nodes {
                    id
                  }
                }
                emails{
                  address
                }
                name
                title
                createdAt
                updatedAt
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Properties query with pagination only.
    # Matches Jobber API: after, before, first, last (no sort or filter parameters)
    #
    # @return [String] GraphQL properties query
    def properties_query
      <<~GRAPHQL
        query GetProperties($after: String, $before: String, $first: Int, $last: Int) {
          properties(after: $after, before: $before, first: $first, last: $last) {
            nodes {
              id
              client {
                id
              }
              address {
                id
                country
                city
                street1
                street2
                postalCode
                province
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Products and services query with sorting, filtering, and pagination.
    #
    # @return [String] GraphQL products query
    def products_query
      <<~GRAPHQL
        query GetProductOrService($filter: ProductsFilterInput, $after: String, $before: String, $first: Int, $last: Int) {
          products(
            filter: $filter
            after: $after
            before: $before
            first: $first
            last: $last
          ) {
            edges {
              node {
                id
                name
                description
                defaultUnitCost
                category
                taxable
                visible
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Line items query with pagination only.
    # Matches Jobber API: after, before, first, last (no sort or filter parameters)
    #
    # @return [String] GraphQL line items query
    def line_items_query
      <<~GRAPHQL
        query GetLineItems(
          $after: String,
          $before: String,
          $first: Int,
          $last: Int,
          $jobId: EncodedId!
        ) {
          job(id: $jobId) {
            id
            title
            lineItems(
              after: $after,
              before: $before,
              first: $first,
              last: $last
            ) {
              nodes {
                id
                linkedProductOrService {
                  id
                }
                name
                category
                quantity
                unitCost
                unitPrice
                totalCost
                totalPrice
                updatedAt
                taxable
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        }
      GRAPHQL
    end

    ##
    # Global timesheet entries query with sorting, filtering, and pagination.
    # Matches Jobber API: sort, filter, after, before, first, last
    #
    # @return [String] GraphQL timesheet entries query
    def timesheet_entries_query
      <<~GRAPHQL
        query GetAllTimeSheets(
          $sort: [TimeSheetEntriesSortAttributes!],
          $filter: TimeSheetEntriesFilterAttributes,
          $after: String,
          $before: String,
          $first: Int,
          $last: Int
        ) {
          timeSheetEntries(
            sort: $sort,
            filter: $filter,
            after: $after,
            before: $before,
            first: $first,
            last: $last
          ) {
            edges {
              node {
                id
                user {
                  id
                }
                visit{
                  id
                }
                approved
                label
                finalDuration
                labourRate
                createdAt
                updatedAt
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    ##
    # Quotes query with pagination only.
    # Matches Jobber API: after, before, first, last (no filter or sort parameters)
    # Used for budget information context and historical scope tracking
    #
    # @return [String] GraphQL quotes query
    def quotes_query
      <<~GRAPHQL
        query GetQuotes($after: String, $before: String, $first: Int, $last: Int) {
          quotes(after: $after, before: $before, first: $first, last: $last) {
            edges {
              node {
                id
                amounts {
                  subtotal
                  total
                }
                client {
                  id
                  name
                }
                property {
                  id
                }
                jobs(first: 10) {
                  nodes {
                    id
                    title
                  }
                }
                lineItems(first: 100) {
                  nodes {
                    id
                    name
                    description
                    quantity
                    unitCost
                    unitPrice
                    totalCost
                    totalPrice
                    category
                    taxable
                  }
                }
                quoteNumber
                quoteStatus
                title
                message
                salesperson {
                  id
                  name {
                    first
                    last
                  }
                }
                createdAt
                updatedAt
                transitionedAt
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end

    def jobs_linked_product_or_services_query
      <<~GRAPHQL
        query GetJobsLinkedProductOrService($after: String, $before: String, $first: Int, $last: Int) {
          jobs(after: $after, before: $before, first: $first, last: $last) {
            totalCount
            edges {
              node {
                id
                title
                lineItems {
                  nodes {
                    id
                    name
                    linkedProductOrService {
                      id
                      name
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      GRAPHQL
    end
  end
end
