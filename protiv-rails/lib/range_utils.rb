# frozen_string_literal: true

module RangeUtils
  private

  def compress_ranges(ranges)
    return [] if ranges.empty?

    ranges = ranges.sort_by { |range| range.first }

    ranges.each_with_object([]) do |range, compressed|
      _add_range(compressed, range)
    end
  end

  # assumed that ranges added are in order by #first)
  def _add_range(compressed, range)
    if compressed.last&.cover?(range.first)
      compressed << (compressed.pop.first .. range.last)
    else
      compressed << range
    end
  end

  def diff_ranges(starting_range, existing_ranges)
    existing_ranges = existing_ranges.sort_by(&:first)

    target_ranges = [starting_range]

    existing_ranges.each do |range|
      new_targets = []

      target_ranges.each do |a|
        _diff_range(a, range).each do |res|
          new_targets << res
        end
      end

      return new_targets if new_targets.empty?
      target_ranges = new_targets
    end

    target_ranges # FIXME: compress_ranges necessary?
  end

  def _diff_range(a, b)
    return [a] unless a.overlap?(b)

    a1 = a.first
    a2 = a.last
    b1 = b.first
    b2 = b.last

    if b1 <= a1 && b2 >= a2
      return []
    end

    #
    # |a--------------------|
    #       |b--------|
    # |c1---|         |c2---|
    #
    if b1 > a1 && b2 < a2
      # split
      return [a1..b1, b2..a2]
    end

    # |a----------|
    #       |b---------|
    # |c----|
    if a1 < b1 && b1 <= a2
      return [a1..b1]
    end

    #
    #       |a--------|
    # |b----------|
    #             |c--|

    if a1 >= b1 && b2 <= a2
      return [b2..a2]
    end

    raise "what did I miss"
  end
end
