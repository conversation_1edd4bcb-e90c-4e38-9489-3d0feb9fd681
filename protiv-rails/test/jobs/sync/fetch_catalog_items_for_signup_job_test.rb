# frozen_string_literal: true

require "test_helper"

class Sync::FetchCatalogItemsForSignupJobTest < ActiveJob::TestCase
  setup do
    @organization = organizations(:acme)
    @integration = integrations(:acme_aspire)
  end

  test "performs job successfully with default days_back and service filtering" do
    # Create a service with units progress_type to test association
    service = Service.create!(
      organization: @organization,
      name: "Test Service",
      progress_type: "units"
    )

    # Mock the adapter method
    mock_catalog_items = [
      mock_catalog_item_record("1", "Fertilizer"),
      mock_catalog_item_record("2", "Herbicide")
    ]

    @integration.adapter.expects(:retrieve_catalog_items_recent_jobs)
                       .with(item_type: "Material", days_back: 7, filter_by_service_ids: [])
                       .returns(mock_catalog_items)

    @integration.materializer.expects(:materialize_catalog_items)

    assert_nothing_raised do
      Sync::FetchCatalogItemsForSignupJob.new.perform(@integration.id)
    end
  end

  test "performs job successfully with custom days_back and service filtering" do
    # Mock the adapter method
    mock_catalog_items = [
      mock_catalog_item_record("1", "Fertilizer"),
      mock_catalog_item_record("2", "Herbicide")
    ]

    @integration.adapter.expects(:retrieve_catalog_items_recent_jobs)
                       .with(item_type: "Material", days_back: 14, filter_by_service_ids: [])
                       .returns(mock_catalog_items)

    @integration.materializer.expects(:materialize_catalog_items)

    assert_nothing_raised do
      Sync::FetchCatalogItemsForSignupJob.new.perform(@integration.id, 14)
    end
  end

  test "performs job successfully without service filtering" do
    # Mock the adapter method
    mock_catalog_items = [
      mock_catalog_item_record("1", "Fertilizer"),
      mock_catalog_item_record("2", "Herbicide")
    ]

    @integration.adapter.expects(:retrieve_catalog_items_recent_jobs)
                       .with(item_type: "Material", days_back: 7, filter_by_service_ids: nil)
                       .returns(mock_catalog_items)

    @integration.materializer.expects(:materialize_catalog_items)

    assert_nothing_raised do
      Sync::FetchCatalogItemsForSignupJob.new.perform(@integration.id, 7, false)
    end
  end

  test "handles empty catalog items gracefully" do
    @integration.adapter.expects(:retrieve_catalog_items_recent_jobs)
                       .with(item_type: "Material", days_back: 7, filter_by_service_ids: [])
                       .returns([])

    @integration.materializer.expects(:materialize_catalog_items)

    assert_nothing_raised do
      Sync::FetchCatalogItemsForSignupJob.new.perform(@integration.id)
    end
  end

  test "handles integration not found" do
    assert_raises(ActiveRecord::RecordNotFound) do
      Sync::FetchCatalogItemsForSignupJob.new.perform(999999)
    end
  end

  private

  def mock_catalog_item_record(id, name)
    catalog_item = Object.new
    catalog_item.define_singleton_method(:catalog_item_id) { id }
    catalog_item.define_singleton_method(:item_name) { name }
    catalog_item.define_singleton_method(:item_type) { "Material" }
    catalog_item.define_singleton_method(:active) { true }

    record = Object.new
    record.define_singleton_method(:source) { "aspire" }
    record.define_singleton_method(:primary_id) { id }
    record.define_singleton_method(:as_json) { { "CatalogItemID" => id, "ItemName" => name } }
    record.define_singleton_method(:resource) { catalog_item }

    record
  end
end
