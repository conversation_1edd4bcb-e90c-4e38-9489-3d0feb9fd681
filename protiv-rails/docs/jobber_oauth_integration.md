# Jobber OAuth 2 Integration

This document describes how to set up and use the Jobber OAuth 2 integration in the Protiv application.

## Overview

The Jobber integration uses OAuth 2.0 Authorization Code flow to securely connect to Jobber's API. This implementation follows Jobber's official OAuth documentation and provides:

- Secure authorization flow with state parameter validation
- Automatic token refresh
- GraphQL API integration
- Comprehensive error handling

## Setup

### 1. Jobber Developer Account Setup

1. Create a developer account at [Jobber Developer Center](https://developer.getjobber.com)
2. Create a new application
3. Configure your redirect URI (e.g., `https://yourdomain.com/api/v2/integrations/jobber/oauth/callback`)
4. Note your Client ID and Client Secret

### 2. Environment Configuration

Add the following environment variables or Rails credentials:

```bash
# Environment variables
JOBBER_CLIENT_ID=your_client_id_here
JOBBER_CLIENT_SECRET=your_client_secret_here
JOBBER_REDIRECT_URI=https://yourdomain.com/api/v2/integrations/jobber/oauth/callback
```

Or using Rails credentials:

```yaml
# config/credentials.yml.enc
jobber:
  client_id: your_client_id_here
  client_secret: your_client_secret_here
  redirect_uri: https://yourdomain.com/api/v2/integrations/jobber/oauth/callback
```
> [TODO:]
>Use credentials from integration.find_by(source: :jobber)

### 3. Install Dependencies

The integration uses the `oauth2` gem. Install it by running:

```bash
bundle install
```

## Usage

### 1. Initiate OAuth Flow

Make a GET request to start the OAuth authorization:

```
GET /api/v2/integrations/jobber/oauth/authorize
```

This redirect to jobber api authorization link:

```
https://api.getjobber.com/api/oauth/authorize?response_type=code&client_id=<CLIENT_ID>&redirect_uri=<CALLBACK_URL>&state=<STATE>
```

> [!IMPORTANT]
> In production, this step should be skipped and called directly from the Jobber marketplace via the "connect" button.

### 2. User Authorization

Redirect the user to the `authorization_url`. After the user authorizes application, Jobber will redirect them back to callback URL with an authorization code.

### 3. Automatic Token Exchange

The callback endpoint automatically:
- Validates the state parameter
- Exchanges the authorization code for access and refresh tokens
- Creates/updates the Integration record
- Stores encrypted tokens in the Credential model
- Initiates the initial data sync

### 4. Token Refresh

Tokens are automatically refreshed when needed. You can also manually refresh tokens:

```
POST /api/v2/integrations/jobber/oauth/refresh_token
```

## API Endpoints

### Authorization
- `GET /api/v2/integrations/jobber/oauth/authorize` - Start OAuth flow
- `GET /api/v2/integrations/jobber/oauth/callback` - OAuth callback (used by Jobber)
- `POST /api/v2/integrations/jobber/oauth/refresh_token` - Manually refresh tokens

## Security Features

### State Parameter Validation
- Generates cryptographically secure random state values
- Validates state parameter to prevent CSRF attacks
- Uses constant-time comparison to prevent timing attacks

### Token Security
- Access and refresh tokens are encrypted at rest
- Tokens are automatically refreshed before expiration
- Failed refresh attempts are logged and handled gracefully

### Session Management
- OAuth state is stored in secure session cookies
- Session data is cleared after successful/failed authorization

## Error Handling

The integration handles various error scenarios:

- **Authorization Denied**: User denies access
- **Invalid State**: CSRF protection triggered
- **Token Exchange Failure**: Invalid authorization code
- **Token Refresh Failure**: Expired or invalid refresh token
- **API Errors**: Network issues, rate limiting, etc.

All errors are logged and return appropriate HTTP status codes with descriptive error messages.

## Data Synchronization

After successful OAuth setup, the integration automatically syncs:

- Jobs and job details
- Visits (milestones)
- Users (identities)
- Clients
- Properties
- Products and services
- Line items
- Timesheet entries

## GraphQL Integration

The JobberClient uses Jobber's GraphQL API for efficient data retrieval. Key features:

- Pagination support
- Filtering by update timestamps
- Structured error handling
- Automatic retry logic

## Testing

Run the test suite:

```bash
# Run OAuth controller tests
rspec spec/controllers/api/integrations/jobber_oauth_controller_spec.rb

# Run OAuth service tests
rspec spec/services/jobber_oauth_service_spec.rb
```

## Troubleshooting

### Common Issues

1. **Invalid Redirect URI**: Ensure the redirect URI in Jobber Developer Center matches your configuration
2. **Token Expiration**: Check logs for token refresh failures
3. **Rate Limiting**: Jobber has API rate limits; the client includes automatic rate limiting
4. **Network Issues**: Check connectivity to `api.getjobber.com`

### Logging

OAuth operations are logged at INFO level. Token refresh failures are logged at ERROR level.

### Monitoring

Monitor the following for integration health:
- Token refresh success/failure rates
- API request success rates
- Sync job completion rates
- Error logs for authentication failures

## Development

### Adding New GraphQL Queries

To add new data types or queries:

1. Update `JobberClient` with new GraphQL queries
2. Add corresponding methods to `Sync::Jobber::Adapter`
3. Update `Sync::Jobber::Materializer` to handle new data types
4. Add tests for new functionality

### Testing with Mock Data

For development and testing, you can temporarily switch back to mock data by modifying the `JobberClient` methods.
