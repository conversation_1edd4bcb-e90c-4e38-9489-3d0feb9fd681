# Jobber GraphQL Rate Limiting - Usage Guide

This guide explains how to use the Jobber GraphQL API rate limiting features in the Protiv application.

> **Note**: As of the current implementation, pagination and adaptive retry are **automatically enabled** for all requests and cannot be disabled. This ensures optimal performance and reliability without requiring manual configuration.

## Overview

The rate limiting implementation provides:
- **Cost-based throttling** based on Jobber's GraphQL query cost system
- **Automatic pagination** for all requests (always enabled)
- **Adaptive retry logic** that automatically reduces batch size when throttled (always enabled)
- **Intelligent backoff** with exponential delay between retries
- **Circuit breaker pattern** for handling repeated failures
- **Real-time throttle monitoring** and adaptive batch sizing

## Basic Usage

### Creating a Rate-Limited Client

```ruby
# Rate limiting is always enabled for production use
client = JobberClient::Client.new(
  access_token,
  integration_id: "jobber_integration_123"
)

# All requests automatically use pagination and adaptive retry
```

### Fetching Data (Automatic Pagination and Adaptive Retry)

```ruby
# All fetch methods automatically use pagination and adaptive retry
all_jobs = client.fetch_jobs(batch_size: :auto)

# Fetch users with custom batch size
users = client.fetch_users(batch_size: 25)

# Fetch visits for a specific job (job_id is required)
visits = client.fetch_visits(job_id: "job_123")

# Fetch clients with filtering and sorting
clients = client.fetch_clients(limit: 100, filter: { status: "active" })
```

### Automatic Features (Always Enabled)

All fetch methods automatically include:

1. **Pagination**: Breaks large datasets into multiple requests using cursor-based pagination
2. **Adaptive Retry**: Automatically reduces batch size when throttled
3. **Rate Limiting**: Cost-based throttling with intelligent backoff

These features are built-in and cannot be disabled:

```ruby
# All methods automatically use pagination and adaptive retry
all_jobs = client.fetch_jobs(limit: 1000)  # Automatically paginated and optimized
all_users = client.fetch_users(batch_size: :auto)  # Auto-optimized batch size
all_clients = client.fetch_clients(limit: 500)  # Handles large datasets efficiently
```

### Using the Sync Adapter

The Jobber sync adapter automatically uses rate limiting, pagination, and adaptive retry:

```ruby
adapter = Sync::Jobber::Adapter.new(integration)

# All methods automatically use pagination and adaptive retry
jobs = adapter.retrieve_jobs(limit: 100)
users = adapter.retrieve_users(limit: 100)
visits = adapter.retrieve_visits(job_id: "job_123", limit: 50)
timesheet_entries = adapter.retrieve_timesheet_entries(limit: 200)
```

## Configuration

### Environment-Specific Settings

Rate limiting behavior is configured in `config/jobber_rate_limiting.yml`:

```yaml
production:
  rate_limiting:
    max_query_cost_percentage: 80  # Use 80% of available cost
    minimum_cost_threshold: 500    # Maintain at least 500 cost points
    safety_margin: 0.2             # 20% safety margin for estimates

  batch_processing:
    default_batch_size: 50
    minimum_batch_size: 10
    maximum_batch_size: 100
    auto_batch_sizing: true

  retry_settings:
    maximum_retry_attempts: 3
    initial_backoff: 1.0
    backoff_multiplier: 2.0
    maximum_backoff: 60.0

  circuit_breaker:
    failure_threshold: 5
    timeout: 300
    enabled: true

  adaptive_retry:
    default_min_batch_size: 5
    safety_margin_start: 0.8
    safety_margin_minimum: 0.3
```

### Runtime Configuration

```ruby
# Configure rate limiting parameters (pagination and adaptive retry are always enabled)
client.configure_rate_limiting(
  max_cost_percentage: 70
)

# Check current status
status = client.rate_limiting_status
puts "Rate limiting enabled: #{status[:enabled]}"
puts "Current throttle status: #{status[:current_throttle_status]}"
puts "Circuit breaker open: #{status[:circuit_breaker_open]}"
```

## Monitoring and Observability

### Rate Limiting Status

```ruby
# Get detailed status information
status = client.rate_limiting_status
# => {
#   enabled: true,
#   integration_id: "jobber_123",
#   max_cost_percentage: 80,
#   minimum_cost_threshold: 500,
#   circuit_breaker_open: false,
#   current_throttle_status: {
#     maximum_available: 10000,
#     currently_available: 8500,
#     restore_rate: 500
#   }
# }
```

### Cost Estimation

```ruby
# Estimate cost before making a request
query = client.jobs_query
variables = { first: 50 }

cost_estimate = client.estimate_query_cost(query, variables)
# => {
#   estimated_cost: 150,
#   base_cost: 125,
#   field_count: 25,
#   connection_multiplier: 50,
#   safety_margin: 0.2
# }
```

### Log Monitoring

The system logs important events:

```json
{
  "event": "jobber_cost_metrics",
  "integration_id": "jobber_123",
  "requested_cost": 150,
  "actual_cost": 142,
  "maximum_available": 10000,
  "currently_available": 9858,
  "restore_rate": 500,
  "cost_efficiency": 94.67
}
```
```json
{
  "event": "jobber_retry_attempt",
  "attempt": 1,
  "max_retries": 3,
  "error_class": "JobberClient::ThrottledError",
  "error_message": "Request throttled: Throttled",
  "wait_time": 1.06,
  "total_wait_time": 1.06,
  "retryable": true,
  "throttling_error": true
}
```

## Error Handling

### Throttling Errors

```ruby
begin
  response = client.fetch_jobs(limit: 100)
rescue JobberClient::ThrottledError => e
  puts "Request throttled: #{e.message}"
  puts "Wait time: #{e.wait_time} seconds"

  # Adaptive retry handles this automatically, but you can add custom logic
  sleep(e.wait_time) if e.wait_time
  retry
end
```

### Circuit Breaker

```ruby
begin
  response = client.fetch_jobs(limit: 100)
rescue JobberClient::RateLimitExceededError => e
  puts "Rate limit exceeded: #{e.message}"
  puts "Retry after: #{e.retry_after} seconds"

  # Circuit breaker is open, wait before retrying
  sleep(e.retry_after) if e.retry_after
end
```

### Recovery

```ruby
# Reset circuit breaker manually if needed
client.reset_rate_limiting!

# Check if rate limiting is working
if client.rate_limiting_enabled?
  puts "Rate limiting is active"
else
  puts "Rate limiting is disabled"
end
```

## Troubleshooting

### High Cost Queries

If your query cost exceeds the available budget, adaptive retry automatically handles it:

```ruby
# Adaptive retry automatically reduces batch size when throttled
jobs = client.fetch_jobs(limit: 1000, batch_size: 100)
# => If throttled, automatically retries with smaller batch sizes (50, 25, 10, etc.)
# => Successfully retrieves data with optimal batch sizes

# You can start with a smaller batch size for known expensive queries
jobs = client.fetch_jobs(limit: 1000, batch_size: 25)
# => Starts with smaller batches to avoid throttling
```

### Diagnosing Rate Limiting Issues

Check the logs for these patterns:

1. **Repeated Throttling**: Look for multiple `jobber_retry_attempt` events with the same batch size
2. **Cost Efficiency**: Low values in `cost_efficiency` indicate estimation problems
3. **Circuit Breaker**: Watch for circuit breaker activations in logs

### Common Solutions

1. **Adaptive Retry is Always Enabled**: No configuration needed - automatically handles throttling
2. **Reduce Initial Batch Size**: Start with smaller batch sizes for known large queries
3. **Increase Retry Attempts**: Configure more retries for critical operations in configuration
4. **Monitor Available Cost**: Check throttle status before large operations

## Best Practices

### 1. Standard Production Setup

```ruby
# Standard setup for production environments
client = JobberClient::Client.new(
  access_token,
  integration_id: integration.id.to_s
)

# Pagination and adaptive retry are always enabled for all operations
all_jobs = client.fetch_jobs()
```

### 2. Use Optimal Batch Sizing

```ruby
# Best approach for large datasets
all_jobs = client.fetch_jobs(
  batch_size: 50           # Start with reasonable batch size
)

# Or let the system auto-optimize
all_jobs = client.fetch_jobs(batch_size: :auto)
```

### 3. Monitor Throttle Status

```ruby
# Check throttle status before large operations
status = client.rate_limiting_status
if status[:current_throttle_status]
  available = status[:current_throttle_status][:currently_available]
  if available < 1000
    puts "Low cost available (#{available}), consider waiting"
  end
end
```

### 4. Handle Errors Gracefully

```ruby
def fetch_with_retry(client, &block)
  retries = 0
  max_retries = 3

  begin
    yield(client)
  rescue JobberClient::ThrottledError => e
    # Adaptive retry usually handles this, but you can add custom logic
    retries += 1
    if retries <= max_retries && e.wait_time
      Rails.logger.warn("Throttled, waiting #{e.wait_time}s (attempt #{retries})")
      sleep(e.wait_time)
      retry
    else
      raise
    end
  end
end

# Usage (though adaptive retry usually makes this unnecessary)
fetch_with_retry(client) do |c|
  c.fetch_jobs()
end
```

### 5. Use Auto Batch Sizing

```ruby
# Let the system determine optimal batch size
all_jobs = client.fetch_jobs(batch_size: :auto)
```

## Integration with Existing Code

All fetch methods automatically use pagination and adaptive retry. No changes needed to existing code:

```ruby
# All code automatically uses pagination and adaptive retry
jobs = client.fetch_jobs(limit: 100)  # Automatically paginated and optimized
users = client.fetch_users()          # Automatically optimized
clients = client.fetch_clients()      # Automatically efficient

# All methods are fully optimized by default
all_jobs = client.fetch_jobs(limit: 1000)  # Handles large datasets automatically
```
