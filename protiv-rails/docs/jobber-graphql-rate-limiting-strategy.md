# Jobber GraphQL API Rate Limiting Strategy

## Overview

This document outlines the comprehensive strategy for handling Jobber's GraphQL API rate limits, which use a query cost-based approach with a leaky bucket algorithm. The strategy ensures efficient data retrieval while staying within API limits and handling large data pulls that cannot be completed in a single request.

## Background

### Jobber's Rate Limiting Model

Jobber uses a **query cost-based rate limiting** system with the following characteristics:

- **Leaky Bucket Algorithm**: Points are subtracted for each query and restored over time
- **Cost Calculation**: Each field costs 1 point (except edges/nodes/node which cost 0)
- **Connection Fields**: Cost = `first`/`last` argument × number of requested fields
- **Default Limits**: 100 nodes assumed if no `first`/`last` argument provided
- **Maximum Available**: Typically 10,000 points per app/account combination
- **Restore Rate**: Usually 500 points per second

### Response Format

API responses include cost information in the `extensions` field:

```json
{
  "extensions": {
    "cost": {
      "requestedQueryCost": 142,
      "actualQueryCost": 47,
      "throttleStatus": {
        "maximumAvailable": 10000,
        "currentlyAvailable": 9953,
        "restoreRate": 500
      }
    }
  }
}
```

## Architecture Components

### 1. Query Cost Calculator

**Purpose**: Estimate GraphQL query costs before execution to prevent throttling.

**Responsibilities**:
- Parse GraphQL queries to count fields
- Calculate connection field costs based on pagination arguments
- Apply safety margins for estimation accuracy
- Provide cost estimates for batch size optimization

**Implementation Location**: `lib/jobber_client/query_cost_calculator.rb`

### 2. Enhanced Rate Limiter

**Purpose**: Extend existing `RateLimiter` service for GraphQL cost-based limiting.

**Responsibilities**:
- Track query costs instead of just request counts
- Monitor available cost budget from API responses
- Implement dynamic throttling based on `currentlyAvailable` points
- Calculate optimal wait times using `restoreRate`

**Implementation Location**: `app/services/jobber_rate_limiter.rb`

### 3. Throttle Status Tracker

**Purpose**: Monitor and respond to API throttle status in real-time.

**Responsibilities**:
- Parse throttle status from API responses
- Track cost budget across multiple requests
- Implement circuit breaker pattern for repeated throttling
- Provide recommendations for batch size adjustments

**Implementation Location**: `lib/jobber_client/throttle_status_tracker.rb`

### 4. Pagination Manager

**Purpose**: Handle automatic pagination for large datasets.

**Responsibilities**:
- Break large queries into optimal batch sizes
- Implement cursor-based pagination using `pageInfo`
- Manage progressive data collection
- Handle pagination state and error recovery

**Implementation Location**: `lib/jobber_client/pagination_manager.rb`

### 5. Retry Mechanism

**Purpose**: Implement intelligent retry logic with exponential backoff.

**Responsibilities**:
- Catch `THROTTLED` errors and implement retry logic
- Calculate wait times based on throttle status
- Implement exponential backoff for repeated failures
- Provide configurable retry limits and strategies

**Implementation Location**: `lib/jobber_client/retry_handler.rb`

## Implementation Phases

### Phase 1: Query Cost Calculator

Create a service to estimate GraphQL query costs:

```ruby
# Example usage
calculator = JobberClient::QueryCostCalculator.new
cost = calculator.estimate_cost(query, variables)
# => { estimated_cost: 150, field_count: 25, connection_multiplier: 6 }
```

**Key Features**:
- Parse GraphQL AST to count fields
- Handle nested connections and multipliers
- Provide detailed cost breakdown
- Include safety margins (10-20%)

### Phase 2: Enhanced Rate Limiter

Extend existing `RateLimiter` for cost-based limiting:

```ruby
# Example usage
limiter = JobberRateLimiter.new(integration_id: "jobber_123")
limiter.check_cost_availability(estimated_cost: 150)
limiter.update_from_response(api_response)
```

**Key Features**:
- Cost-based budget tracking
- Integration with existing Redis infrastructure
- Dynamic adjustment based on API responses
- Per-integration cost tracking

### Phase 3: Throttle Status Management

Track and respond to throttle status:

```ruby
# Example usage
tracker = JobberClient::ThrottleStatusTracker.new
tracker.update_status(throttle_status)
wait_time = tracker.calculate_wait_time(needed_cost: 200)
```

**Key Features**:
- Real-time throttle status monitoring
- Optimal wait time calculations
- Circuit breaker implementation
- Throttling event logging

### Phase 4: Intelligent Pagination

Implement automatic pagination:

```ruby
# Example usage
paginator = JobberClient::PaginationManager.new(client)
all_jobs = paginator.fetch_all_jobs_with_adaptive_retry(batch_size: :auto)
```

**Key Features**:
- Automatic batch size optimization
- Cursor-based pagination
- Error recovery and resumption
- Progress tracking and reporting

## Handling Large Data Pulls

### Strategy for Complete Data Synchronization

When pulling all data that cannot fit in a single request:

1. **Calculate Optimal Batch Size**
   ```ruby
   def calculate_safe_batch_size(available_cost, query_template)
     base_cost = estimate_query_cost(query_template, first: 1)
     safety_margin = 0.8 # Use 80% of available cost
     max_batch = (available_cost * safety_margin / base_cost).floor
     [max_batch, MIN_BATCH_SIZE].max
   end
   ```

2. **Implement Progressive Data Collection**
   ```ruby
   def fetch_all_records(resource_type)
     all_records = []
     cursor = nil
     
     loop do
       batch_size = calculate_safe_batch_size(current_available_cost)
       response = fetch_batch(resource_type, limit: batch_size, after: cursor)
       
       # Extract data and update tracking
       batch_data = extract_batch_data(response)
       all_records.concat(batch_data)
       
       # Check pagination
       page_info = extract_page_info(response)
       break unless page_info['hasNextPage']
       cursor = page_info['endCursor']
       
       # Handle throttling
       handle_throttle_response(response)
     end
     
     all_records
   end
   ```

3. **Implement Smart Waiting**
   ```ruby
   def handle_throttle_response(response)
     throttle_status = response.dig('extensions', 'cost', 'throttleStatus')
     return unless throttle_status
     
     current = throttle_status['currentlyAvailable']
     restore_rate = throttle_status['restoreRate']
     
     if current < MINIMUM_COST_THRESHOLD
       wait_time = (MINIMUM_COST_THRESHOLD - current) / restore_rate
       Rails.logger.info("Waiting #{wait_time}s for cost restoration")
       sleep(wait_time)
     end
   end
   ```

### Batch Processing Workflow

```mermaid
graph TD
    A[Start Data Pull] --> B[Calculate Available Cost]
    B --> C[Determine Batch Size]
    C --> D[Execute Query]
    D --> E[Check Response]
    E --> F{Throttled?}
    F -->|Yes| G[Calculate Wait Time]
    G --> H[Wait for Restoration]
    H --> B
    F -->|No| I[Extract Data]
    I --> J[Update Cursor]
    J --> K{More Data?}
    K -->|Yes| B
    K -->|No| L[Complete]
```

## Configuration Options

### Rate Limiting Configuration

```ruby
# config/jobber_rate_limiting.yml
production:
  max_query_cost_percentage: 80  # Use 80% of maximumAvailable
  minimum_batch_size: 10         # Minimum records per batch
  maximum_retry_attempts: 3      # Max retries before giving up
  backoff_multiplier: 2.0        # Exponential backoff factor
  safety_margin: 0.2             # 20% safety margin for cost estimation
  circuit_breaker_threshold: 5   # Failures before circuit opens
  circuit_breaker_timeout: 300   # Seconds before circuit reset

development:
  max_query_cost_percentage: 50  # More conservative in development
  minimum_batch_size: 5
  # ... other settings
```

### Query Optimization Settings

```ruby
# Optimize queries to reduce costs
OPTIMIZED_FIELD_LIMITS = {
  jobs: {
    line_items: 20,      # Limit nested line items
    time_sheet_entries: 50,  # Limit nested time entries
    visits: 10           # Limit nested visits
  },
  visits: {
    line_items: 50,
    time_sheet_entries: 100
  }
}.freeze
```

## Monitoring and Observability

### Metrics to Track

1. **Cost Efficiency Metrics**
   - Actual vs estimated query costs
   - Cost utilization percentage
   - Average batch sizes

2. **Throttling Metrics**
   - Throttling frequency
   - Average wait times
   - Circuit breaker activations

3. **Performance Metrics**
   - Query execution times
   - Data synchronization duration
   - Error rates and retry counts

### Logging Strategy

```ruby
# Example logging format
Rails.logger.info({
  event: 'jobber_api_request',
  integration_id: integration.id,
  query_type: 'jobs',
  estimated_cost: 150,
  actual_cost: 142,
  available_cost_before: 9953,
  available_cost_after: 9811,
  batch_size: 25,
  execution_time_ms: 1250,
  throttled: false
}.to_json)
```

### Alerting Thresholds

- **High Throttling Rate**: > 10% of requests throttled in 1 hour
- **Low Cost Efficiency**: Actual cost > 150% of estimated cost
- **Sync Delays**: Data sync taking > 2x expected time
- **Circuit Breaker**: Multiple integrations hitting circuit breaker

## Error Handling Strategies

### Throttling Errors

```ruby
rescue JobberClient::ThrottledError => e
  throttle_info = e.throttle_status
  wait_time = calculate_wait_time(throttle_info)
  
  if retry_count < MAX_RETRIES
    Rails.logger.warn("Throttled, waiting #{wait_time}s (attempt #{retry_count})")
    sleep(wait_time)
    retry_count += 1
    retry
  else
    raise JobberClient::RateLimitExceededError
  end
end
```

### Network and API Errors

```ruby
rescue OAuth2::Error, Net::TimeoutError => e
  if retry_count < MAX_RETRIES
    backoff_time = INITIAL_BACKOFF * (BACKOFF_MULTIPLIER ** retry_count)
    Rails.logger.warn("API error, backing off #{backoff_time}s: #{e.message}")
    sleep(backoff_time)
    retry_count += 1
    retry
  else
    raise JobberClient::ApiError.new(e)
  end
end
```

## Integration with Existing Codebase

### JobberClient Enhancement

The strategy integrates with the existing `JobberClient::Client` by:

1. **Wrapping `graphql_request` method** with cost checking
2. **Adding cost tracking** to all fetch methods
3. **Implementing automatic pagination** for large datasets
4. **Providing cost-aware batch processing** utilities

### Service Integration

```ruby
# Enhanced JobberClient usage
client = JobberClient::Client.new(access_token)
client.configure_rate_limiting(
  max_cost_percentage: 80,
  enable_auto_pagination: true
)

# Automatic handling of large datasets
all_jobs = client.fetch_jobs(enable_pagination: true)  # Handles pagination automatically
all_users = client.fetch_users(enable_pagination: true, batch_size: 50)
```

## Future Enhancements

1. **Machine Learning Cost Prediction**: Use historical data to improve cost estimation accuracy
2. **Dynamic Query Optimization**: Automatically adjust query complexity based on available cost
3. **Multi-Integration Coordination**: Coordinate rate limiting across multiple Jobber integrations
4. **Real-time Cost Monitoring Dashboard**: Provide visibility into cost usage patterns
5. **Predictive Throttling Prevention**: Proactively slow down requests before hitting limits

## Conclusion

This strategy provides a comprehensive approach to handling Jobber's GraphQL API rate limits while ensuring efficient data retrieval and system reliability. The phased implementation allows for gradual rollout and testing, while the monitoring and configuration options provide operational flexibility.

The key to success is balancing aggressive data collection with respectful API usage, ensuring that the system can handle both small queries and large data synchronization tasks without hitting rate limits or causing service disruptions.
