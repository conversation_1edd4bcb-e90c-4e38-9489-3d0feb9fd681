# Jobber Integration Documentation

## Overview

The Jobber integration provides seamless synchronization between Jobber's field service management platform and the Protiv system. This integration uses OAuth 2.0 authentication and GraphQL API to pull data from Jobber and materialize it into local Protiv records.

## Architecture Overview

```mermaid
graph TB
    subgraph "External"
        JA[Jobber API<br/>GraphQL]
        JO[Jobber OAuth<br/>Authorization Server]
    end
    
    subgraph "Protiv System"
        subgraph "OAuth Layer"
            JOS[JobberOauthService]
            CRED[Credentials Store]
        end
        
        subgraph "Client Layer"
            JC[JobberClient::Client]
            QCC[QueryCostCalculator]
            RL[JobberRateLimiter]
            RH[RetryHandler]
            PM[PaginationManager]
        end
        
        subgraph "Sync Layer"
            JA_SYNC[Jobber::Adapter]
            JCA[Jobber::CacheAdapter]
            JM[Jobber::Materializer]
            SJ[SyncJobberIntegrationJob]
        end
        
        subgraph "Data Layer"
            SC[SyncCache]
            DB[(Protiv Database)]
        end
    end
    
    JO --> JOS
    JOS --> CRED
    CRED --> JC
    JC --> QCC
    JC --> RL
    JC --> RH
    JC --> PM
    JC --> JA
    
    SJ --> JA_SYNC
    SJ --> JCA
    SJ --> JM
    JA_SYNC --> JC
    JCA --> SC
    JM --> DB
```

## OAuth 2.0 Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant P as Protiv App
    participant JOS as JobberOauthService
    participant JO as Jobber OAuth
    participant JA as Jobber API
    
    U->>P: Initiate Integration
    P->>JOS: authorization_url(state)
    JOS->>JO: Generate Auth URL
    JO-->>JOS: Authorization URL
    JOS-->>P: Return Auth URL
    P->>U: Redirect to Jobber
    
    U->>JO: Authorize Application
    JO->>P: Callback with code
    P->>JOS: exchange_code_for_token(code)
    JOS->>JO: Exchange Code
    JO-->>JOS: Access & Refresh Tokens
    JOS->>P: Store Credentials
    
    Note over P: Integration Ready
    
    P->>JA: API Request with Token
    alt Token Expired
        JA-->>P: 401 Unauthorized
        P->>JOS: refresh_token()
        JOS->>JO: Refresh Request
        JO-->>JOS: New Tokens
        JOS->>P: Updated Credentials
        P->>JA: Retry with New Token
    end
    JA-->>P: API Response
```

## Data Synchronization Flow

```mermaid
flowchart TD
    START([Sync Job Started]) --> USERS[Sync Users]
    USERS --> CLIENTS[Sync Clients]
    CLIENTS --> PROPS[Sync Properties]
    PROPS --> PRODS[Sync Products & Services]
    PRODS --> QUOTES[Sync Quotes]
    QUOTES --> JOBS[Sync Jobs]
    
    JOBS --> VISITS{For Each Job}
    VISITS --> |Recurring Jobs| MILESTONES[Create Milestones<br/>from Visits]
    VISITS --> |One-off Jobs| SKIP_MILESTONES[No Milestones]
    
    MILESTONES --> LINE_ITEMS[Sync Line Items<br/>for Each Visit]
    SKIP_MILESTONES --> TIMESHEET[Sync Timesheet Entries]
    LINE_ITEMS --> TIMESHEET
    
    TIMESHEET --> CACHE[Update Cache]
    CACHE --> END([Sync Complete])
    
    style USERS fill:#e1f5fe
    style CLIENTS fill:#e1f5fe
    style PROPS fill:#e1f5fe
    style PRODS fill:#e1f5fe
    style QUOTES fill:#e1f5fe
    style JOBS fill:#fff3e0
    style MILESTONES fill:#fff3e0
    style LINE_ITEMS fill:#fff3e0
    style TIMESHEET fill:#fff3e0
```

## GraphQL Rate Limiting & Adaptive Retry

```mermaid
graph TB
    subgraph "Request Flow"
        REQ[GraphQL Request]
        QCC[Query Cost Calculator]
        RL[Rate Limiter Check]
        EXEC[Execute Request]
        RESP[Process Response]
    end
    
    subgraph "Adaptive Retry Logic"
        FAIL[Request Failed]
        CALC[Calculate Optimal<br/>Batch Size]
        RETRY[Retry with<br/>Smaller Batch]
    end
    
    subgraph "Cost Calculation"
        PARSE[Parse GraphQL AST]
        COUNT[Count Fields]
        MULT[Apply Connection<br/>Multipliers]
        MARGIN[Add Safety Margin]
    end
    
    REQ --> QCC
    QCC --> PARSE
    PARSE --> COUNT
    COUNT --> MULT
    MULT --> MARGIN
    MARGIN --> RL
    
    RL --> |Available| EXEC
    RL --> |Throttled| FAIL
    EXEC --> |Success| RESP
    EXEC --> |Throttled| FAIL
    
    FAIL --> CALC
    CALC --> RETRY
    RETRY --> QCC
    
    RESP --> |Update Limits| RL
```

## Data Mapping Structure

```mermaid
erDiagram
    JOBBER_JOB ||--o{ JOBBER_VISIT : "has many"
    JOBBER_VISIT ||--o{ JOBBER_LINE_ITEM : "contains"
    JOBBER_JOB ||--o{ JOBBER_LINE_ITEM : "job-level items"
    JOBBER_VISIT ||--o{ JOBBER_TIMESHEET : "time entries"
    JOBBER_JOB }o--|| JOBBER_CLIENT : "belongs to"
    JOBBER_JOB }o--|| JOBBER_PROPERTY : "at location"
    JOBBER_LINE_ITEM }o--|| JOBBER_PRODUCT : "references"
    JOBBER_TIMESHEET }o--|| JOBBER_USER : "logged by"
    
    PROTIV_JOB ||--o{ PROTIV_MILESTONE : "has many"
    PROTIV_MILESTONE ||--o{ PROTIV_MILESTONE_ITEM : "contains"
    PROTIV_MILESTONE ||--o{ PROTIV_MILESTONE_TIME : "time entries"
    PROTIV_JOB }o--|| PROTIV_CLIENT : "belongs to"
    PROTIV_JOB }o--|| PROTIV_PROPERTY : "at location"
    PROTIV_MILESTONE_ITEM }o--|| PROTIV_CATALOG_ITEM : "references"
    PROTIV_MILESTONE_TIME }o--|| PROTIV_IDENTITY : "logged by"
    
    JOBBER_JOB ||--|| PROTIV_JOB : "maps to"
    JOBBER_VISIT ||--|| PROTIV_MILESTONE : "maps to (recurring only)"
    JOBBER_LINE_ITEM ||--|| PROTIV_MILESTONE_ITEM : "maps to"
    JOBBER_TIMESHEET ||--|| PROTIV_MILESTONE_TIME : "maps to"
    JOBBER_CLIENT ||--|| PROTIV_CLIENT : "maps to"
    JOBBER_PROPERTY ||--|| PROTIV_PROPERTY : "maps to"
    JOBBER_PRODUCT ||--|| PROTIV_CATALOG_ITEM : "maps to"
    JOBBER_USER ||--|| PROTIV_IDENTITY : "maps to"
```

## Budget Calculation Formulas

The integration implements specific budget calculation formulas based on Jobber data:

### Labor Calculations
- **Labor Hours** = `(Cost Price × Quantity) ÷ Wage Rate`
- **Labor Budget** = `Cost Price × Quantity`

### Material Calculations  
- **Material Budget** = `Unit Cost × Quantity`

### Contract Pricing
- **Contract Price** = `Unit Price × Quantity`

### Budget Categories
```mermaid
pie title Budget Distribution
    "Labor Budget" : 45
    "Material Budget" : 35
    "Equipment Budget" : 20
```

## Key Components

### 1. JobberOauthService
Handles OAuth 2.0 authentication flow including:
- Authorization URL generation with CSRF protection
- Token exchange and refresh
- Account information retrieval
- Integration and credential management

### 2. JobberClient::Client
Core GraphQL client with features:
- Automatic token refresh
- Cost-based rate limiting
- Adaptive retry mechanisms
- Pagination management

### 3. Sync::Jobber::Adapter
Data retrieval layer that:
- Fetches data from Jobber GraphQL API
- Handles incremental sync with `updated_since` parameters
- Manages OAuth credential refresh
- Implements caching strategies

### 4. Sync::Jobber::Materializer
Data transformation layer that:
- Converts Jobber records to Protiv entities
- Handles data mapping and validation
- Manages relationships between entities
- Implements budget calculations

### 5. Rate Limiting System
Sophisticated rate limiting with:
- GraphQL query cost estimation
- Dynamic batch size optimization
- Circuit breaker pattern
- Throttle status monitoring

## Sync Capabilities

| Entity | Pull | Push | Notes |
|--------|------|------|-------|
| Jobs | ✅ | ❌ | Full job data with costing |
| Milestones | ✅ | ❌ | From visits (recurring jobs only) |
| Milestone Items | ✅ | ❌ | From line items |
| Milestone Times | ✅ | ❌ | From timesheet entries |
| Identities | ✅ | ❌ | From users |
| Clients | ✅ | ❌ | Customer information |
| Properties | ✅ | ❌ | Job locations |
| Catalog Items | ✅ | ❌ | Products and services |
| Quotes | ✅ | ❌ | Quote data |

## Configuration

### Environment Variables
```bash
JOBBER_CLIENT_ID=your_client_id
JOBBER_CLIENT_SECRET=your_client_secret
JOBBER_REDIRECT_URI=https://your-app.com/oauth/jobber/callback
```

### Rate Limiting Configuration
```ruby
# Default settings in JobberClient::Configuration
max_query_cost_percentage: 80    # Use 80% of available cost
default_min_batch_size: 1        # Minimum batch size for adaptive retry
safety_margin: 0.2               # 20% safety margin for cost estimation
```

## Implementation Details

### GraphQL Query Structure

The integration uses standardized GraphQL queries with consistent patterns:

```graphql
query GetJobs($filter: JobFilterAttributes, $searchTerm: String, $sort: [JobsSortInput!], $after: String, $first: Int) {
  jobs(filter: $filter, searchTerm: $searchTerm, sort: $sort, after: $after, first: $first) {
    edges {
      node {
        id
        title
        jobNumber
        jobStatus
        jobType
        client { id name }
        property { id }
        lineItems { nodes { id } }
        jobCosting {
          expenseCost
          labourCost
          totalRevenue
        }
        createdAt
        updatedAt
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
```

### Incremental Sync Support

| Entity | Incremental Sync | Filter Type | Notes |
|--------|------------------|-------------|-------|
| Clients | ✅ | `updated_at` | Full incremental support |
| TimeSheetEntries | ✅ | `updated_at` | Full incremental support |
| Properties | ⚠️ | `created_at` only | Limited to creation date |
| Users | ⚠️ | `created_at` only | Limited to creation date |
| Jobs | ⚠️ | `created_at` only | Limited to creation date |
| Visits | ⚠️ | `created_at` only | Limited to creation date |
| Products | ❌ | None | Full sync required |
| LineItems | ❌ | None | Full sync required |

### Caching Strategy

```mermaid
flowchart LR
    subgraph "Cache Layers"
        L1[Memory Cache<br/>@client]
        L2[Redis Cache<br/>@adapter]
        L3[Database Cache<br/>@sync_cache]
    end
    
    subgraph "Cache Flow"
        REQ[API Request]
        CHECK[Check Cache]
        FETCH[Fetch from API]
        STORE[Store in Cache]
        RETURN[Return Data]
    end
    
    REQ --> CHECK
    CHECK --> |Hit| RETURN
    CHECK --> |Miss| FETCH
    FETCH --> STORE
    STORE --> L1
    STORE --> L2
    STORE --> L3
    STORE --> RETURN
```

### Error Handling & Retry Logic

The integration implements a sophisticated retry mechanism:

1. **Circuit Breaker Pattern**: Prevents cascading failures
2. **Exponential Backoff**: Gradually increases wait times
3. **Adaptive Batch Sizing**: Reduces batch size on throttling
4. **Cost-Based Optimization**: Calculates optimal request sizes

```ruby
# Example of adaptive retry calculation
def calculate_optimal_batch_size(available_cost, failed_batch_size, actual_cost)
  avg_cost_per_item = actual_cost.to_f / failed_batch_size
  safe_available_cost = available_cost * 0.8  # 80% safety margin
  optimal_batch = (safe_available_cost / avg_cost_per_item).floor
  [optimal_batch, MIN_BATCH_SIZE].max
end
```

## Data Transformation Examples

### Job Mapping
```ruby
# Jobber Job → Protiv Job
{
  remote_slug: jobber_job.id,
  name: jobber_job.title,
  job_number: jobber_job.jobNumber,
  status: map_job_status(jobber_job.jobStatus),
  job_type: map_job_type(jobber_job.jobType),
  contract_price_cents: (jobber_job.total * 100).to_i,
  created_date: jobber_job.startAt || jobber_job.createdAt
}
```

### Visit to Milestone Mapping
```ruby
# Jobber Visit → Protiv Milestone (recurring jobs only)
{
  remote_slug: jobber_visit.id,
  job_id: local_job_id,
  status: map_visit_status(jobber_visit.status),
  labor_budget_cents: calculate_labor_budget(jobber_visit.lineItems),
  material_budget_cents: calculate_material_budget(jobber_visit.lineItems),
  seconds_budget: calculate_duration(jobber_visit.startAt, jobber_visit.endAt)
}
```

### Budget Calculation Implementation
```ruby
def calculate_labor_budget(line_items)
  line_items.select { |item| item.category == "LABOR" }
            .sum { |item| item.unit_cost * item.quantity * 100 }
end

def calculate_material_budget(line_items)
  line_items.reject { |item| item.category == "LABOR" }
            .sum { |item| item.unit_cost * item.quantity * 100 }
end
```

## Monitoring & Observability

### Key Metrics to Monitor

1. **Sync Performance**
   - Sync duration per entity type
   - Records processed per minute
   - Error rates by operation

2. **Rate Limiting**
   - Query cost consumption
   - Throttling frequency
   - Adaptive retry effectiveness

3. **Data Quality**
   - Mapping success rates
   - Validation error counts
   - Data consistency checks

### Logging Structure

```ruby
# Structured logging example
Rails.logger.info("JobberSync", {
  integration_id: integration.id,
  entity_type: "jobs",
  records_processed: 150,
  duration_ms: 2500,
  rate_limit_remaining: 850,
  errors: []
})
```

## Troubleshooting Guide

### Common Issues

#### 1. OAuth Token Expiration
**Symptoms**: 401 Unauthorized errors
**Solution**: Automatic token refresh is implemented, but check credential storage

#### 2. Rate Limiting Exceeded
**Symptoms**: 429 Too Many Requests or throttling errors
**Solution**: Adaptive retry will handle this automatically, but monitor query complexity

#### 3. Data Mapping Failures
**Symptoms**: Missing or incorrect data in Protiv
**Solution**: Check materializer logs and validate field mappings

#### 4. Sync Performance Issues
**Symptoms**: Long sync times or timeouts
**Solution**: Review batch sizes and enable incremental sync where supported

### Debug Commands

```ruby
# Check integration status
integration = Integration.find_by(source: "jobber")
integration.adapter.client.account_info

# Test API connectivity
client = JobberClient::Client.new(access_token)
client.fetch_users(limit: 1)

# Review sync cache
integration.sync_caches.jobber_jobs.count
integration.sync_caches.jobber_visits.count

# Check rate limiting status
client.rate_limiter.current_status
```

## Future Enhancements

### Planned Features

1. **Bidirectional Sync**: Push data from Protiv to Jobber
2. **Real-time Webhooks**: Instant sync on data changes
3. **Advanced Filtering**: Custom sync rules and filters
4. **Bulk Operations**: Optimized batch processing
5. **Data Validation**: Enhanced data quality checks

### API Limitations to Address

1. **Limited Incremental Sync**: Many entities only support `created_at` filters
2. **No Webhook Support**: Currently polling-based sync only
3. **Query Complexity**: Some nested queries have high costs
4. **Rate Limiting**: GraphQL cost-based limiting requires careful optimization

## Security Considerations

### Data Protection
- OAuth tokens stored encrypted in database
- API requests use HTTPS only
- State parameters for CSRF protection
- Automatic token rotation

### Access Control
- Integration-specific credentials
- Organization-level data isolation
- Role-based sync permissions
- Audit logging for all operations

## Performance Optimization

### Best Practices

1. **Use Incremental Sync**: Where supported (Clients, TimeSheetEntries)
2. **Optimize Batch Sizes**: Let adaptive retry find optimal sizes
3. **Cache Aggressively**: Implement multi-layer caching
4. **Monitor Costs**: Track GraphQL query costs
5. **Parallel Processing**: Use background jobs for large syncs

### Benchmarks

| Operation | Records | Duration | Rate |
|-----------|---------|----------|------|
| Sync Users | 100 | 15s | 6.7/s |
| Sync Jobs | 500 | 120s | 4.2/s |
| Sync Visits | 1000 | 300s | 3.3/s |
| Sync TimeSheets | 2000 | 180s | 11.1/s |

*Benchmarks based on typical Jobber account with standard rate limits*

## Getting Started

### 1. Setup OAuth Application

1. Register your application in Jobber Developer Portal
2. Configure redirect URI: `https://your-app.com/oauth/jobber/callback`
3. Note down Client ID and Client Secret

### 2. Configure Environment

```bash
# Add to your environment variables
export JOBBER_CLIENT_ID="your_client_id"
export JOBBER_CLIENT_SECRET="your_client_secret"
export JOBBER_REDIRECT_URI="https://your-app.com/oauth/jobber/callback"
```

### 3. Initialize Integration

```ruby
# Create integration through OAuth flow
service = JobberOauthService.new
auth_url = service.authorization_url(state: SecureRandom.hex(32))

# After OAuth callback
result = service.handle_oauth_callback(
  authorization_code: params[:code],
  received_state: params[:state],
  stored_state: session[:oauth_state],
  organization: current_organization
)
```

### 4. Run Initial Sync

```ruby
# Queue sync job
integration = Integration.find_by(source: "jobber", organization: current_organization)
SyncJobberIntegrationJob.perform_later(integration.id)
```

### 5. Monitor Progress

```ruby
# Check sync status
integration.sync_statuses.recent
integration.sync_caches.count

# Review logs
Rails.logger.info("Jobber sync completed for integration #{integration.id}")
```

## API Reference

### JobberOauthService

```ruby
service = JobberOauthService.new

# Generate authorization URL
auth_url = service.authorization_url(state: "secure_state")

# Exchange authorization code for tokens
result = service.exchange_code_for_token(authorization_code: "code123")

# Refresh expired token
result = service.refresh_token(refresh_token: "refresh123")

# Get account information
account_info = service.account_info(access_token: "token123")
```

### JobberClient::Client

```ruby
client = JobberClient::Client.new(access_token, integration_id: "123")

# Fetch entities with pagination
jobs = client.fetch_jobs(limit: 100, batch_size: :auto)
users = client.fetch_users(limit: 50)
visits = client.fetch_visits(job_id: "job123", limit: 200)

# Check rate limiting status
status = client.rate_limiter.current_status
```

### Sync::Jobber::Adapter

```ruby
adapter = integration.adapter

# Retrieve data with sync tracking
jobs = adapter.retrieve_jobs(limit: 100, sync_status: sync_status)
users = adapter.retrieve_users(sync_status: sync_status)

# Check credential status
adapter.credential_needs_refresh?
adapter.refresh_credential
```

---

*This documentation covers the complete Jobber integration implementation. For additional support or questions, please refer to the development team or create an issue in the project repository.*
