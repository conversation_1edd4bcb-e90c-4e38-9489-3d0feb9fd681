#!/bin/bash

# This script fixes the specific issue with contact ID 3 in the integration
# Usage: ./bin/fix_contact_3.sh INTEGRATION_ID

if [ -z "$1" ]; then
  echo "Error: Integration ID is required"
  echo "Usage: ./bin/fix_contact_3.sh INTEGRATION_ID"
  exit 1
fi

INTEGRATION_ID=$1

# Make the script executable
chmod +x "$0"

echo "Creating placeholder for contact ID 3 in integration $INTEGRATION_ID..."
bundle exec rake aspire:create_contact_placeholder[$INTEGRATION_ID,3]

echo "Retrying the sync job..."
bundle exec rails runner "Sync::SyncIntegrationJob.perform_async($INTEGRATION_ID)"

echo "Done! Check the logs for any further errors."
