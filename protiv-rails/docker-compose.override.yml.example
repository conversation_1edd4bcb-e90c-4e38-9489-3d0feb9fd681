version: "3"

services:
  sidekiq_redis:
    container_name: protiv_redis
    image: redis:7.2
    ports:
      - 6379:6379
    volumes:
      - redis-data:/data

  postgresql:
    container_name: protiv_postgres
    image: postgres:16
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: protiv_development
    volumes:
      - postgres-data:/var/lib/postgresql/data

volumes:
  redis-data:
  postgres-data:
