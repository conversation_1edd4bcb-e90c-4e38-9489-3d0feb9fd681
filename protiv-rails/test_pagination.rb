#!/usr/bin/env ruby
# frozen_string_literal: true

# This script tests the pagination functionality of the jobs API endpoint
# Run with: rails runner test_pagination.rb

require 'net/http'
require 'json'
require 'uri'

# Helper method to make API requests
def make_request(path, params = {})
  uri = URI.parse("http://localhost:3000#{path}")
  uri.query = URI.encode_www_form(params)

  req = Net::HTTP::Get.new(uri)
  req['Content-Type'] = 'application/vnd.api+json'
  req['Accept'] = 'application/vnd.api+json'

  # Add authentication if needed
  # req['Authorization'] = 'Bearer your_token_here'

  res = Net::HTTP.start(uri.hostname, uri.port) do |http|
    http.request(req)
  end

  JSON.parse(res.body)
end

# Test different page sizes
puts "Testing page size = 5"
response = make_request('/api/v2/jobs', { 'page[size]' => 5, 'page[number]' => 1 })
puts "Response meta: #{response['meta']}"
puts "Number of records: #{response['data'].size}"
puts

puts "Testing page size = 10"
response = make_request('/api/v2/jobs', { 'page[size]' => 10, 'page[number]' => 1 })
puts "Response meta: #{response['meta']}"
puts "Number of records: #{response['data'].size}"
puts

# Test different pages
puts "Testing page 1"
response = make_request('/api/v2/jobs', { 'page[size]' => 5, 'page[number]' => 1 })
puts "Response meta: #{response['meta']}"
puts "First record ID: #{response['data'].first['id']}"
puts

puts "Testing page 2"
response = make_request('/api/v2/jobs', { 'page[size]' => 5, 'page[number]' => 2 })
puts "Response meta: #{response['meta']}"
puts "First record ID: #{response['data'].first['id'] if response['data'].first}"
puts

# Test with filters
puts "Testing with filter (recent=7d)"
response = make_request('/api/v2/jobs', { 'page[size]' => 5, 'page[number]' => 1, 'filter[recent]' => '7d' })
puts "Response meta: #{response['meta']}"
puts "Number of records: #{response['data'].size}"
