version: '3'

# Ports are not exposed outside of docker. To use them with an external app override the ports in a local
# docker-compose.override.yml file. See docker-compose.override.yml.example for a simple setup

services:
  #  https://github.com/bitnami/containers/tree/main/bitnami/redis
  sidekiq_redis:
    image: 'bitnami/redis:latest'
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - '6379'

  # https://github.com/bitnami/containers/blob/main/bitnami/postgresql/README.md
  postgresql:
    image: 'bitnami/postgresql:16.6.0'
    restart: always
    ports:
      - '5432'
    environment:
      - POSTGRESQL_USERNAME=postgres
      - POSTGRESQL_PASSWORD=postgres
      - ALLOW_EMPTY_PASSWORD=yes
