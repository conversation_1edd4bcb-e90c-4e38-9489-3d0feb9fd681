# frozen_string_literal: true

# Script to create an integration for an organization

# Find the organization
org = Organization.first
puts "Using organization: #{org.name} (ID: #{org.id})"

# Check if there are any existing integrations
existing_integrations = Integration.where(organization_id: org.id)
puts "Existing integrations for this org: #{existing_integrations.count}"

# Create a new integration
integration = Integration.new(
  organization_id: org.id,
  source: 'aspire',  # Using 'source' instead of 'provider'
  status: 'active',
  client_id: 'test_api_key_123',
  secret: 'test_api_secret_456',
  auto_sync: true,
  sync_interval: 3600  # 1 hour (3600 seconds)
)

# Save the integration
if integration.save
  puts "Integration created successfully!"
  puts "ID: #{integration.id}"
  puts "Provider: #{integration.source}"
  puts "Name: #{integration.source}"
  puts "Status: #{integration.status}"
else
  puts "Failed to create integration:"
  puts integration.errors.full_messages
end
