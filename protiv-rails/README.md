# protiv-rails

Welcome to Protiv-rails! This guide will help you set up the application on your local machine including **Ruby 3.3.6** with **rbenv**, and **pnpm** on macOS.

---

## Prerequisites

Before you begin, ensure the following are installed on your macOS system:

1. **Homebrew** - A package manager for macOS.
2. **Git** - Version control system.
3. **Postgresql 16** - See [Postgres and Redis](#Postgres and Redis) for instructions to run from a docker compose setup
4. **Redis**

---

## Step 1: Install rbenv

rbenv is a Ruby version manager that allows you to manage multiple Ruby versions.

1. Install rbenv and ruby-build:
   ```bash
   brew install rbenv ruby-build
   ```

2. Add rbenv to your shell configuration:
   ```bash
   echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> ~/.zshrc
   echo 'eval "$(rbenv init - zsh)"' >> ~/.zshrc
   source ~/.zshrc
   ```

3. Verify rbenv installation:
   ```bash
   rbenv --version
   ```

---

## Step 2: Install Ruby 3.3.6

1. Install Ruby 3.3.6 using rbenv:
   ```bash
   rbenv install 3.3.6
   ```

2. Set Ruby 3.3.6 as the global version:
   ```bash
   rbenv global 3.3.6
   ```

3. Verify the installed Ruby version:
   ```bash
   ruby -v
   ```

---

## Step 3: Set Up the Application

1. Clone the repository:
   ```bash
   git clone https://github.com/protiv/protiv-v2.git
   cd protiv-v2/protiv-rails
   ```

2. Install dependencies:
   ```bash
   bundle install
   ```

3. Set up the database:
   ```bash
   bin/rails db:setup

   ```
---

## Step 4: Install pnpm

**Install pnpm using Homebrew**:
   ```bash
   brew install pnpm
   ```

2. **Verify the installation**:
   ```bash
   pnpm --version
   ```

---

## Step 5: Set Up the Packages

1. **Navigate to the project directory**:
   ```bash
   cd /path/to/protiv-v2
   ```

2. **Install dependencies using pnpm**:
   ```bash
   pnpm install
   ```

3. **Verify the packages are installed**:
   Check the `node_modules` directory and ensure no errors appear during the installation.

4. **Build @protiv/heroicons addon**:
   ```bash
   cd ./@protiv/heroicons/addon
   pnpm build
   ```

   *This is a package we made to bind heroicons to Ember components. Generally you only have to do this step on a fresh clone, or if there are changes made to this package.*

---

## Step 6: Run Application

1. Start the full stack:
   ```bash
   foreman start -f Procfile.dev
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:4200
   ```

---

## Additional Setup Notes

### Postgres and Redis

Protiv-rails requires Postgres and Redis servers to run. These can be installed locally, which is not covered here. You
can use the provided Docker Compose setup to bring up server instances in Docker.

#### Docker Compose Setup

First create a copy of [docker-compose.override.yml.example](docker-compose.override.yml.example) as
`docker-compose.override.yml.example` which will allow you to expose the ports for the two servers locally:

`cp docker-compose.override.yml.example docker-compose.override.yml`

#### Running Postgres and Redis

`docker compose up`

This will start the two servers in a terminal window. You can also manage the servers with the Docker Desktop app.


### Update Aspire API

The Aspire api can be updated with

   ```bash
   bin/update_aspire_api
   ```

FIXME: This will likely generate the aspire client without the `params`. Be sure to fix this after the fact.

