# How the User Password Change Works

This document explains how a logged-in user changes their password.

## Overview

Changing a password is a two-step process designed for security:

1.  **Request the Change:** The user asks to change their password by providing their current one and the new one they want to use.
2.  **Confirm the Change:** The user must click a special link sent to their email address to prove it's really them and finalize the change.

This prevents someone else from changing the password even if they somehow gained access to the user's account, as long as the user's email is still secure.

## Step 1: Requesting the Password Change

1.  **User Action:** The user goes to their settings and enters their current password and their desired new password.
2.  **System Checks:** The system first verifies:
    - That the user is logged in and is trying to change _their own_ password.
    - That the entered `current_password` actually matches the one stored for the user.
    - That the `new_password` meets the security requirements (like minimum length).
3.  **If Checks Pass:**
    - The system temporarily flags the account, indicating a password change is waiting for confirmation (`password_change_pending_verification = true`).
    - It securely generates a unique, temporary verification link (token).
    - It sends an email to the user's registered address containing this verification link.
4.  **Response to User:** The user sees a message saying the request was received and they need to check their email to continue.
5.  **If Checks Fail:** The user sees an error message (e.g., "Incorrect current password" or "New password is too short").

## Step 2: Confirming the Password Change

1.  **User Action:** The user opens their email and clicks the verification link.
2.  **System Checks:** When the link is visited, the system verifies:
    - That the link (token) is valid and hasn't expired.
    - That the token matches the user who requested the change.
    - That the account is actually waiting for a password change confirmation (the flag set in Step 1 is still `true`).
3.  **If Checks Pass:**
    - The system updates the user's password to the new one they provided earlier.
    - It removes the temporary flag (`password_change_pending_verification = false`).
    - **Important:** It automatically logs the user out of all other active sessions on different devices/browsers for security. This is done by changing an internal version counter (`token_version`) for that user, making old login tokens invalid.
4.  **Response to User:** The user sees a success message (e.g., "Password changed successfully"). They might be redirected to the login screen.
5.  **If Checks Fail:** The user sees an error message (e.g., "Invalid or expired link").

## Summary of Key Parts

- **Endpoints:** Specific API addresses handle the request (`PATCH /api/v2/users/{user_id}/password`) and the confirmation (`GET /api/v2/users/{user_id}/verify_password_change`).
- **User Model:** Stores the encrypted password, the pending verification flag, and the token version for session control.
- **Email:** Used to send the vital verification link.
- **Tokens:** Secure, temporary codes used in the verification link.
- **Session Invalidation:** Ensures old logins are ended when a password changes.

## Key Components

- **Routes (`config/routes.rb`):** Defines the `PATCH .../password` and `GET .../verify_password_change` member routes for users.
- **Controller (`app/controllers/api/users_controller.rb`):** Handles requests, authorization, service instantiation, and response rendering.
- **Service Objects:**
  - `app/services/change_password_service.rb`: Encapsulates the logic for initiating the password change.
  - `app/services/verify_password_change_service.rb`: Encapsulates the logic for verifying the change via token.
- **Model (`app/models/user.rb`):**
  - `has_secure_password`
  - `generates_token_for :password_change_verification` (includes `token_version`)
  - `password_change_pending_verification` attribute (boolean)
  - `token_version` attribute (integer)
  - `invalidate_all_sessions!` method (increments `token_version`)
  - Password validations.
- **Mailer (`app/mailers/user_mailer.rb`):** Sends the verification email (`password_change_verification_email`).
- **Policy (`app/policies/user_policy.rb`):** Authorizes the initiation step.
- **Resource (`app/resources/user_resource.rb`):** Exposes the `password_change_pending_verification` status (read-only).
- **Migrations:** Added `password_change_pending_verification` and `token_version` columns to the `users` table.

## Error Handling

Failures at any stage result in a `422 Unprocessable Entity` status code with errors formatted according to the JSON:API specification, consistent with other API endpoints like password resets.
