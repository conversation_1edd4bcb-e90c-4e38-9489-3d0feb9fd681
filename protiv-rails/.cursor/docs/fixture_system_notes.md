# Protiv Fixture System Notes

This document explains how the fixture system works in the Protiv application, based on our investigation and the video transcript.
Video url: https://fathom.video/calls/261273991?tab=summary

## Two Types of Fixtures

The codebase uses two primary types of fixtures:

### 1. Database Fixtures (Ruby Code in spec/support/fixtures)

- Unlike traditional Rails YAML fixtures, these are written in Ruby.
- Created once before any tests run.
- Set up a "mini world" of consistent test data.
- Each test gets a transaction that rolls back at the end.
- Provides a consistent, realistic test environment rather than building data from scratch in each test.

Key advantages:

- Realistic test scenarios with well-defined personas.
- Ensures tests don't accidentally reveal data from other organizations.
- Avoids creating repetitive test data in each test.
- Each fixture serves a specific testing purpose (e.g., <PERSON> belongs to multiple organizations to test organization-scoped data access).

Example personas:

- <PERSON>seed: New user without organization
- David: User with multiple organizations (Protiv, Lawn Mow Inc.)
- <PERSON>/<PERSON>: Users in Lawn Mow Inc.
- <PERSON>/Luigi: Users in Mario Plumbing

### 2. API Fixtures (YAML files in ../@protiv/api-fixtures)

- Record expected API responses for API endpoints.
- Used to verify API responses match expectations.
- Also used by frontend tests to ensure consistent API integration.
- Stored as YAML files with request and response details.

Key points:

- API fixtures are critically important for frontend tests.
- When API responses change (for a new feature or updated code), fixtures need to be updated.
- Can be updated by running tests with `RECORD=true` environment variable.

## How The API Fixture System Works

1. **Recording**: When a test with `api_fixture()` runs with `RECORD=true`, it captures the API request and response in a YAML file.

2. **Verifying**: When a test runs normally, it makes the same request and compares the actual response with the recorded fixture.

3. **Structure**: Each fixture includes:
   - Request method, path, headers, and body
   - Response status, headers, and body

## Lessons for Development

1. **Backend Changes Impact Frontend**: Changes to models, resources or controllers can affect API responses, which in turn affect frontend tests.

2. **Intentional Updates**: When you intentionally change an API response format:

   - Run tests with `RECORD=true` to update fixtures
   - Verify the changes match what you expect
   - Communicate changes to frontend developers

3. **Fixture Maintenance**: The fixture system is designed to provide realistic, consistent test data, but requires active maintenance when API responses change.

4. **Consistency Across Endpoints**: The fixtures help ensure that related endpoints (like `/jobs` and `/job_details`) return compatible data.

## Commands for Working with Fixtures

```bash
# Run a specific test with recording
RECORD=true bundle exec rspec spec/requests/api/accounts_controller_spec.rb

# Run a specific test file
bundle exec rspec spec/requests/api/accounts_controller_spec.rb

# Run all tests
bundle exec rspec
```
