# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# Temporary files generated by your text editor or operating system
# belong in git's global ignore instead:
# `$XDG_CONFIG_HOME/git/ignore` or `~/.config/git/ignore`

# Ignore bundler config.
/.bundle

# Ignore all environment files (except templates).
/.env*
!/.env*.erb

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore storage (uploaded files in development and any SQLite databases).
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

/public/assets

# Ignore master key for decrypting credentials and more.
/config/master.key

/app/assets/builds/*
!/app/assets/builds/.keep

notes.md
/spec/examples.txt
/coverage
/spec/support/aspire_credentials.json

config/initializers/breakpoints.rb
spec/ports
spec/questions
spec/generator

# Ignore docker-compose overrides
docker-compose.override.yml
.byebug_history

# Ignore macOS system files
.DS_Store
*.DS_Store
**/.DS_Store
.cursor/.DS_Store

.cursor/plans/*

aspire_data

# Ignore all files in the .idea directory
.idea/*