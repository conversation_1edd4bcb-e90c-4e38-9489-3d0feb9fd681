# frozen_string_literal: true

class ProPayPayable < ApplicationRecord
  belongs_to :pro_pay
  belongs_to :payable, polymorphic: true

  enum :rate_class, {
    manager: "manager",
    crew_lead: "crew_lead",
    crew: "crew",
    other: "other",
    company: "company"
  }

  def crew_lead?
    return crew_lead_override unless crew_lead_override.nil?

    payable.is_a?(Identity) ? payable.crew_lead? : false
  end
end
