# frozen_string_literal: true

class Job < ApplicationRecord
  validates :name, presence: true

  belongs_to :organization
  belongs_to :branch, optional: true
  belongs_to :manager, class_name: "Identity", optional: true

  has_one :sub_pro_pay_pro_payable, as: :pro_payable
  has_one :sub_pro_pay, through: :sub_pro_pay_pro_payable
  has_one :pro_pay, through: :sub_pro_pay

  has_one :first_milestone,
          -> { order(created_at: :asc) },
          class_name: "Milestone"

  # Needed for Hopping Relationships, see https://www.graphiti.dev/cookbooks/hopping-relationships
  attr_accessor :resolved_pro_pay
  attr_accessor :resolved_sub_pro_pay

  belongs_to :property, optional: true
  belongs_to :invoice_type, optional: true
  has_many :milestones
  has_many :milestone_times, through: :milestones
  has_many :routes, through: :milestone_times
  has_many :schedule_visits, through: :milestones
  # in reality it's usually 1, but only if all currencies are the same.
  has_many :job_budgets

  scope :with_sub_pro_pay, -> do
    join_sql = <<~SQL
        INNER JOIN sub_pro_pay_pro_payables sub_pro_pay_pro_payables ON\
        (sub_pro_pay_pro_payables.pro_payable_type = 'Job' AND \
        sub_pro_pay_pro_payables.pro_payable_id = jobs.id) \
        OR (sub_pro_pay_pro_payables.pro_payable_type = 'Milestone' AND \
        sub_pro_pay_pro_payables.pro_payable_id = milestones.id)
      SQL

    joins(:milestones).joins(join_sql)
  end

  scope :without_sub_pro_pay, -> do
    join_sql = <<~SQL
        LEFT JOIN sub_pro_pay_pro_payables ON \
        (sub_pro_pay_pro_payables.pro_payable_type = 'Job' AND \
        sub_pro_pay_pro_payables.pro_payable_id = jobs.id) \
        OR (sub_pro_pay_pro_payables.pro_payable_type = 'Milestone' AND \
        sub_pro_pay_pro_payables.pro_payable_id = milestones.id)
      SQL

    joins(:milestones).joins(join_sql).where(sub_pro_pay_pro_payables: { id: nil })
  end

  trackable

  validate :branch_in_organization

  # NOTE: `last_activity_at` is required, but does not have a default value.
  # This should be purposefully set by a syncer or some other input.
  validates :last_activity_at, presence: true, strict: true

  scope :by_last_activity, -> { where(initial_sync_done: true).order(last_activity_at: :desc) }

  ON_SCHEDULE = "on_schedule"
  BEHIND_SCHEDULE = "behind_schedule"

  enum :progress_status, {
    on_schedule: ON_SCHEDULE,
    behind_schedule: BEHIND_SCHEDULE
  }

  # FIXME: it's unclear what field to extract from aspire here;
  # OpportunityStatus is all 'Won' and JobStatusName is all 'In Process Edited'
  enum :status, {
    pending: "pending",
    in_progress: "in_progress",
    completed: "completed",
    canceled: "canceled"
  }

  enum :job_type, {
    unknown: "unknown",
    recurring: "recurring",
    non_recurring: "non_recurring"
  }

  alias_attribute(:job_number, :remote_reference)

  # Budget status is no longer an enum, leaving this behind to document the
  # possible variants
  # enum :budget_status, {
  #   under: "under",
  #   near: "near",
  #   over: "over"
  # }

  def client_name
    property&.name
  end

  # NOTE: we will need concurrency conversion info if there are mixed currencies
  def total_budget
    aggregate_job_budgets(:total_budget)
  end

  # NOTE: we will need concurrency conversion info if there are mixed currencies
  def total_costs
    aggregate_job_budgets(:total_costs)
  end

  def contract_price
    aggregate_job_budgets(:contract_price)
  end

  def gross_profit
    aggregate_job_budgets(:gross_profit)
  end

  def budgeted_gross_profit
    aggregate_job_budgets(:budgeted_gross_profit)
  end

  def seconds_budget
    aggregate_job_budgets(:seconds_budget, false)
  end

  def seconds_cost
    aggregate_job_budgets(:seconds_cost, false)
  end

  # Essentially this means that the only way to set a different
  # currency on the job is to ensure the branch is added.
  def currency
    branch&.currency || Money.default_currency.iso_code
  end

  BUDGET_CATEGORIES.each do |category|
    define_method(:"#{category}_cost") do
      aggregate_job_budgets(:"#{category}_cost")
    end

    define_method(:"#{category}_budget") do
      aggregate_job_budgets(:"#{category}_budget")
    end
  end

  def progress
    {
      status: progress_status_calculated,
      percentage: progress_percentage
    }
  end

  def progress_status_calculated
    on_schedule? ? ON_SCHEDULE : BEHIND_SCHEDULE
  end

  # Just to have consistency with milestone
  def percent_complete
    progress_percentage * 100.0
  end

  def progress_percentage
    if milestone_progress_percent_hundredths.nil?
      update_cache_columns
    end

    milestone_progress_percent_hundredths / 100.0
  end

  def update_cache_columns_async
    Cache::UpdateJobCachesJob.perform_async(id)
  end

  def update_cache_columns
    update_milestone_progress_percent
    save if changed?
  end

  def on_schedule
    budget_used_percent <= progress_percentage
  end

  alias_method(:on_schedule?, :on_schedule)

  def budget
    unless total_budget.zero?
      {
        status: budget_status,
        percentage: budget_used_percent
      }
    end
  end

  def budget_used_ratio
    return 0.0 if total_budget.zero?
    total_costs.to_f / total_budget.to_f
  end

  # returns 0.0 .. 100.0 as a float
  def budget_used_percent
    (budget_used_ratio * 100).round(1)
  end

  def reference_name
    components = [remote_reference, branch&.name].compact
    components.compact.join(" - ")
  end

  def budget_status
    ratio = budget_used_ratio

    if ratio > 1
      "over"
    else
      "under"
    end
  end

  def job_budget
    return @job_budget if defined?(@job_budget)

    if job_budgets.map(&:currency).uniq.count > 1
      raise "multiple currencies for job #{id}"
    end

    @job_budget = job_budgets.first

    raise "budget doesn't match currency" unless @job_budget.currency == currency

    @job_budget
  end

  private

  def calculate_milestone_progress_percent
    return 0.0 unless milestones.any?

    total = 0
    total_weights = 0
    milestones.each do |milestone|
      total_weights += milestone.total_budget
      total += (milestone.percent_complete * milestone.total_budget / 100.0)
    end

    return 0.0 if total_weights.zero?

    (100 * total / total_weights.to_f).round(1)
  end

  def update_milestone_progress_percent
    # cached value
    self.milestone_progress_percent_hundredths = (calculate_milestone_progress_percent * 100).to_i
  end

  def branch_in_organization
    return unless branch

    errors.add(:branch, "must belong to organization") unless branch.organization_id == organization_id
  end

  def all_currencies
    attribute_names.grep(/_currency\Z/).each_with_object(Set.new) do |attr, set|
      set << read_attribute(attr)
    end
  end

  def aggregate_job_budgets(property_name, is_monetized = true)
    if is_monetized
      job_budgets.sum(Money.zero(currency), &property_name)
    else
      job_budgets.sum(&property_name)
    end
  end
end
