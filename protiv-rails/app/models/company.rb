# frozen_string_literal: true

class Company < ApplicationRecord
  has_many :jobs
  has_many :roles
  has_many :users, through: :roles

  has_many :admin_roles, -> { where(role_type: "admin") }, class_name: "Role"
  has_many :employee_roles, -> { where(role_type: "employee") }, class_name: "Role"
  has_many :manager_roles, -> { where(role_type: "manager") }, class_name: "Role"

  has_many :admin_users, through: :admin_roles, source: :user
  has_many :employee_users, through: :employee_roles, source: :user
  has_many :manager_users, through: :manager_roles, source: :user

  has_many :payroll_schedules

  trackable
end
