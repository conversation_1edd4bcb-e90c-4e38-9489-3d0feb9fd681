# frozen_string_literal: true

class Branch < ApplicationRecord
  belongs_to :organization
  belongs_to :pay_payroll_schedule, class_name: "PayrollSchedule", required: false, foreign_key: :payroll_schedule_id
  belongs_to :bonus_payroll_schedule, class_name: "PayrollSchedule", required: false, foreign_key: :bonus_schedule_id
  belongs_to :route_payroll_schedule, class_name: "PayrollSchedule", required: false, foreign_key: :route_schedule_id

  has_many :jobs
  has_many :routes
  has_many :milestones, through: :jobs

  has_many :pay_periods
  has_many :pay_pay_periods, -> { where(period_type: "pay") }, class_name: "PayPeriod"
  has_many :bonus_pay_periods, -> {  where(period_type: "bonus") }, class_name: "PayPeriod"
  has_many :route_pay_periods, -> {  where(period_type: "route") }, class_name: "PayPeriod"

  has_one :current_pay_pay_period, -> { where(period_type: "pay").current }, class_name: "PayPeriod", autosave: false
  has_one :future_pay_pay_period, -> { where(period_type: "pay").future }, class_name: "PayPeriod", autosave: false

  has_one :current_bonus_pay_period, -> { where(period_type: "bonus").current }, class_name: "PayPeriod", autosave: false
  has_one :future_bonus_pay_period, -> { where(period_type: "bonus").future }, class_name: "PayPeriod", autosave: false

  has_one :current_route_pay_period, -> { where(period_type: "route").current }, class_name: "PayPeriod", autosave: false
  has_one :future_route_pay_period, -> { where(period_type: "route").future }, class_name: "PayPeriod", autosave: false

  scope :with_future_pay_pay_period, -> { joins(:future_pay_pay_period) }
  scope :without_future_pay_pay_period, -> { where.not(id: Branch.with_future_pay_pay_period) }

  scope :with_future_bonus_pay_period, -> { joins(:future_bonus_pay_period) }
  scope :without_future_bonus_pay_period, -> { where.not(id: Branch.with_future_bonus_pay_period) }

  scope :with_future_route_pay_period, -> { joins(:future_route_pay_period) }
  scope :without_future_route_pay_period, -> { where.not(id: Branch.with_future_route_pay_period) }

  scope :with_current_pay_pay_period, -> { joins(:current_pay_pay_period) }
  scope :without_current_pay_pay_period, -> { where.not(id: Branch.with_current_pay_pay_period) }

  scope :with_current_bonus_pay_period, -> { joins(:current_bonus_pay_period) }
  scope :without_current_bonus_pay_period, -> { where.not(id: Branch.with_current_bonus_pay_period) }

  scope :with_current_route_pay_period, -> { joins(:current_route_pay_period) }
  scope :without_current_route_pay_period, -> { where.not(id: Branch.with_current_route_pay_period) }

  trackable

  validates :time_zone, presence: true, inclusion: { in: ActiveSupport::TimeZone.all.map(&:tzinfo).map(&:identifier) }
  validates :currency, presence: true, inclusion: { in: Money::Currency.all.map(&:iso_code) }

  after_commit :generate_pay_periods

  def active_payroll_schedule
    pay_payroll_schedule || organization.default_payroll_schedule || organization.payroll_schedules.first
  end

  def active_bonus_schedule
    bonus_payroll_schedule || organization.default_bonus_schedule || organization.payroll_schedules.first
  end

  def active_route_schedule
    route_payroll_schedule || organization.default_route_schedule || organization.payroll_schedules.first
  end

  def generate_pay_periods
    Bonuses::GeneratePayPeriodsJob.perform_async(id)
  end
end
