# frozen_string_literal: true

class TrackingMaterial
  def initialize(milestone:, budgeted_items: [], allocations: [])
    @milestone = milestone
    @budgeted_items = budgeted_items.to_a
    @allocations = allocations.to_a
  end

  attr_reader :budgeted_items, :allocations, :milestone

  def catalog_item
    budgeted_items.first.catalog_item
  end

  def material_name
    catalog_item.name
  end

  def unit_of_measure
    catalog_item.allocation_unit
  end

  def budget_quantity
    @budget_quantity ||= budgeted_items.sum(0.0) { |i| i.quantity || 0.0 }
  end

  def actual_quantity
    @actual_quantity ||= allocations.sum(0.0) { |i| i.item_quantity || 0.0 }
  end

  def remaining_quantity
    budget_quantity - actual_quantity
  end

  def percent_complete
    return 0.0 if budget_quantity.zero?
    100 * actual_quantity / budget_quantity
  end

  def install_rate
    return Money.from_amount(0) if budget_quantity.zero?

    milestone.labor_budget / budget_quantity
  end

  def install_rate_per_hour
    budgeted_hours / budget_quantity
  end

  def id
    milestone_id
  end

  def milestone_id
    milestone.id
  end

  private

  def budgeted_hours
    (milestone.seconds_budget || 0) / 3600.0
  end
end
