# frozen_string_literal: true

class SubProPayLineItem < ApplicationRecord
  self.primary_key = [:sub_pro_pay_id, :identity_id]

  belongs_to :sub_pro_pay
  belongs_to :identity
  has_one :sub_pro_pay_line_item_detail, foreign_key: [:sub_pro_pay_id, :identity_id]
  has_many :statement_line_items, foreign_key: [:pro_pay_id, :identity_id]

  monetize :labor_cost_cents
  monetize :labor_cost_entered_cents
  monetize :labor_cost_adjustment_cents

  # this isn't strictly necessary since this model is backed by a view, but it will prevent
  # rails from calling save, which would fail anyway.
  def readonly?
    true
  end

  def duration_hours
    self.duration_seconds / 3600.0
  end

  # Calculate this worker's share of the available bonus for the sub_pro_pay
  def available_bonus
    Money.infinite_precision = true
    total_bonus = sub_pro_pay.bonus(percent_complete: 1) - sub_pro_pay.approved_bonus
    return Money.zero unless total_bonus.positive?

    crew_bonus_pool = total_bonus * (sub_pro_pay.pro_pay.crew_percent_hundredths / 100_00.0)
    crew_lead_bonus_pool = total_bonus * (sub_pro_pay.pro_pay.crew_lead_percent_hundredths / 100_00.0)

    total_hours = sub_pro_pay.total_duration_seconds / 3600.0
    bonus_per_hour = (crew_lead ? crew_lead_bonus_pool / total_hours : crew_bonus_pool / total_hours)

    bonus = (bonus_per_hour * (duration_seconds / 3600.0))
    Money.infinite_precision = false
    bonus
  end

  # Get this worker's approved bonus from statement line items
  def approved_bonus
    # Find statement line items for this identity related to this pro_pay that are bonuses (positive amounts)
    statement_line_items = StatementLineItem.where(
      identity: identity,
      pro_pay: sub_pro_pay.pro_pay
    ).where("amount_cents > 0")

    total_bonus = statement_line_items.sum(&:amount)
    total_bonus.is_a?(Money) ? total_bonus : Money.zero
  end

  # Get this worker's deductions from statement line items
  def deductions
    # Find statement line items for this identity related to this pro_pay that are deductions (negative amounts)
    statement_line_items = StatementLineItem.where(
      identity: identity,
      pro_pay: sub_pro_pay.pro_pay
    ).where("amount_cents < 0")

    total_deductions = statement_line_items.sum(&:amount)

    total_deductions.is_a?(Money) ? total_deductions : Money.zero
  end
end
