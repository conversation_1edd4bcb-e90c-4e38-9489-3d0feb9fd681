# frozen_string_literal: true

class PayrollSchedule < ApplicationRecord
  belongs_to :organization

  has_many :bonus_branches, class_name: "Branch", foreign_key: :bonus_schedule_id, inverse_of: :bonus_payroll_schedule
  has_many :payroll_branches, class_name: "Branch", foreign_key: :payroll_schedule_id, inverse_of: :pay_payroll_schedule

  has_many :pay_periods

  enum :period, {
    weekly: "weekly",
    biweekly: "biweekly",
    semimonthly: "semimonthly",
    monthly: "monthly",
    bimonthly: "bimonthly"
  }

  def end_date_from_start_date(start_date)
    case period
    when "weekly"
      start_date + 6
    when "biweekly"
      start_date + 13
    when "semimonthly"
      # FIXME: this schedule doesn't have a strict definition
      # and should be configurable
      if start_date.mday == 1
        start_date + 14
      elsif start_date.mday == 15
        start_date.end_of_month
      end
    when "monthly"
      start_date.end_of_month
    when "bimonthly"
      start_date + 2.months
    else
      raise "unsupported period"
    end
  end

  def start_date_from_date(date)
    case period
    when "weekly", "biweekly"
      prior_week_day_date(date)
    when "semimonthly"
      if date.mday < 15
        date.beginning_of_month
      else
        date.beginning_of_month.next_day(14)
      end
    when "monthly", "bimonthly"
      date.beginning_of_month
    else
      raise "unsupported period"
    end
  end

  WEEK_DAYS = %i[sunday monday tuesday wednesday thursday friday saturday].freeze

  def prior_week_day_date(date = Date.today)
    date.prev_occurring(WEEK_DAYS[self.week_day])
  end
end
