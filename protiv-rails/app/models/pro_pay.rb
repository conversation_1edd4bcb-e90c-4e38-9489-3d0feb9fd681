# frozen_string_literal: true

class ProPay < ApplicationRecord
  include AASM # Acts As State Machine

  belongs_to :organization
  belongs_to :reference_bonus_pool, optional: true, class_name: "BonusPool"
  belongs_to :source_route, optional: true, class_name: "Route"
  belongs_to :source_route_pay_period, optional: true, class_name: "PayPeriod"
  belongs_to :source_job, optional: true, class_name: "Job"

  belongs_to :manager, optional: true, class_name: "Identity"

  has_many :sub_pro_pays
  has_many :bonus_line_items, through: :partial_pro_pays
  has_many :sub_pro_pay_line_items, through: :sub_pro_pays
  has_many :pro_pay_payables

  # Needed for Hopping Relationships, see https://www.graphiti.dev/cookbooks/hopping-relationships
  attr_accessor :crew_bonuses

  attr_accessor :milestones
  attr_accessor :jobs

  enum :source_type, {
    route_recurring_jobs: "route_recurring_jobs",
    route_non_recurring_jobs: "route_non_recurring_jobs",
    job_with_phases: "job_with_phases",
    manual: "manual"
  }, prefix: true, _type: :string

  enum :payout_type, {
    milestone: "milestone",
    job: "job",
    phase: "phase",
    route: "route"
  }, prefix: true, _type: :string

  enum :status, {
    active: "active",
    completed: "completed",
    approved: "approved",
    paid: "paid",
    deleted: "deleted"
  }, prefix: true, _type: :string

  scope :deleted, -> { where(status: "deleted") }
  scope :active, -> { where.not(status: "deleted") }

  attribute :reset_bonus_pool, :boolean, default: false

  # AASM state machine
  aasm column: :status, enum: true, whiny_persistence: true do # Added whiny_persistence for easier debugging if transitions fail
    state :active, initial: true
    state :completed
    state :approved
    state :paid
    state :deleted # Existing status, ensure it's part of AASM if managed by it

    event :mark_approved do
      transitions from: :active, to: :approved, after: :after_approve_tasks
      # transitions from: :completed, to: :approved, after: :after_approve_tasks # Potentially allow approval from completed
    end

    # TODO: Define other events and transitions as needed (e.g., complete, pay, delete)
  end

  validates :crew_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :manager_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :crew_lead_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :company_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :other_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :crew_retention_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validate :totals_100_percent

  after_create :initialize_bonus_pool_values_and_payables
  before_update :reset_bonus_pool_values_and_payables
  before_create :set_pro_pay_code

  def approve!
    self.mark_approved
    changed? ? save! : true
  end

  def approve
    approve!
  rescue
    false
  end

  def crew_percent
    crew_percent_hundredths.to_f / 100_00
  end

  def crew_retention_percent
    crew_retention_percent_hundredths.to_f / 100_00
  end

  # Just calculate the average for now, implementation when working with phases
  def percent_complete
    return 100.0 if status == "completed" || status == "approved"

    total_percent = 0.0
    count = 0

    sub_pro_pays.flat_map(&:sub_pro_pay_pro_payables).each do |sppp|
      total_percent += sppp.pro_payable.percent_complete
      count += 1
    end

    return 0.0 if count.zero?

    (total_percent / count) / 100.0
  end

  def progress
    percent_used = sub_pro_pays.first.calculated_percent_complete || 0.0
    difference = percent_complete / 100.0 - percent_used

    {
      percentage: percent_complete,
      seconds_difference: (difference * seconds_budget).round,
      amount_difference: difference * total_budget
    }
  end

  def payable_percent(rate_class:)
    case rate_class
    when "manager"
      manager_percent_hundredths.to_f / 100_00
    when "crew_lead"
      crew_lead_percent_hundredths.to_f / 100_00
    when "company"
      company_percent_hundredths.to_f / 100_00
    when "other"
      other_percent_hundredths.to_f / 100_00
    end
  end

  def total_budget
    sub_pro_pays.sum(Money.zero) { |spp| spp.full_budget_amount || Money.zero }
  end

  def total_costs
    sub_pro_pays.sum(Money.zero) { |spp| spp.total_labor_cost || Money.zero }
  end

  def seconds_budget
    sub_pro_pays.sum(0) { |spp| spp.budget_minutes || 0 } * 60
  end

  def seconds_cost
    sub_pro_pays.sum(0) { |spp| spp.total_duration_seconds || 0 }
  end

  def total_bonus
    current_sum = Money.new(0, Money.default_currency)
    sub_pro_pays.each do |spp|
      # TODO: Get percent_complete from sub_pro_pay_pro_payable
      individual_bonus = spp.bonus(percent_complete: 1) # This can be nil or a Money object (positive/negative)
      if individual_bonus.is_a?(Money)
        current_sum += individual_bonus
      end
    end
    current_sum
  end

  def available_bonus
    current_sum = Money.new(0, Money.default_currency)
    sub_pro_pays.each do |spp|
      # TODO: Get percent_complete from sub_pro_pay_pro_payable
      individual_bonus = spp.available_bonus(percent_complete: 1) # This can be nil or a Money object (positive/negative)
      if individual_bonus.is_a?(Money)
        current_sum += individual_bonus
      end
    end
    current_sum
  end

  def approved_bonus
    current_sum = Money.new(0, Money.default_currency)
    sub_pro_pays.each do |spp|
      spp.bonus_line_items.each do |bli|
        current_sum += bli.bonus if bli.bonus.is_a?(Money)
      end
    end
    current_sum
  end

  def budget
    return unless total_budget.positive?

    ratio = total_budget.zero? ? 0.0 : total_costs.to_f / total_budget.to_f
    percentage = (ratio * 100).round(1)

    status = if ratio > 1
      "over"
    else
      "under"
    end

    {
      status: status,
      percentage: percentage
    }
  end

  def first_check_in
    min_check_in = nil
    sub_pro_pays.flat_map(&:sub_pro_pay_pro_payables).each do |sppp|
      payable = sppp.pro_payable

      if payable.is_a?(Milestone)
        milestone_check_in = payable.first_check_in
        if milestone_check_in
          min_check_in = milestone_check_in if min_check_in.nil? || milestone_check_in < min_check_in
        end
      elsif payable.is_a?(Job)
        payable.milestones.each do |milestone|
          milestone_check_in = milestone.first_check_in
          if milestone_check_in
            min_check_in = milestone_check_in if min_check_in.nil? || milestone_check_in < min_check_in
          end
        end
      end
    end
    min_check_in
  end

  def last_check_out
    max_check_out = nil
    sub_pro_pays.flat_map(&:sub_pro_pay_pro_payables).each do |sppp|
      payable = sppp.pro_payable

      if payable.is_a?(Milestone)
        milestone_check_out = payable.last_check_out
        if milestone_check_out
          max_check_out = milestone_check_out if max_check_out.nil? || milestone_check_out > max_check_out
        end
      elsif payable.is_a?(Job)
        payable.milestones.each do |milestone|
          milestone_check_out = milestone.last_check_out
          if milestone_check_out
            max_check_out = milestone_check_out if max_check_out.nil? || milestone_check_out > max_check_out
          end
        end
      end
    end
    max_check_out
  end

  def partial_bonus_paid
    # TODO: Implement this
    false
  end

  def distribution_type
    sub_pro_pays.first.distribution_type
  end

  def budget_type
    sub_pro_pays.first.budget_type
  end

  def scheduled_date
    # TODO: Implement this
    nil
  end

  def worker_count
    sub_pro_pay_line_items.map(&:identity).uniq.count
  end

  private

  def totals_100_percent
    total_percent = crew_percent_hundredths + company_percent_hundredths + manager_percent_hundredths +
      crew_lead_percent_hundredths + other_percent_hundredths

    if total_percent != 100_00
      errors.add(:base, "percentages must total to 100%")
    end
  end

  def exclude_payable_others_from_bonus
    pro_pay_payable_others.each { |po| po.update(participating: false) }
  end

  def initialize_bonus_pool_payables(bonus_pool)
    # bonus_pool.bonus_pool_payables should all be of rate_class Other
    bonus_pool.bonus_pool_payables.each do |bonus_payable|
      ppp = ProPayPayable.find_by(pro_pay: self,
                                  payable: bonus_payable.payable,
                                  rate_class: bonus_payable.rate_class)
      if ppp
        ppp.update!(participating: true) unless ppp.participating
      else
        ppp = ProPayPayable.create!(pro_pay: self,
                                    payable: bonus_payable.payable,
                                    rate_class: bonus_payable.rate_class)
        pro_pay_payables << ppp
      end
    end
  end

  def update_pool_values(bonus_pool)
    return unless bonus_pool

    self.company_percent_hundredths = bonus_pool.company_percent_hundredths
    self.crew_lead_percent_hundredths = bonus_pool.crew_lead_percent_hundredths
    self.manager_percent_hundredths = bonus_pool.manager_percent_hundredths
    self.other_percent_hundredths = bonus_pool.other_percent_hundredths
    self.crew_percent_hundredths = bonus_pool.crew_percent_hundredths
    self.crew_retention_percent_hundredths = bonus_pool.crew_retention_percent_hundredths
    # Note: save! is not called here to avoid infinite loop with before_update callback
    # The caller is responsible for saving the record
  end

  def initialize_bonus_pool_values_and_payables
    if reference_bonus_pool.present?
      initialize_bonus_pool_payables(reference_bonus_pool)
      update_pool_values(reference_bonus_pool)
    end
  end

  def reset_bonus_pool_values_and_payables
    if reference_bonus_pool_changed? || reset_bonus_pool
      exclude_payable_others_from_bonus
      initialize_bonus_pool_payables(reference_bonus_pool)

      # Updating pool values from reference before yielding to allow payload to further override the values
      update_pool_values(reference_bonus_pool)
    end
  end

  def pro_pay_payable_others
    pro_pay_payables.select { |ppp| ppp.other? }
  end

  def set_pro_pay_code
    return unless organization_id # Ensure organization_id is present
    return if code.present? # Allow manual setting or if already set

    # Lock the table or use a more sophisticated locking mechanism if high concurrency is expected
    # For most cases, this approach should be sufficient.
    last_pro_pay = ProPay.where(organization_id: organization_id).order(code: :desc).first
    self.code = last_pro_pay ? last_pro_pay.code.to_i + 1 : 1
  end

  def after_approve_tasks
    ActiveRecord::Base.transaction do
      sub_pro_pays.each do |spp|
        # Find or create an open partial_sub_pro_pay
        partial_spp = spp.find_or_create_open_partial_sub_pro_pay

        # Ensure partial_spp is persisted if new, before updating
        partial_spp.save! if partial_spp.new_record?

        # Update estimated_percent_complete.
        # Convert float (0.0-1.0) from calculated_percent_complete to integer (0-100)
        # percent_complete_integer = (spp.calculated_percent_complete * 100).round
        partial_spp.update!(estimated_percent_complete: 100)

        # Materialize the partial_sub_pro_pay to create/update BonusLineItems
        # Pass finalized: true as per the requirement to make it ready for statement generation
        materialization_result = partial_spp.materialize!

        unless materialization_result
          # If materialization fails, log the error and consider how to handle it.
          # For now, we'll add an error to the ProPay object and halt further processing for this sub_pro_pay.
          # This will cause the transaction to rollback if save! is called on pro_pay later with this error.
          errors.add(:base, "Failed to materialize bonuses for SubProPay ##{spp.id}: #{materialization_result.error_message}")
          # Rails.logger.error "Failed to materialize bonuses for SubProPay ##{spp.id}: #{materialization_result.error_message}"
          raise ActiveRecord::Rollback # Rollback for this spp, or decide if the whole approval should fail
          # next # or continue to next sub_pro_pay, depending on desired behavior
        end

        # Refresh bonus_line_items association as they were just created/updated
        partial_spp.bonus_line_items.reload

        # Create StatementLineItems from the materialized BonusLineItems
        partial_spp.bonus_line_items.each do |bli|
          next unless bli.payable.is_a?(Identity)

          StatementLineItem.create!(
            organization: organization,
            identity: bli.payable, # Assuming bli.payable is an Identity record
            amount: bli.bonus,     # This is a Money object
            pro_pay: self,         # Link to the current ProPay
            status: :in_process,   # Default status for new line items
          )
        end
      end
    end
  rescue StandardError => e
    # Log the error and add it to the ProPay object to prevent the state transition if not already handled by whiny_persistence
    Rails.logger.error "Error during after_approve_tasks for ProPay ##{id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    errors.add(:base, "An error occurred during the approval process: #{e.message}")
    raise ActiveRecord::Rollback # Ensure transaction is rolled back
  end
end
