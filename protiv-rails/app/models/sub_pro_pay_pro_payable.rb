# frozen_string_literal: true

class SubProPayProPayable < ApplicationRecord
  belongs_to :sub_pro_pay
  belongs_to :pro_payable, polymorphic: true

  belongs_to :job, -> { joins(:sub_pro_pay_pro_payable).where(sub_pro_pay_pro_payable: { pro_payable_type: "Job" }) },
             foreign_key: "pro_payable_id", optional: true
  belongs_to :milestone, -> { joins(:sub_pro_pay_pro_payable).where(sub_pro_pay_pro_payables: { pro_payable_type: "Milestone" }) },
             foreign_key: "pro_payable_id", optional: true

  validate :job_is_not_already_in_a_pro_pay, on: :create

  private

  def job_is_not_already_in_a_pro_pay
    case pro_payable
    when Job
      if SubProPayProPayable.where(pro_payable: self.pro_payable).any?
        errors.add(:pro_payable, "already in a pro_pay")
      end
      if SubProPayProPayable.where(pro_payable: self.pro_payable.milestones).any?
        errors.add(:pro_payable, "already in a pro_pay through milestones")
      end
    when Milestone
      if SubProPayProPayable.where(pro_payable: self.pro_payable).any? ||
        SubProPayProPayable.where(pro_payable: self.pro_payable.job).any?
        errors.add(:pro_payable, "already in a pro_pay")
      end
    end
  end
end
