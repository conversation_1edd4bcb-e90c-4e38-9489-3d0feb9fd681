# frozen_string_literal: true

class SyncStatusRange < ApplicationRecord
  scope :initially_synced, -> { where.not(last_synced_at: nil) }
  belongs_to :integration

  validate :ranges_are_valid
  validate :timestamps_are_utc_day_aligned

  attr_accessor :skip

  def date_range
    return unless start_timestamp && end_timestamp

    (start_timestamp..end_timestamp)
  end

  def remote_id_range
    return unless start_remote_id && end_remote_id

    (start_remote_id..end_remote_id)
  end

  def initially_synced?
    last_synced_at.present?
  end

  private

  def ranges_are_valid
    has_timestamps = start_timestamp || end_timestamp
    has_remote_ids = start_remote_id || end_remote_id

    if has_timestamps && has_remote_ids
      errors.add(:base, "cannot have both timestamp and remote id ranges")
    end

    if has_timestamps
      errors.add(:start_timestamp, "must be present") unless start_timestamp
      errors.add(:end_timestamp, "must be present") unless end_timestamp

      if start_timestamp > end_timestamp
        errors.add(:base, "start_timestamp cannot exceed end_timestamp")
      end
    elsif has_remote_ids
      errors.add(:start_remote_id, "must be present") unless start_remote_id
      errors.add(:end_remote_id, "must be present") unless end_remote_id

      if start_remote_id > end_remote_id
        errors.add(:base, "start_remote_id cannot exceed end_remote_id")
      end
    else
      errors.add(:base, "range data missing")
    end
  end

  def timestamps_are_utc_day_aligned
    [:start_timestamp, :end_timestamp].each do |attr|
      next unless ts = read_attribute(attr)

      unless ts == ts.utc.beginning_of_day
        errors.add(attr, "#{ts} is not UTC day-aligned")
      end
    end
  end
end
