# frozen_string_literal: true

class SubProPay < ApplicationRecord
  belongs_to :pro_pay

  has_many :sub_pro_pay_pro_payables
  has_many :pro_payables, through: :sub_pro_pay_pro_payables, source_type: "ProPayable"
  has_many :milestones, through: :sub_pro_pay_pro_payables

  has_one :sub_pro_pay_calculated_totals
  has_many :sub_pro_pay_finalized_bonus_totals
  has_many :sub_pro_pay_line_items
  has_many :sub_pro_pay_line_item_details
  has_many :partial_sub_pro_pays
  has_one :open_partial_sub_pro_pay, -> { where(status: ["draft", "materialized"]) }, class_name: "PartialSubProPay"

  has_many :bonus_line_items, through: :partial_sub_pro_pays

  belongs_to :reference_bonus_pool, optional: true, class_name: "BonusPool"
  has_many :pro_pay_payables, through: :pro_pay

  attribute :reset_bonus_pool, :boolean, default: false

  enum :status, {
    draft: "draft",
    finalized: "finalized"
  }

  enum :budget_type, {
    hours: "hours",
    amount: "amount",
    rate: "rate"
  }

  enum :distribution_type, {
    equal_rate: "equal_rate",
    equal_weighted: "equal_weighted",
    crew_lead_weighted: "crew_lead_weighted",
    manual_distribution: "manual_distribution"
  }

  monetize :budget_cents, allow_nil: true
  monetize :budget_rate_cents, allow_nil: true

  # validates_with BudgetTypeValidator
  validates :budget_minutes, numericality: { only_integer: true, allow_nil: true }
  validates :quantity_budgeted, numericality: { allow_nil: true }
  validates :crew_lead_bonus_rate, numericality: { allow_nil: true }

  validates_presence_of :budget_minutes, if: -> { self.budget_type == "hours" }

  validates_presence_of :budget, if: -> { self.budget_type == "amount" }

  validates_presence_of :budget_rate, if: -> { self.budget_type == "rate" }
  validates_presence_of :quantity_budgeted, if: -> { self.budget_type == "rate" }

  after_create :initialize_bonus_pool_values_and_payables
  before_update :reset_bonus_pool_values_and_payables

  def find_or_create_open_partial_sub_pro_pay
    partial_sub_pro_pays.find_by(status: ["draft", "materialized"]) ||
      PartialSubProPay.new(sub_pro_pay: self, status: "draft")
  end

  def calculated_percent_complete
    return 0.0 if self.budget_type.blank?

    case self.budget_type
    when "hours"
      (total_duration_seconds / 60.0) / self.budget_minutes
    when "amount"
      (total_hours_worked * Money.from_cents(average_wage_hourly_cents)) / self.budget
    when "rate"
      self.quantity_complete / self.quantity_budgeted
    end.round(4)
  end

  # The total to date cost of labor
  def total_labor_cost
    self.sub_pro_pay_calculated_totals&.total_labor_cost || Money.from_amount(0.0)
  end

  def total_crew_lead_labor_cost
    self.sub_pro_pay_calculated_totals&.total_crew_lead_labor_cost || Money.from_amount(0.0)
  end

  # The total to date of seconds worked
  def total_duration_seconds
    self.sub_pro_pay_calculated_totals&.total_duration_seconds || 0
  end

  def total_crew_lead_duration_seconds
    self.sub_pro_pay_calculated_totals&.total_crew_lead_duration_seconds || 0
  end

  # The total to date of hours worked
  def total_hours_worked
    total_duration_seconds / 3600.0
  end

  # The average to date of hours worked
  def average_wage_hourly_cents
    return 0.0 if total_hours_worked.zero?
    total_labor_cost.cents / total_hours_worked
  end

  # The calculated budget for the ProPay when 100% complete, based on the budget type
  def full_budget_amount
    case self.budget_type
    when "hours"
      # The budget is based on set number of hours worked times the average hourly wage. By
      # nature that means the budget will fluctuate based on the proportion of hours worked
      # at which rates
      Money.from_cents(average_wage_hourly_cents * (budget_minutes / 60))
    when "amount"
      # The budget is a fixed amount of currency
      self.budget
    when "rate"
      # The budget is based on the quantity completed at the set rate
      self.budget_rate * quantity_budgeted
    end
  end

  # The budget scaled by the percent complete. This allows the calculation of intermediate bonuses
  def relative_budget_amount(percent_complete: calculated_percent_complete)
    full_budget_amount * percent_complete
  end

  def can_calculate_bonus?
    self.budget_type.present?
  end

  def bonus(percent_complete: calculated_percent_complete)
    return nil unless can_calculate_bonus?

    relative_budget_amount(percent_complete: percent_complete) - total_labor_cost
  end

  def available_bonus(percent_complete: calculated_percent_complete)
    return Money.from_amount(0.0) unless can_calculate_bonus?

    # Calculate base bonus: (Percent Complete - Percent of Budget Used) × Total Job Budget
    base_bonus = bonus(percent_complete: percent_complete)

    # Only consider positive values
    positive_bonus = [base_bonus - approved_bonus, Money.from_amount(0.0)].max

    # Apply bonus pool split (exclude company pool)
    crew_share_percentage = (100_00 - (company_percent_hundredths || 0)) / 100_00.0
    positive_bonus * crew_share_percentage
  end

  def approved_bonus
    # Sum up all finalized bonus totals and bonus line items from partial payments
    # Reload associations to ensure fresh data, especially important during approval process
    finalized_total = sub_pro_pay_finalized_bonus_totals.reload.sum(:total_bonus_cents) || 0
    partial_total = bonus_line_items.reload.sum(:bonus_cents) || 0

    Money.from_cents(finalized_total + partial_total)
  end

  def manager
    pro_pay.manager
  end

  def organization
    pro_pay.organization
  end

  private

  def totals_100_percent
    total_percent = crew_percent_hundredths + company_percent_hundredths + manager_percent_hundredths +
      crew_lead_percent_hundredths + other_percent_hundredths

    if total_percent != 100_00
      errors.add(:base, "percentages must total to 100%")
    end
  end

  def exclude_payable_others_from_bonus
    sub_pro_pay_pro_payable_others.each { |po| po.update(participating: false) }
  end

  def initialize_bonus_pool_payables(bonus_pool)
    # bonus_pool.bonus_pool_payables should all be of rate_class Other
    bonus_pool.bonus_pool_payables.each do |bonus_payable|
      ppp = ProPayPayable.find_by(sub_pro_pay: self,
                                  payable: bonus_payable.payable,
                                  rate_class: bonus_payable.rate_class)
      if ppp
        ppp.update!(participating: true) unless ppp.participating
      else
        ppp = ProPayPayable.create!(sub_pro_pay: self,
                                    payable: bonus_payable.payable,
                                    rate_class: bonus_payable.rate_class)
        sub_pro_pay_pro_payables << ppp
      end
    end
  end

  def update_pool_values(bonus_pool)
    return unless bonus_pool

    self.company_percent_hundredths = bonus_pool.company_percent_hundredths
    self.crew_lead_percent_hundredths = bonus_pool.crew_lead_percent_hundredths
    self.manager_percent_hundredths = bonus_pool.manager_percent_hundredths
    self.other_percent_hundredths = bonus_pool.other_percent_hundredths
    self.crew_percent_hundredths = bonus_pool.crew_percent_hundredths
    self.crew_retention_percent_hundredths = bonus_pool.crew_retention_percent_hundredths
  end

  def initialize_bonus_pool_values_and_payables
    if reference_bonus_pool.present?
      initialize_bonus_pool_payables(reference_bonus_pool)
      update_pool_values(reference_bonus_pool)
    end
  end

  def reset_bonus_pool_values_and_payables
    if reference_bonus_pool_changed? || reset_bonus_pool
      exclude_payable_others_from_bonus
      initialize_bonus_pool_payables(reference_bonus_pool)

      # Updating pool values from reference before yielding to allow payload to further override the values
      update_pool_values(reference_bonus_pool)
    end
  end

  def sub_pro_pay_pro_payable_others
    sub_pro_pay_pro_payables.select { |ppp| ppp.other? }
  end
end
