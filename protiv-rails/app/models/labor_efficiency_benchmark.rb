# frozen_string_literal: true

class LaborEfficiencyBenchmark < ApplicationRecord
  LEVELS = %w[organization branch route job].freeze

  belongs_to :organization

  # --- Validations ---

  validates :organization, presence: true

  validates :level,
            presence: true,
            inclusion: { in: LEVELS }

  validates :target_percentage,
            presence: true,
            numericality: {
              greater_than_or_equal_to: 0,
              less_than_or_equal_to: 100
            }

  # level_record_id is required for all levels except 'organization'
  validates :level_record_id,
            presence: true,
            unless: :organization_level?

  # Only one active benchmark per unique scope
  validate :only_one_active_benchmark_per_scope

  # --- Helper Methods ---

  def organization_level?
    level == "organization"
  end

  private

  # Custom validation to ensure only one active benchmark per scope
  def only_one_active_benchmark_per_scope
    return unless active? # Only validate if this benchmark is active

    existing_active = LaborEfficiencyBenchmark.where(
      organization_id: organization_id,
      level: level,
      level_record_id: level_record_id,
      active: true
    ).where.not(id: id) # Exclude current record for updates

    if existing_active.exists?
      errors.add(:active, "already has an active benchmark for this combination")
    end
  end
end
