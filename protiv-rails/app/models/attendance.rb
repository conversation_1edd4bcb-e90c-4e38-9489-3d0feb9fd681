# frozen_string_literal: true

class Attendance < ApplicationRecord
  # This is a LOCAL RECORD ONLY and should always be synced back
  # to a remote integration, which will be the source of truth for time tracking.
  # I.e., this data should not be used directly in propays or other calculations.

  # t.timestamps
  # t.references :milestone, null: false
  # t.references :identity, null: false
  # t.datetime :started_at, null: false
  # t.datetime :ended_at
  # t.references :created_by_user, null: false, foreign_key: { to_table: :users }
  # t.integer :break_time_seconds

  around_save :create_sync_datum

  belongs_to :identity
  belongs_to :milestone, optional: true
  belongs_to :organization
  belongs_to :created_by_user, class_name: "User"
  belongs_to :manager, class_name: "Identity", optional: true
  belongs_to :route, optional: true

  scope :open, -> { where(ended_at: nil) }
  scope :closed, -> { where.not(ended_at: nil) }

  validate :no_overlap_on_same_milestone
  validate :ended_at_exceeds_started_at
  validate :break_time_within_range
  validate :organization_consistency
  validate :has_a_route_or_manager
  validate :no_changes_after_close
  validate :can_clock_in

  def open?
    ended_at.blank?
  end

  def seconds
    if ended_at.present?
      ended_at - started_at
    end
  end

  def break_time_hours
    break_time_seconds / 3600.0
  end

  # Aspire requires some numerical value when creating clock times,
  # but doesn't _actually_ enforce their presence in their own database.
  # In order to make a POST request to their endpoint, default to Null Island
  # unless we already have both components of the geolocation
  def started_geolocation
    if (latitude = started_latitude) && (longitude = started_longitude)
      { latitude:, longitude: }
    else
      _default_location
    end
  end

  # See above
  def ended_geolocation
    if (latitude = ended_latitude) && (longitude = ended_longitude)
      { latitude:, longitude: }
    else
      _default_location
    end
  end

  private

  def _default_location
    { latitude: 0, longitude: 0 }
  end

  def has_a_route_or_manager
    # NOTE: this validation is rather specific to aspire
    if route.blank? && manager.blank?
      errors.add(:base, "either a route or a manager is required")
    end
  end

  def organization_consistency
    if milestone && milestone.organization != organization
      errors.add(:base, "milestone belongs to a different organization")
    end

    if manager && manager.organization != organization
      errors.add(:base, "manager belongs to a different organization")
    end

    if route && route.organization != organization
      errors.add(:base, "route belongs to a different organization")
    end
  end

  def create_sync_datum
    transaction do
      yield

      if saved_change_to_ended_at? && ended_at_previously_was.nil?
        organization.integrations.each do |integration|
          integration.maybe_create_sync_datum(self)
        end
      end
    end
  end

  def infix_date_node(datetime)
    Arel::Nodes::InfixOperation.new(
      "::",
      Arel::Nodes.build_quoted(datetime),
      Arel.sql("TIMESTAMP")
    )
  end

  def no_overlap_on_same_milestone
    base = Attendance.where(identity:, milestone:).where.not(id: id)
    arel = self.class.arel_table

    _started_at = arel[:started_at]
    _ended_at = arel[:ended_at]

    scope =
      if ended_at.present?
        base.merge(
          Attendance.open.or(
            Attendance.where(
              Arel::Nodes::InfixOperation.new(
                # NOTE: OVERLAPS allows the end of one range to be equal to the start of the next,
                # which is what we want.
                "OVERLAPS",
                Arel::Nodes::Grouping.new([_started_at, _ended_at]),
                Arel::Nodes::Grouping.new(
                  [infix_date_node(started_at), infix_date_node(ended_at)]
                )
              )
            )
          )
        )
      else
        q_started_at = Arel::Nodes.build_quoted(started_at)
        base.merge(
          Attendance.open.or(
            Attendance.where(
              _started_at.lteq(q_started_at).and(
                _ended_at.gt(q_started_at)
              )
            )
          )
        )
      end

    if scope.any?
      errors.add(:base, "attendance overlaps with existing record")
    end
  end

  def ended_at_exceeds_started_at
    if ended_at.present? && ended_at <= started_at
      errors.add(:ended_at, "ended_at must exceed started_at")
    end
  end

  def break_time_within_range
    if seconds&.<(break_time_seconds)
      errors.add(:break_time_seconds, "break_time_seconds must fall within time range")
    end
  end

  # NOTE: we can't edit clock times on aspire after posting them.
  def no_changes_after_close
    if ended_at_was.present?
      if changes.any?
        errors.add(:base, "Attendances cannot be altered after they are closed")
      end
    end
  end

  def can_clock_in
    unless created_by_user.can_manage_organization?(organization)
      errors.add(:base, "User cannot clock in identities for this organization")
    end

    if identity.attendances.open.where(milestone:).where.not(id:).any?
      errors.add(:base, "Identity is already clocked in with milestone_id=#{milestone_id}")
    end
  end
end
