# frozen_string_literal: true

module Trackable
  extend ActiveSupport::Concern

  included do
    class_attribute :require_tracking
  end

  class_methods do
    def trackable(**options)
      self.require_tracking = options.fetch(:require_tracking, false)

      around_save :track_sync_changes

      has_many :changesets, as: :record
      has_many :integration_records, as: :record
      has_many :integrations, through: :integration_records

      attr_accessor :remote_slug
      attr_accessor :remote_updated_at
    end
  end

  def track_sync_changes
    if require_tracking? && !Sync::Current.tracking?
      raise "required tracking id not provided"
    end

    if Sync::Current.tracking? && changes.any? # don't track touches
      transaction do
        changeset = changesets.build(
          sync_uuid: Sync::Current.uuid,
          integration_id: Sync::Current.integration_id,
          remote_slug: remote_slug || changesets.where(integration_id: Sync::Current.integration_id).last&.remote_slug,
          remote_updated_at: remote_updated_at,
          data: changes
        )

        yield

        # Include both integration_id and remote_slug in the query to avoid race conditions
        # This ensures we're properly using the unique index on (integration_id, remote_slug)
        begin
          if remote_slug.present?
            integration_records.find_or_create_by!(
              integration_id: Sync::Current.integration_id,
              remote_slug: remote_slug
            )
          else
            # If no remote_slug is provided, just find by integration_id
            integration_records.where(
              integration_id: Sync::Current.integration_id,
            ).first_or_create!
          end
        rescue ActiveRecord::RecordNotUnique => e
          # If we hit a race condition, just find the existing record
          Rails.logger.warn("Duplicate integration record detected: #{e.message}")
          integration_records.find_by(
            integration_id: Sync::Current.integration_id,
            remote_slug: remote_slug
          )
        end

        changeset.save!
      end
    else
      yield
    end
  end
end
