# frozen_string_literal: true

# Stores calculated labor metrics for a specific worker on a specific milestone for a specific day
#
# This model is used to track Time & Materials (T&M) labor metrics including billable time,
# efficiency, and labor costs. The data is calculated based on ClockTime and MilestoneTime
# records by the CalculateLaborMetricsJob.
class LaborMetric < ApplicationRecord
  belongs_to :organization
  belongs_to :identity
  belongs_to :milestone

  validates :date, presence: true

  validates :total_clocked_seconds,
            :total_billable_seconds,
            :non_billable_seconds,
            :non_billable_labor_cost_cents,
            numericality: { greater_than_or_equal_to: 0 }

  validates :efficiency_percentage,
            numericality: {
              greater_than_or_equal_to: 0,
              less_than_or_equal_to: 100
            }

  # Money gem integration for labor cost
  monetize :labor_cost_cents, :non_billable_labor_cost_cents

  # Ensure uniqueness of organization-worker-milestone-date combination
  validates :identity_id,
            uniqueness: {
              scope: %i[organization_id milestone_id date]
            }

  # Expanded scopes with organization
  scope :for_organization, ->(organization_id) { where(organization_id: organization_id) }
  scope :for_date, ->(date) { where(date: date) }
  scope :for_milestone, ->(milestone_id) { where(milestone_id: milestone_id) }
  scope :for_worker, ->(identity_id) { where(identity_id: identity_id) }

  private
end
