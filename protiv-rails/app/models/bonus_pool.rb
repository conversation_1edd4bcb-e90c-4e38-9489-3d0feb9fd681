# frozen_string_literal: true

class BonusPool < ApplicationRecord
  belongs_to :organization
  has_many :sub_pro_pays
  has_many :bonus_pool_payables, inverse_of: :bonus_pool

  validates :name, presence: true

  validates :crew_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :manager_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :crew_lead_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :company_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :other_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :crew_retention_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validate :totals_100_percent

  private

  def totals_100_percent
    total_percent = crew_percent_hundredths + company_percent_hundredths + manager_percent_hundredths +
      crew_lead_percent_hundredths + other_percent_hundredths

    if total_percent != 100_00
      errors.add(:base, "percentages must total to 100%")
    end
  end
end
