# frozen_string_literal: true

class PercentComplete
  # Duck-type reader for the manual percent complete milestones.
  # It's assumed you've already checked for percent_complete_hundredths
  def initialize(percent_complete_hundredths)
    @percent_complete_hundredths = percent_complete_hundredths
  end

  attr_reader :percent_complete_hundredths

  def percent_complete
    percent_complete_hundredths / 100.0
  end
end
