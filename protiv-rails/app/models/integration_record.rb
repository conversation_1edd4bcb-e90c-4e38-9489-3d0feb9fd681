# frozen_string_literal: true

class IntegrationRecord < ApplicationRecord
  belongs_to :integration
  belongs_to :record, polymorphic: true

  def remote_id
    remote_slug.split(":", 3).last
  end

  # Example:
  # IntegrationRecord.where(created_at: ..Date.yesterday).join_records(Job.first.milestones)
  def self.join_records(record_relation)
    record_arel = record_relation.arel_table
    my_arel = arel_table

    joins(
      my_arel.join(record_arel).on(
        my_arel["record_id"].eq(record_arel["id"])
      ).join_sources
    ).merge(record_relation).where(
      my_arel["record_type"].eq(record_relation.model.name)
    )
  end
end
