# frozen_string_literal: true

class MilestoneTime < ApplicationRecord
  belongs_to :milestone
  belongs_to :identity
  belongs_to :route, optional: true
  has_one :job, through: :milestone

  trackable

  monetize :base_hourly_rate_cents, nil: true
  monetize :labor_cost_cents

  before_validation :update_computed_attributes

  validates_numericality_of :duration_seconds

  around_save :update_job_activity

  private

  def update_computed_attributes
    # FIXME: When do we allow these to be fixed so future updates to the underlying date do not change the
    # computed values?
    self.duration_seconds =
      if self.end_time && self.start_time
        self.end_time - self.start_time
      else
        0
      end

    self.labor_cost = self.base_hourly_rate * self.duration_seconds / 3600
  end

  def update_job_activity
    transaction do
      if start_time
        # NOTE: this is used in an `update_all` query; The merges
        # are necessary to ensure condition precedence.
        base = Job.joins(:milestones).merge(Milestone.where(id: milestone_id))

        base.merge(
          Job.where(last_activity_at: ..start_time)
        ).update_all(last_activity_at: start_time)

        # don't transition back from completed -> in_progress
        base.merge(
          Job.where(status: :pending)
        ).update_all(status: :in_progress)
      end

      yield
    end
  end
end
