# frozen_string_literal: true

class ItemAllocation < ApplicationRecord
  belongs_to :milestone
  belongs_to :organization
  belongs_to :catalog_item

  trackable

  monetize :unit_cost_cents, with_model_currency: :currency_code
  monetize :total_cost_cents, with_model_currency: :currency_code

  delegate :name, :item_type, :material?, :labor?, to: :catalog_item

  after_commit :update_milestone_caches

  private

  def update_milestone_caches
    Cache::UpdateMilestoneCachesJob.perform_async(milestone_id)
  end
end
