# frozen_string_literal: true

class Account
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  attribute :email, :string
  attribute :name, :string
  attribute :password, :string
  attribute :invitation_token, :string
  attribute :company, :string
  attribute :phone, :string
  attribute :phone_country_code, :string
  attr_accessor :user, :organization

  validate :validate_user

  def attributes
    { email: nil, name: nil, password: nil, invitation_token: nil, company: nil, phone: nil, phone_country_code: nil }
  end

  def self.current
    new.tap do |account|
      account.user = Current.user
      account.organization = Current.organization
    end
  end

  def email
    super || user&.email
  end

  def name
    super || user&.name
  end

  def build_and_validate_user
    self.user = User.build(
      email: email,
      password: password,
      password_confirmation: password,
      name: name,
      phone: phone,
      phone_country_code: phone_country_code
    )
    validate
  end

  def create_user!
    ApplicationRecord.transaction do
      user.save!

      if invitation_token.present?
        Rails.logger.info "Looking for invitation with token: #{invitation_token}"
        invitation = Invitation.find_by_token_for(:account_create, invitation_token)

        if invitation
          Rails.logger.info "Found invitation for organization: #{invitation.organization.name}"
          # Instead of deferring to after_commit, claim the invitation immediately
          invitation.claim!(user)
          self.organization = invitation.organization

          # Defer the actual claiming to an after_commit callback
          @pending_invitation = invitation
        else
          Rails.logger.warn "No invitation found for token: #{invitation_token}"
        end
      end

      # Ensure we have the latest associations
      user.reload
    end
  end

  def after_commit_organization_setup
    return unless @pending_invitation

    @pending_invitation.claim!(user)
    # Force reload of organizations
    user.organizations.reload
  end

  def id
    user&.id
  end

  def errors
    user.errors
  end

  def validate_user
    user&.validate
  end

  def organizations
    user&.organizations&.reload || Organization.none
  end
end
