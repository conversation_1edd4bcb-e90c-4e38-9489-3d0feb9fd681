# frozen_string_literal: true

class ServiceMaterial < ApplicationRecord
  belongs_to :service
  belongs_to :catalog_item

  validates :catalog_item, presence: true
  validate :catalog_item_must_be_material

  after_commit :update_service_milestone_caches

  private

  def catalog_item_must_be_material
    return unless catalog_item

    unless catalog_item.material?
      errors.add(:catalog_item, "Only materials can be associated with services")
    end
  end

  def update_service_milestone_caches
    # Update milestone caches when service materials change
    service.send(:update_milestone_caches)
  end
end
