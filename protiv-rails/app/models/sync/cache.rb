# frozen_string_literal: true

class Sync::Cache < ApplicationRecord
  belongs_to :integration

  scope :placeholder, -> { where(raw_data: nil) }
  scope :cached, -> { where.not(raw_data: nil) }

  scope :aspire, -> { where(source: Sync::Aspire::SOURCE) }
  scope :aspire_contacts, -> { aspire.where(remote_resource: :contact) }
  scope :aspire_catalog_items, -> { aspire.where(remote_resource: :catalog_item) }
  scope :aspire_opportunities, -> { aspire.where(remote_resource: :opportunity) }
  scope :aspire_numbered_opportunities, -> { aspire.where(remote_resource: :numbered_opportunity) }
  scope :aspire_opportunity_services, -> { aspire.where(remote_resource: :opportunity_service) }
  scope :aspire_work_tickets, -> { aspire.where(remote_resource: :work_ticket) }
  scope :aspire_work_ticket_items, -> { aspire.where(remote_resource: :work_ticket_item) }
  scope :aspire_item_allocations, -> { aspire.where(remote_resource: :item_allocation) }
  scope :aspire_branches, -> { aspire.where(remote_resource: :branch) }
  scope :aspire_routes, -> { aspire.where(remote_resource: :route) }

  scope :jobber, -> { where(source: Sync::Jobber::SOURCE) }
  scope :jobber_jobs, -> { jobber.where(remote_resource: :job) }
  scope :jobber_visits, -> { jobber.where(remote_resource: :visit) }
  scope :jobber_users, -> { jobber.where(remote_resource: :user) }
  scope :jobber_clients, -> { jobber.where(remote_resource: :client) }
  scope :jobber_properties, -> { jobber.where(remote_resource: :property) }
  scope :jobber_line_items, -> { jobber.where(remote_resource: :line_item) }
  scope :jobber_timesheet_entries, -> { jobber.where(remote_resource: :timesheet_entry) }
  scope :jobber_products_or_services, -> { jobber.where(remote_resource: :product_or_service) }
  scope :jobber_quotes, -> { jobber.where(remote_resource: :quote) }
  scope :jobber_all_timesheet_entries, -> { jobber.where(remote_resource: :all_timesheet_entries) }
  scope :jobber_visit_timesheet_entries, -> { jobber.where(remote_resource: :timesheet_entry) }

  scope :by_aspire_opportunity_number, ->(num) { aspire_opportunities.where("raw_data->>'OpportunityNumber' = ?", num.to_s) }
  scope :by_jobber_job_number, ->(num) { jobber_jobs.where("raw_data->>'jobNumber' = ?", num.to_s) }
  scope :last_synced_after, ->(date) { where(updated_at: date..) }

  validates :remote_resource, presence: true

  def placeholder?
    raw_data.nil?
  end

  def to_resource
    sync_module.cache_to_resource(self)
  end

  def sync_module
    Sync.lookup(source)
  end

  def remote_slug
    "#{source}:#{remote_resource}:#{remote_primary_id}"
  end

  private
end
