# frozen_string_literal: true

class Current < ActiveSupport::CurrentAttributes
  attribute :session
  attribute :user_agent, :ip_address
  attribute :user
  attribute :organization
  attribute :token_scope
  attribute :host

  def self.identities
    return Identity.none unless user && organization

    user.identities.where(organization:)
  end

  def self.manager_identity
    identities.where(crew_lead: true).first
  end
end
