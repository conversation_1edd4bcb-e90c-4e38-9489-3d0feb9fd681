# frozen_string_literal: true

class Organization < ApplicationRecord
  # An `Organization` is any arrangement of people as a group.
  # This could represent a team, a company, or an ad-hoc group
  # of freelance contractors.

  generates_token_for(:api)

  # Explicitly define attribute accessors for new tracking preferences
  attribute :default_progress_tracking_type, :string, default: "percentage"
  attribute :default_tracking_material, :string

  belongs_to :default_payroll_schedule, class_name: "PayrollSchedule", required: false
  belongs_to :default_bonus_schedule, class_name: "PayrollSchedule", required: false
  belongs_to :default_route_schedule, class_name: "PayrollSchedule", required: false

  belongs_to :default_bonus_pool, class_name: "BonusPool",
             default: -> { BonusPool.new(organization: self, name: "Organization Default Bonus Pool") }

  has_many :payroll_schedules
  has_many :pay_periods, through: :payroll_schedules

  has_many :identities
  has_many :roles
  has_many :inactive_roles, -> { inactive }, class_name: "Role"
  has_many :users, through: :roles
  has_many :inactive_users, through: :inactive_roles

  has_many :admin_roles, -> { admin }, class_name: "Role"
  has_many :employee_roles, -> { employee }, class_name: "Role"
  has_many :manager_roles, -> { manager }, class_name: "Role"

  has_many :admin_users, through: :admin_roles, source: :user
  has_many :employee_users, through: :employee_roles, source: :user
  has_many :manager_users, through: :manager_roles, source: :user

  has_many :integrations
  has_many :branches
  has_many :properties

  has_many :jobs
  has_many :milestones, through: :jobs
  has_many :pro_pays
  has_many :sub_pro_pays, through: :pro_pays
  has_many :sub_pro_pay_pro_payables, through: :sub_pro_pays
  has_many :statements, through: :identities
  has_many :statement_line_items, through: :identities

  has_many :services
  has_many :catalog_items
  has_many :routes
  has_many :schedule_visits

  has_one :labor_efficiency_benchmark

  scope :active, -> { } # FIXME implement this when we get account activity sorted out

  has_many :attendances

  validates :name, presence: true
  validates :default_progress_tracking_type, presence: true, inclusion: { in: %w[percentage units] }

  # Create default labor efficiency benchmark after organization is created
  after_create :create_default_benchmark

  private

  def create_default_benchmark
    # Skip if organization already has an active benchmark
    return if labor_efficiency_benchmark&.active?

    benchmark_manager = Metrics::BenchmarkManager.new(self)
    benchmark_manager.reset_to_default
  end
end
