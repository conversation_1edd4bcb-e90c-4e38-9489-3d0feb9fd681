# frozen_string_literal: true

class Statement < ApplicationRecord
  include AASM # Acts As State Machine

  belongs_to :organization
  belongs_to :pay_period
  belongs_to :identity

  # Explicitly allow identity_id to be set
  attribute :identity_id, :integer

  has_many :statement_line_items

  enum :status, {
    paid: "paid",
    open: "open",
    void: "void"
  }

  monetize :net_amount_cents
  monetize :bonuses_cents
  monetize :deductions_cents

  monetize :previous_balance_cents
  monetize :overtime_cents

  validates_presence_of :payment_date, if: -> { self.status == "paid" }
  validate :validate_paid_statement_line_items, if: -> { status == "paid" }
  validate :validate_voided_statement_line_items, if: -> { status == "void" }

  before_create :set_code
  before_save :reset_payment_date_if_not_paid

  after_touch :check_if_empty

  ### State
  aasm column: :status, enum: true do
    state :open, initial: true
    state :paid
    state :void

    event :mark_open do
      transitions from: :paid, to: :open, if: :net_amounts_match_line_items?
      transitions from: :void, to: :open, if: :net_amounts_match_line_items?
    end

    event :mark_paid do
      transitions from: :open, to: :paid, if: :net_amounts_match_line_items?
    end

    event :mark_void do
      transitions from: :paid, to: :void, if: :net_amounts_match_line_items?
      transitions from: :open, to: :void, if: :net_amounts_match_line_items?
    end
  end

  def pay!
    self._pay
    self.mark_paid

    changed? ? save! : true
  end

  def void!
    self._void_line_items
    self.mark_void

    changed? ? save! : true
  end

  def open!(release_voided = false)
    release = ActiveModel::Type::Boolean.new.cast(release_voided)

    # We must mark it open first before releasing all voided items
    self.mark_open

    if release && self.status_was == "void"
      self._release_line_items
    end
    self._unpay_line_items

    changed? ? save! : true
  end

  def touch
    update_net_amount

    save! if changed?
    super
  end

  def previous_statement
    previous_period = self.pay_period.previous_period
    return nil if !previous_period

    previous_period.statements.first
  end

  def set_code
    return unless organization_id # Ensure organization_id is present
    return if code.present? # Allow manual setting or if already set

    # Lock the table or use a more sophisticated locking mechanism if high concurrency is expected
    # For most cases, this approach should be sufficient.
    last_statement = Statement
      .where(organization_id: organization_id)
      .where.not(code: nil)
      .order(code: :desc)
      .first
    self.code = last_statement ? last_statement.code.to_i + 1 : 1
  end

  private

  def calculated_net_amount
    # FIXME: This will sum the Money's up and convert currencies. Should we instead create a Statement per location
    # where we could fix the currency for that location? Punting on this for now since we only support USD
    Money::Collection.new(
      self.statement_line_items
      .where.not(status: "void")
      .collect(&:amount)
    ).sum
  end

  def calculated_bonuses_amount
    Money::Collection.new(
      self.statement_line_items
      .where.not(status: "void")
      .where("amount_cents > 0")
      .collect(&:amount)
    ).sum
  end

  def calculated_deductions_amount
    Money::Collection.new(
      self.statement_line_items
      .where.not(status: "void")
      .where("amount_cents < 0")
      .collect(&:amount)
    ).sum
  end

  def calculated_previous_balance
    statement = self.previous_statement
    statement&.net_amount
  end

  def update_net_amount
    self.net_amount = calculated_net_amount
    self.bonuses = calculated_bonuses_amount
    self.deductions = calculated_deductions_amount

    self.previous_balance = calculated_previous_balance
  end

  def net_amounts_match_line_items?
    calculated_net_amount == self.net_amount
  end

  def reset_payment_date_if_not_paid
    if status_changed? && status_was == "paid" && status != "paid"
      self.payment_date = nil
    end
  end

  def validate_paid_statement_line_items
    invalid_items = statement_line_items.reject { |item| item.status.in?(%w[paid void]) }
    if invalid_items.any?
      errors.add(:base, "All line items of a paid Statement must be either 'paid' or 'void'")
    end
  end

  def validate_voided_statement_line_items
    invalid_items = statement_line_items.reject { |item| item.status == "void" }
    if invalid_items.any?
      errors.add(:base, "All line items of a voided Statement must be 'void'")
    end
  end

  def check_if_empty
    return if destroyed? || new_record?

    if statement_line_items.reload.empty?
      destroy!
    end
  end

  def _pay
    self.payment_date = Time.current
    PayStatementService.new(statement: self).execute!
  end

  def _void_line_items
    Statement.transaction do
      statement_line_items.each do |item|
        item.void! unless item.status == "void"
      end
    end
  end

  def _release_line_items
    Statement.transaction do
      statement_line_items.each do |item|
        item.release! unless item.status == "in_process"
      end
    end
  end

  def _unpay_line_items
    Statement.transaction do
      statement_line_items.each do |item|
        item.release! if item.status == "paid"
      end
    end
  end
end
