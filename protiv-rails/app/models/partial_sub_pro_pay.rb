# frozen_string_literal: true

class PartialSubProPay < ApplicationRecord
  include AASM # Acts As State Machine

  belongs_to :sub_pro_pay
  has_many :bonus_line_items

  scope :draft, -> { where(status: "draft") }
  scope :open, -> { where(status: ["draft", "materialized"]) }

  enum :status, {
    draft: "draft",
    materialized: "materialized",
    finalized: "finalized" # Paid
  }

  monetize :bonus_cents, allow_nil: true

  ### Validations
  validates :estimated_percent_complete, numericality: { only_integer: true, in: 0..100, allow_nil: true }
  validate :only_one_open

  ### State
  aasm column: :status, enum: true do
    state :draft, initial: true
    state :materialized
    state :finalized

    event :materialize do
      transitions from: :draft, to: :materialized, after: :_materialize
      transitions from: :materialized, to: :materialized, after: :_materialize
    end

    event :finalize do
      transitions from: :materialized, to: :finalized, after: :_finalize
    end
  end

  ### Computed values
  def percent_complete
    estimated_percent_complete.nil? ? sub_pro_pay.calculated_percent_complete : estimated_percent_complete / 100.0
  end

  def is_open?
    ["draft", "materialized"].include?(self.status)
  end

  private

  def only_one_open
    return unless draft? && self.sub_pro_pay

    matches = sub_pro_pay.partial_sub_pro_pays.where(status: ["draft", "materialized"])
    if persisted?
      matches = matches.where.not(id: self.id)
    end

    if matches.exists?
      errors.add(:status, "only one partial_sub_pro_pay may be in draft status at a time")
    end
  end

  def _materialize
    # create or update the bonus_line items
    MaterializePartialSubProPay.new(partial_sub_pro_pay: self).execute!
  end

  def _finalize
    self.update(finalized_at: Time.now)
  end
end
