# frozen_string_literal: true

class Role < ApplicationRecord
  belongs_to :user
  belongs_to :organization

  ADMIN = "admin"
  MANAGER = "manager"
  EMPLOYEE = "employee"

  scope :active, -> { where(active: true) }
  default_scope { active }

  scope :inactive, -> { where(active: false) }
  scope :admin, -> { where(role_type: ADMIN) }
  scope :manager, -> { where(role_type: MANAGER) }
  scope :employee, -> { where(role_type: EMPLOYEE) }

  # The role_type enum is defined in the database as a PostgreSQL enum type
  # with values: owner, admin, manager, crew_lead, employee
end
