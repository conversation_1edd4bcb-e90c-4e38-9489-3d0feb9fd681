# frozen_string_literal: true

class Service < ApplicationRecord
  belongs_to :organization

  # New associations for multiple materials
  has_many :service_materials, dependent: :destroy
  has_many :tracking_materials, through: :service_materials, source: :catalog_item

  has_many :milestones
  has_many :catalog_items, through: :milestones # TODO: I need to check if this is still needed

  # Flag to skip expensive callbacks during bulk operations or configuration
  attr_accessor :skip_milestone_cache_updates

  after_commit :update_milestone_caches

  # Add progress_type enum
  enum :progress_type, {
    percentage: "percentage",
    units: "units"
  }

  trackable

  # Note: tracking_materials association already filtered to materials only via ServiceMaterial validation
  # No need for additional filtering method since ServiceMaterial validates catalog_item must be material

  private

  def update_milestone_caches
    # Skip expensive cache updates if flag is set (e.g., during signup/bulk operations)
    return if skip_milestone_cache_updates

    # Skip if no milestones exist (nothing to update)
    return if milestones.none?

    # Update all milestone caches when service configuration changes
    # This ensures progress calculations are updated when materials are added/removed
    Rails.logger.info "Service #{id}: Updating #{milestones.count} milestone caches"
    milestones.pluck(:id).each do |milestone_id|
      Cache::UpdateMilestoneCachesJob.perform_async(milestone_id)
    end
  end
end
