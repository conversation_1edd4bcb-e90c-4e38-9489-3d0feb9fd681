# frozen_string_literal: true

class Invitation < ApplicationRecord
  include GlobalID::Identification

  belongs_to :organization
  belongs_to :user, optional: true
  belongs_to :invited_by, class_name: "User", optional: true

  enum :role_type, { admin: "admin", manager: "manager", employee: "employee" }

  def generate_token_for(purpose)
    data = "#{organization_id}:#{email}"
    Rails.application.message_verifier(purpose).generate(to_gid, expires_in: 7.days)
  end

  generates_token_for :account_create, expires_in: 7.days do
    "#{organization_id}:#{email}"
  end

  scope :active, -> { where(user_id: nil, deleted_at: nil).where("expires_at > ?", Time.now) }

  before_create :set_expires_at
  after_create :send_invitation_email
  validate :unique_active_invitation, on: :create

  validates :role_type, presence: true
  validates :organization, presence: true
  validates :email, presence: true

  def self.find_by_token_for(purpose, token)
    return nil if token.blank?

    begin
      # Try the generated token method first
      method_name = "find_by_#{purpose}_token"
      if respond_to?(method_name)
        invitation = send(method_name, token)
        return invitation if invitation&.active?
      end

      # Fallback to manual verification
      gid = Rails.application.message_verifier(purpose).verify(token)
      invitation = GlobalID::Locator.locate(gid)
      invitation if invitation&.active?
    rescue ActiveSupport::MessageVerifier::InvalidSignature
      nil
    end
  end

  def self.find_by_token_for!(purpose, token)
    find_by_token_for(purpose, token) || raise(ActiveSupport::MessageVerifier::InvalidSignature)
  end

  scope :active, -> { where(user_id: nil, deleted_at: nil).where("expires_at > ?", Time.now) }

  before_create :set_expires_at
  after_create :send_invitation_email
  validate :unique_active_invitation, on: :create

  validates :role_type, presence: true
  validates :organization, presence: true
  validates :email, presence: true

  def valid_token_for?(purpose, token)
    method_name = "valid_#{purpose}_token?"
    if respond_to?(method_name)
      send(method_name, token)
    else
      begin
        gid = Rails.application.message_verifier(purpose).verify(token)
        GlobalID::Locator.locate(gid) == self
      rescue ActiveSupport::MessageVerifier::InvalidSignature
        false
      end
    end
  end

  def claim!(user)
    raise AlreadyClaimed if user_id.present?
    raise Expired if expired?

    transaction(requires_new: false) do
      # Update user_id without validation since email is already validated
      update_column(:user_id, user.id)

      AddUserToOrganization.new(
        user: user,
        organization: organization,
        role_type: role_type
      ).execute!
    end
  end

  def active?
    user_id.nil? && deleted_at.nil? && !expired?
  end

  private

  def send_invitation_email
    UserMailer.with(invitation: self).invitation_instructions.deliver_now
  end

  def unique_active_invitation
    return unless email.present? && organization_id.present?

    existing_invitation = Invitation.active
      .where(email: email, organization_id: organization_id)
      .where.not(id: id)
      .exists?

    if existing_invitation
      errors.add(:base, "An active invitation already exists for this email in this organization")
    end
  end

  def set_expires_at
    self.expires_at ||= 1.week.from_now
  end

  def expired?
    expires_at.present? && expires_at < Time.zone.now
  end

  class AlreadyClaimed < StandardError; end
  class Expired < StandardError; end
end
