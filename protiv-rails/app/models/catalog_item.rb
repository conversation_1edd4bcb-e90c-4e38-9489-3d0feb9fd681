# frozen_string_literal: true

class CatalogItem < ApplicationRecord
  belongs_to :organization
  has_many :milestone_items
  has_many :milestones, through: :milestone_items

  # Service materials associations
  has_many :service_materials, dependent: :destroy
  has_many :services, through: :service_materials

  trackable

  MATERIAL = "Material"
  LABOR = "Labor"

  scope :material, -> { where(item_type: MATERIAL) }
  scope :labor, -> { where(item_type: LABOR) }

  def material?
    item_type == MATERIAL
  end

  def labor?
    item_type == LABOR
  end
end
