# frozen_string_literal: true

class StatementLineItem < ApplicationRecord
  include AASM # Acts As State Machine

  belongs_to :organization
  belongs_to :identity
  belongs_to :statement, optional: true
  belongs_to :line_item_category, optional: true
  belongs_to :pro_pay, optional: true
  belongs_to :job, optional: true

  # Explicitly allow identity_id to be set
  attribute :identity_id, :integer

  enum :status, {
    paid: "paid",
    held: "held",
    in_process: "in_process",
    void: "void"
  }

  attr_accessor :skip_update_statement

  before_create :set_code
  after_create :assign_to_statement
  after_save :update_statement

  validates :status, presence: true
  validates :held_days,
    numericality: { only_integer: true, greater_than_or_equal_to: 0 },
    allow_nil: true,
    if: -> { held_days.present? && !held_indefinitely? }
  validate :no_statement_on_held, on: :update
  validate :same_statement_identity
  validate :category_matches_amount_type
  validate :cannot_release_if_statement_voided, if: -> { status_changed? && status_was == "void" }

  monetize :amount_cents

  ### State
  aasm column: :status, enum: true do
    state :in_process, initial: true
    state :paid
    state :held
    state :void

    event :mark_in_process, after: :update_statement do
      transitions from: :held, to: :in_process
      transitions from: :void, to: :in_process, if: :statement_not_voided?
      transitions from: :paid, to: :in_process
    end

    event :mark_held do
      transitions from: :in_process, to: :held
    end

    # LineItems shouldn't be mark as paid individually, but from a Statement
    event :mark_paid do
      transitions from: :in_process, to: :paid
    end

    event :mark_void, after: :update_statement do
      transitions from: :held, to: :void
      transitions from: :in_process, to: :void
      transitions from: :paid, to: :void
    end
  end

  def extend!(days = nil, indefinitely = false)
    indefinitely = ActiveModel::Type::Boolean.new.cast(indefinitely)

    if !days.present? && !indefinitely
      self.errors.add(:status, "cannot be marked as held if no days and indefinitely is false")
      return false
    end

    old_statement = self.statement
    self.statement = nil

    self.held_days = days
    self.held_indefinitely = indefinitely
    if self.status != "held"
      self.mark_held
    end

    result = changed? ? save! : true
    old_statement.touch if old_statement

    result
  end

  def extend(days = nil, indefinitely = false)
    extend!(days, indefinitely)
  rescue
    false
  end

  def release!
    self.held_days = nil
    self.held_indefinitely = false
    self.mark_in_process
    if statement == nil
      set_statement_to_nearest
      statement.touch
    end
    changed? ? save! : true
  end

  def release
    release!
  rescue
    false
  end

  def void!
    self.held_days = nil
    self.held_indefinitely = false
    self.mark_void
    if statement == nil
      set_statement_to_nearest
      statement.touch
    end
    changed? ? save! : true
  end

  def void
    void!
  rescue
    false
  end

  def set_code
    return unless organization_id # Ensure organization_id is present
    return if code.present? # Allow manual setting or if already set

    # Lock the table or use a more sophisticated locking mechanism if high concurrency is expected
    # For most cases, this approach should be sufficient.
    last_line_item = StatementLineItem
      .where(organization_id: organization_id)
      .where.not(code: nil)
      .order(code: :desc)
      .first
    self.code = last_line_item ? last_line_item.code.to_i + 1 : 1
  end

  private

  def set_statement_to_nearest
    generator = StatementGeneratorService.new(identity: identity, organization: organization)
    statement = generator.execute!

    self.statement = statement
    save!
  end

  def assign_to_statement
    if !held? && statement.nil?
      set_statement_to_nearest
      save! if changed?
    end
  end

  def same_statement_identity
    return unless statement

    if statement.identity != identity
      errors.add(:identity, "is not the same as Statement's")
    end
  end

  def no_statement_on_held
    if status == "held" and statement != nil
      errors.add(:base, "LineItem is marked as held and belongs to a statement")
    end
    if status != "held" and statement == nil
      errors.add(:base, "LineItem does not belong to a statement")
    end
  end

  def cannot_release_if_statement_voided
    return unless statement
    if statement&.void? && status_change == ["void", "in_process"]
      errors.add(:base, "Cannot release a line item from a voided statement")
    end
  end

  def statement_not_voided?
    return true unless statement
    !statement.void?
  end

  def category_matches_amount_type
    return unless line_item_category && amount

    if amount.positive? && line_item_category.deduction?
      errors.add(:line_item_category, "must be a bonus for positive amounts")
    elsif amount.negative? && line_item_category.bonus?
      errors.add(:line_item_category, "must be a deduction for negative amounts")
    end
  end

  def update_statement
    return if skip_update_statement
    statement.touch if statement
  end
end
