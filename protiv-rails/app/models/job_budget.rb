# frozen_string_literal: true

class JobBudget < ApplicationRecord
  # The data for this comes from the job_budgets view, which is an aggregation of
  # this job's milestones.
  belongs_to :job
  readonly

  BUDGET_CATEGORIES.each do |category|
    monetize :"#{category}_cost_cents", with_model_currency: :currency
    monetize :"#{category}_budget_cents", with_model_currency: :currency

    # we should assume any of these may exceed 100
    define_method(:"percent_#{category}_consumed") do
      budget_cents = read_attribute("#{category}_budget_cents")
      cost_cents = read_attribute("#{category}_cost_cents")

      if !budget_cents.zero?
        100 * cost_cents / budget_cents.to_f
      else
        0.0
      end
    end
  end

  monetize :contract_price_cents, with_model_currency: :currency

  def total_costs
    equipment_cost + labor_cost + material_cost
  end

  def total_budget
    equipment_budget + labor_budget + material_budget
  end

  def gross_profit
    contract_price - total_costs
  end

  def budgeted_gross_profit
    contract_price - total_budget
  end
end
