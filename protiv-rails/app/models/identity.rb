# frozen_string_literal: true

class Identity < ApplicationRecord
  belongs_to :user, optional: true
  belongs_to :organization, optional: true
  has_many :milestone_times
  has_many :sub_pro_pay_line_items
  has_many :sub_pro_pays, through: :sub_pro_pay_line_items
  has_many :bonus_line_items, as: :payable
  has_many :attendances
  has_many :statements
  has_many :statement_line_items

  scope :unclaimed, -> { where(user_id: nil) }

  before_create :associate_user

  trackable

  def self.find_unclaimed_for_user(user)
    # FIXME: merge in some other conditions
    unclaimed.where(email: user.email)
  end

  def clocked_in?
    attendances.open.any?
  end

  private

  def associate_user
    if (user = User.verified.find_by_email(email))
      self.user_id = user.id
    end
  end
end
