# frozen_string_literal: true

class MilestoneItem < ApplicationRecord
  belongs_to :catalog_item
  belongs_to :organization
  belongs_to :milestone

  trackable

  monetize :cost_cents

  delegate :name, :item_type, :material?, :labor?, to: :catalog_item

  after_commit :update_milestone_caches

  private

  def update_milestone_caches
    Cache::UpdateMilestoneCachesJob.perform_async(milestone_id)
  end
end
