# frozen_string_literal: true

class Session
  attr_accessor :user, :organization

  def initialize(user, organization = nil)
    @user = user
    @organization = organization
  end

  def id
    user&.id
  end

  def admin
    user&.admin
  end

  def verified
    user&.verified
  end

  def email
    user&.email
  end

  def generate_token_for(purpose, expires_in: nil)
    return nil unless user

    payload = {}

    # use the models' own token definitions to allow them to invalidate the session
    # if necessary
    payload[:user_token] = user.generate_token_for(purpose) if user
    payload[:organization_token] = organization.generate_token_for(purpose) if organization

    message_verifier.generate(
      payload, purpose: ["Session", purpose].join("\n")
    )
  end

  def self.message_verifier
    ApplicationRecord.generated_token_verifier
  end

  def message_verifier
    self.class.message_verifier
  end

  def self.find_by_token_for!(purpose, token)
    find_by_token_for(purpose, token) \
      or raise ActiveSupport::MessageVerifier::InvalidSignature
  end

  def self.find_by_token_for(purpose, token)
    full_purpose = ["Session", purpose].join("\n")
    return unless (verified = message_verifier.verified(token, purpose: full_purpose))

    user = verified["user_token"] ? User.find_by_token_for(purpose, verified["user_token"]) : nil
    organization = verified["organization_token"] ? user&.organizations&.find_by_token_for(purpose, verified["organization_token"]) : nil

    new(user, organization)
  end
end
