# frozen_string_literal: true

class Bonuses::DecrementHeldDaysJob < ApplicationJob
  queue_as :default

  def perform
    # Find all line items that are held with a specific number of days (not indefinite)
    held_items = StatementLineItem.held.where("held_days IS NOT NULL AND held_indefinitely = ?", false)

    held_items.find_each do |item|
      item.with_lock do
        # Decrement the held days
        item.held_days -= 1

        if item.held_days <= 0
          # If days reach zero, release the item
          item.release!
        else
          # Otherwise just save the decremented days
          item.save!
        end
      end
    end
  end

  if Protiv::Application.schedule_cron?
    Sidekiq::Cron::Job.create(
      name: "Bonuses::DecrementHeldDaysJob - every day",
      cron: "0 0 * * *",
      class: name,
      queue: "cron"
    )
  end
end
