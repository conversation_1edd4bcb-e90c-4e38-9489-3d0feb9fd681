# frozen_string_literal: true

module Debounced<PERSON>ob
  extend ActiveSupport::Concern

  class_methods do
    def debounce_key
      "_debounce:#{name}"
    end

    def debounce?
      Protiv::Application.schedule_debounce_cron?
    end

    def perform_async(*args, debounce: true)
      if debounce? && debounce && args.present?
        redis.sadd(debounce_key, JSON.generate(args))
      else
        super(*args)
      end
    end

    def debounce_key
      "_debounce:#{name}"
    end

    def redis
      Rails.application.redis
    end
  end

  included do
    # FIXME: prepend instead
    alias_method :perform_without_debounce, :perform

    define_method(:perform) do |*args|
      if args.empty?
        enqueue_debounced_jobs
      else
        perform_without_debounce(*args)
      end
    end

    if debounce?
      Sidekiq::Cron::Job.create(
        name: "Debounce #{name} - every 1 minute",
        cron: "* * * * *",
        class: name,
        queue: "cron"
      )
    end
  end

  def redis
    self.class.redis
  end

  def enqueue_debounced_jobs
    redis.with do |conn|
      while (job_args = conn.spop(self.class.debounce_key))
        job_args = JSON.parse(job_args)
        self.class.perform_async(*job_args, debounce: false)
      end
    end
  end
end
