# frozen_string_literal: true

# Job that processes a single work ticket
class Sync::ReMaterializationWorkticket < ApplicationJob
  def perform(integration_id, work_ticket_id)
    integration = Integration.find(integration_id)

    Sync::Current.track(integration_id: integration.id) do
      cache = integration.sync_caches.aspire_work_tickets.find_by(remote_primary_id: work_ticket_id)
      return unless cache

      work_ticket = cache.to_resource
      materializer = integration.materializer

      # Try to find the associated job
      opportunity_id = work_ticket.opportunity_id
      job = nil

      if opportunity_id.present?
        opportunity = integration.sync_caches.where(
          remote_resource: "opportunity",
          remote_primary_id: opportunity_id
        ).first&.to_resource

        if opportunity&.opportunity_number.present?
          job_id = materializer.send(:local_id,
                                     remote_id: opportunity.opportunity_number,
                                     remote_resource: :numbered_opportunity,
                                     materialize: false
          )
          job = Job.find_by(id: job_id) if job_id
        end
      end

      # Materialize the milestone
      materializer.send(:materialize_milestone, work_ticket, job: job)
    end
  rescue => e
    # Log error but don't re-raise so that Sidekiq doesn't retry
    Rails.logger.error("Error materializing work ticket #{work_ticket_id}: #{e.message}")
    Bugsnag.notify(e) if defined?(Bugsnag)
  end
end
