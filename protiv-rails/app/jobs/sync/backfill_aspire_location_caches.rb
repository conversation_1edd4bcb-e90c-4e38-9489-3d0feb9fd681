# frozen_string_literal: true

class Sync::BackfillAspireLocationCaches < ApplicationJob
  def perform(integration_id)
    integration = Integration.find(integration_id)

    Sync::Current.track(integration_id: integration.id) do
      sql = Arel.sql("raw_data->>'PropertyID'")
      integration.sync_caches.aspire_opportunities.cached.pluck(sql).uniq.each_slice(50) do |property_ids|
        property_ids = property_ids.map(&:presence).compact
        next unless property_ids.any?

        integration.cache_adapter.cache_properties(ids: property_ids)
      end

      materializer = integration.materializer

      integration.sync_caches.aspire_numbered_opportunities.find_each do |cache|
        materializer.send(:materialize_job, cache.to_resource)
      end
    end
  end
end
