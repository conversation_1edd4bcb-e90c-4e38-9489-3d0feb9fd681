# frozen_string_literal: true

class Sync::SyncDatumJob < ApplicationJob
  def perform(sync_datum_id)
    datum = SyncDatum.find(sync_datum_id)

    record = datum.record
    integration = datum.integration

    unless datum.synced_at.present?
      # sync phase: datum is posted to integration
      datum.with_lock("FOR UPDATE NOWAIT") do
        remote_resource_identifier = integration.sync_to_remote!(record)
        datum.update(synced_at: Time.zone.now, **remote_resource_identifier)
      end
    end

    # resync phase: retrieve remote resource and sync it.
    # May not apply to all resources.
    # The resource may not necessarily be immediately available,
    # depending on filters.
    if datum.has_remote_resource_info?
      integration.backsync_datum(**datum.remote_resource_info)
    end
  end
end
