# frozen_string_literal: true

module Sync
  # Job that processes job status fixes for individual jobs
  class FixJobStatusJob < ApplicationJob
    sidekiq_options queue: "sync_jobs"

    def perform(integration_id, job_id)
      begin
        integration = Integration.find(integration_id)
      rescue ActiveRecord::RecordNotFound
        Rails.logger.warn("FixJobStatusJob: Integration ##{integration_id} not found - it may have been deleted. Skipping job status fix.")
        return
      end

      job = integration.organization.jobs.find(job_id)

      Sync::Current.track(integration_id: integration.id) do
        materializer = integration.materializer

        # Get current status
        old_status = job.status

        # Find the numbered opportunity for this job
        numbered_opportunity = Sync::Aspire::NumberedOpportunity.new(job.remote_reference.to_i)

        # Force update the job status using new logic
        materializer.send(:materialize_job, numbered_opportunity, force_status_update: true)

        # Check if status changed and log it
        job.reload
        if job.status != old_status
          Rails.logger.info("Job #{job.id} (#{job.name}): #{old_status} → #{job.status}")
        end
      end
    rescue => e
      job_info = job ? "job #{job.id}" : "job_id #{job_id}"
      Rails.logger.error("Error fixing job status for #{job_info}: #{e.message}")
      # Don't re-raise to prevent Sidekiq retries for this type of error
      Bugsnag.notify(e) if defined?(Bugsnag)
    end
  end
end
