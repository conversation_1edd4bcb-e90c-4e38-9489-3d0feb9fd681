# frozen_string_literal: true

# Job that enqueues individual milestone processing jobs in manageable batches
class Sync::QueueWorkTicketRematerializationBackgroundJobs < ApplicationJob
  def perform(integration_id, batch_size: 1000, max_batches: nil, start_batch: 1)
    integration = Integration.find(integration_id)
    total_count = integration.sync_caches.aspire_work_tickets.count
    batch_count = 0
    processed = 0

    puts "Found #{total_count} work tickets to process"

    Sync::Current.track(integration_id: integration.id) do
      integration.sync_caches.aspire_work_tickets.find_in_batches(batch_size: batch_size) do |batch|
        batch_count += 1

        # Skip batches if we're starting from a specific batch number
        next if batch_count < start_batch

        # Stop if we've reached max_batches
        break if max_batches && (batch_count - start_batch + 1) > max_batches

        # Use bulk operation if available to reduce Redis overhead
        if Sync::ReMaterializationWorkticket.respond_to?(:perform_bulk)
          ticket_ids = batch.map(&:remote_primary_id)
          Sync::ReMaterializationWorkticket.perform_bulk(
            ticket_ids.map { |id| [integration_id, id] }
          )
        else
          # Fall back to individual jobs if perform_bulk isn't available
          batch.each do |cache|
            Sync::ReMaterializationWorkticket.perform_async(
              integration_id,
              cache.remote_primary_id
            )
          end
        end

        processed += batch.size
        puts "Queued batch #{batch_count}: #{processed}/#{total_count} work tickets"

        # Add a small delay between batches to prevent overwhelming Redis and Sidekiq
        sleep(5)
      end
    end

    puts "Finished queuing #{processed} work tickets across #{batch_count - start_batch + 1} batches"
  end
end
