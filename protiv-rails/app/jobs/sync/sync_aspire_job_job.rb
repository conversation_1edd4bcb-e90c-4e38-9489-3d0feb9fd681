# frozen_string_literal: true

module Sync
  class SyncAspire<PERSON>ob<PERSON>ob < ApplicationJob
    sidekiq_options queue: "sync_jobs"

    def perform(integration_id, job_id)
      begin
        integration = Integration.find(integration_id)
      rescue ActiveRecord::RecordNotFound
        Rails.logger.warn("SyncAspireJobJob: Integration ##{integration_id} not found - it may have been deleted. Skipping job sync.")
        return
      end

      raise ArgumentError, "integration does not connect to aspire" unless integration.aspire?
      job = integration.jobs.find(job_id)
      raise ArgumentError, "job is not synced by this integration" unless job

      Sync::Current.track(integration_id: integration_id) do
        Sync::Aspire::Util.new(integration).sync_job_initial(job)

        job.update!(initial_sync_done: true)
        integration.check_initial_sync_complete!
      end
    end

    def self.priority(use_priority_queue)
      return self unless use_priority_queue
      set(queue: "sync_jobs_priority")
    end
  end
end
