# frozen_string_literal: true

class Sync::BackfillRouteOrganizationAndManager < ApplicationJob
  def perform(integration_id)
    integration = Integration.find(integration_id)
    return unless integration.aspire?

    Sync::Current.track(integration_id: integration.id) do
      materializer = integration.materializer

      # Backfill route data (organization and route_manager)
      integration.sync_caches.cached.aspire_routes.find_each do |route_cache|
        materializer.send(:materialize_route, route_cache.to_resource)
      end

      # Make sure route_manager flags are set on identities
      materializer.send(:sync_route_managers)
    end
  end
end
