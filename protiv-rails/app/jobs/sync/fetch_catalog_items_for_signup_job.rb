# frozen_string_literal: true

class Sync::FetchCatalogItemsForSignupJob < ApplicationJob
  sidekiq_options queue: :critical

  def perform(integration_id, days_back = 7, filter_by_selected_services = true)
    integration = Integration.find(integration_id)

    Rails.logger.info("FetchCatalogItemsForSignupJob: Starting for integration ##{integration_id}, days_back: #{days_back}, filter_by_selected_services: #{filter_by_selected_services}")

    Sync::Current.track(integration_id: integration.id) do
      # Get selected service IDs if filtering is enabled
      selected_service_ids = if filter_by_selected_services
        get_selected_service_remote_ids(integration)
      else
        nil
      end

      # Get catalog items from recent jobs, optionally filtered by services
      catalog_items = integration.adapter.retrieve_catalog_items_recent_jobs(
        item_type: "Material",
        days_back: days_back,
        filter_by_service_ids: selected_service_ids
      )

      Rails.logger.info("FetchCatalogItemsForSignupJob: Retrieved #{catalog_items.size} catalog items")

      # Cache them in sync_caches
      cached_count = 0
      catalog_items.each do |catalog_item_record|
        cache_record = integration.sync_caches.where(
          source: catalog_item_record.source,
          remote_resource: :catalog_item,
          remote_primary_id: catalog_item_record.primary_id
        ).first_or_initialize

        if cache_record.raw_data != catalog_item_record.as_json || cache_record.new_record?
          cache_record.raw_data = catalog_item_record.as_json
          cache_record.save!
          cached_count += 1
        end
      end

      Rails.logger.info("FetchCatalogItemsForSignupJob: Cached #{cached_count} new/updated items")

      # Materialize them immediately (deduplication happens automatically)
      integration.materializer.materialize_catalog_items

      Rails.logger.info("FetchCatalogItemsForSignupJob: Materialization complete")

      # Auto-associate materialized materials with selected services for signup
      if filter_by_selected_services
        associate_materials_with_services(integration)
      end
    end

  rescue ActiveRecord::RecordNotFound => e
    Rails.logger.warn("FetchCatalogItemsForSignupJob: Integration ##{integration_id} not found - it may have been deleted. Skipping catalog items fetch.")
    # Don't re-raise for missing integrations - just log and exit gracefully
    nil
  rescue => e
    Rails.logger.error("FetchCatalogItemsForSignupJob: Error for integration ##{integration_id}: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    raise # Re-raise to let Sidekiq handle it
  end

  private

  def get_selected_service_remote_ids(integration)
    # Get services with progress_type 'units' (selected in signup step 2)
    selected_services = integration.organization.services.where(progress_type: "units")

    # Get their remote IDs from integration records
    remote_ids = selected_services.map do |service|
      service.integration_records.where(integration: integration).first&.remote_id
    end.compact

    Rails.logger.info("FetchCatalogItemsForSignupJob: Found #{remote_ids.size} selected services: #{remote_ids}")
    remote_ids
  end

  def associate_materials_with_services(integration)
    # Get services with progress_type 'units' (selected in signup step 2)
    selected_services = integration.organization.services.where(progress_type: "units")

    # Get recently materialized materials (from the last hour to catch materials from this job)
    recent_materials = integration.organization.catalog_items
                                  .where(item_type: "Material")
                                  .where("updated_at > ?", 1.hour.ago)
                                  .distinct # Ensure no duplicate materials

    Rails.logger.info("FetchCatalogItemsForSignupJob: Auto-associating #{recent_materials.count} materials with #{selected_services.count} services")

    associations_created = 0
    selected_services.each do |service|
      # Get existing associations to avoid duplicates
      existing_material_ids = service.service_materials.pluck(:catalog_item_id)

      recent_materials.each do |material|
        # Skip if association already exists
        next if existing_material_ids.include?(material.id)

        # Create ServiceMaterial association
        begin
          ServiceMaterial.create!(
            service: service,
            catalog_item: material
          )
          associations_created += 1
        rescue ActiveRecord::RecordInvalid => e
          # Log but don't fail if there's a validation error (e.g., duplicate)
          Rails.logger.warn("FetchCatalogItemsForSignupJob: Failed to create association for service #{service.id}, material #{material.id}: #{e.message}")
        end
      end
    end

    Rails.logger.info("FetchCatalogItemsForSignupJob: Created #{associations_created} new service-material associations")
  end
end
