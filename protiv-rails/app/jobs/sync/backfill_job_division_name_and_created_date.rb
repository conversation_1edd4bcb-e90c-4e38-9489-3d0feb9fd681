# frozen_string_literal: true

class Sync::BackfillJobDivisionNameAndCreatedDate < ApplicationJob
  def perform(integration_id)
    integration = Integration.find(integration_id)
    return unless integration.aspire?

    Sync::Current.track(integration_id: integration.id) do
      integration.sync_caches.cached.aspire_numbered_opportunities.find_each do |numbered_opportunity|
        integration.materializer.send(:materialize_job, numbered_opportunity.to_resource)
      end
    end
  end
end
