# frozen_string_literal: true

module Sync
  # Job that enqueues individual job status fix jobs in manageable batches
  class QueueJobStatusFixJobs < ApplicationJob
    sidekiq_options queue: "sync_jobs"

    def perform(integration_id, batch_size: 1000, max_batches: nil, start_batch: 1)
      integration = Integration.find(integration_id)
      total_count = integration.organization.jobs.count
      batch_count = 0
      processed = 0

      Rails.logger.info("Found #{total_count} jobs to process for status fixes")

      Sync::Current.track(integration_id: integration.id) do
        integration.organization.jobs.find_in_batches(batch_size: batch_size) do |batch|
          batch_count += 1

          # Skip batches if we're starting from a specific batch number
          next if batch_count < start_batch

          # Stop if we've reached max_batches
          break if max_batches && (batch_count - start_batch + 1) > max_batches

          # Use bulk operation if available to reduce Redis overhead
          if FixJobStatusJob.respond_to?(:perform_bulk)
            job_ids = batch.map(&:id)
            FixJobStatusJob.perform_bulk(
              job_ids.map { |id| [integration_id, id] }
            )
          else
            # Fall back to individual jobs if perform_bulk isn't available
            batch.each do |job|
              FixJobStatusJob.perform_async(integration_id, job.id)
            end
          end

          processed += batch.size
          Rails.logger.info("Queued batch #{batch_count}: #{processed}/#{total_count} jobs")

          # Add a small delay between batches to prevent overwhelming Redis and Sidekiq
          sleep(2)
        end
      end

      Rails.logger.info("Finished queuing #{processed} jobs across #{batch_count - start_batch + 1} batches")
    end
  end
end
