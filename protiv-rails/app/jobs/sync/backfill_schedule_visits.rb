# frozen_string_literal: true

# This task pulls data from Aspire's `WorkTicketVisit` endpoint, based on the
# `WorkTicketID`s we have stored for our local `Milestone` records.
class Sync::BackfillScheduleVisits < ApplicationJob
  # I'll use the default queue for now. If this job turns out to be
  # very long-running, I might consider moving it to a dedicated :long_running queue.
  queue_as :default

  # I want to be able to run this for a specific integration_id
  # or for all Aspire integrations if no ID is provided.
  def perform(integration_id = nil)
    integrations_to_process = find_integrations_to_process(integration_id)

    if integrations_to_process.empty?
      log_no_integrations_found(integration_id)
      return
    end

    integrations_to_process.each do |integration|
      log_processing_start(integration)
      process_integration(integration)
      log_processing_complete(integration)
    rescue StandardError => e
      log_and_notify_integration_error(e, integration)
    end
  end

  private

  def find_integrations_to_process(integration_id)
    if integration_id
      integration = Integration.find_by(id: integration_id, source: "aspire")
      if integration
        # I'm returning an array here so it's consistent with the `else` branch.
        [integration]
      else
        Rails.logger.warn "[BackfillScheduleVisits] Aspire Integration with ID " \
                            "#{integration_id} not found. I'm aborting for this ID."
        [] # Return an empty array to stop processing if a specific ID was not found.
      end
    else
      # If no ID is given, I'll get all 'aspire' integrations.
      # I might want to add more filters later, like `.where(initial_sync_done: true)`.
      Integration.where(source: "aspire")
    end
  end

  def log_no_integrations_found(integration_id)
    if integration_id.nil? # Only log "no integrations found" if we weren't looking for a specific one.
      Rails.logger.info "[BackfillScheduleVisits] No Aspire integrations found to process."
    end
  end

  def log_processing_start(integration)
    Rails.logger.info "[BackfillScheduleVisits] Starting for Integration ID: #{integration.id}"
  end

  def log_processing_complete(integration)
    Rails.logger.info "[BackfillScheduleVisits] Completed for Integration ID: #{integration.id}"
  end

  def log_and_notify_integration_error(error, integration)
    log_message = "[BackfillScheduleVisits] Error processing Integration ID " \
      "#{integration.id}: #{error.message}\n#{error.backtrace.join("\n")}"
    Rails.logger.error log_message
    Bugsnag.notify(error) do |report|
      report.add_metadata(
        :backfill_context,
        { integration_id: integration.id, stage: "integration_processing" }
      )
    end
  end

  def process_integration(integration)
    # I need to wrap the main logic in `Sync::Current.track`. This is important
    # because it sets up the necessary context (like `Sync::Current.uuid` and
    # `Sync::Current.integration_id`) that the `Trackable` concern uses to
    # automatically create `IntegrationRecord` entries when I save local models.
    Sync::Current.track(integration_id: integration.id) do
      adapter = Sync::Aspire::Adapter.new(integration)
      materializer = Sync::Aspire::Materializer.new(integration)

      # First, I need to get the remote IDs of the parent resources (Aspire WorkTickets)
      # from our local Milestone records.
      work_ticket_remote_ids = collect_work_ticket_remote_ids(integration)

      if work_ticket_remote_ids.empty?
        Rails.logger.info "[BackfillScheduleVisits] No relevant Milestones (WorkTickets) " \
                            "found for Integration ID: #{integration.id}"
        return
      end

      log_fetching_remote_data(work_ticket_remote_ids.count, integration.id)

      # Now, I'll use the adapter to fetch the new child resource data (WorkTicketVisits) from Aspire.
      # The `segment_key` tells Aspire which field on WorkTicketVisit links to the WorkTicketID list I'm providing.
      raw_fetched_data_enumerator = adapter.retrieve_remote(
        remote_resource: :work_ticket_visit,
        ids: work_ticket_remote_ids,
        segment_key: "WorkTicketID" # This must be the exact field name on Aspire's WorkTicketVisit.
      )

      # I'll cache the raw data first, then materialize.
      fetched_aspire_models = cache_fetched_data(
        raw_fetched_data_enumerator,
        integration,
        adapter.source
      )

      materialize_aspire_visits(fetched_aspire_models, materializer, integration)
    end
  end

  def log_fetching_remote_data(count, integration_id)
    Rails.logger.info "[BackfillScheduleVisits] Fetching WorkTicketVisits for #{count} " \
                        "WorkTicket remote IDs for Integration ID: #{integration_id}."
  end

  # I'm responsible for getting the Aspire `WorkTicketID`s from our local `Milestone`s.
  def collect_work_ticket_remote_ids(integration)
    remote_ids = []
    # `Milestone`s are our local representation of Aspire `WorkTicket`s.
    # I need to find milestones for the current integration's organization.
    # Since `Milestone` `has_one :organization, through: :job`, I'll join through `jobs`.
    Milestone.joins(:job)
             .where(jobs: { organization_id: integration.organization_id })
             .find_each do |milestone|
      # Each `Milestone` should have an `IntegrationRecord` linking it to this Aspire integration
      # and storing its remote Aspire ID (WorkTicketID) in the `remote_slug`.
      integration_record = milestone.integration_records
                                    .find_by(integration_id: integration.id)

      if integration_record&.remote_slug&.starts_with?("aspire:work_ticket:")
        # The remote_slug is "aspire:work_ticket:[ID]", so I'll grab the ID part.
        remote_id = integration_record.remote_slug.split(":").last
        remote_ids << remote_id if remote_id.present?
      end
    end
    remote_ids.uniq # I only want unique IDs.
  end

  # I'll iterate through the fetched data, save it to `sync_caches`,
  # and collect the Aspire model instances.
  def cache_fetched_data(enumerator, integration, source_name)
    fetched_models = []
    Rails.logger.info "[BackfillScheduleVisits] Caching fetched WorkTicketVisits " \
                        "for Integration ID: #{integration.id}."

    enumerator.each do |wrapper_array_or_obj|
      # The adapter's `_retrieve` method yields `[wrapper, context]`.
      wrapper = wrapper_array_or_obj.is_a?(Array) ? wrapper_array_or_obj.first : wrapper_array_or_obj
      aspire_visit_model = wrapper.model # This is the actual AspireClient model instance.

      visit_remote_id = aspire_visit_model.work_ticket_visit_id # This is the Aspire WorkTicketVisitID.

      cache_record = integration.sync_caches.where(
        source: source_name,
        remote_resource: :work_ticket_visit,
        remote_primary_id: visit_remote_id
      ).first_or_initialize

      current_raw_data = aspire_visit_model.as_json.compact_blank
      # I'll save the cache record if its data has changed or if it's a new record.
      if cache_record.raw_data != current_raw_data || cache_record.new_record?
        cache_record.raw_data = current_raw_data
        # `updated_at` is handled by ActiveRecord automatically on save if `raw_data` changes.
        # For new records, it's also set on creation.
        cache_record.save!
      end
      fetched_models << aspire_visit_model
    end
    Rails.logger.info "[BackfillScheduleVisits] Cached #{fetched_models.count} WorkTicketVisits."
    fetched_models
  end

  # This method takes the array of Aspire model instances and materializes them.
  def materialize_aspire_visits(aspire_visits_array, materializer, integration)
    materialized_count = 0
    processed_count = 0

    aspire_visits_array.each do |aspire_work_ticket_visit|
      processed_count += 1
      begin
        # I'm calling the private `materialize_schedule_visit` method on the materializer.
        materializer.send(:materialize_schedule_visit, aspire_work_ticket_visit)
        materialized_count += 1
      rescue Sync::Errors::Skipped => e
        log_skipped_materialization(e, aspire_work_ticket_visit, integration.id)
      rescue ActiveRecord::RecordInvalid => e
        # I need to handle the case where a record might already exist due to a previous
        # run or normal sync, to make this job idempotent.
        if e.message.include?("Remote has already been taken")
          log_existing_record_conflict(e, aspire_work_ticket_visit, integration.id)
        else
          log_and_notify_materialization_error(e, aspire_work_ticket_visit, integration.id)
        end
      rescue StandardError => e
        log_and_notify_materialization_error(e, aspire_work_ticket_visit, integration.id)
      end
    end
    log_materialization_summary(processed_count, materialized_count, integration.id)
  end

  def log_skipped_materialization(error, visit_model, integration_id)
    visit_id = visit_model&.work_ticket_visit_id
    work_ticket_id = visit_model&.work_ticket_id
    Rails.logger.warn "[BackfillScheduleVisits] Skipped materializing WorkTicketVisit for " \
                        "Integration ID #{integration_id}. Reason: #{error.message}. Remote Visit ID: " \
                        "#{visit_id}, Remote WorkTicket ID: #{work_ticket_id}"
  end

  def log_existing_record_conflict(error, visit_model, integration_id)
    visit_id = visit_model&.work_ticket_visit_id
    Rails.logger.warn "[BackfillScheduleVisits] Record already exists or conflict for " \
                        "WorkTicketVisit for Integration ID #{integration_id}. Remote Visit ID: " \
                        "#{visit_id}. Error: #{error.message}"
  end

  def log_and_notify_materialization_error(error, visit_model, integration_id)
    visit_id = visit_model&.work_ticket_visit_id
    work_ticket_id = visit_model&.work_ticket_id
    log_message = "[BackfillScheduleVisits] Error materializing WorkTicketVisit for " \
      "Integration ID #{integration_id}. Remote Visit ID: #{visit_id}, " \
      "Remote WorkTicket ID: #{work_ticket_id}. Error: #{error.message}"
    Rails.logger.error "#{log_message}\n#{error.backtrace.join("\n")}"

    Bugsnag.notify(error) do |report|
      report.add_metadata(
        :backfill_context,
        {
          integration_id: integration_id,
          stage: "materialization",
          remote_visit_id: visit_id,
          remote_work_ticket_id: work_ticket_id
        }
      )
    end
  end

  def log_materialization_summary(processed, materialized, integration_id)
    Rails.logger.info "[BackfillScheduleVisits] Processed #{processed} fetched visits, " \
                        "successfully materialized (or attempted to) #{materialized} " \
                        "ScheduleVisits for Integration ID: #{integration_id}."
  end
end
