# frozen_string_literal: true

class Sync::Sync<PERSON>lockTimeRangeJob < ApplicationJob
  def perform(status_range_id)
    status_range = SyncStatusRange.find(status_range_id)
    integration = status_range.integration

    raise ArgumentError, "not a clock time range" unless status_range.resource.to_s == "clock_times"

    # These will essentially always be set as initial_sync: true
    caches = integration.cache_clock_time_range(status_range, initial_sync: true)
    placeholders, caches = caches.partition(&:placeholder?)
    caches = integration.cache_adapter.fetch_placeholders(placeholders) | caches
    integration.materializer.materialize_clock_times(caches)

    integration.check_initial_sync_complete!
  end
end
