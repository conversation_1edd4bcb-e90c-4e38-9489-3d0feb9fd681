# frozen_string_literal: true

class Sync::SyncJobberIntegrationJob < ApplicationJob
  sidekiq_options queue: :critical

  def perform(integration_id)
    Rails.logger.info("SyncJobberIntegrationJob: Starting sync for integration ##{integration_id}")

    begin
      integration = Integration.find(integration_id)
      Rails.logger.info("SyncJobberIntegrationJob: Found integration ##{integration_id} for organization ##{integration.organization_id}")

      if integration.source != "jobber"
        Rails.logger.error("SyncJobberIntegrationJob: Integration ##{integration_id} is not a Jobber integration")
        return
      end

      adapter = integration.adapter
      materializer = integration.materializer

      # Sync users first (needed for relationships)
      integration.tracking_sync(:users) do |sync_status|
        Rails.logger.info("SyncJobberIntegrationJob: Syncing users")
        users = adapter.retrieve_users(sync_status: sync_status)
        users.each do |user|
          materializer.materialize_user(user)
        end
      end

      # Sync clients
      integration.tracking_sync(:clients) do |sync_status|
        Rails.logger.info("SyncJobberIntegrationJob: Syncing clients")
        clients = adapter.retrieve_clients(sync_status: sync_status)
        clients.each do |client|
          materializer.materialize_client(client)
        end
      end

      # Sync properties
      integration.tracking_sync(:properties) do |sync_status|
        Rails.logger.info("SyncJobberIntegrationJob: Syncing properties")
        properties = adapter.retrieve_properties(sync_status: sync_status)
        properties.each do |property|
          materializer.materialize_property(property)
        end
      end

      # Sync products and services
      integration.tracking_sync(:products_and_services) do |sync_status|
        Rails.logger.info("SyncJobberIntegrationJob: Syncing products and services")
        products = adapter.retrieve_products_and_services(sync_status: sync_status)
        products.each do |product|
          materializer.materialize_product_or_service(product)
        end
      end

      # Sync jobs and related data
      integration.tracking_sync(:jobs) do |sync_status|
        Rails.logger.info("SyncJobberIntegrationJob: Syncing jobs")
        jobs = adapter.retrieve_jobs(sync_status: sync_status)
        jobs.each do |job|
          protiv_job = materializer.materialize_job(job)

          # Sync visits for this job
          visits = adapter.retrieve_visits(job_id: job.id, sync_status: sync_status)
          visits.each do |visit|
            milestone = materializer.materialize_visit(visit, protiv_job.id)

            # Sync line items for this visit
            line_items = adapter.retrieve_line_items(visit_id: visit.id, sync_status: sync_status)
            line_items.each do |line_item|
              materializer.materialize_line_item(line_item, milestone.id)
            end
          end
        end
      end

      # Sync timesheet entries globally
      integration.tracking_sync(:timesheet_entries) do |sync_status|
        Rails.logger.info("SyncJobberIntegrationJob: Syncing timesheet entries")
        timesheet_entries = adapter.retrieve_timesheet_entries(sync_status: sync_status)
        timesheet_entries.each do |entry|
          # Find the milestone for this timesheet entry based on visit
          if entry.visit&.id
            milestone_id = integration.sync_caches.jobber_visits.find_by(remote_primary_id: entry.visit.id)&.local_primary_id
            if milestone_id
              materializer.materialize_timesheet_entry(entry, milestone_id)
            else
              Rails.logger.warn("SyncJobberIntegrationJob: Could not find milestone for visit #{entry.visit.id}")
            end
          else
            Rails.logger.warn("SyncJobberIntegrationJob: Timesheet entry #{entry.id} has no associated visit")
          end
        end
      end

      # Update integration status
      integration.update!(
        last_synced_at: Time.current,
        initial_sync_done: true,
        status: "active"
      )

      Rails.logger.info("SyncJobberIntegrationJob: Completed sync for integration ##{integration_id}")
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.warn("SyncJobberIntegrationJob: Integration ##{integration_id} not found - it may have been deleted. Skipping sync.")
      # Don't re-raise for missing integrations - just log and exit gracefully
      nil
    rescue => e
      Rails.logger.error("SyncJobberIntegrationJob: Error syncing integration ##{integration_id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      raise # Re-raise the exception to let Sidekiq handle it
    end
  end
end
