# frozen_string_literal: true

class Sync::SyncIntegrationJob < ApplicationJob
  sidekiq_options queue: :critical

  def perform(integration_id)
    Rails.logger.info("SyncIntegrationJob: Starting sync for integration ##{integration_id}")

    begin
      integration = Integration.find(integration_id)
      Rails.logger.info("SyncIntegrationJob: Found integration ##{integration_id} for organization ##{integration.organization_id}")

      integration.sync_all(async: false)

      Rails.logger.info("SyncIntegrationJob: Completed sync for integration ##{integration_id}")
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.warn("SyncIntegrationJob: Integration ##{integration_id} not found - it may have been deleted. Skipping sync.")
      # Don't re-raise for missing integrations - just log and exit gracefully
      nil
    rescue => e
      Rails.logger.error("SyncIntegrationJob: Error syncing integration ##{integration_id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      raise # Re-raise the exception to let Sidekiq handle it
    end
  end
end
