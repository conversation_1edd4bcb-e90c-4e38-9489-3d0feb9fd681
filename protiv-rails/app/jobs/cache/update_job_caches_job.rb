# frozen_string_literal: true

class Cache::UpdateJobCachesJob < ApplicationJob
  def perform(job_id = nil)
    job = Job.find(job_id)

    # Log cache update for better visibility in material tracking scenarios
    Rails.logger.info "Updating job #{job_id} caches - #{job.milestones.count} milestones"

    # Check for any milestones that might need attention
    validate_milestone_progress_sources(job)

    # Update the cache columns
    job.update_cache_columns

    Rails.logger.info "Job #{job_id} cache update complete - progress: #{job.progress_percentage}%"
  rescue => e
    Rails.logger.error "Failed to update job #{job_id} caches: #{e.message}"
    raise e
  end

  include Debounced<PERSON><PERSON>

  private

  def validate_milestone_progress_sources(job)
    # Check for milestones that might have inconsistent progress tracking
    problematic_milestones = job.milestones.select do |milestone|
      next false unless milestone.service&.progress_type == "units"
      next false if milestone.tracked_milestone_item.present?

      # This milestone uses units tracking but has no material selected
      milestone.service.tracking_materials.any?
    end

    if problematic_milestones.any?
      milestone_ids = problematic_milestones.map(&:id).join(", ")
      Rails.logger.warn "Job #{job.id} has milestones needing material selection: #{milestone_ids}"
    end
  end
end
