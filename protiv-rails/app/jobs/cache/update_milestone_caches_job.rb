# frozen_string_literal: true

class Cache::UpdateMilestoneCachesJob < ApplicationJob
  def perform(milestone_id)
    milestone = Milestone.find(milestone_id)

    # Handle material selection and validation
    handle_material_tracking(milestone)

    # Update the cache columns
    milestone.update_cache_columns
  end

  include Deboun<PERSON><PERSON><PERSON>

  private

  def handle_material_tracking(milestone)
    return unless milestone.service&.progress_type == "units"

    # Check if tracked material is still valid
    validate_tracked_material(milestone)

    # Attempt auto-assignment if needed
    attempt_auto_assignment(milestone)
  end

  def validate_tracked_material(milestone)
    return unless milestone.tracked_milestone_item

    service = milestone.service
    tracked_catalog_item = milestone.tracked_milestone_item.catalog_item

    # If the currently tracked material is no longer a tracking material, clear it
    if service.tracking_materials.exclude?(tracked_catalog_item)
      Rails.logger.info "Clearing invalid tracked material #{tracked_catalog_item.id} from milestone #{milestone.id}"
      milestone.update!(tracked_milestone_item: nil)
    end
  end

  def attempt_auto_assignment(milestone)
    return if milestone.tracked_milestone_item.present?

    assignment_service = MilestoneProgressAssignmentService.new(milestone)
    if assignment_service.auto_assign_material_if_possible
      Rails.logger.info "Auto-assigned material for milestone #{milestone.id}"
    end
  end
end
