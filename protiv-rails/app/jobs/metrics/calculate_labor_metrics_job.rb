# frozen_string_literal: true

module Metrics
  # Job responsible for calculating and updating labor metrics based on recent changes
  # to ClockTime and MilestoneTime records.
  #
  # This job is designed to run after sync processes complete to ensure metrics reflect
  # the latest data. It identifies which worker-milestone-day combinations need updates
  # based on recent changes to the underlying data or for historical backfilling.
  class CalculateLaborMetricsJob < ApplicationJob
    queue_as :default # Use the default queue

    # Calculates and saves labor metrics for all affected worker-milestone-day combinations.
    # Handles errors gracefully for individual combinations and reports failures.
    #
    # @param options [Hash] Options to control which records to process
    # @option options [DateTime, String] :since       Only process records updated/starting since this time. Defaults to 1 day ago.
    # @option options [Boolean]          :backfill    When true, uses date range based on :since instead of updated_at. Defaults to false.
    # @option options [Integer]          :organization_id Required: Filter processing to a specific organization.
    # @option options [Integer]          :job_id Optional: Filter processing to a specific job.
    def perform(options = {})
      # Require organization_id
      organization_id = options[:organization_id] || options["organization_id"]
      unless organization_id.present?
        message = "Organization ID is required but was not provided"
        Rails.logger.error(message)
        raise ArgumentError, message
      end

      organization_id = organization_id.to_i
      start_time = Time.current
      Rails.logger.info("Starting job for organization_id=#{organization_id} with options: #{options.inspect}")

      worker_milestone_days = find_records_to_process(options)
      total_combinations = worker_milestone_days.size

      Rails.logger.info(
        "Found #{total_combinations} " \
          "worker-milestone-day combinations to check."
      )

      processed_count = 0
      failed_combinations = []

      worker_milestone_days.each do |combo|
        begin
          calculate_and_save_metrics(
            identity_id: combo[:identity_id],
            milestone_id: combo[:milestone_id],
            date: combo[:date]
          )
        rescue StandardError => e
          Bugsnag.notify(e) do |report|
            report.add_metadata(:job_context, {
              job_class: self.class.name,
              failed_combination: combo,
              job_options: options.transform_values(&:to_s)
            })
            report.severity = "error"
            report.grouping_hash =
              "CalculateLaborMetricsJob-" \
                "#{combo[:identity_id]}-" \
                "#{combo[:milestone_id]}-" \
                "#{combo[:date]}"
          end

          log_individual_error(combo, e)
          failed_combinations << combo
        end

        processed_count += 1

        if processed_count % 100 == 0 && total_combinations > 0
          Rails.logger.info(
            "Processed " \
              "#{processed_count}/#{total_combinations} combinations " \
              "(#{failed_combinations.size} failures so far)"
          )
        end
      end

      log_final_status(processed_count, failed_combinations, start_time)
    end

    private

    # Identifies which worker-milestone-day combinations need to be recalculated
    # based on recent updates (normal mode) or date range (backfill mode).
    #
    # @param options [Hash] Options controlling the record selection (:since, :backfill, :organization_id, :job_id)
    # @return [Array<Hash>] Unique array of hashes {identity_id:, milestone_id:, date:}
    def find_records_to_process(options)
      # --- Correctly read options regardless of key type (symbol vs string) ---
      since_value = options[:since] || options["since"]
      backfill_value = options[:backfill] || options["backfill"]
      organization_id = options[:organization_id] || options["organization_id"]
      job_id = options[:job_id] || options["job_id"]

      since_time = since_value ? Time.parse(since_value.to_s) : 1.day.ago
      backfill_mode = !!backfill_value # Ensure boolean true/false

      # Organization filter is always applied
      organization_id = organization_id.to_i
      Rails.logger.info("Filtering by organization_id=#{organization_id}")

      # --- Base queries with required organization filter ---
      milestone_times_base = MilestoneTime
                               .joins(milestone: { job: :organization })
                               .where(jobs: { organization_id: organization_id })

      # Apply job filter if specified
      if job_id.present?
        job_id = job_id.to_i
        Rails.logger.info("Filtering by job_id=#{job_id}")

        milestone_times_base = milestone_times_base
                                 .where(milestones: { job_id: job_id })
      end

      # --- Apply Time Filters ---
      if backfill_mode
        milestone_times_criteria_query = milestone_times_base.where("milestone_times.start_time > ?", since_time)

        # Build clock times query
        clock_times_base = ClockTime
                             .joins("INNER JOIN milestone_times ON milestone_times.identity_id = clock_times.identity_id")
                             .joins("INNER JOIN milestones ON milestones.id = milestone_times.milestone_id")
                             .joins("INNER JOIN jobs ON jobs.id = milestones.job_id")
                             .where("DATE(clock_times.start_at) = DATE(milestone_times.start_time)")
                             .where(jobs: { organization_id: organization_id })

        # Apply job filter to clock times if needed
        if job_id.present?
          clock_times_base = clock_times_base.where(jobs: { id: job_id })
        end

        clock_times_criteria_query = clock_times_base
                                       .where("clock_times.start_at > ?", since_time)
      else
        milestone_times_criteria_query = milestone_times_base.where("milestone_times.updated_at > ?", since_time)

        # Build clock times query for non-backfill mode
        clock_times_base = ClockTime
                             .joins("INNER JOIN milestone_times ON milestone_times.identity_id = clock_times.identity_id")
                             .joins("INNER JOIN milestones ON milestones.id = milestone_times.milestone_id")
                             .joins("INNER JOIN jobs ON jobs.id = milestones.job_id")
                             .where("DATE(clock_times.start_at) = DATE(milestone_times.start_time)")
                             .where(jobs: { organization_id: organization_id })

        # Apply job filter to clock times if needed
        if job_id.present?
          clock_times_base = clock_times_base.where(jobs: { id: job_id })
        end

        clock_times_criteria_query = clock_times_base
                                       .where("clock_times.updated_at > ?", since_time)
      end

      # --- Extract Combinations ---
      combos_from_milestone_time = milestone_times_criteria_query
                                     .pluck(:identity_id, :milestone_id, "DATE(start_time) AS date")
                                     .uniq
                                     .map { |id, mid, date| { identity_id: id, milestone_id: mid, date: date } }

      combos_from_clock_time = clock_times_criteria_query
                                 .select("clock_times.identity_id", "milestone_times.milestone_id", "DATE(clock_times.start_at) AS date")
                                 .distinct
                                 .map { |r| { identity_id: r.identity_id, milestone_id: r.milestone_id, date: r.date } }

      # --- Debug Logging ---
      Rails.logger.info("============ Combinations Found =============")
      Rails.logger.info("Using Since Time: #{since_time}, Backfill: #{backfill_mode}")
      Rails.logger.info("Combos from MilestoneTime: #{combos_from_milestone_time.count}")
      Rails.logger.info("Combos from ClockTime: #{combos_from_clock_time.count}")
      Rails.logger.info("==========================================")

      # Combine and unique the two sets
      final_combinations = (combos_from_milestone_time + combos_from_clock_time).uniq

      Rails.logger.info("Total unique combinations: #{final_combinations.count}")

      final_combinations
    end

    # Calculates and saves metrics for a specific worker-milestone-day combination.
    #
    # @param identity_id [Integer] The worker's identity ID
    # @param milestone_id [Integer] The milestone ID
    # @param date [Date] The date to calculate metrics for
    def calculate_and_save_metrics(identity_id:, milestone_id:, date:)
      calculator = Metrics::LaborMetricCalculator.new(
        identity_id: identity_id,
        milestone_id: milestone_id,
        date: date
      )

      metrics = calculator.call

      labor_metric = LaborMetric.find_or_initialize_by(
        organization_id: metrics[:organization_id],
        identity_id: identity_id,
        milestone_id: milestone_id,
        date: date
      )

      labor_metric.update!(metrics)
    end

    # Logs details about an error encountered during the processing of a single combination.
    #
    # @param combo [Hash] The combination hash {identity_id:, milestone_id:, date:}
    # @param error [StandardError] The exception that was rescued
    def log_individual_error(combo, error)
      backtrace = error.backtrace&.first(10)&.join("\n") || "No backtrace available"
      Rails.logger.error(
        "ERROR processing combo " \
          "worker=#{combo[:identity_id]}, milestone=#{combo[:milestone_id]}, date=#{combo[:date]}: " \
          "#{error.class} - #{error.message}\nBacktrace sample:\n#{backtrace}"
      )
    end

    # Logs the final outcome of the job run.
    #
    # @param processed_count [Integer] Total number of combinations attempted.
    # @param failed_combinations [Array<Hash>] List of combinations that failed.
    # @param start_time [Time] When the job started.
    def log_final_status(processed_count, failed_combinations, start_time)
      elapsed_time = Time.current - start_time

      if failed_combinations.empty?
        Rails.logger.info(
          "Job completed successfully. " \
            "Processed #{processed_count} combinations in #{elapsed_time.round(2)}s."
        )
      else
        Rails.logger.error(
          "Job finished with errors. " \
            "Processed #{processed_count} combinations in #{elapsed_time.round(2)}s.\n" \
            "Failures: #{failed_combinations.size}"
        )

        Rails.logger.error(
          "Failed combinations: " \
            "#{failed_combinations.take(50).inspect}" \
            "#{failed_combinations.size > 50 ? ' (truncated)' : ''}"
        )
      end
    end
  end
end
