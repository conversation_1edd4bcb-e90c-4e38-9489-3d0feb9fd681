# frozen_string_literal: true

class VerifyPasswordChangeService
  attr_reader :user, :token, :errors

  def initialize(user, token)
    @user = user
    @token = token
    @errors = []
  end

  def call
    user_from_token = User.find_by_token_for(:password_change_verification, token)

    if user_from_token.nil?
      @errors << "Invalid or expired token"
      return self
    end

    if user_from_token.id != user.id
      @errors << "Token does not match this user"
      return self
    end

    unless user.password_change_pending_verification?
      @errors << "Password change already verified"
      return self
    end

    user.password_change_pending_verification = false

    unless user.save
      @errors.concat(user.errors.full_messages)
      return self
    end

    # Invalidate all active sessions after successful verification
    user.invalidate_all_sessions!

    self
  end

  def success?
    @errors.empty?
  end
end
