# frozen_string_literal: true

class PayStatementService < ApplicationService
  def initialize(statement:)
    @statement = statement
  end

  def execute
    unless @statement.status == "open"
      return Result.new.error!("statement is not ready to pay")
    end

    result = Result.new
    result.ok!

    Statement.transaction do
      @statement.touch
      @statement.statement_line_items.each do |item|
        item.mark_paid if item.status == "in_process"
        item.save!
      end
      @statement.save!
    rescue => e
      result.error!(e)
      raise ActiveRecord::Rollback
    end

    result.state = @statement
    result
  end
end
