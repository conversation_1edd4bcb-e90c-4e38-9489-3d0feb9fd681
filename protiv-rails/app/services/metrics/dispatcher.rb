# frozen_string_literal: true

module Metrics
  # Dispatches metric calculation jobs after sync processes complete
  #
  # This service acts as a central dispatcher for triggering various metric
  # calculation jobs when data changes. It maintains a registry of jobs
  # that should run after sync operations complete.
  class Dispatcher
    class << self
      # Triggers all registered metric calculation jobs after a sync completes
      #
      # @param integration_id [Integer] The ID of the integration that completed syncing
      def after_sync(integration_id)
        integration = Integration.find(integration_id)
        organization_id = integration.organization_id

        # Use the timestamp of the last successful sync to determine which records to check
        # If this is the first sync, use a very old time to process all records
        since_time = integration.last_synced_at || Time.at(0)
        since_time_str = since_time.iso8601 # Convert Time to ISO 8601 string

        # Run all registered jobs
        registered_jobs.each do |job_class|
          # Convert keys to strings and since_time to string
          job_class.perform_async(
            "since" => since_time_str,
            "integration_id" => integration_id,
            "organization_id" => organization_id
          )

          Rails.logger.info(
            "[Metrics::Dispatcher] #{job_class} scheduled | " \
              "org=#{organization_id}, integration=#{integration_id}, since=#{since_time_str}" # Log the string version
          )
        end
      end

      # Get list of metric calculation jobs to run after sync
      # Add new metric calculation jobs here as they are created
      #
      # @return [Array<Class>] Array of job classes to trigger
      def registered_jobs
        [
          Metrics::CalculateLaborMetricsJob
        ]
      end
    end
  end
end
