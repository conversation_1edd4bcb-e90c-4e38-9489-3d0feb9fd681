# app/services/metrics/labor_metric_calculator.rb
# frozen_string_literal: true

module Metrics
  class LaborMetricCalculator
    MIN_BILLABLE_DURATION_SECONDS = 60

    def initialize(identity_id:, milestone_id:, date:)
      @identity_id = identity_id
      @milestone_id = milestone_id
      @date = date.to_date
      @milestone = Milestone.includes(:invoice_type).find_by(id: milestone_id) # Eager load invoice_type
      @organization_id = @milestone&.job&.organization_id # Handle if milestone is nil
    end

    def call
      # If milestone doesn't exist or not T&M, it might be better to return zero metrics early
      # or let the individual calculation methods handle it.
      # For now, rely on calculate_labor_cost and valid_milestone_times_for_day to check.

      current_benchmark = benchmark_percentage
      return zero_metrics(current_benchmark) if total_clocked_seconds.zero? && !(@milestone&.invoice_type&.time_and_materials?)

      billable = total_billable_seconds # Already filtered by T&M in the method
      # Ensure billable time doesn't exceed clocked time, though less likely if billable_seconds are correctly filtered
      billable = [billable, total_clocked_seconds].min

      has_time_discrepancy = total_billable_seconds_raw > total_clocked_seconds

      hourly_rate_cents = get_worker_hourly_rate # Will be 0 if not T&M or no rate on T&M

      # Calculate distributed non-billable time instead of attributing all non-billable time to this milestone
      total_recorded_seconds_all_jobs = calculate_total_recorded_seconds_all_jobs
      true_non_billable_seconds = [total_clocked_seconds - total_recorded_seconds_all_jobs, 0].max

      # Calculate this milestone's recorded time (whether T&M or not)
      this_milestone_recorded_seconds = calculate_milestone_recorded_seconds

      # Distribute the non-billable time proportionally based on this milestone's recorded time
      distribution_ratio = total_recorded_seconds_all_jobs.zero? ? 0 : (this_milestone_recorded_seconds.to_f / total_recorded_seconds_all_jobs)
      distributed_non_billable_seconds = (true_non_billable_seconds * distribution_ratio).round

      # Calculate efficiency based on billable time vs total allocated time
      # (billable + distributed non-billable)
      total_allocated = billable + distributed_non_billable_seconds
      efficiency = total_allocated.zero? ? 0.0 : ((billable.to_f / total_allocated) * 100).round(2)

      non_billable_labor_cost_cents_val = ((distributed_non_billable_seconds / 3600.0) * hourly_rate_cents).round

      {
        organization_id: @organization_id,
        identity_id: @identity_id,
        milestone_id: @milestone_id,
        date: @date,
        total_clocked_seconds: total_clocked_seconds,
        total_billable_seconds: billable,
        non_billable_seconds: distributed_non_billable_seconds,
        has_time_discrepancy: has_time_discrepancy,
        efficiency_percentage: efficiency,
        labor_cost_cents: calculate_labor_cost, # Already filtered by T&M
        non_billable_labor_cost_cents: non_billable_labor_cost_cents_val,
        benchmark_percentage: current_benchmark,
        variance_from_benchmark: calculate_variance_from_benchmark(efficiency, current_benchmark)
      }
    end

    private

    def total_billable_seconds_raw
      # Raw calculation without T&M filter, if needed for has_time_discrepancy
      MilestoneTime
        .where(milestone_id: @milestone_id, identity_id: @identity_id)
        .where("DATE(start_time) = ?", @date)
        .where.not(end_time: nil)
        .where("EXTRACT(EPOCH FROM (end_time - start_time)) >= ?", MIN_BILLABLE_DURATION_SECONDS)
        .sum { |mt| (mt.end_time - mt.start_time).to_i }
    end

    def zero_metrics(benchmark_percent)
      # For milestone with no recorded time or zero clocked time
      # Return zero for all metrics
      {
        organization_id: @organization_id,
        identity_id: @identity_id,
        milestone_id: @milestone_id,
        date: @date,
        total_clocked_seconds: total_clocked_seconds,
        total_billable_seconds: 0,
        non_billable_seconds: 0,
        efficiency_percentage: 0,
        labor_cost_cents: 0,
        non_billable_labor_cost_cents: 0,
        benchmark_percentage: benchmark_percent,
        variance_from_benchmark: -benchmark_percent,
        has_time_discrepancy: false
      }
    end

    def get_worker_hourly_rate_for_zero_billable
      # If milestone is not T&M, there are no "valid_milestone_times_for_day" to get a rate from.
      # This method could look for *any* milestone time for that worker/day if a general rate is desired,
      # or stick to 0 if rates are strictly per-T&M-milestone-context.
      # For simplicity and strictness to current logic:
      return 0 unless @milestone&.invoice_type&.time_and_materials?
      valid_milestone_times_for_day.first&.base_hourly_rate_cents || 0
    end

    def total_clocked_seconds
      @total_clocked_seconds ||= ClockTime
                                   .where(identity_id: @identity_id)
                                   .where("DATE(start_at) = ?", @date)
                                   .where.not(end_at: nil)
                                   .sum do |ct|
        duration = (ct.end_at - ct.start_at).to_i
        duration -= ct.break_time_seconds.to_i if ct.break_time_seconds.present?
        [duration, 0].max
      end
    end

    def valid_milestone_times_for_day
      # Crucial: Ensure the milestone itself is T&M for its time to be considered "valid billable"
      return MilestoneTime.none unless @milestone&.invoice_type&.time_and_materials?

      MilestoneTime
        .where(milestone_id: @milestone_id, identity_id: @identity_id)
        .where("DATE(start_time) = ?", @date)
        .where.not(end_time: nil)
        .where("EXTRACT(EPOCH FROM (end_time - start_time)) >= ?",
               MIN_BILLABLE_DURATION_SECONDS)
    end

    def total_billable_seconds
      # This now correctly sums only for T&M milestones due to valid_milestone_times_for_day change
      @total_billable_seconds ||= valid_milestone_times_for_day.sum do |mt|
        (mt.end_time - mt.start_time).to_i
      end
    end

    # Calculate total recorded seconds across all milestones for this worker on this day
    def calculate_total_recorded_seconds_all_jobs
      @total_recorded_seconds_all_jobs ||= MilestoneTime
        .where(identity_id: @identity_id)
        .where("DATE(start_time) = ?", @date)
        .where.not(end_time: nil)
        .where("EXTRACT(EPOCH FROM (end_time - start_time)) >= ?", MIN_BILLABLE_DURATION_SECONDS)
        .sum { |mt| (mt.end_time - mt.start_time).to_i }
    end

    # Calculate recorded seconds for this specific milestone
    def calculate_milestone_recorded_seconds
      @milestone_recorded_seconds ||= MilestoneTime
        .where(identity_id: @identity_id, milestone_id: @milestone_id)
        .where("DATE(start_time) = ?", @date)
        .where.not(end_time: nil)
        .where("EXTRACT(EPOCH FROM (end_time - start_time)) >= ?", MIN_BILLABLE_DURATION_SECONDS)
        .sum { |mt| (mt.end_time - mt.start_time).to_i }
    end

    def calculate_labor_cost
      # This will be 0 if not a T&M milestone due to valid_milestone_times_for_day change
      valid_milestone_times_for_day.sum { |mt| mt.labor_cost_cents.to_i }
    end

    def get_worker_hourly_rate
      # This will be 0 if not a T&M milestone or no rate on T&M milestone_time
      valid_milestone_times_for_day.first&.base_hourly_rate_cents || 0
    end

    def benchmark_percentage
      # BenchmarkFinder might need to be aware of T&M context or return a default
      # For now, assumes it fetches a relevant benchmark if @milestone is present
      @benchmark_percentage ||= @milestone ? Metrics::BenchmarkFinder.new(@milestone).call : 0.0
    end

    def calculate_variance_from_benchmark(actual_efficiency, benchmark_percent_arg)
      (actual_efficiency - benchmark_percent_arg).round(2).to_f
    end
  end
end
