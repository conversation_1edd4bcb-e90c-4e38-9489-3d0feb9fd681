# frozen_string_literal: true

module Metrics
  # Manages LaborEfficiencyBenchmark records at the organization level
  #
  # Provides methods to set custom percentages or reset to default values
  class BenchmarkManager
    # Default target percentage to use when resetting
    DEFAULT_TARGET_PERCENTAGE = Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE

    # @param organization [Organization] The organization to manage benchmarks for
    def initialize(organization)
      @organization = organization
    end

    # Sets a custom target percentage for the organization
    #
    # @param percentage [Float, Integer] The target percentage (0-100)
    # @return [LaborEfficiencyBenchmark] The updated or created benchmark
    # @raise [ActiveRecord::RecordInvalid] If the percentage is invalid
    def set_custom_percentage(percentage)
      benchmark = find_or_initialize_benchmark

      # Deactivate any existing benchmarks
      deactivate_existing_benchmarks if benchmark.new_record?

      # Set the new percentage and ensure it's active
      benchmark.target_percentage = percentage
      benchmark.active = true

      benchmark.save!
      benchmark
    end

    # Resets the organization's benchmark to the default percentage (75%)
    #
    # @return [LaborEfficiencyBenchmark] The updated or created benchmark
    def reset_to_default
      set_custom_percentage(DEFAULT_TARGET_PERCENTAGE)
    end

    private

    attr_reader :organization

    # Finds an existing benchmark or initializes a new one
    #
    # @return [LaborEfficiencyBenchmark] The found or new benchmark
    def find_or_initialize_benchmark
      LaborEfficiencyBenchmark.find_or_initialize_by(
        organization: organization,
        level: "organization",
        level_record_id: nil
      )
    end

    # Deactivates any existing benchmarks for this organization at the organization level
    def deactivate_existing_benchmarks
      LaborEfficiencyBenchmark.where(
        organization: organization,
        level: "organization",
        level_record_id: nil
      ).update_all(active: false)
    end
  end
end
