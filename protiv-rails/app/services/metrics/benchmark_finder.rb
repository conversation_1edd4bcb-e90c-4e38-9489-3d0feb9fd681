# frozen_string_literal: true

module Metrics
  # Finds the most specific, active LaborEfficiencyBenchmark target_percentage
  # applicable to a given context (e.g., a Milestone).
  #
  # Currently only supports organization-level benchmarks, but designed for
  # future expansion to Job, Route, and Branch levels.
  #
  # Hierarchy (Future):
  # 1. Job
  # 2. Route
  # 3. Branch
  # 4. Organization
  class BenchmarkFinder
    # Default target percentage if no specific benchmark is found
    DEFAULT_TARGET_PERCENTAGE = 75.0

    # @param context [Milestone, Job, Hash] An object or hash providing context.
    #        Must respond to or contain: :organization_id
    def initialize(context)
      @context = context
      @organization_id = extract_organization_id

      validate_context!
    end

    # Finds and returns the applicable benchmark percentage
    # @return [Float]
    def call
      find_benchmark_percentage || DEFAULT_TARGET_PERCENTAGE
    end

    private

    attr_reader :context, :organization_id

    # Attempts to find the active organization-level benchmark
    # @return [Float, nil]
    def find_benchmark_percentage
      # TODO: Expand to check Job, Route, Branch levels
      # benchmark = find_by_level('job',   job_id)   ||
      #             find_by_level('route', route_id) ||
      #             find_by_level('branch',branch_id)||
      #             find_by_level('organization', nil)

      benchmark = find_by_level("organization", nil)
      benchmark&.target_percentage&.to_f
    end

    # Queries for an active benchmark at a specific level and ID
    # @param level [String]
    # @param record_id [Integer, nil]
    # @return [LaborEfficiencyBenchmark, nil]
    def find_by_level(level, record_id)
      return nil if level != "organization" && record_id.blank?
      return nil unless organization_id

      LaborEfficiencyBenchmark.find_by(
        organization_id: organization_id,
        level: level,
        level_record_id: record_id,
        active: true
      )
    end

    # Extracts organization_id from context
    # @return [Integer, nil]
    def extract_organization_id
      if context.is_a?(Hash)
        context[:organization_id]
      elsif context.respond_to?(:organization_id)
        context.organization_id
      elsif context.is_a?(Milestone) &&
        context.respond_to?(:job) &&
        context.job&.respond_to?(:organization_id)
        context.job.organization_id
      else
        nil
      end
    end

    # Ensures organization_id is present
    def validate_context!
      raise ArgumentError,
            "Context must provide organization_id" unless organization_id.present?
    end
  end
end
