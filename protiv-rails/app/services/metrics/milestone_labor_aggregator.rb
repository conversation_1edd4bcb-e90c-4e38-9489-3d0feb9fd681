# frozen_string_literal: true

module Metrics
  # Aggregates pre-calculated daily LaborMetric records for a specific Milestone
  # over a given period (or all time). Calculates summary metrics for display.
  class MilestoneLaborAggregator
    # @param milestone_id [Integer] The ID of the Milestone to aggregate metrics for.
    # @param start_date [Date, nil] The start date for filtering metrics (inclusive). Defaults to nil (all time).
    # @param end_date [Date, nil] The end date for filtering metrics (inclusive). Defaults to nil (all time).
    def initialize(milestone_id:, start_date: nil, end_date: nil)
      @milestone_id = milestone_id
      @start_date = start_date
      @end_date = end_date
      # Fetch milestone for context (e.g., contract value) - raise if not found
      @milestone = Milestone.find(milestone_id)
      # Pre-fetch the base scope to avoid redundant filtering
      @metrics_scope = fetch_metrics_scope
    end

    # Calculate and return all aggregated metrics
    # @return [Hash] A hash containing aggregated sums and calculated metrics
    def call
      aggregated_sums = calculate_aggregated_sums
      # Return early with defaults if no data was aggregated
      return default_metrics if aggregated_sums[:total_billable_seconds].zero? && aggregated_sums[:labor_cost_cents].zero?

      overall_efficiency = calculate_overall_efficiency(aggregated_sums)
      billable_cost_per_hour_cents = calculate_billable_cost_per_hour_cents(aggregated_sums)
      labor_cost_percentage = calculate_labor_cost_percentage(aggregated_sums)
      non_billable_labor_cost_percentage = calculate_non_billable_labor_cost_percentage(aggregated_sums)
      total_labor_cost_percentage = calculate_total_labor_cost_percentage(aggregated_sums)
      charge_rate_cents = calculate_charge_rate_cents(aggregated_sums)
      # Fetch benchmark percentage from the aggregated data
      benchmark_percentage = fetch_benchmark_percentage

      raw_metrics = {
        milestone_id: @milestone_id,
        start_date: @start_date,
        end_date: @end_date,
        total_clocked_seconds: aggregated_sums[:total_clocked_seconds],
        total_billable_seconds: aggregated_sums[:total_billable_seconds],
        non_billable_seconds: aggregated_sums[:non_billable_seconds],
        labor_cost_cents: aggregated_sums[:labor_cost_cents],
        non_billable_labor_cost_cents: aggregated_sums[:non_billable_labor_cost_cents],
        total_labor_cost_cents: aggregated_sums[:labor_cost_cents] + aggregated_sums[:non_billable_labor_cost_cents],
        overall_efficiency_percentage: overall_efficiency,
        benchmark_percentage: benchmark_percentage,
        variance_from_benchmark: (overall_efficiency - benchmark_percentage).round(2).to_f,
        billable_cost_per_hour_cents: billable_cost_per_hour_cents,
        labor_cost_percentage_of_contract: labor_cost_percentage,
        non_billable_labor_cost_percentage_of_contract: non_billable_labor_cost_percentage,
        total_labor_cost_percentage_of_contract: total_labor_cost_percentage,
        charge_rate_cents: charge_rate_cents
      }

      # Format the response structure
      format_response(raw_metrics)
    end

    private

    attr_reader :milestone_id, :start_date, :end_date, :milestone, :metrics_scope

    # Fetches the base ActiveRecord::Relation for LaborMetric records,
    # filtered by milestone and optionally by date.
    # @return [ActiveRecord::Relation]
    def fetch_metrics_scope
      scope = LaborMetric.all

      # Apply the mandatory filter
      scope = scope.where(milestone_id: milestone_id)

      # Apply optional date filters
      scope = scope.where("date >= ?", start_date) if start_date
      scope = scope.where("date <= ?", end_date) if end_date

      scope
    end

    # Calculates the sums of key metrics from the filtered scope
    # @return [Hash] Hash with summed values (:total_clocked_seconds, :total_billable_seconds, etc.)
    def calculate_aggregated_sums
      total_clocked = metrics_scope.calculate(:sum, :total_clocked_seconds).to_i
      total_billable = metrics_scope.calculate(:sum, :total_billable_seconds).to_i
      total_non_billable = metrics_scope.calculate(:sum, :non_billable_seconds).to_i
      total_cost = metrics_scope.calculate(:sum, :labor_cost_cents).to_i
      total_non_billable_cost = metrics_scope.calculate(:sum, :non_billable_labor_cost_cents).to_i

      {
        total_clocked_seconds: total_clocked,
        total_billable_seconds: total_billable,
        non_billable_seconds: total_non_billable,
        labor_cost_cents: total_cost,
        non_billable_labor_cost_cents: total_non_billable_cost
      }
    end

    # Calculates the overall efficiency percentage from aggregated sums
    # @param aggregated_sums [Hash] The hash returned by calculate_aggregated_sums
    # @return [Float] Overall efficiency percentage
    def calculate_overall_efficiency(aggregated_sums)
      billable = aggregated_sums[:total_billable_seconds]
      total_allocated = billable + aggregated_sums[:non_billable_seconds]

      return 0.0 if total_allocated.zero?

      ((billable.to_f / total_allocated) * 100.0).round(2).to_f
    end

    # Calculates the billable cost per hour from aggregated sums
    # @param aggregated_sums [Hash] The hash returned by calculate_aggregated_sums
    # @return [Integer, nil] Billable cost per hour in cents, or nil if no billable time
    def calculate_billable_cost_per_hour_cents(aggregated_sums)
      total_cost_cents = aggregated_sums[:labor_cost_cents] + aggregated_sums[:non_billable_labor_cost_cents]
      billable_seconds = aggregated_sums[:total_billable_seconds]

      return nil if billable_seconds.zero?

      billable_hours = billable_seconds / 3600.0
      (total_cost_cents / billable_hours).round # Result is in cents
    end

    # Calculates the labor cost as a percentage of the milestone contract value
    # @param aggregated_sums [Hash] The hash returned by calculate_aggregated_sums
    # @return [Float, nil] Labor cost percentage, or nil if contract value is zero/unavailable
    def calculate_labor_cost_percentage(aggregated_sums)
      cost_cents = aggregated_sums[:labor_cost_cents]
      contract_value_cents = milestone.contract_price_cents.to_i

      return nil if contract_value_cents.zero?

      ((cost_cents.to_f / contract_value_cents.to_f) * 100.0).round(2).to_f
    end

    # Calculates the non-billable labor cost as a percentage of the milestone contract value
    # @param aggregated_sums [Hash] The hash returned by calculate_aggregated_sums
    # @return [Float, nil] Non-billable labor cost percentage, or nil if contract value is zero/unavailable
    def calculate_non_billable_labor_cost_percentage(aggregated_sums)
      cost_cents = aggregated_sums[:non_billable_labor_cost_cents]
      contract_value_cents = milestone.contract_price_cents.to_i

      return nil if contract_value_cents.zero?

      ((cost_cents.to_f / contract_value_cents.to_f) * 100.0).round(2).to_f
    end

    # Calculates the total labor cost (billable + non-billable) as a percentage of the milestone contract value
    # @param aggregated_sums [Hash] The hash returned by calculate_aggregated_sums
    # @return [Float, nil] Total labor cost percentage, or nil if contract value is zero/unavailable
    def calculate_total_labor_cost_percentage(aggregated_sums)
      billable_cost_cents = aggregated_sums[:labor_cost_cents]
      non_billable_cost_cents = aggregated_sums[:non_billable_labor_cost_cents]
      total_cost_cents = billable_cost_cents + non_billable_cost_cents
      contract_value_cents = milestone.contract_price_cents.to_i

      return nil if contract_value_cents.zero?

      ((total_cost_cents.to_f / contract_value_cents.to_f) * 100.0).round(2).to_f
    end

    # Calculates the charge rate in cents
    # @param aggregated_sums [Hash] The hash returned by calculate_aggregated_sums
    # @return [Integer, nil] Charge rate in cents, or nil if no billable time
    def calculate_charge_rate_cents(aggregated_sums)
      billable_seconds = aggregated_sums[:total_billable_seconds]
      contract_value_cents = milestone.contract_price_cents.to_i

      return nil if billable_seconds.zero?

      billable_hours = billable_seconds / 3600.0
      (contract_value_cents / billable_hours).round # Result in cents
    end

    # Fetches the benchmark percentage stored on the LaborMetric records.
    # Takes the value from the first record found in the scope (assumes consistency).
    # @return [Float] The benchmark percentage
    def fetch_benchmark_percentage
      # Find the first metric in the scope and return its stored benchmark.
      first_metric = metrics_scope.first
      first_metric ? first_metric.benchmark_percentage.to_f : Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE
    end

    # Formats the raw metrics into the structured response format for the UI
    # @param raw_metrics [Hash] The raw metrics hash
    # @return [Hash] The formatted response structure
    def format_response(raw_metrics)
      total_time = raw_metrics[:total_billable_seconds] + raw_metrics[:non_billable_seconds]
      non_billable_percentage = total_time.zero? ? 0 :
                                  ((raw_metrics[:non_billable_seconds].to_f / total_time) * 100).round(2)

      {
        labor_metrics_summary: {
          billable_time: {
            hours: seconds_to_hours(raw_metrics[:total_billable_seconds]),
            percentage: raw_metrics[:overall_efficiency_percentage],
            cost_cents: raw_metrics[:labor_cost_cents],
            percentage_of_contract: raw_metrics[:labor_cost_percentage_of_contract] || 0.0
          },
          non_billable_time: {
            hours: seconds_to_hours(raw_metrics[:non_billable_seconds]),
            percentage: non_billable_percentage,
            cost_cents: raw_metrics[:non_billable_labor_cost_cents],
            percentage_of_contract: raw_metrics[:non_billable_labor_cost_percentage_of_contract] || 0.0
          },
          total_time: {
            hours: seconds_to_hours(total_time),
            percentage: 100.0,
            cost_cents: raw_metrics[:total_labor_cost_cents],
            percentage_of_contract: raw_metrics[:total_labor_cost_percentage_of_contract] || 0.0
          },
          benchmark: {
            target_percentage: raw_metrics[:benchmark_percentage],
            actual_percentage: raw_metrics[:overall_efficiency_percentage],
            variance: raw_metrics[:variance_from_benchmark]
          },
          billable_cost_per_hour: raw_metrics[:billable_cost_per_hour_cents] || 0,
          charge_rate: raw_metrics[:charge_rate_cents] || 0
        }
      }
    end

    # Converts seconds to hours with decimal precision
    # @param seconds [Integer] The number of seconds
    # @return [Float] The equivalent hours value
    def seconds_to_hours(seconds)
      (seconds.to_f / 3600.0).round(2).to_f
    end

    # Returns default metrics when there's no data to aggregate
    # @return [Hash] Default values for the metrics hash
    def default_metrics
      default_benchmark = Metrics::BenchmarkFinder::DEFAULT_TARGET_PERCENTAGE

      # Format the default metrics using the new structure
      format_response({
                        milestone_id: @milestone_id,
                        start_date: @start_date,
                        end_date: @end_date,
                        total_clocked_seconds: 0,
                        total_billable_seconds: 0,
                        non_billable_seconds: 0,
                        labor_cost_cents: 0,
                        non_billable_labor_cost_cents: 0,
                        total_labor_cost_cents: 0,
                        overall_efficiency_percentage: 0.0,
                        benchmark_percentage: default_benchmark,
                        variance_from_benchmark: -default_benchmark,
                        billable_cost_per_hour: 0,
                        labor_cost_percentage_of_contract: 0.0,
                        non_billable_labor_cost_percentage_of_contract: 0.0,
                        total_labor_cost_percentage_of_contract: 0.0,
                        charge_rate: 0
                      })
    end
  end
end
