# frozen_string_literal: true

class RateLimiter
  class LimitExceeded < StandardError
  end

  class FaradayMiddleware < ::Faraday::Middleware
    def initialize(app, options = {})
      super(app)

      @rate_limiter = RateLimiter.new(**options)
    end

    def call(env)
      @rate_limiter.limit { super }
    end
  end

  Faraday::Request.register_middleware(rate_limiter: FaradayMiddleware)

  def self.limit(max_count:, period:, name:, &block)
    new(max_count: max_count, period: period, name: name, &block)
  end

  def initialize(max_count:, period:, name:)
    @max_count = max_count
    @period = period
    @name = name
  end

  attr_reader :max_count, :period, :name

  # A 'draining bucket' model, i.e. we count up each request for a particular time span
  # until it exceeds the limit.
  # It is recommended to apply the 'real' rate limit as follows:
  # E.g., your api token has a limit of 30 requests in 1 minute. We don't know how the remote
  # service necessarily counts these requests, or whether the limit window is rolling or fixed.
  # Limiting with `limit(max_count: 10, period: 20.seconds, ...)` or
  # `limit(max_count: 3, period: 6.seconds, ...)` are good options that lower the odds of
  # exceeding the remote's rate limit.
  def limit
    return yield unless redis

    window = Time.now.to_i / period
    key = "protiv_v2_rate_limit:#{name}:#{window}"

    count, _ = redis.multi do |r|
      r.incr(key)
      r.expire(key, period)
    end

    if count > max_count
      raise LimitExceeded
    else
      yield
    end
  end

  private

  def redis
    Protiv::Application.redis
  end
end
