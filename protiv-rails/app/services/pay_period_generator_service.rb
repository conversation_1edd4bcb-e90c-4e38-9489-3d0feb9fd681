# frozen_string_literal: true

class PayPeriodGeneratorService < ApplicationService
  def initialize(branch: nil)
    @branch = branch
  end

  def execute
    result = Result.new
    result.ok!

    ApplicationRecord.transaction do
      # FIXME: Scope to active organizations. We don't want to keep updating for non paying customers. In addition
      #   if the system is modified to support creating new PayrollSchedules we will also need to move past this
      #   simplified approach of creating pay_periods for all PayrollSchedules and only handle to the ones that are
      #   in use.

      if @branch.present?
        pay_periods = []
        pay_periods << create_current_pay_period(branch: @branch, period_type: "route")
        pay_periods << create_current_pay_period(branch: @branch, period_type: "bonus")
        pay_periods << create_current_pay_period(branch: @branch, period_type: "pay")
        pay_periods.compact!
        PayPeriod.insert_all!(pay_periods) if pay_periods.any?
      else
        generate_for_all_branches
      end

    rescue => e
      result.error!(e)
      raise ActiveRecord::Rollback
    end

    result.state = true
    result
  end

  def generate_for_all_branches
    # Route Periods
    Branch.without_current_route_pay_period.find_in_batches(batch_size: 100).each do |batch|
      pay_periods = []

      batch.each do |branch|
        pay_periods << create_current_pay_period(branch: branch, period_type: "route")
      end
      pay_periods.compact!
      PayPeriod.insert_all!(pay_periods) if pay_periods.any?
    end

    # Bonus Periods
    Branch.without_current_bonus_pay_period.find_in_batches(batch_size: 100).each do |batch|
      pay_periods = []

      batch.each do |branch|
        pay_periods << create_current_pay_period(branch: branch, period_type: "bonus")
      end
      pay_periods.compact!
      PayPeriod.insert_all!(pay_periods) if pay_periods.any?
    end

    # Pay Periods
    Branch.without_current_pay_pay_period.find_in_batches(batch_size: 100).each do |batch|
      pay_periods = []

      batch.each do |branch|
        pay_periods << create_current_pay_period(branch: branch, period_type: "pay")
      end
      pay_periods.compact!
      PayPeriod.insert_all!(pay_periods) if pay_periods.any?
    end
  end

  def create_current_pay_period(branch:, period_type:)
    case period_type
    when "route"
      payroll_schedule = branch.active_route_schedule
      return if branch.current_route_pay_period
    when "bonus"
      payroll_schedule = branch.active_bonus_schedule
      return if branch.current_bonus_pay_period
    when "pay"
      payroll_schedule = branch.active_payroll_schedule
      return if branch.current_pay_pay_period
    end

    return if payroll_schedule.nil?

    start_time = Time.use_zone(branch.time_zone) do
      payroll_schedule.start_date_from_date(Date.current)
    end
    end_time = Time.use_zone(branch.time_zone) do
      payroll_schedule.end_date_from_start_date(start_time.to_date)
    end

    {
      payroll_schedule_id: payroll_schedule.id,
      period_type: period_type,
      branch_id: branch.id,
      start_time: start_time,
      end_time: end_time
    }
  end
end
