# frozen_string_literal: true

module Sync
  LOCK = Mutex.new

  def self.table_name_prefix
    "sync_"
  end

  def self.register_module(mod, source = nil)
    source ||= mod.source

    modules[source] = mod
  end

  def self.lookup(source)
    modules.fetch(source)
  end

  def self.modules
    @modules ||= begin
      LOCK.synchronize do
        @modules ||= {}.with_indifferent_access
      end
    end
  end

  module CommonClassMethods
    def can_create?(resource)
      capabilities[resource]&.include?(:create)
    end
  end
end
