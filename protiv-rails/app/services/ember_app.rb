# frozen_string_literal: true

class EmberApp
  ALLOWED_BOOTSTRAP_PARAMS = %i[account.id account.authentication api.host api.namespace dev]
  # In production, we load the built index.html from a well-known location in
  # the Docker image and serve the content as-is
  def self.load(path: "/ember/index.html")
    raise "#{path} not found" unless File.exist?(path)
    new(File.read(path))
  end

  # In development, we use the Ember development server, so redirect there
  def self.redirect_to_ember(port: 4200)
    new <<~HTML
      <!doctype html>
      <html>
      <head>
        <meta charset=utf-8>

        <title>Redirecting to Ember...</title>
      </head>
      <body>
        <p>Redirecting to Ember...</p>
        <!-- BOOTSTRAP_START -->
        <!-- this doesn't do anything useful, but it conforms to the expectations of the initialize method -->
        <!-- BOOTSTRAP_END -->
        <script id='redirect-to-ember'>
          const url = new URL(document.location.href);
          url.port = #{port.to_i};
          document.location.replace(url);
        </script>
      </body>
      </html>
    HTML
  end

  BOOTSTRAP_REGEX = /<!-- BOOTSTRAP_START -->.*<!-- BOOTSTRAP_END -->/m.freeze

  def initialize(index_html)
    @segment_a, @segment_b = index_html.split(BOOTSTRAP_REGEX, 2)
  end

  def index_html(...)
    "#{@segment_a}#{bootstrap_script(...)}#{@segment_b}"
  end

  def bootstrap_js(...)
    "window._protiv_bootstrap = #{JSON.generate(bootstrap_payload(...))};"
  end

  private

  def bootstrap_script(...)
    "<script id='protiv-bootstrap'>#{bootstrap_js(...)}</script>"
  end

  ##
  # This method generates the payload for the bootstrap script.
  # we inject the rails session token into the frontend app.
  def bootstrap_payload(user, organization, dev: enable_dev_portal?, **additional_params)
    payload = additional_params.with_indifferent_access
    session = Session.new(user, organization)

    payload[:"account.id"] = user&.id&.to_s
    payload[:"account.authentication"] = session.generate_token_for(:api)

    payload[:"api.host"] = api_host
    payload[:"api.namespace"] = "api/v2"

    payload[:dev] = true if dev

    if (unpermitted = payload.except(*ALLOWED_BOOTSTRAP_PARAMS)).any?
      raise "unpermitted bootstrap keys: #{unpermitted.keys}"
    end

    payload
  end

  def api_host
    Rails.application.config.api_host || Current.host
  end

  def enable_dev_portal?
    Rails.application.enable_dev_portal?
  end
end
