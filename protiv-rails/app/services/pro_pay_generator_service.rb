# frozen_string_literal: true

class ProPayGeneratorService < ApplicationService
  def initialize
  end

  def execute
    result = Result.new
    result.ok!

    # Handle this one Organization at a time. Not strictly needed, but will allow us to isolate issues per org.
    Organization.includes(:default_route_schedule, :default_bonus_pool).all.each do |organization|
      next unless organization_is_ready_for_pro_pay_generation?(organization)

      ApplicationRecord.transaction do
        create_recurring_job_pro_pays(organization)

        create_non_recurring_job_pro_pays(organization)

        create_non_recurring_job_phased_pro_pays(organization)

      rescue => e
        result.error!(e)
        raise ActiveRecord::Rollback
      end

      result.state = true
      result
    end
  end

  def organization_is_ready_for_pro_pay_generation?(organization)
    organization.default_route_schedule.present? &&
      organization.default_bonus_pool.present?
  end

  def create_recurring_job_pro_pays(organization)
    # FIXME: Filter on Divisions that also support create_grouped_pro_pays
    milestones = Milestone.without_sub_pro_pay.merge(Milestone.with_recurring_job)
                          .joins(:routes)
                          .where(jobs: { organization: organization })
                          .where(routes: { create_grouped_pro_pays: true })
                          .includes({ routes: { branch: :current_route_pay_period } }, :job).distinct

    milestones.each do |milestone|
      # FIXME: How should we handle milestones with workers from different routes
      route = milestone.routes.first
      current_route_pay_period = route.branch.current_route_pay_period

      # This ProPay may already exist, so we will get it instead of creating it
      pro_pay = ProPay.find_or_create_by!(organization: organization,
                                          reference_bonus_pool: organization.default_bonus_pool,
                                          source_route: route,
                                          source_type: "route_recurring_jobs",
                                          source_route_pay_period: current_route_pay_period)

      # Gets the first sub_pro_pay since we will only need one for a route_recurring_jobs pro_pay
      sub_pro_pay = SubProPay.find_or_create_by(pro_pay: pro_pay)
      SubProPayProPayable.create!(sub_pro_pay: sub_pro_pay, pro_payable: milestone)
    end
  end

  def create_non_recurring_job_pro_pays(organization)
    phases_hour_threshold = organization.phases_hour_threshold.hours

    # FIXME: Filter on Divisions that also support create_grouped_pro_pays
    jobs = Job.without_sub_pro_pay.merge(Job.non_recurring)
              .joins(:routes, :job_budgets)
              .where(jobs: { organization: organization })
              .where(routes: { create_grouped_pro_pays: true })
              .where(job_budgets: { seconds_budget: ...phases_hour_threshold }) # beginless range for <
              .includes({ routes: { branch: :current_route_pay_period } }).distinct

    jobs.each do |job|
      budget = job.job_budgets.first
      next unless budget.present?

      # FIXME: How should we handle jobs with workers from different routes
      route = job.routes.first
      current_route_pay_period = route.branch.current_route_pay_period
      pro_pay = ProPay.find_or_create_by!(organization: organization,
                                          reference_bonus_pool: organization.default_bonus_pool,
                                          source_route: route,
                                          source_type: "route_non_recurring_jobs",
                                          source_route_pay_period: current_route_pay_period)
      sub_pro_pay = SubProPay.find_or_create_by(pro_pay: pro_pay)
      SubProPayProPayable.create!(sub_pro_pay: sub_pro_pay, pro_payable: job)
    end

    def create_non_recurring_job_phased_pro_pays(organization)
      phases_hour_threshold = organization.phases_hour_threshold.hours

      # FIXME: Filter on Divisions that also support create_grouped_pro_pays
      jobs = Job.without_sub_pro_pay.merge(Job.non_recurring)
                .joins(:routes, :job_budgets)
                .where(jobs: { organization: organization })
                .where(routes: { create_grouped_pro_pays: true })
                .where(job_budgets: { seconds_budget: phases_hour_threshold.. }) # endless range for >=
                .includes({ routes: { branch: :current_route_pay_period } }).distinct

      jobs.each do |job|
        route = job.routes.first
        current_route_pay_period = route.branch.current_route_pay_period
        pro_pay = ProPay.find_or_create_by!(organization: organization,
                                            reference_bonus_pool: organization.default_bonus_pool,
                                            source_route: route,
                                            source_type: "job_with_phases",
                                            source_job_id: job,
                                            source_route_pay_period: current_route_pay_period)

        sub_pro_pay = SubProPay.find_or_create_by(pro_pay: pro_pay)
        SubProPayProPayable.create!(sub_pro_pay: sub_pro_pay, pro_payable: job)
      end
    end
  end
end
