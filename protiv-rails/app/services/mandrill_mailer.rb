# frozen_string_literal: true

require "net/http"
require "uri"
require "json"

class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.send_mail(to:, subject:, html_content:)
    from_email = ENV.fetch("MAILER_FROM", "<EMAIL>")
    from_name = ENV.fetch("MAILER_FROM_NAME", "Protiv")
    api_key = ENV["MANDRILL_API_KEY"]

    uri = URI.parse("https://mandrillapp.com/api/1.0/messages/send.json")
    request = Net::HTTP::Post.new(uri)
    request.content_type = "application/json"

    payload = {
      key: api_key,
      message: {
        html: html_content,
        subject: subject,
        from_email: from_email,
        from_name: from_name,
        to: [
          {
            email: to,
            type: "to"
          }
        ],
        headers: {
          "Reply-To": from_email
        },
        important: false,
        track_opens: true,
        track_clicks: true,
        auto_text: true
      }
    }

    request.body = JSON.dump(payload)

    begin
      response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
        http.request(request)
      end

      result = JSON.parse(response.body)

      if response.code == "200" && result.is_a?(Array) && result.first["status"] != "rejected"
        Rails.logger.info "Email sent to #{to} using Mandrill. Status: #{result.first['status']}"
        true
      else
        error_message = result.is_a?(Array) ? result.first["reject_reason"] : result["message"]
        Rails.logger.error "Failed to send email via Mandrill: #{error_message}"
        raise "Mandrill API error: #{error_message}"
      end
    rescue => e
      Rails.logger.error "Failed to send email: #{e.message}"
      raise
    end
  end
end
