# frozen_string_literal: true

class MaterializePartialSubProPay < ApplicationService
  def initialize(partial_sub_pro_pay: nil, estimated_percent_complete: nil, finalized: false)
    @partial_sub_pro_pay = partial_sub_pro_pay
    @sub_pro_pay = @partial_sub_pro_pay.sub_pro_pay
    @pro_pay = @sub_pro_pay.pro_pay
    @finalized = finalized

    @estimated_percent_complete = estimated_percent_complete || @partial_sub_pro_pay.estimated_percent_complete
    @pro_pay_payables = @pro_pay.pro_pay_payables.includes(:payable).to_a
    @finalized_bonus_totals = @sub_pro_pay.sub_pro_pay_finalized_bonus_totals
  end

  def execute
    if @sub_pro_pay.bonus.blank?
      return Result.new.error!("SubProPay does not have a bonus")
    end

    unless @partial_sub_pro_pay&.is_open?
      return Result.new.error!("PartialSubProPay is not open")
    end

    result = Result.new
    result.ok!

    PartialSubProPay.transaction do
      @partial_sub_pro_pay.estimated_percent_complete = @estimated_percent_complete

      pro_pay_bonus = @sub_pro_pay.bonus(percent_complete: @partial_sub_pro_pay.percent_complete)

      @bonus_line_items = @partial_sub_pro_pay.bonus_line_items.includes(:payable).all.to_a
      @pro_pay_line_items = @sub_pro_pay.sub_pro_pay_line_items.includes(:identity)

      @partial_sub_pro_pay.bonus = pro_pay_bonus

      # Materialize Line Items
      materialize_crew_line_items

      # Others will always be set on the pro_pay
      materialize_payables_line_items(rate_class: "other")

      # Managers and crew leads may change as new data is synced
      materialize_manager_line_items(manager: @sub_pro_pay.pro_pay.manager)
      materialize_crew_lead_line_items
      materialize_company_line_items
      materialize_rounding_line_items

      @partial_sub_pro_pay.status = "materialized" if @partial_sub_pro_pay.draft?
      @partial_sub_pro_pay.materialized_at = Time.now
      @partial_sub_pro_pay.save!
    rescue => e
      result.error!(e)
      raise ActiveRecord::Rollback
    end

    result.state = @partial_sub_pro_pay
    result
  end

  private

  def materialize_crew_line_items
    cbdc = CrewBonusDistributionCalculator.new(bonus_amount: @partial_sub_pro_pay.bonus,
                                               distribution_type: @sub_pro_pay.distribution_type,
                                               total_labor_cost: @sub_pro_pay.total_labor_cost,
                                               total_seconds_worked: @sub_pro_pay.total_duration_seconds,
                                               total_crew_lead_labor_cost: @sub_pro_pay.total_crew_lead_labor_cost,
                                               total_crew_lead_seconds_worked: @sub_pro_pay.total_crew_lead_duration_seconds)

    # Crew is made up of identities that have billed hours to the milestone
    @pro_pay_line_items.each do |li|
      crew_member = li.identity
      ppp = find_payable_crew_member(crew_member)

      unadjusted_share_of_bonus = cbdc.share_of_bonus(crew_labor_cost: li.labor_cost,
                                                      crew_member_seconds_worked: li.duration_seconds,
                                                      crew_lead: li.crew_lead?,
                                                      manual_percent_hundredths: 0)
      # Immediately payable bonus
      prior_bonus = finalized_bonus_total(source: "crew", payable: crew_member)

      share_of_bonus =
        (unadjusted_share_of_bonus * @pro_pay.crew_percent * (1.0 - @pro_pay.crew_retention_percent)) - prior_bonus

      unless share_of_bonus == prior_bonus
        record_bonus_line_item(payable: crew_member,
                               source: "crew",
                               share_of_bonus: share_of_bonus,
                               labor_cost: li.labor_cost,
                               duration_seconds: li.duration_seconds)
      end

      # Retained bonus
      prior_retained_bonus = finalized_bonus_total(source: "crew_retention", payable: crew_member)

      share_of_retained_bonus =
        (unadjusted_share_of_bonus * @pro_pay.crew_percent * @pro_pay.crew_retention_percent) - prior_retained_bonus

      unless share_of_retained_bonus == prior_retained_bonus
        record_bonus_line_item(payable: crew_member,
                               source: "crew_retention",
                               share_of_bonus: share_of_retained_bonus,
                               labor_cost: li.labor_cost,
                               duration_seconds: li.duration_seconds)
      end
    end
  end

  def materialize_payables_line_items(rate_class:)
    payables = rate_class_pro_pay_payables(rate_class)
    participating_count = payables.count { |p| p.participating? }

    return if participating_count.zero?

    payables.each do |pp|
      ppp = find_or_new_pro_pay_payable(payable: pp.payable, rate_class: pp.rate_class)

      prior_bonus = finalized_bonus_total(source: pp.rate_class, payable: pp.payable)

      rate_class_split_bonus = @partial_sub_pro_pay.bonus / participating_count.to_f

      share_of_bonus =
        if ppp.participating?
          (rate_class_split_bonus * @pro_pay.payable_percent(rate_class: pp.rate_class)) - prior_bonus
        else
          Money.from_amount(0.0) - prior_bonus
        end

      unless share_of_bonus == prior_bonus
        record_bonus_line_item(payable: pp.payable,
                               source: pp.rate_class,
                               share_of_bonus: share_of_bonus)
      end
    end
  end

  def materialize_company_line_items
    prior_bonus = finalized_bonus_total(source: "company", payable: @sub_pro_pay.organization)
    share_of_bonus = (@partial_sub_pro_pay.bonus * @pro_pay.payable_percent(rate_class: "company")) - prior_bonus

    unless share_of_bonus == prior_bonus
      record_bonus_line_item(payable: @sub_pro_pay.organization,
                             source: "company",
                             share_of_bonus: share_of_bonus)
    end
  end

  # before materializing will ensure the payable_manager is current
  def materialize_manager_line_items(manager: @pro_pay.manager)
    # Find old manager pro_pay_payables, if one exists
    current_manager = find_payable_manager(manager)

    pro_pay_payable_managers.each do |m|
      next if m == current_manager
      m.participating = false
      m.save!
    end

    materialize_payables_line_items(rate_class: "manager")
  end

  # before materializing will ensure the crew_leads are current
  def materialize_crew_lead_line_items
    # Ensure a pro_pay_payable is found for each crew_lead
    @pro_pay_line_items.each do |li|
      next unless li.crew_lead
      find_payable_crew_lead(li.identity)
    end

    materialize_payables_line_items(rate_class: "crew_lead")
  end

  def materialize_rounding_line_items
    total_line_items = @sub_pro_pay.bonus_line_items.sum(&:bonus)
    round_amount = @partial_sub_pro_pay.bonus - total_line_items

    return if round_amount.zero?

    record_bonus_line_item(payable: @sub_pro_pay.organization,
                           source: "rounding",
                           share_of_bonus: round_amount)
  end

  def rate_class_pro_pay_payables(rate_class)
    @pro_pay_payables.select { |ppp| ppp.rate_class == rate_class }
  end

  def pro_pay_payable_managers
    @pro_pay_payables.select { |ppp| ppp.manager? }
  end

  def find_payable_manager(payable)
    find_or_new_pro_pay_payable(payable: payable, rate_class: "manager")
  end

  def find_payable_crew_member(payable)
    find_or_new_pro_pay_payable(payable: payable, rate_class: "crew")
  end

  def find_payable_crew_lead(payable)
    find_or_new_pro_pay_payable(payable: payable, rate_class: "crew_lead")
  end

  def find_or_new_pro_pay_payable(payable:, rate_class:)
    return nil unless payable

    p = @pro_pay_payables.find { |ppp| ppp.rate_class == rate_class && ppp.payable == payable }
    return p if p

    p = ProPayPayable.create!(pro_pay: @pro_pay, rate_class: rate_class, payable: payable)
    # Keep the @pro_pay_payables up to date without re-querying
    @pro_pay_payables << p
    p
  end

  def find_finalized_bonus_total_for_payable(payable:, source:)
    @finalized_bonus_totals.find { |t| t.payable == payable && t.bonus_source == source }
  end

  def finalized_bonus_total(payable:, source:)
    find_finalized_bonus_total_for_payable(source: source, payable: payable)&.total_bonus || 0
  end

  def find_bonus_line_item_for_payable(payable:, source:)
    @bonus_line_items.find { |li| li.payable == payable && li.bonus_source == source }
  end

  def record_bonus_line_item(payable:,
                             source:,
                             share_of_bonus:,
                             labor_cost: Money.from_amount(0),
                             duration_seconds: 0)
    bli = find_bonus_line_item_for_payable(payable: payable, source: source)

    date_payable = date_payable_by_source(source)

    if bli.nil?
      # no need to write new line items if they are zero
      unless share_of_bonus.zero?
        BonusLineItem.create!(partial_sub_pro_pay: @partial_sub_pro_pay,
                              payable: payable,
                              duration_seconds: duration_seconds,
                              labor_cost: labor_cost,
                              bonus_source: source,
                              bonus: share_of_bonus,
                              date_payable: date_payable)
      end
    else
      bli.update!(duration_seconds: duration_seconds,
                  labor_cost: labor_cost,
                  bonus_source: source,
                  bonus: share_of_bonus,
                  date_payable: date_payable)
    end
  end

  def date_payable_by_source(source)
    return nil unless @finalized

    Date.today +
      case source
      when "crew_retention"
        @pro_pay.crew_retention_days
      when "crew_lead"
        @pro_pay.crew_lead_retention_days
      when "manager"
        @pro_pay.manager_retention_days
      when "other"
        @pro_pay.other_retention_days
      else
        0
      end
  end
end
