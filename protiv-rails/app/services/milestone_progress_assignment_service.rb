# frozen_string_literal: true

class MilestoneProgressAssignmentService
  def initialize(milestone)
    @milestone = milestone
  end

  # Auto-assign material if the service has exactly one tracking material
  def auto_assign_material_if_possible
    return false unless should_auto_assign?

    # Find the MilestoneItem for this material on this milestone
    material = tracking_materials.first
    milestone_item = @milestone.milestone_items.find_by(catalog_item: material)

    if milestone_item
      @milestone.update!(tracked_milestone_item: milestone_item)
      true
    else
      false
    end
  end

  # Check if milestone needs material selection
  def needs_material_selection?
    return false unless @milestone.service&.progress_type == "units"
    return false if tracking_materials.count <= 1
    return false if @milestone.tracked_milestone_item.present?
    true
  end

  # Get tracking materials from service
  def tracking_materials
    return CatalogItem.none unless @milestone.service&.progress_type == "units"
    @milestone.service.tracking_materials
  end

  private

  def should_auto_assign?
    return false unless @milestone.service&.progress_type == "units"
    return false if @milestone.tracked_milestone_item.present?
    tracking_materials.count == 1
  end
end
