# frozen_string_literal: true

class Standard<PERSON>ailer
  def self.send_mail(to:, subject:, html_content:)
    mail = Mail.new
    mail.from = ENV.fetch("MAILER_FROM", "<EMAIL>")
    mail.to = to
    mail.subject = subject
    mail.html_part do
      content_type "text/html; charset=UTF-8"
      body html_content
    end

    begin
      mail.deliver
      Rails.logger.info "Email sent to #{to} using standard SMTP."
      true
    rescue => e
      Rails.logger.error "Failed to send email: #{e.message}"
      raise
    end
  end
end
