# frozen_string_literal: true

module Sync::ErrorTracking
  def self.metadata
    Bugsnag.configuration.request_data[:sync_metadata]
  end

  def self.clear!
    self.metadata = nil
  end

  def self.metadata=(value)
    Bugsnag.configuration.request_data[:sync_metadata] = value
  end

  def self.track(pairs = {})
    return yield if pairs.empty?

    metadata_was = metadata
    self.metadata = (metadata_was || {}).merge(pairs)

    yield.tap do
      # NOTE: this is _not_ to be moved to an `ensure` block -
      # if an error is raised we need the current metadata to stay where it is.
      self.metadata = metadata_was
    end
  end

  class BugsnagMiddleware
    def initialize(bugsnag)
      @bugsnag = bugsnag
    end

    def call(report)
      if (metadata = Sync::ErrorTracking.metadata.presence)
        report.add_tab(:sync_metadata, metadata)
      end

      @bugsnag.call(report)
    end
  end
end
