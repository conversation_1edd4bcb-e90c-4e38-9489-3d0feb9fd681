# frozen_string_literal: true

module Sync::Jobber
    extend Sync::CommonClassMethods

    SOURCE = "jobber"

    RESOURCE_MAP = {
      user: :identity,
      job: :job,
      visit: :milestone,
      product_or_service: :catalog_item,
      timesheet_entry: :milestone_time,
      property: :property,
      line_item: :milestone_item,
      client: :client,
      quote: :quote
    }.freeze


    def self.capabilities
      {
        jobs: [:pull],
        milestones: [:pull],
        milestone_items: [:pull],
        milestone_times: [:pull],
        identities: [:pull],
        branches: [],
        routes: [],
        clock_times: [],
        catalog_items: [:pull],
        services: [:pull],
        item_allocations: [],
        schedule_visits: [:pull],
        clients: [:pull],
        quotes: [:pull]
      }
    end

    def self.source
      SOURCE
    end

    def self.adapter
      Adapter
    end

    def self.materializer
      Materializer
    end

    def self.cache_to_resource(cache)
      # Create a simple data object from the cached raw_data
      data_object = OpenStruct.new(cache.raw_data)
      Record.new(data_object, remote_resource: cache.remote_resource.to_sym)
    end
end

Sync.register_module(Sync::<PERSON><PERSON>)
