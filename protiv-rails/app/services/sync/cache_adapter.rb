# frozen_string_literal: true

class Sync::CacheAdapter
  def initialize(adapter)
    @adapter = adapter
  end

  # FIXME: track full caches (ignore tracking for job-specific cache ingestion, for example)

  attr_reader :adapter

  def integration
    adapter.integration
  end

  def cache_all
    # sync updates to helpers
    cache_remote_helper_models

    # core models
    cache_identities
    cache_branches
    cache_catalog_items
    cache_properties
    cache_services
    cache_jobs
    cache_routes
    cache_milestones
    cache_milestone_times
    cache_schedule_visits
    cache_clock_times

    fetch_placeholders
  end

  delegate :tracking_sync, to: :integration

  def sync_caches
    integration.sync_caches
  end

  def cache_remote_helper_models
    integration.sync_statuses.helper_model.each do |sync_status|
      tracking_sync(sync_status.resource, sync_status:) do |sync_status|
        cache(remote_resource: sync_status.resource, sync_status:, updates_only: true)
      end
    end
  end

  def cache_identities(**options)
    tracking_sync(:identities) do |sync_status|
      cache(local_resource: :identities, **options, sync_status:)
    end
  end

  def cache_catalog_items(**options)
    tracking_sync(:catalog_items) do |sync_status|
      cache(local_resource: :catalog_items, **options, sync_status:)
    end
  end

  def cache_branches(**options)
    tracking_sync(:branches) do |sync_status|
      cache(local_resource: :branches, **options, sync_status:)
    end
  end

  def cache_properties(**options)
    tracking_sync(:properties) do |sync_status|
      cache(local_resource: :properties, **options, sync_status:)
    end
  end

  def cache_jobs(**options)
    tracking_sync(:jobs) do |sync_status|
      cache(local_resource: :jobs, **options, sync_status:)
    end
  end

  def cache_routes(**options)
    tracking_sync(:routes) do |sync_status|
      cache(local_resource: :routes, **options, sync_status:)
    end
  end

  def cache_services(**options)
    tracking_sync(:services) do |sync_status|
      cache(local_resource: :services, **options, sync_status:)
    end
  end

  def cache_milestones(**options)
    tracking_sync(:milestones) do |sync_status|
      cache(local_resource: :milestones, **options, sync_status:)
    end
  end

  def cache_milestone_times(**options)
    tracking_sync(:milestone_times) do |sync_status|
      cache(local_resource: :milestone_times, **options, sync_status:)
    end
  end

  def cache_schedule_visits(**options)
    tracking_sync(:schedule_visits) do |sync_status|
      cache(local_resource: :schedule_visits, **options, sync_status:)
    end
  end

  def cache_milestone_items(**options)
    tracking_sync(:milestone_items) do |sync_status|
      cache(local_resource: :milestone_items, **options, sync_status:)
    end
  end

  def cache_item_allocations(**options)
    tracking_sync(:item_allocations) do |sync_status|
      cache(local_resource: :item_allocations, **options, sync_status:)
    end
  end

  def cache_clock_times(**options)
    if adapter.ranged_clock_times?
      cache_clock_time_ranges(**options)

      if integration.initial_resource_sync_done?(:clock_times)
        integration.ensure_current_clock_time_range

        tracking_sync(:clock_times) do |sync_status|
          cache(local_resource: :clock_times, **options, sync_status:)
        end
      end
    else
      tracking_sync(:clock_times) do |sync_status|
        cache(local_resource: :clock_times, **options, sync_status:)
      end
    end
  end

  def cache_clock_time_ranges(sync_status_range: nil, **options)
    raise "adapter does not use ranged clock times" unless adapter.ranged_clock_times?

    if sync_status_range
      tracking_sync(:clock_times, sync_status: sync_status_range) do |sync_status|
        cache(
          local_resource: :clock_times,
          **options,
          sync_status: sync_status_range,
        )
      end
    else
      integration.ensure_clock_time_ranges!
      integration.sync_status_ranges.where(resource: :clock_times).pluck(:id).each do |status_range_id|
        Sync::SyncClockTimeRangeJob.perform_async(status_range_id)
      end

      [] # should return an enumerable
    end
  end

  def fetch_placeholders(search_set = nil)
    searches =
      if search_set
        return [] if search_set.empty?

        search_set.group_by(&:remote_resource).map { |resource, records| [resource, records.map(&:remote_primary_id)] }
      else
        sync_caches.placeholder.group(:remote_resource).pluck(:remote_resource, "array_agg(remote_primary_id)")
      end

    searches.flat_map do |remote_resource, ids|
      result = cache(remote_resource:, ids:)

      placeholders, caches = result.partition(&:placeholder?)

      # recursively resolve additional dependencies
      caches | fetch_placeholders(placeholders)
    end
  end

  # Asks the backend to retrieve materials for the given options,
  # and adds or updates a cache record for each result.
  def cache(local_resource: nil, remote_resource: nil, sync_status: nil, updates_only: false, **options)
    unless [local_resource, remote_resource].one?
      raise ArgumentError, "provide only one of `local_resource`, `remote_resource` (given local_resource=#{local_resource} remote_resource=#{remote_resource})"
    end

    ret = []

    # In this case, we skip this request altogether - some other process will be caching
    # the initial data set...
    if adapter.has_initial_sync_stage?(local_resource) && !sync_status&.initially_synced?
      sync_status.skip = true unless sync_status.is_a?(SyncStatusRange)

      # ... unless it's flagged as the initial sync.
      return ret unless options[:initial_sync]
    end

    local_resource ||= adapter.local_resource_for(remote_resource)

    enum = local_resource ? \
      adapter.public_send("retrieve_#{local_resource.to_s.pluralize}", sync_status:, **options) : \
      adapter.retrieve_remote(remote_resource:, sync_status:, **options)

    enum.each do |record, context|
      record.dependencies.each do |dependency_type, dependency_primary_id|
        ret << add_placeholder(dependency_type, dependency_primary_id)
      end

      record.reverse_dependencies.each do |dependency_type, dependency_primary_id|
        ret << add_placeholder(dependency_type, dependency_primary_id).tap do |placeholder|
          unless placeholder.saved_change_to_id?
            # reverse dependencies should bump their updated_at timestamp
            # so they get re-materialized
            placeholder.touch
          end
        end
      end

      record.embedded_dependencies.each do |remote_resource, remote_primary_id, remote_record|
        cache_record = sync_caches.where(
          source: record.source,
          remote_resource:,
          remote_primary_id:
        ).first_or_initialize

        raw_data = cache_record.raw_data || {}
        # Updates may come from multiple sources
        raw_data.merge!(remote_record.as_json.compact_blank)
        cache_record.raw_data = raw_data
        cache_record.save if cache_record.changed?
      end

      cache_record = sync_caches.where(
        source: record.source,
        remote_resource: record.remote_resource,
        remote_primary_id: record.primary_id
      ).first_or_initialize

      next if cache_record.new_record? && updates_only

      cache_record.raw_data = record.as_json

      if cache_record.changed?
        cache_record.save!
        ret << cache_record
      end
    end

    ret
  end

  private

  def add_placeholder(remote_resource, remote_primary_id)
    sync_caches.where(
      source: adapter.source,
      remote_resource:,
      remote_primary_id:,
    ).first_or_create!
  end
end
