# frozen_string_literal: true

class Sync::Current < ActiveSupport::CurrentAttributes
  attribute :uuid
  attribute :integration_id

  def self.track(requires_new: false, with_uuid: nil, integration_id: nil)
    uuid_was = uuid
    integration_id_was = self.integration_id

    if uuid.nil? || requires_new || with_uuid
      with_uuid ||= new_uuid
      self.uuid = with_uuid || new_uuid
    end

    if integration_id
      self.integration_id = integration_id
    end

    yield
  ensure
    self.uuid = uuid_was
    self.integration_id = integration_id_was
  end

  def self.tracking?
    uuid.present?
  end

  def self.new_uuid
    SecureRandom.uuid_v7
  end
end
