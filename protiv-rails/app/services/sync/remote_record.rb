# frozen_string_literal: true

class Sync::RemoteRecord
  def initialize(model, additional_attributes = {})
    @model = model

    @source = additional_attributes[:source]
    @remote_resource = additional_attributes[:remote_resource]
    @primary_id = additional_attributes[:primary_id]
  end

  attr_accessor :source, :remote_resource, :primary_id
  attr_reader :model

  def add_dependency(remote_resource, remote_primary_id)
    dependencies << [remote_resource, remote_primary_id]
  end

  def add_reverse_dependency(remote_resource, remote_primary_id)
    reverse_dependencies << [remote_resource, remote_primary_id]
  end

  def add_embedded_dependency(remote_resource, remote_primary_id, remote_record)
    embedded_dependencies << [remote_resource, remote_primary_id, remote_record]
  end

  def dependencies
    @dependencies ||= []
  end

  def reverse_dependencies
    @reverse_dependencies ||= []
  end

  def embedded_dependencies
    @embedded_dependencies ||= []
  end

  def as_json
    model.as_json
  end
end
