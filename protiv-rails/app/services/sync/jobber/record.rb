# frozen_string_literal: true

module Sync
  module <PERSON><PERSON>
    class Record
      attr_reader :data, :remote_resource

      def initialize(data, remote_resource:)
        @data = data
        @remote_resource = remote_resource
        @dependencies = {}
        @reverse_dependencies = {}
        @embedded_dependencies = []
      end

      def id
        if data.respond_to?(:id)
          data.id
        elsif data.is_a?(Hash)
          data['id'] || data[:id]
        else
          raise "Unable to extract ID from data: #{data.class}"
        end
      end

      def primary_id
        id
      end

      def source
        Sync::Jobber.source
      end

      def updated_at
        if data.respond_to?(:updated_at)
          data.updated_at
        elsif data.is_a?(Hash)
          data['updated_at'] || data[:updated_at] || data['updatedAt'] || data[:updatedAt]
        else
          nil
        end
      end

      def as_json
        if data.respond_to?(:as_json)
          data.as_json
        elsif data.is_a?(Hash)
          data
        elsif data.respond_to?(:to_h)
          data.to_h
        else
          # Fallback: try to convert to hash or return as-is
          data.respond_to?(:to_hash) ? data.to_hash : data
        end
      end

      # Dependency management methods for cache adapter integration
      def dependencies
        @dependencies
      end

      def reverse_dependencies
        @reverse_dependencies
      end

      def embedded_dependencies
        @embedded_dependencies
      end

      def add_dependency(remote_resource, remote_primary_id)
        @dependencies[remote_resource] = remote_primary_id
      end

      def add_reverse_dependency(remote_resource, remote_primary_id)
        @reverse_dependencies[remote_resource] = remote_primary_id
      end

      def add_embedded_dependency(remote_resource, remote_primary_id, embedded_record)
        @embedded_dependencies << [remote_resource, remote_primary_id, embedded_record]
      end

      def method_missing(method_name, *args, &block)
        if data.respond_to?(method_name)
          data.public_send(method_name, *args, &block)
        elsif data.is_a?(Hash)
          # Try different key formats for Hash access
          key_variants = [
            method_name.to_s,
            method_name.to_sym,
            method_name.to_s.camelize(:lower), # camelCase
            method_name.to_s.camelize          # PascalCase
          ]

          key_variants.each do |key|
            return data[key] if data.key?(key)
          end

          super
        else
          super
        end
      end

      def respond_to_missing?(method_name, include_private = false)
        if data.respond_to?(method_name)
          true
        elsif data.is_a?(Hash)
          key_variants = [
            method_name.to_s,
            method_name.to_sym,
            method_name.to_s.camelize(:lower),
            method_name.to_s.camelize
          ]
          key_variants.any? { |key| data.key?(key) }
        else
          super
        end
      end
    end
  end
end
