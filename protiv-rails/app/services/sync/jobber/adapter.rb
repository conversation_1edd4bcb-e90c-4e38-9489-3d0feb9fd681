# frozen_string_literal: true

require "jobber_client"

module Sync
  module Jobber
    ##
    # Adapter for Jobber integration using OAuth 2.0 authentication.
    #
    # This adapter handles data retrieval from Jobber's GraphQL API and manages
    # OAuth token refresh automatically. It follows the OAuth integration pattern
    # established by the JobberOauthService.
    #
    # @example Basic usage
    #   adapter = Sync::Jobber::Adapter.new(integration)
    #   jobs = adapter.retrieve_jobs(limit: 50)
    #   users = adapter.retrieve_users
    class Adapter < Sync::Abstract::Adapter
      # Default limit for fetching records when no specific limit is provided
      DEFAULT_FETCH_LIMIT = 10000

      # How far back from the latest record should we sync for safety overlap
      SYNC_OVERLAP_SAFETY = 5.minutes

      ##
      # Returns the source identifier for this adapter.
      #
      # @return [String] The source identifier
      def source
        Sync::Jobber.source
      end

      ##
      # Returns the remote helper models for this adapter.
      #
      # @return [Array] Array of remote helper model symbols
      def remote_helper_models
        [] # Jobber doesn't have remote helper models like Aspire
      end

      ##
      # Maps a remote resource to its corresponding local resource.
      #
      # @param remote_resource [Symbol] The remote resource type
      # @return [Symbol] The corresponding local resource type
      def local_resource_for(remote_resource)
        Sync::Jobber::RESOURCE_MAP[remote_resource.to_sym]
      end

      ##
      # Maps a local resource to its corresponding remote resource.
      #
      # @param local_resource [Symbol] The local resource type
      # @return [Symbol] The corresponding remote resource type
      def remote_resource_for(local_resource)
        Sync::Jobber::RESOURCE_MAP.invert.fetch(local_resource.to_sym)
      end

      ##
      # Generic remote resource retrieval method.
      #
      # This method provides a unified interface for retrieving any remote resource
      # type and is used by the cache adapter.
      #
      # @param remote_resource [Symbol] The remote resource type to retrieve
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Enumerator of retrieved records
      def retrieve_remote(remote_resource:, **options)
        retrieve_method = "retrieve_#{remote_resource.to_s.pluralize}"

        if respond_to?(retrieve_method)
          send(retrieve_method, **options)
        else
          # Fallback to generic _retrieve method
          _retrieve(
            remote_resource: remote_resource,
            **options
          )
        end
      end
      ##
      # Retrieves jobs from Jobber API.
      #
      # @param limit [Integer] Maximum number of jobs to retrieve (default: DEFAULT_FETCH_LIMIT)
      # @param filter [Hash] GraphQL filter attributes
      # @param sort [Array] GraphQL sort attributes
      # @param search_term [String] Search term for filtering
      # @return [Enumerator] Enumerator of job records
      def retrieve_jobs(limit: DEFAULT_FETCH_LIMIT, filter: nil, sort: nil, search_term: nil, **options)
        _retrieve(
          remote_resource: :job,
          limit: limit,
          filter: filter,
          sort: sort,
          search_term: search_term,
          updated_records_filter: nil, # Jobs don't support updated_at filtering - full sync only
          **options
        )
      end

      ##
      # Retrieves visits from Jobber API.
      #
      # @param job_id [String, nil] Filter visits by job ID
      # @param limit [Integer] Maximum number of visits to retrieve (default: DEFAULT_FETCH_LIMIT)
      # @param filter [Hash] GraphQL filter attributes
      # @param sort [Array] GraphQL sort attributes
      # @return [Enumerator] Enumerator of visit records
      def retrieve_visits(job_id: nil, limit: DEFAULT_FETCH_LIMIT, filter: nil, sort: nil, **options)
        _retrieve(
          remote_resource: :visit,
          job_id: job_id,
          limit: limit,
          filter: filter,
          sort: sort,
          updated_records_filter: nil,
          **options
        )
      end

      def retrieve_all_visits(limit: DEFAULT_FETCH_LIMIT, filter: nil, sort: nil, **options)
        _retrieve(
          remote_resource: :visit,
          limit: limit,
          filter: filter,
          sort: sort,
          updated_records_filter: nil,
          **options
        )
      end

      ##
      # Retrieves users from Jobber API.
      #
      # @param limit [Integer] Maximum number of users to retrieve (default: DEFAULT_FETCH_LIMIT)
      # @param filter [Hash] GraphQL filter attributes
      # @return [Enumerator] Enumerator of user records
      def retrieve_users(limit: DEFAULT_FETCH_LIMIT, filter: nil, **options)
        _retrieve(
          remote_resource: :user,
          limit: limit,
          filter: filter,
          updated_records_filter: nil, # Users don't support updated_at filtering - full sync only
          **options
        )
      end

      ##
      # Retrieves clients from Jobber API.
      #
      # @param limit [Integer] Maximum number of clients to retrieve (default: 100)
      # @param filter [Hash] GraphQL filter attributes
      # @param sort [Hash] GraphQL sort attributes
      # @param search_term [String] Search term for filtering
      # @return [Enumerator] Enumerator of client records
      def retrieve_clients(limit: 100, filter: nil, sort: nil, search_term: nil, **options)
        _retrieve(
          remote_resource: :client,
          limit: limit,
          filter: filter,
          sort: sort,
          search_term: search_term,
          updated_records_filter: ->(date) { { filter: { updatedAt: { after: date.iso8601 } } } },
          **options
        )
      end

      ##
      # Retrieves properties from Jobber API.
      #
      # Note: Properties query only supports pagination - no filter or sort parameters
      #
      # @param client_id [String, nil] Filter properties by client ID
      # @param limit [Integer] Maximum number of properties to retrieve (default: 100)
      # @return [Enumerator] Enumerator of property records
      def retrieve_properties(client_id: nil, limit: 100, **options)
        _retrieve(
          remote_resource: :property,
          client_id: client_id,
          limit: limit,
          updated_records_filter: nil,
          **options
        )
      end

      ##
      # Retrieves products and services from Jobber API.
      #
      # Note: Products query supports filter but not sort parameters
      #
      # @param limit [Integer] Maximum number of products to retrieve (default: 100)
      # @param filter [Hash] GraphQL filter attributes
      # @return [Enumerator] Enumerator of product/service records
      def retrieve_products_and_services(limit: 100, filter: nil, **options)
        _retrieve(
          remote_resource: :product_or_service,
          limit: limit,
          filter: filter,
          updated_records_filter: nil,
          **options
        )
      end

      ##
      # Retrieves line items from Jobber API.
      #
      # @param job_id [String] Filter line items by job ID (required)
      # @param limit [Integer] Maximum number of line items to retrieve (default: 100)
      # @return [Enumerator] Enumerator of line item records
      def retrieve_line_items(job_id:, limit: 100, **options)
        _retrieve(
          remote_resource: :line_item,
          job_id: job_id,
          limit: limit,
          updated_records_filter: nil,
          **options
        )
      end

      ##
      # Retrieves timesheet entries from Jobber API with adaptive retry.
      #
      # @param job_id [String, nil] Filter entries by job ID
      # @param limit [Integer] Maximum number of entries to retrieve (default: 100)
      # @param filter [Hash] GraphQL filter attributes
      # @param sort [Array] GraphQL sort attributes
      # @return [Enumerator] Enumerator of timesheet entry records
      def retrieve_timesheet_entries(job_id: nil, limit: 100, filter: nil, sort: nil, **options)
        _retrieve(
          remote_resource: :timesheet_entry,
          job_id: job_id,
          limit: limit,
          filter: filter,
          sort: sort,
          updated_records_filter: ->(date) { { filter: { updatedAt: { after: date.iso8601 } } } },
          **options
        )
      end

      ##
      # Retrieves quotes from Jobber API with adaptive retry.
      #
      # Note: Quotes query only supports pagination - no filter or sort parameters
      # Used for budget information context and historical scope tracking
      #
      # @param limit [Integer] Maximum number of quotes to retrieve (default: 100)
      # @return [Enumerator] Enumerator of quote records
      def retrieve_quotes(limit: 100, **options)
        _retrieve(
          remote_resource: :quote,
          limit: limit,
          updated_records_filter: nil, # Quotes don't support updated_at filtering - full sync only
          **options
        )
      end

      def retrieve_jobs_linked_product_or_services(**options)
        _retrieve(
          remote_resource: :job_linked_product_or_service,
          limit: limit,
          updated_records_filter: nil, # Jobs don't support updated_at filtering - full sync only
          **options
        )
      end

      ##
      # Retrieve methods for local resource names (required by cache adapter)
      #

      ##
      # Retrieves identities (users) - alias for retrieve_users to match local resource name.
      #
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Enumerator of user records
      def retrieve_identities(**options)
        retrieve_users(**options)
      end

      ##
      # Retrieves milestones (visits) - alias for retrieve_visits to match local resource name.
      #
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Enumerator of visit records
      def retrieve_milestones(**options)
        retrieve_visits(**options)
      end

      ##
      # Retrieves catalog_items (products/services) - alias for retrieve_products_and_services.
      #
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Enumerator of product/service records
      def retrieve_catalog_items(**options)
        retrieve_products_and_services(**options)
      end

      ##
      # Retrieves milestone_times (timesheet_entries) - alias for retrieve_timesheet_entries.
      #
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Enumerator of timesheet entry records
      def retrieve_milestone_times(**options)
        retrieve_timesheet_entries(**options)
      end

      ##
      # Retrieves milestone_items (line_items) - alias for retrieve_line_items.
      #
      # Note: This method is used by the cache adapter. When called without job_id,
      # it will raise an error since line items require a job_id.
      #
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Array of line item records
      def retrieve_milestone_items(**options)
        unless options[:job_id]
          raise ArgumentError, "retrieve_milestone_items requires job_id parameter. Use cache_line_items with job_ids instead."
        end
        retrieve_line_items(**options)
      end


      ##
      # Generic data retrieval method following Aspire adapter pattern.
      #
      # This method provides a unified interface for retrieving data from Jobber's
      # GraphQL API with support for filtering, sorting, pagination, and dependency resolution.
      # It also supports caching integration through sync_status.
      #
      # @param options [Hash] Additional options for the retrieval
      # @return [Enumerator] Enumerator of retrieved records
      def _retrieve(**options)
        Retrieve.new(self, **options).execute
      end

      ##
      # Checks if credentials need refreshing and refreshes if necessary.
      #
      # This method is called automatically by the client method but can also
      # be called manually to force a credential refresh.
      #
      # @return [void]
      def maybe_refresh_credential
        return unless credential_needs_refresh?

        refresh_credential
      end

      private

      ##
      # Returns a configured Jobber client with valid authentication and rate limiting.
      #
      # Automatically refreshes credentials if they are expired or missing.
      # Enables rate limiting with the integration ID for proper throttling.
      #
      # @return [JobberClient::Client] Configured client instance
      def client
        @client ||= begin
          maybe_refresh_credential
          JobberClient::Client.new(
            credential.bearer_token,
            integration_id: integration.id.to_s,
          )
        end
      end

      ##
      # Refreshes OAuth credentials using the JobberOauthService.
      #
      # This method handles the complete token refresh flow and updates
      # the credential record with new tokens. It clears the cached client
      # to ensure the next request uses the new tokens.
      #
      # @raise [Sync::Errors::CredentialError] If token refresh fails
      # @return [void]
      def refresh_credential
        Rails.logger.info("Jobber Adapter: Refreshing OAuth credentials for integration #{integration.id}")

        oauth_service = JobberOauthService.new
        result = oauth_service.refresh_integration_token(integration)

        Rails.logger.info(result)

        unless result[:success]
          Rails.logger.error("Jobber Adapter: Token refresh failed - #{result[:error]}")
          raise Sync::Errors::CredentialError, result[:error]
        end

        # Update the credential with new tokens
        credential_record = credential || integration.build_credential
        credential_record.update!(
          bearer_token: result[:credential].bearer_token,
          refresh_token: result[:credential].refresh_token,
          expires_at: result[:credential].expires_at
        )

        # Update cached credential and clear client
        @credential = credential_record
        @client = nil

        Rails.logger.info("Jobber Adapter: OAuth credentials refreshed successfully")
      end

      ##
      # Determines if credentials need to be refreshed.
      #
      # Credentials are considered in need of refresh if:
      # - No credential exists
      # - The credential has no bearer token
      # - The credential expires within the next hour
      #
      # @return [Boolean] true if credentials need refresh, false otherwise
      def credential_needs_refresh?
        credential.blank? ||
          credential.bearer_token.blank? ||
          credential.expires_at&.<(Time.now.utc)
      end

      ##
      # Retrieve class for handling data retrieval from Jobber's GraphQL API.
      #
      # This class follows the same pattern as Aspire's Retrieve class but is adapted
      # for GraphQL queries with cursor-based pagination and Jobber-specific parameters.
      class Retrieve
        # Mapping of remote resources to client method names
        RESOURCE_TO_METHOD_MAP = {
          job: :fetch_jobs,
          visit: :fetch_visits,
          user: :fetch_users,
          client: :fetch_clients,
          property: :fetch_properties,
          line_item: :fetch_line_items,
          timesheet_entry: :fetch_timesheet_entries,
          product_or_service: :fetch_products_or_services,
          quote: :fetch_quotes,
          job_linked_product_or_service: :fetch_jobs_linked_product_or_services
        }.freeze

        def initialize(
          adapter,
          remote_resource:,
          limit: nil,
          filter: nil,
          sort: nil,
          search_term: nil,
          resolve_dependencies: {},
          reverse_dependencies: {},
          embedded_dependencies: {},
          updated_records_filter: nil,
          sync_status: nil,
          ids: nil,
          **options
        )
          @adapter = adapter
          @remote_resource = remote_resource.to_s
          @limit = limit
          @filter = filter
          @sort = sort
          @search_term = search_term
          @resolve_dependencies = resolve_dependencies
          @reverse_dependencies = reverse_dependencies
          @embedded_dependencies = embedded_dependencies
          @updated_records_filter = updated_records_filter
          @sync_status = sync_status
          @ids = ids
          @options = options
        end

        attr_reader \
          :adapter,
          :remote_resource,
          :limit,
          :filter,
          :sort,
          :search_term,
          :resolve_dependencies,
          :reverse_dependencies,
          :embedded_dependencies,
          :updated_records_filter,
          :sync_status,
          :ids,
          :options

        def last_synced_at
          sync_status&.last_synced_at
        end

        def initial_sync?
          last_synced_at.blank?
        end

        def execute
          effective_limit = limit || DEFAULT_FETCH_LIMIT

          response = retrieve(effective_limit)

          # Return an enumerable that yields [record, context] pairs for cache adapter compatibility
          Enumerator.new do |yielder|
            response.each do |record_data|
              wrapper = Record.new(record_data, remote_resource: remote_resource.to_sym)
              apply_dependencies(wrapper, record_data)
              # Yield record with nil context (context not used in Jobber integration)
              yielder << [wrapper, nil]
            end
          end
        end

        private

        def client
          adapter.send(:client)
        end

        ##
        # Universal retrieve method that dynamically calls the appropriate client method.
        #
        # @param limit [Integer] The limit to use for the request
        # @return [Array<Hash>] Array of retrieved records
        def retrieve(limit)
          method_name = RESOURCE_TO_METHOD_MAP[remote_resource.to_sym]

          unless method_name
            raise ArgumentError, "Unknown remote resource: #{remote_resource}"
          end

          # Build parameters hash with all the options, let the client method handle parameter building
          params = {
            limit: limit,
            filter: filter,
            sort: sort,
            search_term: search_term
          }.merge(options).compact

          # Add incremental sync support
          if !initial_sync? && updated_records_filter && last_synced_at
            # Apply safety overlap like Aspire adapter
            date_with_overlap = last_synced_at - SYNC_OVERLAP_SAFETY
            incremental_filter = updated_records_filter.call(date_with_overlap)
            params.merge!(incremental_filter)
          end

          client.public_send(method_name, **params)
        end

        def apply_dependencies(wrapper, data)
          resolve_dependencies.each do |remote_resource, key|
            if data.is_a?(Hash) && (value = data[key.to_s])
              wrapper.add_dependency(remote_resource, value)
            elsif data.respond_to?(key) && (value = data.public_send(key))
              wrapper.add_dependency(remote_resource, value)
            end
          end

          reverse_dependencies.each do |remote_resource, key|
            if data.is_a?(Hash) && (value = data[key.to_s])
              wrapper.add_reverse_dependency(remote_resource, value)
            elsif data.respond_to?(key) && (value = data.public_send(key))
              wrapper.add_reverse_dependency(remote_resource, value)
            end
          end

          embedded_dependencies.each do |remote_resource, (key, mapper)|
            primary_key = if data.is_a?(Hash)
              data[key.to_s]
            elsif data.respond_to?(key)
              data.public_send(key)
            end

            if primary_key && mapper.respond_to?(:call)
              if (embedded_record = mapper.call(data))
                wrapper.add_embedded_dependency(remote_resource, primary_key, embedded_record)
              end
            end
          end
        end
      end
    end
  end
end
