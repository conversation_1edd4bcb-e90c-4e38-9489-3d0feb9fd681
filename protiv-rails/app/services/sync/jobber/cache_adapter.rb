# frozen_string_literal: true

module Sync
  module <PERSON>ber
    ##
    # Cache adapter for Jobber integration.
    #
    # This adapter handles caching of Jobber API data following the same pattern
    # as the Aspire cache adapter. It provides methods to cache different resource
    # types and manages incremental sync operations.
    #
    # @example Basic usage
    #   cache_adapter = Sync::Jobber::CacheAdapter.new(integration)
    #   cache_adapter.cache_all
    #   cache_adapter.cache_jobs(limit: 100)
    class CacheAdapter < Sync::CacheAdapter
      ##
      # Initializes a new Jobber cache adapter.
      #
      # @param integration [Integration] The integration instance
      def initialize(integration)
        @integration = integration
        super(integration.adapter)
      end

      ##
      # Caches all Jobber data types in the correct order.
      #
      # This method overrides the base cache_all to handle Jobber-specific
      # resource types and dependencies.
      def cache_all
        cache_remote_helper_models

        cache_users
        cache_clients
        cache_properties
        cache_products_and_services
        cache_quotes
        cache_jobs
        cache_visits
        cache_line_items
        cache_timesheet_entries

        fetch_placeholders
      end

      ##
      # Caches Jobber users (identities).
      #
      # @param options [Hash] Additional options for caching
      # @return [Array<Sync::Cache>] Array of cached records
      def cache_users(**options)
        tracking_sync(:users) do |sync_status|
          cache(remote_resource: :user, **options, sync_status:)
        end
      end

      def cache_clients(**options)
        tracking_sync(:clients) do |sync_status|
          cache(remote_resource: :client, **options, sync_status:)
        end
      end

      def cache_properties(**options)
        tracking_sync(:properties) do |sync_status|
          cache(remote_resource: :property, **options, sync_status:)
        end
      end

      def cache_products_and_services(**options)
        tracking_sync(:products_and_services) do |sync_status|
          cache(remote_resource: :product_or_service, **options, sync_status:)
        end
      end

      def cache_quotes(**options)
        tracking_sync(:quotes) do |sync_status|
          cache(remote_resource: :quote, **options, sync_status:)
        end
      end

      def cache_jobs(**options)
        tracking_sync(:jobs) do |sync_status|
          cache(remote_resource: :job, **options, sync_status:)
        end
      end

      def cache_visits(**options)
        tracking_sync(:visits) do |sync_status|
          cache(remote_resource: :visit, **options, sync_status:)
        end
      end

      def cache_line_items(job_ids: nil, **options)
        job_ids ||= integration.sync_caches.jobber_jobs.pluck(:remote_primary_id)

        return [] if job_ids.empty?

        all_cached_items = []

        # Cache line items for each job individually
        job_ids.each do |job_id|
          tracking_sync("line_items_job_#{job_id}".to_sym) do |sync_status|
            cached_items = cache(
              remote_resource: :line_item,
              job_id: job_id,
              sync_status: sync_status,
              **options
            )
            all_cached_items.concat(cached_items)
          end
        end

        all_cached_items
      end

      def cache_line_items_for_job(job_id:, **options)
        tracking_sync("line_items_job_#{job_id}".to_sym) do |sync_status|
          cache(
            remote_resource: :line_item,
            job_id: job_id,
            sync_status: sync_status,
            **options
          )
        end
      end

      def cache_timesheet_entries(**options)
        tracking_sync(:timesheet_entries) do |sync_status|
          cache(remote_resource: :timesheet_entry, **options, sync_status:)
        end
      end

      private

      def integration
        @integration
      end
    end
  end
end
