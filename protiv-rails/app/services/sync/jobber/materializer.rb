# frozen_string_literal: true

module Sync
  module <PERSON>ber
    ##
    # Materializer for Jobber integration data.
    #
    # This class handles the conversion of Jobber API data into local Protiv
    # database records. It follows the same pattern as other sync materializers
    # and works in conjunction with the Jobber adapter.
    #
    # @example Basic usage
    #   materializer = Sync::Jobber::Materializer.new(integration)
    #   job = materializer.materialize_job(remote_job_record)
    #   user = materializer.materialize_user(remote_user_record)
    class Materializer < Sync::Abstract::Materializer
      ##
      # Materializes all Jobber data into local Protiv records.
      #
      # @return [void]
      def materialize_all
        materialize_jobs
      end

      ##
      # Materializes a Jobber job record into a local Job record.
      #
      # @param remote_job [Sync::Jobber::Record] Remote job data from Jobber API
      # @return [Job] Materialized local job record
      def materialize_job(remote_job)
        raise "missing job id" if remote_job.id.nil?
        raise "not in syncing context" if Sync::Current.integration_id.nil?

        remote_slug = remote_slug(remote_job.id, :job)

        track_error(remote_slug:) do
          # Find existing job by remote_slug
          job = integration_records.where(remote_slug:).last&.record
          job ||= Job.new(
            organization_id: organization.id,
            remote_slug: remote_slug
          )

          # Extract client name - handle nested structure
          client_name = nil
          if remote_job.client.present?
            client_name = remote_job.client.is_a?(Hash) ? remote_job.client["name"] : remote_job.client&.name
          end

          job.assign_attributes(
            name: remote_job.title,
            job_number: remote_job.jobNumber.to_s,
            status: map_job_status(remote_job.jobStatus),
            job_type: map_job_type(remote_job.jobType),
            created_date: parse_date(remote_job.startAt) || parse_date(remote_job.createdAt),
            last_activity_at: parse_date(remote_job.updatedAt),
            remote_updated_at: parse_date(remote_job.updatedAt)
          )

          # Associate with property if available
          if remote_job.property.present?
            property_id = remote_job.property.is_a?(Hash) ? remote_job.property["id"] : remote_job.property&.id
            if property_id.present?
              job.property_id = local_id(remote_id: property_id, remote_resource: :property)
            end
          end

          job.save! if job.changed?
          job
        end
      end

      # TODO: Fix these methods to follow the same pattern as materialize_job
      # For now, commenting out to focus on jobs only

      # def materialize_visit(remote_visit, job_id)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_visit not yet implemented"
      # end

      # def materialize_user(remote_user)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_user not yet implemented"
      # end

      # def materialize_client(remote_client)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_client not yet implemented"
      # end

      # def materialize_property(remote_property)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_property not yet implemented"
      # end

      # def materialize_product_or_service(remote_product)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_product_or_service not yet implemented"
      # end

      # def materialize_line_item(remote_line_item, milestone_id)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_line_item not yet implemented"
      # end

      # def materialize_timesheet_entry(remote_entry, milestone_id)
      #   # TODO: Implement following the Aspire pattern
      #   raise "materialize_timesheet_entry not yet implemented"
      # end

      private

      def source
        Sync::Jobber.source
      end

      def local_id(remote_id:, remote_resource:, materialize: true)
        return nil unless remote_id.present?

        remote_slug = remote_slug(remote_id, remote_resource)
        remote_index.fetch(remote_slug) do
          if (id = integration_records.where(remote_slug:).pluck(:record_id).first)
            remote_index[remote_slug] = id
          else
            # For now, return nil if not found - we can add materialization later
            nil
          end
        end
      end

      def parse_date(date_string)
        return nil if date_string.blank?

        case date_string
        when String
          Time.parse(date_string)
        when Time, DateTime
          date_string
        else
          nil
        end
      rescue ArgumentError
        nil
      end

      def map_job_status(jobber_status)
        case jobber_status
        when "ACTIVE", "IN_PROGRESS"
          :in_progress
        when "COMPLETED"
          :completed
        when "CANCELED"
          :canceled
        else
          :pending
        end
      end

      def map_job_type(jobber_type)
        case jobber_type
        when "ONE_OFF"
          "non_recurring"
        when "RECURRING"
          "recurring"
        else
          "unknown"
        end
      end

      def map_visit_status(jobber_status)
        case jobber_status
        when "SCHEDULED", "TRAVELING", "IN_PROGRESS"
          :in_progress
        when "COMPLETED"
          :completed
        when "CANCELED"
          :canceled
        else
          :pending
        end
      end

      def map_item_type(jobber_category)
        case jobber_category
        when "LABOR", "SERVICE"
          "Labor"
        when "PRODUCT", "MATERIAL"
          "Material"
        else
          "Other"
        end
      end
    end
  end
end
