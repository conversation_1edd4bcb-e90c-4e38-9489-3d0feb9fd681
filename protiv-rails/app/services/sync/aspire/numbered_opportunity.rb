# frozen_string_literal: true

class Sync::Aspire::NumberedOpportunity
  # NOTE: duck types AspireRecord

  def initialize(opportunity_number, *)
    @opportunity_number = opportunity_number
  end

  attr_reader :opportunity_number

  def source
    Sync::Aspire::SOURCE
  end

  def primary_id
    opportunity_number
  end

  def remote_resource
    "numbered_opportunity"
  end

  def as_json(*)
    { "OpportunityNumber" => opportunity_number }
  end

  def model
    self
  end

  def dependencies
    []
  end

  def reverse_dependencies
    []
  end

  def embedded_dependencies
    []
  end
end
