# frozen_string_literal: true

class Sync::Aspire::Materializer < Sync::Abstract::Materializer
  include Skylight::Helpers
  include Sync::Aspire::SqlHelpers

  def materialize_all
    materialize_identities
    materialize_branches
    materialize_catalog_items # Ensure catalog items are materialized
    materialize_properties
    materialize_services
    materialize_routes
    materialize_jobs

    if integration.initial_sync_done?
      materialize_milestones
      materialize_milestone_times
      materialize_milestone_items
      materialize_clock_times
      materialize_item_allocations
      materialize_schedule_visits
    end
  end

  def remote_resource(protiv_resource)
    Sync::Aspire::RESOURCE_MAP.invert.fetch(protiv_resource.to_s.singularize.to_sym, nil)
  end

  def materialize_clock_times(caches = nil, **options)
    # caches are required when syncing by status range
    return unless caches

    tracking_sync(:materialize_clock_times, sync_status: false) do
      caches.select { |x| x.remote_resource.to_s == "clock_time" }.each do |clock_time|
        materialize_clock_time(clock_time.to_resource, caches:)
      end
    end
  end

  set_callback :materialize_routes, :after, :sync_crew_leads
  set_callback :materialize_routes, :after, :sync_route_managers

  def source
    Sync::Aspire.source
  end

  private

  def unmaterialized_milestones(last_synced_at)
    opportunity_service_ids = unmaterialized_opportunity_services(last_synced_at).pluck(:remote_primary_id)
    base = sync_caches.cached.aspire_work_tickets
    # FIXME: is there a better way to handle reverse dependencies?
    base.last_synced_after(last_synced_at).or(base.where(OPPORTUNITY_SERVICE_ID_IN, opportunity_service_ids))
  end

  def unmaterialized_opportunity_services(last_synced_at)
    sync_caches.cached.aspire_opportunity_services.last_synced_after(last_synced_at)
  end

  def unmaterialized_numbered_opportunities(last_synced_at)
    sync_caches.cached.aspire_numbered_opportunities.last_synced_after(last_synced_at)
  end

  def unmaterialized_schedule_visits(last_synced_at)
    result = super
    puts "DEBUG: unmaterialized_schedule_visits count: #{result.count}"
    result
  end

  alias_method(:unmaterialized_jobs, :unmaterialized_numbered_opportunities)

  instrument_method

  def materialize_identity(contact, caches: nil, **)
    raise "missing contact_id" if (contact_id = contact.contact_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(contact_id, :contact)
    track_error(remote_slug:) do
      # First check if we already have an integration record for this contact
      # This helps avoid race conditions with duplicate contacts
      integration_record = IntegrationRecord.find_by(
        integration_id: Sync::Current.integration_id,
        remote_slug: remote_slug
      )

      if integration_record && integration_record.record_type == "Identity" && integration_record.record_id.present?
        # If we already have an integration record, use that identity
        begin
          identity = Identity.find(integration_record.record_id)
          Rails.logger.info("Found existing identity #{identity.id} for contact #{contact_id}")
        rescue ActiveRecord::RecordNotFound
          # If the identity doesn't exist anymore, we'll create a new one below
          Rails.logger.warn("Integration record exists for contact #{contact_id} but identity #{integration_record.record_id} not found")
          integration_record = nil
        end
      end

      # If we don't have an integration record or the identity wasn't found, look up by remote_id
      if integration_record.nil?
        identity_id = local_id(remote_id: contact.contact_id, remote_resource: :contact, materialize: false)
        identity = Identity.find(identity_id) if identity_id
      end

      # If we still don't have an identity, create a new one
      identity ||= Identity.new(remote_slug:, organization_id: organization.id)
      identity.email = contact.email
      identity.name = [contact.last_name, contact.first_name].compact.join(", ").presence

      # FIXME: what are these
      identity.other_id = contact.contact_type_id
      identity.other_id_description = contact.contact_type_name

      # Only save if there are changes to avoid unnecessary database writes
      identity.save! if identity.changed?

      # Update our remote index
      remote_index[remote_slug] = identity.id

      # Ensure we have an integration record
      begin
        IntegrationRecord.find_or_create_by!(
          integration_id: Sync::Current.integration_id,
          remote_slug: remote_slug,
          record: identity
        )
      rescue ActiveRecord::RecordNotUnique => e
        # If we hit a race condition, just log it and continue
        Rails.logger.warn("Duplicate integration record detected for contact #{contact_id}: #{e.message}")
      end

      identity
    end
  end

  instrument_method

  def materialize_branch(aspire_branch)
    raise "missing branch_id" if (branch_id = aspire_branch.branch_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(branch_id, :branch)
    track_error(remote_slug:) do
      branch_id = local_id(remote_id: aspire_branch.branch_id, remote_resource: :branch, materialize: false)
      branch = Branch.find(branch_id) if branch_id
      branch ||= Branch.new(remote_slug:, organization_id: organization.id)
      branch.name = aspire_branch.branch_name
      branch.address_line_1 = aspire_branch.branch_address_line1
      branch.address_line_2 = aspire_branch.branch_address_line2
      branch.address_city = aspire_branch.branch_address_city
      branch.address_state_province = aspire_branch.branch_address_state_province_code
      branch.address_zip_code = aspire_branch.branch_address_zip_code
      branch.legal_name = aspire_branch.legal_name
      branch.save! if branch.changed?

      remote_index[remote_slug] = branch.id
    end
  end

  instrument_method

  def materialize_property(aspire_property)
    raise "missing property_id" if (property_id = aspire_property.property_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(property_id, :property)
    track_error(remote_slug:) do
      property_id = local_id(remote_id: aspire_property.property_id, remote_resource: :property, materialize: false)
      property = Property.find(property_id) if property_id
      property ||= Property.new(remote_slug:, organization_id: organization.id)
      property.name = aspire_property.property_name
      property.address_line_1 = aspire_property.property_address_line1
      property.address_line_2 = aspire_property.property_address_line2
      property.address_city = aspire_property.property_address_city
      property.address_state_province = aspire_property.property_address_state_province_code
      property.address_zip_code = aspire_property.property_address_zip_code

      if (contact_id = aspire_property.primary_contact_id)
        property.primary_contact_id = local_id(remote_id: contact_id, remote_resource: :contact)
      else
        property.primary_contact_id = nil
      end

      property.geolocation_latitude = aspire_property.geo_location_latitude
      property.geolocation_longitude = aspire_property.geo_location_longitude
      property.save! if property.changed?

      remote_index[remote_slug] = property.id
    end
  end

  instrument_method

  def materialize_catalog_item(aspire_catalog_item)
    raise "missing catalog_item_id" if (catalog_item_id = aspire_catalog_item.catalog_item_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(catalog_item_id, :catalog_item)
    track_error(remote_slug:) do
      catalog_item = catalog_item_records.where(remote_slug:).last&.record
      catalog_item ||= CatalogItem.new(remote_slug:, organization_id:)

      catalog_item.item_type = aspire_catalog_item.item_type
      catalog_item.name = aspire_catalog_item.item_name
      catalog_item.description = aspire_catalog_item.item_description
      catalog_item.purchase_unit = aspire_catalog_item.purchase_unit_type_name
      catalog_item.allocation_unit = aspire_catalog_item.allocation_unit_type_name
      catalog_item.save! if catalog_item.changed?

      remote_index[remote_slug] = catalog_item.id
    end
  end

  instrument_method

  def materialize_job(numbered_opportunity, force_status_update: false)
    raise ArgumentError, "expected a numbered_opportunity" unless numbered_opportunity.is_a?(Sync::Aspire::NumberedOpportunity)
    # raise "missing opportunity_id" if (opportunity_id = opportunity.opportunity_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    # FIXME: update conditions
    # if opportunity.opportunity_revisions.present?
    #   raise "#{opportunity_id} is not a root opportunity (has #{opportunity.opportunity_revisions.count} revisions)"
    # end

    opportunity_number = numbered_opportunity.opportunity_number
    remote_slug = remote_slug(opportunity_number, :numbered_opportunity)

    track_error(remote_slug:) do
      children = sync_caches.by_aspire_opportunity_number(opportunity_number).map(&:to_resource)

      # newest updates first
      sorted_opportunities = children.sort_by do |opportunity|
        opportunity.modified_date || opportunity.created_date_time
      end.reverse.lazy

      job = job_records.where(remote_slug:).last&.record
      job ||= Job.new(remote_slug:, organization_id: organization.id)
      job.remote_updated_at = sorted_opportunities.lazy.filter_map { |o| o.modified_date || o.created_date_time }.first

      operations_manager_contact_id = sorted_opportunities.filter_map(&:operations_manager_contact_id).first
      job.manager_id = local_id!(remote_id: operations_manager_contact_id, remote_resource: :contact)

      branch_id = sorted_opportunities.filter_map(&:branch_id).first
      job.branch_id = local_id!(remote_id: branch_id, remote_resource: :branch)

      job.name = sorted_opportunities.filter_map(&:opportunity_name).first
      job.last_activity_at ||= sorted_opportunities.filter_map(&:created_date_time).first
      job.remote_reference = opportunity_number
      property_id = sorted_opportunities.filter_map(&:property_id).first
      job.property_id = local_id!(remote_id: property_id, remote_resource: :property)
      job.created_date = sorted_opportunities.filter_map(&:created_date_time).min
      job.division_name = sorted_opportunities.filter_map(&:division_name).first

      # Extract, materialize, and set the invoice type if present
      invoice_type_name = sorted_opportunities.filter_map(&:invoice_type).first
      invoice_type_id = materialize_invoice_type(invoice_type_name)
      job.invoice_type_id = invoice_type_id

      case sorted_opportunities.filter_map(&:opportunity_type).first
      when "Contract"
        job.job_type = "recurring"
      when "Work Order"
        job.job_type = "non_recurring"
      else
        job.job_type = "unknown"
      end

      # Determine job status based on opportunity status and activity
      # Only set status if it's a new job, if we need to update it, or if forced
      if job.new_record? || force_status_update || should_update_job_status?(job, sorted_opportunities)
        # Get the most recent opportunity status and job status name (using existing pattern)
        opportunity_status = sorted_opportunities.filter_map(&:opportunity_status).first&.strip
        job_status_name = sorted_opportunities.filter_map(&:job_status_name).first&.strip

        # Apply job status mapping logic
        if opportunity_status == "Won"
          # Won opportunities: use JobStatusName mapping
          new_status = map_aspire_job_status(job_status_name)
        else
          # Pre-won opportunities: check for activity (costs or attendances)
          has_costs = has_opportunity_costs?(sorted_opportunities)
          has_attendances = job.persisted? ? has_job_attendances?(job.id) : false

          new_status = (has_costs || has_attendances) ? :in_progress : :pending
        end

        job.status = new_status
      end

      job.save! if job.changed?

      if job.saved_change_to_id?
        Sync::SyncAspireJobJob.perform_async(integration.id, job.id)
      end

      remote_index[remote_slug] = job.id
    end
  end

  def local_id!(remote_id:, remote_resource:, **options)
    return nil unless remote_id.present?

    local_id(remote_id:, remote_resource:, **options) or raise Sync::Errors::NotFound, "required local resource not available for #{remote_slug(remote_id, remote_resource)}"
  end

  # maps aspire id to protiv id - it's assumed that the cache has been appropriately
  # populated by this point
  def local_id(remote_id:, remote_resource:, materialize: true)
    return nil unless remote_id.present?

    unless Sync::Aspire::SLUGGABLE_RESOURCES.include?(remote_resource)
      raise ArgumentError, "cannot construct remote_id for type #{remote_resource}"
    end

    remote_slug = remote_slug(remote_id, remote_resource)
    remote_index.fetch(remote_slug) do
      if (id = integration_records.where(remote_slug:).pluck(:record_id).first)
        remote_index[remote_slug] = id
      else
        try_materialize(remote_id:, remote_resource:) if materialize
        remote_index[remote_slug]
      end
    end
  end

  def allowed_materialized_placeholders
    Sync::Aspire::ALLOW_MATERIALIZED_PLACEHOLDERS
  end

  def try_materialize(remote_id:, remote_resource:)
    cache = sync_caches.where(remote_resource:, remote_primary_id: remote_id).first

    # If no cache is found but the resource is in the allowed placeholders list,
    # create a placeholder cache entry and try to materialize it
    if cache.nil? && allowed_materialized_placeholders.include?(remote_resource)
      Rails.logger.info("Creating placeholder for missing cache: aspire:#{remote_resource}:#{remote_id}")
      cache = create_placeholder_cache(remote_resource, remote_id)
    elsif cache.nil?
      raise "cannot materialize aspire:#{remote_resource}:#{remote_id}; no cache found"
    end

    if cache.placeholder?
      integration.cache_adapter.fetch_placeholders([cache])

      cache.reload

      if cache.placeholder?
        # if we're still a placeholder, we likely have a 'phantom' record -
        # aspire can refer to contacts that it doesn't return in the API
        return try_materialize_placeholder(cache, remote_resource:, remote_id:)
      end
    end

    local_resource = adapter.local_resource_for(remote_resource)

    send("materialize_#{local_resource}", cache.to_resource)
  end

  # Creates a placeholder cache entry for a resource that doesn't exist in the cache
  def create_placeholder_cache(remote_resource, remote_id)
    sync_caches.create!(
      source: source,
      remote_resource: remote_resource,
      remote_primary_id: remote_id,
      raw_data: nil # This makes it a placeholder
    )
  end

  def try_materialize_placeholder(cache, remote_id:, remote_resource:)
    unless allowed_materialized_placeholders.include?(remote_resource)
      raise "cannot materialize aspire:#{remote_resource}:#{remote_id}"
    end

    send("try_materialize_placeholder_#{remote_resource}", cache, remote_id:)
  end

  def try_materialize_placeholder_contact(cache, remote_id:)
    # First check if we already have an integration record for this contact
    remote_slug = remote_slug(remote_id, :contact)
    integration_record = IntegrationRecord.find_by(
      integration_id: Sync::Current.integration_id,
      remote_slug: remote_slug
    )

    if integration_record && integration_record.record_type == "Identity" && integration_record.record_id.present?
      begin
        identity = Identity.find(integration_record.record_id)
        Rails.logger.info("Found existing identity #{identity.id} for placeholder contact #{remote_id}")
        return identity
      rescue ActiveRecord::RecordNotFound
        Rails.logger.warn("Integration record exists for placeholder contact #{remote_id} but identity #{integration_record.record_id} not found")
      end
    end

    # If we don't have an existing identity, create a placeholder contact
    contact = AspireClient::Resources::Contacts::RESPONSE_TYPE_MAPPING.new
    contact.contact_id = remote_id
    contact.last_name = "<remote record missing>"
    contact.first_name = "<[contact:#{remote_id}]>"
    contact.home_address = {}
    contact.office_address = {}

    Rails.logger.info("Creating placeholder contact for ID: #{remote_id}")
    materialize_identity(contact)
  end

  # Utility method to manually create a placeholder contact
  # This can be called from the Rails console to fix missing contacts:
  # Sync::Current.track(integration_id: integration.id) do
  #   integration.materializer.create_placeholder_contact(contact_id)
  # end
  def create_placeholder_contact(contact_id)
    # Check if we already have a cache entry for this contact
    existing_cache = sync_caches.find_by(
      source: source,
      remote_resource: :contact,
      remote_primary_id: contact_id
    )

    # If we already have a cache entry, use that
    if existing_cache
      Rails.logger.info("Found existing cache entry for contact #{contact_id}")
      return try_materialize_placeholder_contact(existing_cache, remote_id: contact_id)
    end

    # Otherwise create a new placeholder cache
    cache = create_placeholder_cache(:contact, contact_id)
    try_materialize_placeholder_contact(cache, remote_id: contact_id)
  end

  def materialize_milestone(work_ticket, caches: nil, job: nil, **options)
    raise "missing work_ticket_id" if (work_ticket_id = work_ticket.work_ticket_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(work_ticket_id, :work_ticket)

    track_error(remote_slug:) do
      if work_ticket.opportunity_id.blank?
        Bugsnag.notify(
          Sync::Errors::Skipped.new(
            "aspire work_ticket=#{work_ticket.work_ticket_id} for integration=#{Sync::Current.integration_id} was skipped"
          )
        )

        return
      end

      opportunity_service = caches&.find { |x| x.remote_resource.to_s == "opportunity_service" && x.to_resource.opportunity_service_id == work_ticket.opportunity_service_id }
      opportunity_service ||= sync_caches.aspire_opportunity_services.where(OPPORTUNITY_SERVICE_ID_EQ, work_ticket.opportunity_service_id).first

      opportunity = caches&.find { |x| x.remote_resource.to_s == "opportunity" && x.to_resource.opportunity_id == work_ticket.opportunity_id }
      opportunity ||= sync_caches.aspire_opportunities.where(remote_primary_id: work_ticket.opportunity_id).first

      # raise "missing opportunity_service cache for work ticket #{work_ticket_id}" unless opportunity_service
      raise "missing opportunity cache for work ticket #{work_ticket_id}" unless opportunity

      opportunity_service = opportunity_service&.to_resource
      opportunity = opportunity.to_resource

      milestone = milestone_records.where(remote_slug:).last&.record
      milestone ||= Milestone.new(remote_slug:)

      # NOTE: OpportunityNumber does not appear to be stable on WorkTickets, so it isn't a
      # reasonable way to link Milestones to Jobs. We need to hop through the milestone's OpportunityID first
      # to get the (presumably) correct OpportunityNumber
      #
      # raise "missing opportunity number" unless (opportunity_number = work_ticket.opportunity_number)
      # if opportunity_number.is_a?(Float) # Honestly this is just so infuriating
      #   if opportunity_number.denominator != 1
      #     raise "malformed opportunity number"
      #   end
      #
      #   opportunity_number = opportunity_number.to_i
      # end

      job_id = job&.id
      job_id ||= begin
                   local_id!(remote_id: opportunity.opportunity_number, remote_resource: :numbered_opportunity)
                 end

      raise "work_ticket=#{work_ticket_id}; missing job for work ticket #{work_ticket.work_ticket_id}" unless job_id

      milestone.job_id = job_id
      milestone.remote_updated_at = work_ticket.last_modified_date_time || work_ticket.created_date_time
      milestone.contract_price = Money.from_amount(work_ticket.price || 0)
      milestone.seconds_budget = (work_ticket.hours_est || 0) * 3600
      milestone.seconds_cost = (work_ticket.hours_act || 0) * 3600
      # FIXME: is hour_cost_est the correct attribute?
      milestone.labor_budget = Money.from_amount(work_ticket.hour_cost_est || 0)
      milestone.labor_cost = Money.from_amount(work_ticket.labor_cost_act || 0)
      milestone.equipment_budget = Money.from_amount(work_ticket.equip_cost_est || 0)
      milestone.equipment_cost = Money.from_amount(work_ticket.equipment_cost_act || 0)
      milestone.material_budget = Money.from_amount(work_ticket.material_cost_est || 0)
      milestone.material_cost = Money.from_amount(work_ticket.material_cost_act || 0)

      # Set initial status from WorkTicketStatus
      milestone.status = map_work_ticket_status(work_ticket.work_ticket_status_name)

      # Override to in_progress if milestone has costs (only for pending/scheduled/in_review statuses)
      if milestone.status.in?(["pending", "scheduled"]) && has_work_ticket_costs?(work_ticket)
        milestone.status = "in_progress"
      end

      milestone.remote_reference = work_ticket.work_ticket_number&.to_s&.strip

      if opportunity_service
        milestone.name = opportunity_service.display_name&.to_s&.strip
        milestone.description = opportunity_service.service_description&.to_s&.strip
        milestone.service_id = local_id(remote_id: opportunity_service.service_id, remote_resource: :service, materialize: false)

        invoice_type_id = materialize_invoice_type(opportunity_service.invoice_type)
        milestone.invoice_type_id = invoice_type_id
      end

      milestone.save! if milestone.changed?

      remote_index[remote_slug] = milestone.id
    end
  end

  def materialize_service(aspire_service, caches: nil, **opts)
    raise "missing service_id" if (aspire_service_id = aspire_service.service_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(aspire_service_id, :service)

    track_error(remote_slug:) do
      service_id = local_id(remote_id: aspire_service_id, remote_resource: :service, materialize: false)
      service = Service.find(service_id) if service_id
      service ||= Service.new(remote_slug:, organization_id: organization.id)

      service.name = aspire_service.service_name
      service.display_name = aspire_service.display_name
      service.service_type = aspire_service.service_type_name

      service.save if service.changed?

      remote_index[remote_slug] = service.id
    end
  end

  instrument_method

  def materialize_milestone_time(work_ticket_time, caches: nil, **)
    raise "missing work_ticket_time_id" if (work_ticket_time_id = work_ticket_time.work_ticket_time_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(work_ticket_time_id, :work_ticket_time)

    track_error(remote_slug:) do
      milestone_time = milestone_time_records.where(remote_slug:).last&.record

      unless milestone_time
        milestone_id = local_id(remote_id: work_ticket_time.work_ticket_id, remote_resource: :work_ticket)

        if !milestone_id
          Bugsnag.notify(
            Sync::Errors::Skipped.new(
              "milestone [WorkTicketID=#{work_ticket_time.work_ticket_id}] not found; skipping time entry"
            )
          )

          return
        end

        milestone_time = MilestoneTime.new(remote_slug:, milestone_id:)
      end

      milestone_time.identity_id = local_id!(remote_id: work_ticket_time.contact_id, remote_resource: :contact)
      milestone_time.route_id = local_id!(remote_id: work_ticket_time.route_id, remote_resource: :route)
      milestone_time.remote_updated_at = work_ticket_time.last_modified_date_time || work_ticket_time.created_date_time

      if (rate = work_ticket_time.base_hourly_rate)
        milestone_time.base_hourly_rate = Money.from_amount(rate)
      end

      milestone_time.start_time = work_ticket_time.start_time
      milestone_time.end_time = work_ticket_time.end_time
      milestone_time.accepted_at = work_ticket_time.accepted_date_time
      milestone_time.approved_at = work_ticket_time.approved_date_time
      # NOTE: we don't have a way to correlate an aspire User to an aspire Contact.
      # Unless it's clear that we need to, just saving the name is probably enough for now.
      milestone_time.accepted_by_name = work_ticket_time.accepted_user_name
      milestone_time.approved_by_name = work_ticket_time.approved_user_name
      milestone_time.save! if milestone_time.changed?
    end
  end

  instrument_method

  def materialize_milestone_item(work_ticket_item, caches: nil, **)
    raise "missing work_ticket_item_id" if (work_ticket_item_id = work_ticket_item.work_ticket_item_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(work_ticket_item_id, :work_ticket_item)

    track_error(remote_slug:) do
      milestone_item = milestone_item_records.where(remote_slug:).last&.record

      unless milestone_item
        milestone_id = local_id(remote_id: work_ticket_item.work_ticket_id, remote_resource: :work_ticket)

        if !milestone_id
          Bugsnag.notify(
            Sync::Errors::Skipped.new(
              "milestone [WorkTicketID=#{work_ticket_item.work_ticket_id}] not found; skipping item entry"
            )
          )

          return
        end

        milestone_item = MilestoneItem.new(remote_slug:, milestone_id:, organization:)
      end

      # Get the catalog item ID, but don't try to materialize it if it doesn't exist
      catalog_item_id = local_id(remote_id: work_ticket_item.catalog_item_id, remote_resource: :catalog_item, materialize: false)

      # Skip this milestone item if the catalog item doesn't exist
      if !catalog_item_id
        Bugsnag.notify(
          Sync::Errors::Skipped.new(
            "catalog item [CatalogItemID=#{work_ticket_item.catalog_item_id}] not found; skipping milestone item"
          )
        )

        return
      end

      milestone_item.catalog_item_id = catalog_item_id
      milestone_item.allocation_unit = work_ticket_item.allocation_unit_type_name
      milestone_item.quantity = work_ticket_item.item_quantity_extended
      milestone_item.cost = Money.from_amount(work_ticket_item.item_cost || 0)

      milestone_item.save! if milestone_item.changed?
    end
  end

  instrument_method

  def materialize_item_allocation(aspire_item_allocation, caches: nil, **)
    raise "missing item_allocation_id" if (item_allocation_id = aspire_item_allocation.item_allocation_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(item_allocation_id, :item_allocation)

    track_error(remote_slug:) do
      item_allocation = item_allocation_records.where(remote_slug:).last&.record

      unless item_allocation
        milestone_id = local_id(remote_id: aspire_item_allocation.work_ticket_id, remote_resource: :work_ticket)

        if !milestone_id
          Bugsnag.notify(
            Sync::Errors::Skipped.new(
              "milestone [WorkTicketID=#{aspire_item_allocation.work_ticket_id}] not found; skipping item entry"
            )
          )

          return
        end

        item_allocation = ItemAllocation.new(remote_slug:, milestone_id:, organization:)
      end

      # Get the catalog item ID, but don't try to materialize it if it doesn't exist
      catalog_item_id = local_id(remote_id: aspire_item_allocation.catalog_item_id, remote_resource: :catalog_item, materialize: false)

      # Skip this item allocation if the catalog item doesn't exist
      if !catalog_item_id
        Bugsnag.notify(
          Sync::Errors::Skipped.new(
            "catalog item [CatalogItemID=#{aspire_item_allocation.catalog_item_id}] not found; skipping item allocation"
          )
        )

        return
      end

      item_allocation.catalog_item_id = catalog_item_id
      item_allocation.unit_cost = Money.from_amount(aspire_item_allocation.item_unit_cost || 0)
      item_allocation.total_cost = Money.from_amount(aspire_item_allocation.item_total_cost || 0)
      item_allocation.item_quantity = aspire_item_allocation.item_quantity

      item_allocation.save! if item_allocation.changed?
    end
  end

  instrument_method

  def materialize_route(aspire_route)
    raise "missing route_id" if (route_id = aspire_route.route_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(route_id, :route)

    track_error(remote_slug:) do
      route = route_records.where(remote_slug:).last&.record

      route ||= Route.new(remote_slug:)

      route.crew_lead_id = local_id!(remote_id: aspire_route.crew_leader_contact_id, remote_resource: :contact)
      route.branch_id = local_id!(remote_id: aspire_route.branch_id, remote_resource: :branch)
      route.name = aspire_route.route_name
      route.manager_id = local_id!(remote_id: aspire_route.manager_contact_id, remote_resource: :contact)
      route.organization_id = integration.organization_id

      route.save! if route.changed?

      remote_index[remote_slug] = route.id
    end
  end

  def materialize_clock_time(aspire_clock_time, caches: nil)
    raise "missing clock_time_id" if (clock_time_id = aspire_clock_time.clock_time_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(clock_time_id, :clock_time)
    track_error(remote_slug:) do
      clock_time = clock_time_records.where(remote_slug:).last&.record

      clock_time ||= ClockTime.new(remote_slug:)

      clock_time.start_at = aspire_clock_time.clock_start
      clock_time.end_at = aspire_clock_time.clock_end
      clock_time.accepted_at = aspire_clock_time.accepted_date_time
      clock_time.identity = Identity.find(local_id!(remote_id: aspire_clock_time.contact_id, remote_resource: :contact))

      clock_time.break_time_seconds = (aspire_clock_time.break_time * 60).to_i
      clock_time.used_breaks = aspire_clock_time.used_breaks

      clock_time.save! if clock_time.changed?

      remote_index[remote_slug] = clock_time.id
    end
  end

  def map_work_ticket_status(work_ticket_status)
    return unless work_ticket_status.present?

    case work_ticket_status
    when "Pending Approval"
      :in_review
    when "Scheduled"
      :scheduled
    when "Open"
      :pending
    when "Complete"
      :completed
    when "Canceled"
      :canceled
    else
      raise "unknown WorkTicketStatus #{work_ticket_status}"
    end
  end

  # Maps Aspire JobStatusName to Protiv job status for won opportunities
  def map_aspire_job_status(job_status_name)
    return :in_progress unless job_status_name.present?

    case job_status_name.strip
    when "In Process", "In Process Edited", "In Production"
      :in_progress
    when "Complete"
      :completed
    when "Canceled"
      :canceled
    else
      :in_progress # Default for unknown statuses
    end
  end

  # Checks if any opportunity in the hierarchy has costs > 0
  def has_opportunity_costs?(opportunities)
    opportunities.any? do |opportunity|
      cost_fields = [
        # Estimated costs
        opportunity.try(:estimated_labor_cost),
        opportunity.try(:estimated_material_cost),
        opportunity.try(:estimated_equipment_cost),
        opportunity.try(:estimated_other_cost),
        opportunity.try(:estimated_sub_cost),
        opportunity.try(:estimated_cost_dollars),
        opportunity.try(:estimated_dollars),
        # Actual costs
        opportunity.try(:actual_cost_labor),
        opportunity.try(:actual_cost_material),
        opportunity.try(:actual_cost_sub),
        opportunity.try(:actual_cost_dollars),
        opportunity.try(:actual_earned_revenue),
        # Budget fields
        opportunity.try(:budgeted_dollars)
      ]

      cost_fields.any? { |cost| cost.present? && cost.to_f > 0 }
    end
  end

  # Checks if the job has any attendances/time entries
  def has_job_attendances?(job_id)
    return false unless job_id

    # Check if job has any milestone times (attendances)
    MilestoneTime.joins(:milestone)
                 .where(milestones: { job_id: job_id })
                 .exists?
  end

  # Determines if we should update the job status based on current state and opportunity data
  def should_update_job_status?(job, opportunities)
    return true if job.new_record?

    # Don't transition back from completed status (preserve existing behavior)
    return false if job.completed?

    # Always update for won opportunities (they might change JobStatusName)
    opportunity_status = opportunities.filter_map(&:opportunity_status).first&.strip
    return true if opportunity_status == "Won"

    # For pre-won opportunities, only update if currently pending
    # (this allows the MilestoneTime callback to handle pending -> in_progress transitions)
    job.pending?
  end

  # FIXME: dedupe these methods
  Sync::Abstract::Adapter::SYNC_CLASSES.each do |class_name|
    define_method "#{class_name}_records" do
      integration_records.where(record_type: class_name.camelize)
    end
  end

  def sync_crew_leads
    route_crew_leads = integration.crew_leads.distinct.pluck(:id)
    current_crew_leads = integration.identities.where(crew_lead: true).pluck(:id)

    crew_lead_added = route_crew_leads - current_crew_leads
    crew_lead_removed = current_crew_leads - route_crew_leads

    integration.identities.where(id: crew_lead_added).each do |identity|
      # avoid update_all because we want callbacks to run
      identity.update(crew_lead: true)
    end

    integration.identities.where(id: crew_lead_removed).each do |identity|
      # FIXME: create a confirmable to-do item for this change?
      identity.update(crew_lead: false)
    end
  end

  def sync_route_managers
    route_managers = integration.routes.where.not(manager_id: nil).distinct.pluck(:manager_id)
    current_managers = integration.identities.where(route_manager: true).pluck(:id)

    manager_added = route_managers - current_managers
    manager_removed = current_managers - route_managers

    integration.identities.where(id: manager_added).each do |identity|
      identity.update(route_manager: true)
    end

    integration.identities.where(id: manager_removed).each do |identity|
      identity.update(route_manager: false)
    end
  end

  def materialize_invoice_type(invoice_type_name)
    # Ensure we don't try to materialize blank names
    return if invoice_type_name.blank?

    # We use the name itself as the 'remote_id' part of the slug since Aspire provides no ID
    remote_slug = remote_slug(invoice_type_name, :invoice_type)

    track_error(remote_slug:) do
      # Find or initialize by name scoped to the integration's organization
      invoice_type = InvoiceType.find_or_initialize_by(
        name: invoice_type_name,
        organization: integration.organization
      )

      # Set remote_slug for the Trackable concern to create IntegrationRecord
      invoice_type.remote_slug = remote_slug

      invoice_type.save! if invoice_type.changed?

      # Update the index mapping remote_slug to local ID
      # Avoid overwriting if already present from another source in the same sync run
      remote_index[remote_slug] ||= invoice_type.id
    end
  rescue ActiveRecord::RecordInvalid => e
    Bugsnag.notify(e) do |report|
      report.add_metadata(:sync_context, { integration_id: integration.id, remote_slug: remote_slug })
    end
    nil # Return nil or error to avoid breaking calling method if ID is expected
  end

  instrument_method

  def materialize_schedule_visit(work_ticket_visit, caches: nil, **)
    raise "missing work_ticket_visit_id" if (work_ticket_visit_id = work_ticket_visit.work_ticket_visit_id).nil?
    raise "not in syncing context" if Sync::Current.integration_id.nil?

    remote_slug = remote_slug(work_ticket_visit_id, :work_ticket_visit)

    track_error(remote_slug:) do
      schedule_visit = schedule_visit_records.where(remote_slug:).last&.record

      unless schedule_visit
        milestone_id = local_id(remote_id: work_ticket_visit.work_ticket_id, remote_resource: :work_ticket)

        if !milestone_id
          Bugsnag.notify(
            Sync::Errors::Skipped.new(
              "milestone [WorkTicketID=#{work_ticket_visit.work_ticket_id}] not found; skipping schedule visit"
            )
          )

          return
        end

        route_id = local_id(remote_id: work_ticket_visit.route_id, remote_resource: :route)

        if !route_id
          Bugsnag.notify(
            Sync::Errors::Skipped.new(
              "route [RouteID=#{work_ticket_visit.route_id}] not found; skipping schedule visit"
            )
          )

          return
        end

        schedule_visit = ScheduleVisit.new(
          remote_slug:,
          milestone_id:,
          route_id:,
          organization_id: organization.id
        )
      end

      schedule_visit.hours = work_ticket_visit.hours
      schedule_visit.scheduled_date = work_ticket_visit.scheduled_date
      schedule_visit.remote_id = work_ticket_visit.work_ticket_visit_id

      schedule_visit.save! if schedule_visit.changed?

      remote_index[remote_slug] = schedule_visit.id
    end
  end

  # Checks if a work ticket has any actual costs > 0
  def has_work_ticket_costs?(work_ticket)
    # Only check ACTUAL cost fields, not estimates
    actual_cost_fields = [
      work_ticket.labor_cost_act,      # Actual labor costs
      work_ticket.material_cost_act,   # Actual material costs
      work_ticket.equipment_cost_act,  # Actual equipment costs
      work_ticket.sub_cost_act,        # Actual subcontractor costs
      work_ticket.other_cost_act,      # Other actual costs
      work_ticket.total_cost_act       # Total actual cost
    ]

    actual_cost_fields.any? { |cost| cost.present? && cost.to_f > 0 }
  end
end
