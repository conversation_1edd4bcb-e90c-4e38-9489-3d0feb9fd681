# frozen_string_literal: true

module Sync::Aspire::SqlHelpers
  OPPORTUNITY_NUMBER = "raw_data->>'OpportunityNumber'"
  MASTER_OPPORTUNITY_ID = "raw_data->>'MasterOpportunityID'"
  MASTER_OPPORTUNITY_ID_IS_NOT_NULL = "#{MASTER_OPPORTUNITY_ID} IS NOT NULL".freeze
  MASTER_OPPORTUNITY_ID_IS_NULL = "#{MASTER_OPPORTUNITY_ID} IS NULL".freeze

  OPPORTUNITY_SERVICE_ID = "raw_data->>'OpportunityServiceID'"
  OPPORTUNITY_SERVICE_ID_EQ = "#{OPPORTUNITY_SERVICE_ID} = ? :: text"
  OPPORTUNITY_SERVICE_ID_IN = "#{OPPORTUNITY_SERVICE_ID} IN (?)"
end
