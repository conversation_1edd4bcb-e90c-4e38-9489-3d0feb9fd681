# frozen_string_literal: true

class Sync::Aspire::Util
  def initialize(integration)
    raise ArgumentError, "integration is not aspire" unless integration.aspire?
    @integration = integration
  end

  attr_reader :integration

  delegate \
    :adapter,
    :cache_adapter,
    :sync_caches,
    :materializer,
    :integration_records,
    to: :integration

  include Sync::Aspire::<PERSON><PERSON>lHelpers

  def sync_job_initial(job)
    record = job_records.where(record_id: job.id).last
    raise "job not synced to this integration" unless record

    # get every opportunity with the same OpportunityNumber.
    caches = cache_adapter.cache_jobs(
      ids: [job.remote_reference],
      segment_key: "OpportunityNumber",
    )

    placeholders, caches = caches.partition(&:placeholder?)
    caches |= cache_adapter.fetch_placeholders(placeholders)

    job_cache = integration.sync_caches.find_by(remote_primary_id: job.remote_reference, remote_resource: :numbered_opportunity)
    materializer.send(:materialize_job, job_cache.to_resource) # re-materialize with all matching opportunity numbers

    all_opportunity_ids = integration.sync_caches.aspire_opportunities \
      .where("#{OPPORTUNITY_NUMBER} = ?", job.remote_reference) \
      .pluck(:remote_primary_id)

    caches = cache_adapter.cache_milestones(
      ids: all_opportunity_ids,
      segment_key: "OpportunityID",
      job:,
      initial_sync: true
    )

    work_ticket_caches = caches.select { |x| x.remote_resource.to_s == "work_ticket" }
    work_ticket_ids = work_ticket_caches.map(&:remote_primary_id)

    caches |= cache_adapter.cache_milestone_items(
      ids: work_ticket_ids,
      segment_key: "WorkTicketID",
      initial_sync: true
    )

    caches |= cache_adapter.cache_item_allocations(
      ids: work_ticket_ids,
      segment_key: "WorkTicketID",
      initial_sync: true
    )

    caches |= cache_adapter.cache_milestone_times(
      ids: work_ticket_ids,
      segment_key: "WorkTicketID",
      initial_sync: true
    )

    caches |= cache_adapter.cache_schedule_visits(
      ids: work_ticket_ids,
      segment_key: "WorkTicketID",
      initial_sync: true
    )

    placeholders, caches = caches.partition(&:placeholder?)
    caches |= cache_adapter.fetch_placeholders(placeholders)

    materializer.materialize_milestones(caches:, initial_sync: true, job:)
    materializer.materialize_milestone_times(caches:, initial_sync: true, job:)
    materializer.materialize_milestone_items(caches:, initial_sync: true, job:)
    materializer.materialize_item_allocations(caches:, initial_sync: true, job:)
    materializer.materialize_schedule_visits(caches:, initial_sync: true, job:)
  end

  private

  def job_records
    integration_records.where(record_type: "Job")
  end
end
