# frozen_string_literal: true

class CrewBonusDistributionCalculator
  # FIXME: Should we use Dry Initializer (https://github.com/dry-rb/dry-initializer) here?
  def initialize(bonus_amount:,
                 total_labor_cost:,
                 total_crew_lead_labor_cost:,
                 distribution_type:,
                 total_seconds_worked:,
                 total_crew_lead_seconds_worked:)
    @bonus_amount = bonus_amount
    @total_labor_cost = total_labor_cost
    @total_crew_lead_labor_cost = total_crew_lead_labor_cost
    @distribution_type = distribution_type
    @total_seconds_worked = total_seconds_worked
    @total_crew_lead_seconds_worked = total_crew_lead_seconds_worked
  end

  def crew_member_bonus(crew_labor_cost:, crew_member_seconds_worked:, crew_lead:, manual_percent_hundredths:)
    share_of_bonus(crew_labor_cost: crew_labor_cost,
                   crew_member_seconds_worked: crew_member_seconds_worked,
                   crew_lead: crew_lead,
                   manual_percent_hundredths: manual_percent_hundredths)
  end

  def seconds_to_hours(seconds)
    seconds / 3600.0
  end

  def total_hours_worked
    seconds_to_hours(@total_seconds_worked)
  end

  def crew_total_hours_worked
    seconds_to_hours(@total_seconds_worked - @total_crew_lead_seconds_worked)
  end

  def equal_rate_per_hour_float
    @bonus_amount.to_f / total_hours_worked
  end

  def crew_lead_bonus_total
    percent_increase = (@bonus_amount.to_f / @total_labor_cost.to_f).round(2)
    @total_crew_lead_labor_cost * percent_increase
  end

  def equal_bonus_rate
    @bonus_amount
  end

  def share_of_bonus(crew_labor_cost:, crew_member_seconds_worked:, crew_lead:, manual_percent_hundredths:)
    case @distribution_type
    when "equal_rate"
      Money.from_amount(equal_rate_per_hour_float * seconds_to_hours(crew_member_seconds_worked))
    when "equal_weighted"
      percent_increase = (@bonus_amount.to_f / @total_labor_cost.to_f).round(2)
      crew_labor_cost * percent_increase
    when "crew_lead_weighted"
      if crew_lead
        percent_increase = (@bonus_amount.to_f / @total_labor_cost.to_f).round(2)
        crew_labor_cost * percent_increase
      else
        crew_lead_excluded_bonus = @bonus_amount - crew_lead_bonus_total
        hourly_rate = crew_lead_excluded_bonus.to_f / crew_total_hours_worked
        Money.from_amount(hourly_rate * seconds_to_hours(crew_member_seconds_worked))
      end
    when "manual_distribution"
      percent_increase = manual_percent_hundredths / 10000.000
      Money.from_amount(@bonus_amount.to_f * percent_increase)
    end
  end
end
