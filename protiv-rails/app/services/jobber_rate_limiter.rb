# frozen_string_literal: true

##
# Enhanced rate limiter for Jobber GraphQL API cost-based limiting.
#
# This service extends the existing RateLimiter functionality to handle
# GraphQL query costs instead of just request counts. It monitors available
# cost budget from API responses and implements dynamic throttling.
#
# @example Basic usage
#   limiter = JobberRateLimiter.new(integration_id: "jobber_123")
#   limiter.check_cost_availability(estimated_cost: 150)
#   limiter.update_from_response(api_response)
#
# <AUTHOR> Team
# @since 1.0.0
class JobberRateLimiter
  class CostLimitExceeded < StandardError
    attr_reader :wait_time, :available_cost, :needed_cost

    def initialize(message, wait_time: nil, available_cost: nil, needed_cost: nil)
      super(message)
      @wait_time = wait_time
      @available_cost = available_cost
      @needed_cost = needed_cost
    end
  end

  class CircuitBreakerOpen < StandardError
    attr_reader :retry_after

    def initialize(message, retry_after: nil)
      super(message)
      @retry_after = retry_after
    end
  end

  # Configuration constants
  DEFAULT_MAX_COST_PERCENTAGE = 80 # Use 80% of maximumAvailable
  DEFAULT_MINIMUM_COST_THRESHOLD = 500 # Minimum cost to maintain
  DEFAULT_CIRCUIT_BREAKER_THRESHOLD = 5 # Failures before circuit opens
  DEFAULT_CIRCUIT_BREAKER_TIMEOUT = 300 # Seconds before circuit reset
  DEFAULT_RESTORE_RATE = 500 # Default restore rate if not provided

  attr_reader :integration_id, :max_cost_percentage, :minimum_cost_threshold

  ##
  # Initializes a new JobberRateLimiter instance.
  #
  # @param integration_id [String] Unique identifier for the integration
  # @param max_cost_percentage [Integer] Percentage of maximum cost to use (default: 80)
  # @param minimum_cost_threshold [Integer] Minimum cost to maintain (default: 500)
  def initialize(integration_id:, max_cost_percentage: DEFAULT_MAX_COST_PERCENTAGE,
                 minimum_cost_threshold: DEFAULT_MINIMUM_COST_THRESHOLD)
    @integration_id = integration_id
    @max_cost_percentage = max_cost_percentage
    @minimum_cost_threshold = minimum_cost_threshold
  end

  ##
  # Checks if sufficient cost is available for a query.
  #
  # @param estimated_cost [Integer] Estimated cost of the query
  # @return [Boolean] True if cost is available
  # @raise [CostLimitExceeded] If insufficient cost is available
  # @raise [CircuitBreakerOpen] If circuit breaker is open
  def check_cost_availability(estimated_cost:)
    check_circuit_breaker!

    current_status = get_current_throttle_status
    return true unless current_status

    available_cost = current_status[:currently_available]
    usable_cost = calculate_usable_cost(current_status[:maximum_available])

    if available_cost < estimated_cost || available_cost < minimum_cost_threshold
      wait_time = calculate_wait_time(
        needed_cost: [estimated_cost, minimum_cost_threshold].max,
        available_cost: available_cost,
        restore_rate: current_status[:restore_rate]
      )

      raise CostLimitExceeded.new(
        "Insufficient cost available. Need: #{estimated_cost}, Available: #{available_cost}",
        wait_time: wait_time,
        available_cost: available_cost,
        needed_cost: estimated_cost
      )
    end

    true
  end

  ##
  # Updates rate limiter state from API response.
  #
  # @param response [Hash] API response containing extensions.cost data
  # @return [void]
  def update_from_response(response)
    cost_data = response.dig("extensions", "cost")
    return unless cost_data

    throttle_status = cost_data["throttleStatus"]
    if throttle_status
      store_throttle_status(throttle_status)

      # Log cost efficiency metrics
      log_cost_metrics(cost_data)
    end

    # Check if we were throttled
    if response.dig("errors")&.any? { |error| error["message"]&.include?("THROTTLED") }
      increment_failure_count
    else
      reset_failure_count
    end
  end

  ##
  # Calculates optimal wait time based on throttle status.
  #
  # @param needed_cost [Integer] Cost needed for next operation
  # @param available_cost [Integer] Currently available cost
  # @param restore_rate [Integer] Cost restoration rate per second
  # @return [Float] Wait time in seconds
  def calculate_wait_time(needed_cost:, available_cost:, restore_rate: DEFAULT_RESTORE_RATE)
    cost_deficit = needed_cost - available_cost
    return 0 if cost_deficit <= 0

    base_wait_time = cost_deficit.to_f / restore_rate

    # Add small buffer to account for timing variations
    buffer_time = 1.0

    base_wait_time + buffer_time
  end

  ##
  # Gets current throttle status from cache.
  #
  # @return [Hash, nil] Current throttle status or nil if not available
  def get_current_throttle_status
    return nil unless redis

    data = redis.get(throttle_status_key)
    return nil unless data

    JSON.parse(data, symbolize_names: true)
  rescue JSON::ParserError
    nil
  end

  ##
  # Checks if circuit breaker is open.
  #
  # @return [Boolean] True if circuit breaker is open
  def circuit_breaker_open?
    return false unless redis

    failure_count = redis.get(failure_count_key).to_i
    last_failure_time = redis.get(last_failure_key)

    return false if failure_count < DEFAULT_CIRCUIT_BREAKER_THRESHOLD

    if last_failure_time
      time_since_failure = Time.current.to_i - last_failure_time.to_i
      return time_since_failure < DEFAULT_CIRCUIT_BREAKER_TIMEOUT
    end

    false
  end

  ##
  # Forces circuit breaker to open state.
  #
  # @return [void]
  def open_circuit_breaker!
    return unless redis

    redis.multi do |r|
      r.set(failure_count_key, DEFAULT_CIRCUIT_BREAKER_THRESHOLD)
      r.set(last_failure_key, Time.current.to_i)
      r.expire(failure_count_key, DEFAULT_CIRCUIT_BREAKER_TIMEOUT)
      r.expire(last_failure_key, DEFAULT_CIRCUIT_BREAKER_TIMEOUT)
    end
  end

  ##
  # Resets circuit breaker to closed state.
  #
  # @return [void]
  def reset_circuit_breaker!
    return unless redis

    redis.multi do |r|
      r.del(failure_count_key)
      r.del(last_failure_key)
    end
  end

  private

  ##
  # Checks circuit breaker state and raises if open.
  #
  # @raise [CircuitBreakerOpen] If circuit breaker is open
  # @return [void]
  def check_circuit_breaker!
    if circuit_breaker_open?
      last_failure_time = redis.get(last_failure_key).to_i
      retry_after = DEFAULT_CIRCUIT_BREAKER_TIMEOUT - (Time.current.to_i - last_failure_time)

      raise CircuitBreakerOpen.new(
        "Circuit breaker is open due to repeated failures",
        retry_after: retry_after
      )
    end
  end

  ##
  # Stores throttle status in Redis cache.
  #
  # @param throttle_status [Hash] Throttle status from API response
  # @return [void]
  def store_throttle_status(throttle_status)
    return unless redis

    status_data = {
      maximum_available: throttle_status["maximumAvailable"],
      currently_available: throttle_status["currentlyAvailable"],
      restore_rate: throttle_status["restoreRate"],
      updated_at: Time.current.to_i
    }

    redis.setex(throttle_status_key, 60, status_data.to_json) # Cache for 1 minute
  end

  ##
  # Calculates usable cost based on maximum available and percentage limit.
  #
  # @param maximum_available [Integer] Maximum available cost from API
  # @return [Integer] Usable cost amount
  def calculate_usable_cost(maximum_available)
    (maximum_available * max_cost_percentage / 100.0).floor
  end

  ##
  # Increments failure count for circuit breaker.
  #
  # @return [void]
  def increment_failure_count
    return unless redis

    redis.multi do |r|
      r.incr(failure_count_key)
      r.set(last_failure_key, Time.current.to_i)
      r.expire(failure_count_key, DEFAULT_CIRCUIT_BREAKER_TIMEOUT)
      r.expire(last_failure_key, DEFAULT_CIRCUIT_BREAKER_TIMEOUT)
    end
  end

  ##
  # Resets failure count for circuit breaker.
  #
  # @return [void]
  def reset_failure_count
    return unless redis

    redis.del(failure_count_key)
  end

  ##
  # Logs cost efficiency metrics.
  #
  # @param cost_data [Hash] Cost data from API response
  # @return [void]
  def log_cost_metrics(cost_data)
    Rails.logger.info({
      event: "jobber_cost_metrics",
      integration_id: integration_id,
      requested_cost: cost_data["requestedQueryCost"],
      actual_cost: cost_data["actualQueryCost"],
      maximum_available: cost_data.dig("throttleStatus", "maximumAvailable"),
      currently_available: cost_data.dig("throttleStatus", "currentlyAvailable"),
      restore_rate: cost_data.dig("throttleStatus", "restoreRate"),
      cost_efficiency: calculate_cost_efficiency(cost_data)
    }.to_json)
  end

  ##
  # Calculates cost efficiency percentage.
  #
  # @param cost_data [Hash] Cost data from API response
  # @return [Float] Cost efficiency as percentage
  def calculate_cost_efficiency(cost_data)
    requested = cost_data["requestedQueryCost"].to_f
    actual = cost_data["actualQueryCost"].to_f

    return 100.0 if requested == 0

    (actual / requested * 100.0).round(2)
  end

  ##
  # Redis key for storing throttle status.
  #
  # @return [String] Redis key
  def throttle_status_key
    "jobber_rate_limiter:#{integration_id}:throttle_status"
  end

  ##
  # Redis key for storing failure count.
  #
  # @return [String] Redis key
  def failure_count_key
    "jobber_rate_limiter:#{integration_id}:failure_count"
  end

  ##
  # Redis key for storing last failure time.
  #
  # @return [String] Redis key
  def last_failure_key
    "jobber_rate_limiter:#{integration_id}:last_failure"
  end

  ##
  # Returns Redis connection.
  #
  # @return [Redis, nil] Redis connection or nil if not available
  def redis
    Protiv::Application.redis
  end
end
