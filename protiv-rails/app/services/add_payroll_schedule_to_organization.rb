# frozen_string_literal: true

class AddPayrollScheduleToOrganization < ApplicationService
  def initialize(organization:)
    @organization = organization
  end

  def execute
    result = Result.new
    result.ok!

    ApplicationRecord.transaction do
      payroll_schedule = PayrollSchedule.find_or_create_by!(
        name: "default",
        period: "monthly",
        organization: @organization
      )
      result.state = payroll_schedule
    rescue => e
      result.error!(e)
      raise ActiveRecord::Rollback
    end

    result
  end
end
