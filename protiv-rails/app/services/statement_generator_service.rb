# frozen_string_literal: true

class StatementGeneratorService < ApplicationService
  def initialize(identity:, organization:)
    @identity = identity
    @organization = organization
  end

  def execute
    result = Result.new
    result.ok!

    ApplicationRecord.transaction do
      branches = @organization.branches

      # PayPeriods are supposed to always exist, one after the other for each branch
      pay_period = PayPeriod.current
        .where(
          branch: branches,
          period_type: "bonus"
        )
        .first || raise("No current pay period")

      statement = Statement.find_or_create_by!(
        organization: @organization,
        identity: @identity,
        pay_period: pay_period,
        status: "open"
      )
      result.state = statement
    rescue => e
      result.error!(e)
      raise ActiveRecord::Rollback
    end

    result
  end
end
