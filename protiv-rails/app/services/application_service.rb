# frozen_string_literal: true

class ApplicationService
  def execute!
    execute.unwrap!
  end

  def perform
    raise NotImplementedError
  end

  def execute
    result = Result.new
    result.ok!

    begin
      result.state = perform
    rescue => e
      result.error!(e)
    end

    result
  end

  class Builder
    attr_accessor :current_user, :current_organization

    def build_and_execute!
      build.execute!
    end
  end
end
