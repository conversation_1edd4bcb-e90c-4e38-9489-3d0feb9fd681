# frozen_string_literal: true

require "net/http"
require "uri"
require "json"

##
# Service class for handling Jobber OAuth 2.0 authentication flow.
#
# This service manages the complete OAuth flow including:
# - Authorization URL generation
# - Token exchange and refresh
# - Account information retrieval
# - Integration and credential management
# - State validation for CSRF protection
#
# @example Basic usage
#   service = JobberOauthService.new
#   auth_url = service.authorization_url(state: "secure_state")
#   result = service.exchange_code_for_token(authorization_code: "code123")
#
# @example With custom credentials
#   service = JobberOauthService.new(
#     client_id: "custom_id",
#     client_secret: "custom_secret",
#     redirect_uri: "https://example.com/callback"
#   )
#
# <AUTHOR> Team
# @since 1.0.0
class JobberOauthService
  JOBBER_OAUTH_BASE_URL = "https://api.getjobber.com"
  AUTHORIZATION_ENDPOINT = "/api/oauth/authorize"
  TOKEN_ENDPOINT = "/api/oauth/token"
  JOBBER_REDIRECT_URI = "http://localhost:3000/api/v2/integrations/jobber/oauth/callback"

  # @return [String] OAuth client ID
  attr_reader :client_id

  # @return [String] OAuth client secret
  attr_reader :client_secret

  # @return [String] OAuth redirect URI
  attr_reader :redirect_uri

  ##
  # Initializes a new JobberOauthService instance.
  #
  # @param client_id [String, nil] OAuth client ID (defaults to ENV value)
  # @param client_secret [String, nil] OAuth client secret (defaults to ENV value)
  # @param redirect_uri [String, nil] OAuth redirect URI (defaults to ENV value)
  def initialize(client_id: nil, client_secret: nil, redirect_uri: nil)
    @client_id = client_id || default_client_id
    @client_secret = client_secret || default_client_secret
    @redirect_uri = redirect_uri || default_redirect_uri
  end

  ##
  # Generates the OAuth authorization URL for initiating the OAuth flow.
  #
  # @param state [String, nil] CSRF protection state parameter
  # @return [String] Complete authorization URL
  #
  # @example
  #   service = JobberOauthService.new
  #   url = service.authorization_url(state: "secure_random_state")
  #   # => "https://api.getjobber.com/api/oauth/authorize?response_type=code&..."
  def authorization_url(state: nil)
    params = build_authorization_params(state)
    query_string = URI.encode_www_form(params)

    Rails.logger.info("Generated OAuth authorization URL with state: #{state}")
    "#{JOBBER_OAUTH_BASE_URL}#{AUTHORIZATION_ENDPOINT}?#{query_string}"
  end

  ##
  # Exchanges an authorization code for access and refresh tokens.
  #
  # @param authorization_code [String] Authorization code from OAuth callback
  # @param state [String, nil] State parameter for validation (unused in exchange)
  # @return [Hash] Token exchange result with success status and token data
  #
  # @example Successful exchange
  #   result = service.exchange_code_for_token(authorization_code: "auth_code_123")
  #   if result[:success]
  #     access_token = result[:access_token]
  #     refresh_token = result[:refresh_token]
  #     expires_at = result[:expires_at]
  #   end
  #
  # @example Failed exchange
  #   result = service.exchange_code_for_token(authorization_code: "invalid_code")
  #   unless result[:success]
  #     error_message = result[:error]
  #   end
  def exchange_code_for_token(authorization_code:, state: nil)
    oauth_client = create_oauth_client

    begin
      token = oauth_client.auth_code.get_token(
        authorization_code,
        redirect_uri: redirect_uri
      )

      build_token_response(token, success: true)
    rescue OAuth2::Error => e
      Rails.logger.error("Jobber OAuth token exchange failed: #{e.message}")
      build_error_response(e)
    end
  end

  ##
  # Refreshes an access token using a refresh token.
  #
  # @param refresh_token [String] Valid refresh token
  # @return [Hash] Token refresh result with success status and new token data
  #
  # @example Successful refresh
  #   result = service.refresh_access_token(refresh_token: "refresh_token_123")
  #   if result[:success]
  #     new_access_token = result[:access_token]
  #     new_refresh_token = result[:refresh_token]
  #   end
  #
  # @example Failed refresh
  #   result = service.refresh_access_token(refresh_token: "expired_token")
  #   unless result[:success]
  #     error_message = result[:error]
  #   end
  def refresh_access_token(refresh_token:)
    oauth_client = create_oauth_client

    begin
      old_token = OAuth2::AccessToken.new(oauth_client, nil, refresh_token: refresh_token)
      new_token = old_token.refresh!

      build_token_response(new_token, success: true)
    rescue OAuth2::Error => e
      Rails.logger.error("Jobber OAuth token refresh failed: #{e.message}")
      build_error_response(e)
    end
  end

  ##
  # Generates a cryptographically secure state parameter for CSRF protection.
  #
  # @return [String] 64-character hexadecimal string
  #
  # @example
  #   state = JobberOauthService.generate_state
  #   # => "a1b2c3d4e5f6..."
  def self.generate_state
    SecureRandom.hex(32)
  end

  ##
  # Validates state parameter to prevent CSRF attacks using constant-time comparison.
  #
  # @param expected_state [String] State stored in session
  # @param received_state [String] State received from OAuth callback
  # @return [Boolean] true if states match, false otherwise
  #
  # @example Valid state
  #   JobberOauthService.validate_state("abc123", "abc123") # => true
  #
  # @example Invalid state
  #   JobberOauthService.validate_state("abc123", "xyz789") # => false
  #   JobberOauthService.validate_state(nil, "abc123")      # => false
  def self.validate_state(expected_state, received_state)
    return false if expected_state.blank? || received_state.blank?

    ActiveSupport::SecurityUtils.secure_compare(expected_state, received_state)
  end

  ##
  # Retrieves account information using an access token.
  #
  # @param access_token [String] Valid OAuth access token
  # @return [Hash] Account information or error details
  #
  # @example Successful request
  #   info = service.get_account_info(access_token: "token_123")
  #   account_id = info.dig("data", "account", "id")
  #   account_name = info.dig("data", "account", "name")
  #
  # @example Failed request
  #   info = service.get_account_info(access_token: "invalid_token")
  #   error = info["error"]
  def get_account_info(access_token:)
    oauth_client = create_oauth_client
    token = OAuth2::AccessToken.new(oauth_client, access_token)

    query = build_account_info_query

    begin
      response = token.post("/api/graphql",
                            headers: graphql_headers(access_token),
                            body: { query: query }.to_json)

      JSON.parse(response.body)
    rescue OAuth2::Error => e
      Rails.logger.error("Failed to get Jobber account info: #{e.message}")
      { error: e.message }
    end
  end

  ##
  # Handles the complete OAuth callback flow including validation and integration setup.
  #
  # @param authorization_code [String] Authorization code from callback
  # @param received_state [String] State parameter from callback
  # @param stored_state [String] State parameter stored in session
  # @param organization [Organization] Organization to associate with integration
  # @return [Hash] Result with success status and integration data or error details
  #
  # @example Successful callback
  #   result = service.handle_oauth_callback(
  #     authorization_code: "code123",
  #     received_state: "state123",
  #     stored_state: "state123",
  #     organization: organization
  #   )
  #   if result[:success]
  #     integration = result[:integration]
  #     account_info = result[:account_info]
  #   end
  def handle_oauth_callback(authorization_code:, received_state:, stored_state:, organization:)
    # Exchange code for token
    token_result = exchange_code_for_token(authorization_code: authorization_code)

    unless token_result[:success]
      return {
        success: false,
        error: token_result[:error] || "Failed to exchange authorization code for access token",
        error_type: :token_exchange_failed
      }
    end

    # Create or update integration and credentials
    integration_result = create_or_update_integration(organization, token_result)

    unless integration_result[:success]
      return integration_result
    end

    # Get account information for verification
    account_info = get_account_info(access_token: token_result[:access_token])

    {
      success: true,
      integration: integration_result[:integration],
      account_info: account_info
    }
  end

  ##
  # Refreshes tokens for an existing integration.
  #
  # @param integration [Integration] Integration with refresh token
  # @return [Hash] Result with success status and updated credential data
  def refresh_integration_token(integration)
    credential = integration.credential

    unless credential&.refresh_token
      return {
        success: false,
        error: "No refresh token available for this integration",
        error_type: :no_refresh_token
      }
    end

    token_result = refresh_access_token(refresh_token: credential.refresh_token)

    unless token_result[:success]
      return token_result
    end

    # Update credential with new tokens
    if credential.update(
      bearer_token: token_result[:access_token],
      refresh_token: token_result[:refresh_token],
      expires_at: token_result[:expires_at]
    )
      { success: true, credential: credential }
    else
      {
        success: false,
        error: "Failed to update credential with new tokens",
        error_type: :credential_update_failed
      }
    end
  end

  private

  ##
  # Creates a configured OAuth2 client instance.
  #
  # @return [OAuth2::Client] Configured OAuth2 client
  def create_oauth_client
    OAuth2::Client.new(
      client_id,
      client_secret,
      site: JOBBER_OAUTH_BASE_URL,
      token_url: TOKEN_ENDPOINT,
      auth_scheme: :request_body
    )
  end

  ##
  # Builds authorization parameters for OAuth URL.
  #
  # @param state [String, nil] CSRF protection state parameter
  # @return [Hash] Authorization parameters
  def build_authorization_params(state)
    params = {
      response_type: "code",
      client_id: client_id,
      redirect_uri: redirect_uri
    }
    params[:state] = state if state.present?
    params
  end

  ##
  # Builds a standardized token response hash.
  #
  # @param token [OAuth2::AccessToken] OAuth2 token object
  # @param success [Boolean] Success status
  # @return [Hash] Standardized token response
  def build_token_response(token, success:)
    {
      access_token: token.token,
      refresh_token: token.refresh_token,
      expires_at: extract_expires_at(token),
      success: success
    }
  end

  ##
  # Builds a standardized error response hash.
  #
  # @param error [OAuth2::Error] OAuth2 error object
  # @return [Hash] Standardized error response
  def build_error_response(error)
    {
      success: false,
      error: error.message,
      error_description: error.description
    }
  end

  ##
  # Extracts expiration time from token, handling different formats.
  #
  # @param token [OAuth2::AccessToken] OAuth2 token object
  # @return [Time] Token expiration time
  def extract_expires_at(token)
    return Time.at(token.expires_at) if token.expires_at

    # Fallback to JWT decoding if expires_at is not available
    jwt_payload = JWT.decode(token.token, nil, false).first
    Time.at(jwt_payload["exp"]).utc
  end

  ##
  # Builds GraphQL query for account information.
  #
  # @return [String] GraphQL query string
  def build_account_info_query
    <<~GRAPHQL
      query {
        account {
          id
          name
        }
      }
    GRAPHQL
  end

  ##
  # Builds headers for GraphQL requests.
  #
  # @param access_token [String] OAuth access token
  # @return [Hash] Request headers
  def graphql_headers(access_token)
    {
      "Authorization" => "Bearer #{access_token}",
      "Content-Type" => "application/json",
      "X-JOBBER-GRAPHQL-VERSION" => "2025-01-20"
    }
  end

  ##
  # Creates or updates integration and credentials with token data.
  #
  # @param organization [Organization] Organization to associate with integration
  # @param token_result [Hash] Token data from OAuth exchange
  # @return [Hash] Result with success status and integration data
  def create_or_update_integration(organization, token_result)
    integration = Integration.find_or_initialize_by(
      organization: organization,
      source: :jobber
    )

    credential = integration.credential || integration.build_credential
    credential.assign_attributes(
      bearer_token: token_result[:access_token],
      refresh_token: token_result[:refresh_token],
      expires_at: token_result[:expires_at]
    )

    if integration.save && credential.save
      Rails.logger.info("Jobber OAuth: Integration ##{integration.id} for organization ##{organization.id} created successfully")
      { success: true, integration: integration }
    else
      Rails.logger.error("Jobber OAuth: Failed to save integration or credential")
      {
        success: false,
        error: "Failed to save integration or credential",
        error_type: :save_failed
      }
    end
  end

  ##
  # Gets default client ID from environment or credentials.
  #
  # @return [String] OAuth client ID
  def default_client_id
    Rails.application.credentials.dig(:jobber, :client_id) ||
      ENV["JOBBER_CLIENT_ID"]
  end

  ##
  # Gets default client secret from environment or credentials.
  #
  # @return [String] OAuth client secret
  def default_client_secret
    Rails.application.credentials.dig(:jobber, :client_secret) ||
      ENV["JOBBER_CLIENT_SECRET"]
  end

  ##
  # Gets default redirect URI from environment or credentials.
  #
  # @return [String] OAuth redirect URI
  def default_redirect_uri
    Rails.application.credentials.dig(:jobber, :redirect_uri) ||
      ENV["JOBBER_REDIRECT_URI"] ||
      JOBBER_REDIRECT_URI
  end
end
