# frozen_string_literal: true

class ChangePasswordService
  attr_reader :user, :current_password, :new_password, :errors, :token

  def initialize(user, current_password, new_password)
    @user = user
    @current_password = current_password
    @new_password = new_password
    @errors = []
    @token = nil
  end

  def call
    unless authenticate_current_password
      @errors << { code: "invalid_current_password", detail: "Current password is incorrect" }
      return self
    end

    unless validate_new_password
      # validate_new_password adds errors to @errors
      return self
    end

    update_password
    set_pending_verification_flag

    if save_user && @errors.empty?
      generate_token
      send_verification_email
    end

    self
  end

  def success?
    @errors.empty?
  end

  private

  def authenticate_current_password
    user.authenticate(current_password)
  end

  def validate_new_password
    return false if new_password.blank?

    # Check password length according to the specification (minimum 8 characters)
    if new_password.length < 8
      @errors << { code: "invalid_password", detail: "Password must be at least 8 characters long" }
      return false
    end

    true
  end

  def update_password
    user.password = new_password
    user.password_confirmation = new_password
  end

  def set_pending_verification_flag
    user.password_change_pending_verification = true
  end

  def save_user
    unless user.save
      user.errors.full_messages.each do |message|
        @errors << { code: "validation_error", detail: message }
      end
      return false
    end
    true
  end

  def generate_token
    @token = user.generate_token_for(:password_change_verification)
  end

  def send_verification_email
    UserMailer.with(user: user, token: @token).password_change_verification_email.deliver_later
  end
end
