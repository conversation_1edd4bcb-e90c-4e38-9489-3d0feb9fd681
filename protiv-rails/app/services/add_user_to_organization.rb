# frozen_string_literal: true

class AddUserToOrganization < ApplicationService
  def initialize(user:, organization:, role_type:, **additional_opts)
    @user = user
    @organization = organization
    @role_type = role_type
    @additional_opts = additional_opts
  end

  def execute
    result = Result.new
    result.ok!

    begin
      existing_active_roles = @user.roles.where(
        organization: @organization,
        role_type: @role_type,
        active: true
      )

      result.state = existing_active_roles.first_or_create!({
        active_at: Time.zone.now
      }.merge(@additional_opts))
    rescue => e
      result.error!(e)
    end

    result
  end
end
