# frozen_string_literal: true

class JobsByProductsOrServiceFilter
  def initialize(jobs:, product_or_service_ids:)
    @jobs = jobs
    @target_ps_ids = product_or_service_ids.to_set
  end

  def filter
    @jobs.select do |job|
      line_items = job.dig("lineItems", "nodes") || []

      line_items.any? do |item|
        ps_id = item.dig("linkedProductOrService", "id")
        ps_id && @target_ps_ids.include?(ps_id)
      end
    end
  end
end
