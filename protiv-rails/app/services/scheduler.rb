# frozen_string_literal: true

module Scheduler
  def self.sync_integrations
    Integration.needs_sync.find_each do |integration|
      if integration.last_synced_at == integration.last_sync_started_at
        # if these timestamps match, the last sync was successful and we should
        # start a new one
        integration.sync_all(async: true)
      elsif integration.last_sync_started_at > integration.last_synced_at
        # we either have a sync in progress, or it's stuck in a retry loop.

        if integration.last_sync_started_at + integration.sync_interval > Time.now
          # if we've missed an entire sync interval, something is probably wrong
          error = Sync::Errors::Stalled.new("integration id=#{integration.id} hasn't synced within its configured interval")
          Bugsnag.notify(error)
        end
      end
    end

    Integration.needs_initial_sync.find_each do |integration|
      if integration.last_sync_started_at
        if integration.last_sync_started_at + integration.sync_interval > Time.now
          error = Sync::Errors::Stalled.new("integration id=#{integration.id} hasn't initially synced within its configured interval")
          Bugsnag.notify(error)
        end
      elsif integration.created_at < 1.hour.ago
        error = Sync::Errors::NotStarted.new("integration id=#{integration.id} hasn't started its initial sync within 1 hour")
        Bugsnag.notify(error)
      end
    end
  end

  # These tokens come with a refresh token with a week-long expiry, and Aspire claims that _using_ the token
  # automatically extends its expiration, but in practice this doesn't seem to be true and
  # they need to be refreshed before the access token expires.
  def self.refresh_aspire_tokens
    Integration.aspire.joins(:credential).merge(Credential.where(expires_at: ..2.hours.from_now)).find_each do |integration|
      Sync::RefreshAspireTokenJob.perform_async(integration.id)
    end
  end
end
