# frozen_string_literal: true

class CreateJob < ApplicationService
  def initialize(organization:, branch: nil, job_name:, last_activity_at:)
    @branch = branch
    @organization = organization
    @job_name = job_name
    @last_activity_at = last_activity_at
  end

  def execute
    result = Result.new
    result.ok!

    begin
      result.state = Job.create(
        organization: @organization,
        branch: @branch,
        name: @job_name,
        last_activity_at: @last_activity_at
      )
    rescue => e
      result.error!(e)
    end

    result
  end
end
