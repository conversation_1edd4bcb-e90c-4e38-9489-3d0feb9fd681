# frozen_string_literal: true

module PermittedRedirectHelper
  def permitted_redirect
    if (redirect = params[:redirect])
      begin
        redirect = URI(redirect)
      rescue URI::InvalidURIError
        raise ActionController::BadRequest.new("redirect is invalid")
      end

      unless redirect.host.nil? && redirect.scheme.nil?
        raise ActionController::BadRequest.new("redirect is invalid")
      end

      { redirect: redirect.to_s }
    else
      {}
    end
  end
end
