# frozen_string_literal: true

module AuthenticationHelpers
  extend ActiveSupport::Concern

  included do |base|
    include ActionController::HttpAuthentication::Token::ControllerMethods
    class_attribute :allow_token_from_cookie
    class_attribute :allow_sign_in_token_from_params

    if Rails.env.development?
      def allow_token_from_cookie?
        return true if request&.referer&.[]("vandal")
        self.class.allow_token_from_cookie?
      end
    end
  end

  class_methods do
    # We want the API registration/session endpoints to _set_ a persistent http cookie,
    # but otherwise it should authenticate using an API token.
    def allow_token_from_cookie!
      self.allow_token_from_cookie = true
    end

    # Allows this controller to act as a passwordless sign-in endpoint.
    # This should be called before other authentication methods.
    def allow_sign_in_token_from_params!
      self.allow_sign_in_token_from_params = true
    end
  end

  def sign_in_user(user, organization = nil)
    request.session[:user_id] = user.id
    Current.user = user

    if organization && user.organizations.include?(organization)
      request.session[:organization_id] = organization.id
      Current.organization = organization
    else
      request.session[:organization_id] = nil
    end
  end

  def sign_out_user
    request.session[:user_id] = nil
  end

  def current_user_admin?
    current_user&.admin?
  end

  def current_user
    Current.user
  end

  def current_organization
    return unless current_user

    Current.organization
  end

  def current_roles
    return [] unless current_user && current_organization
    current_user.roles.where(organization: current_organization).pluck(:role_type)
  end

  def fetch_current_user
    if allow_sign_in_token_from_params? && (signed_id = params[:sid])
      token = SignInToken.find_signed!(signed_id)
      user = token.user
      sign_in_user(token.user, token.organization)
      user.sign_in_tokens.delete_all
      return if current_user
    end

    authenticate_with_http_token do |token, options|
      if (session = Session.find_by_token_for(:api, token)) && session.user
        sign_in_user(session.user, session.organization)
        Current.token_scope = :api
        # FIXME: this branch can probably be deprecated
      elsif (user = User.find_by_token_for(:api, token))
        sign_in_user(user)
        Current.token_scope = :api
      end
      return if current_user
    end

    if allow_token_from_cookie? && (user_id = request.session[:user_id])
      user = User.find(user_id)

      organization = if (org_id = request.session[:organization_id])
        Organization.find(org_id)
      end

      sign_in_user(user, organization)
    end
  rescue ActiveRecord::RecordNotFound
    nil
  rescue ActiveSupport::MessageVerifier::InvalidSignature => e
    handle_invalid_token_signature(e)
  end

  def set_current_request_details
    fetch_current_user
    Current.user_agent = request.user_agent
    Current.ip_address = request.ip
    Current.host = fetch_current_host
  end

  if Rails.env.development?
    # include host & port for development mode
    def fetch_current_host
      "#{request.scheme}://#{request.host_authority}"
    end
  else
    def fetch_current_host
      "#{request.scheme}://#{request.hostname}"
    end
  end

  def handle_invalid_token_signature(error)
    # Log the error for debugging purposes, but don't re-raise
    Rails.logger.warn("Invalid token signature encountered: #{error.message}")
    nil # Ensure fetch_current_user completes without setting Current.user
  end

  private

  def find_user_organization(organization)
    current_user.organizations.find!(organization.id)
  end
end
