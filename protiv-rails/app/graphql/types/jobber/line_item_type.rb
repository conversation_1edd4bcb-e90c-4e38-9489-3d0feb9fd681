# frozen_string_literal: true

module Types
  module Jobber
    class LineItemType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON><PERSON>'s unique identifier for the line item"
      field :product_or_service, Types::Jobber::ProductOrServiceType, null: false, description: "The product or service"
      field :quantity, Float, null: false, description: "The quantity"
      field :unit_cost, Float, null: false, description: "The unit cost"
      field :total, Float, null: false, description: "The total cost"
      field :description, String, null: true, description: "Custom description for this line item"

      # Methods to map to Protiv Milestone Item model
      def to_protiv_milestone_item(milestone_id, catalog_item_id)
        {
          remote_slug: object.id,
          milestone_id: milestone_id,
          catalog_item_id: catalog_item_id,
          quantity: object.quantity,
          cost_cents: (object.unit_cost * 100).to_i,
          total_cost_cents: (object.total * 100).to_i
        }
      end
    end
  end
end
