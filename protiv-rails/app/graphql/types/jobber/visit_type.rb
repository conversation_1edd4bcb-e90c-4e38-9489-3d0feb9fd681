# frozen_string_literal: true

module Types
  module Jobber
    class VisitType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON><PERSON>'s unique identifier for the visit"
      field :job_id, ID, null: false, description: "The ID of the job this visit belongs to"
      field :title, String, null: true, description: "The title of the visit"
      field :visit_status, String, null: false, description: "The status of the visit"

      field :property, Types::Jobber::PropertyType, null: true, description: "The property for this visit"
      field :assigned_users, [Types::Jobber::UserType], null: true, description: "Users assigned to this visit"
      field :line_items, [Types::Jobber::LineItemType], null: true, description: "Line items for this visit"
      field :timesheet_entries, [Types::Jobber::TimesheetEntryType], null: true, description: "Timesheet entries for this visit"

      field :start_at, GraphQL::Types::ISO8601DateTime, null: true, description: "Start time of the visit"
      field :end_at, GraphQL::Types::ISO8601DateTime, null: true, description: "End time of the visit"
      field :completed_at, GraphQL::Types::ISO8601DateTime, null: true, description: "When the visit was completed"

      # Methods to map to Protiv Milestone model
      def to_protiv_milestone(job_id)
        # Calculate budget values from line items
        labor_budget_cents = 0
        material_budget_cents = 0

        object.line_items&.each do |item|
          if item.product_or_service&.category == "LABOR"
            labor_budget_cents += (item.unit_cost * item.quantity * 100).to_i
          else
            material_budget_cents += (item.unit_cost * item.quantity * 100).to_i
          end
        end

        # Calculate time budget from scheduled duration
        seconds_budget = 0
        if object.start_at && object.end_at
          seconds_budget = (object.end_at - object.start_at).to_i
        end

        {
          remote_slug: object.id,
          job_id: job_id,
          name: object.title,
          status: map_visit_status(object.visit_status),
          seconds_budget: seconds_budget,
          labor_budget_cents: labor_budget_cents,
          material_budget_cents: material_budget_cents,
          manager_id: object.assigned_users&.first&.id, # First assigned user as manager
          scheduled_start_at: object.start_at,
          scheduled_end_at: object.end_at,
          completed_at: object.completed_at
        }
      end

      private

      def map_visit_status(jobber_status)
        case jobber_status
        when "SCHEDULED", "TRAVELING", "IN_PROGRESS"
          :in_progress
        when "COMPLETED"
          :completed
        when "CANCELED"
          :canceled
        else
          :waiting
        end
      end
    end
  end
end
