# frozen_string_literal: true

module Types
  module Jobber
    class UserType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON>ber's unique identifier for the user"
      field :name, Types::Jobber::NameType, null: false, description: "The user's name"
      field :email, String, null: false, description: "The user's email address"
      field :phone, String, null: true, description: "The user's phone number"
      field :is_account_admin, Boolean, null: false, description: "Whether the user is an account admin"
      field :is_account_owner, <PERSON><PERSON><PERSON>, null: false, description: "Whether the user is the account owner"
      field :status, String, null: false, description: "The user's status"

      # Methods to map to Protiv Identity model
      def to_protiv_identity(organization_id)
        {
          remote_slug: object.id,
          organization_id: organization_id,
          name: object.name.full,
          email: object.email,
          phone_number: object.phone,
          identity_type: :employee,
          crew_lead: object.is_account_admin || object.is_account_owner
        }
      end
    end
  end
end
