# frozen_string_literal: true

module Types
  module Jobber
    class TimesheetEntryType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON><PERSON>'s unique identifier for the timesheet entry"
      field :user, Types::Jobber::UserType, null: false, description: "The user who logged the time"
      field :duration, Integer, null: false, description: "The duration in seconds"
      field :labor_rate, Float, null: true, description: "The labor rate"
      field :start_time, GraphQL::Types::ISO8601DateTime, null: false, description: "When the time entry started"
      field :end_time, GraphQL::Types::ISO8601DateTime, null: true, description: "When the time entry ended"
      field :notes, String, null: true, description: "Notes for this time entry"

      # Methods to map to Protiv Milestone Time model
      def to_protiv_milestone_time(milestone_id, identity_id)
        {
          remote_slug: object.id,
          milestone_id: milestone_id,
          identity_id: identity_id,
          seconds: object.duration,
          labor_rate_cents: object.labor_rate ? (object.labor_rate * 100).to_i : nil,
          logged_at: object.start_time,
          notes: object.notes
        }
      end
    end
  end
end
