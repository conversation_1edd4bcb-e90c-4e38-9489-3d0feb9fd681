# frozen_string_literal: true

module Types
  module Jobber
    module Enums
      class VisitStatusTypeEnum < Types::BaseEnum
        value "SCHEDULED", "Visit is scheduled"
        value "TRAVELING", "En route to visit"
        value "IN_PROGRESS", "Visit is in progress"
        value "COMPLETED", "Visit is completed"
        value "CANCELED", "Visit is canceled"
        value "INCOMPLETE", "Visit is incomplete"
      end
    end
  end
end
