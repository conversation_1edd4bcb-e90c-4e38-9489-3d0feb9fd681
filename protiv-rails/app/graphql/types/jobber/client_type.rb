# frozen_string_literal: true

module Types
  module Jobber
    class ClientType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON><PERSON>'s unique identifier for the client"
      field :name, Types::Jobber::NameType, null: false, description: "The client's name"
      field :email, String, null: true, description: "The client's email address"
      field :phone, String, null: true, description: "The client's phone number"
      field :properties, [Types::Jobber::PropertyType], null: true, description: "Properties associated with this client"

      # Methods to map to Protiv Identity model
      def to_protiv_identity(organization_id)
        {
          remote_slug: object.id,
          organization_id: organization_id,
          name: object.name.full,
          email: object.email,
          phone_number: object.phone,
          identity_type: :client
        }
      end
    end
  end
end
