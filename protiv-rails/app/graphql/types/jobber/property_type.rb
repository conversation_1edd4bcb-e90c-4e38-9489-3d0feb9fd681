# frozen_string_literal: true

module Types
  module Jobber
    class PropertyType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON><PERSON>'s unique identifier for the property"
      field :client_id, ID, null: false, description: "The ID of the client this property belongs to"
      field :street, String, null: true, description: "The street address"
      field :city, String, null: true, description: "The city"
      field :province, String, null: true, description: "The province/state"
      field :postal_code, String, null: true, description: "The postal/zip code"
      field :country, String, null: true, description: "The country"

      # Methods to map to Protiv Property model
      def to_protiv_property(organization_id)
        {
          remote_slug: object.id,
          organization_id: organization_id,
          address: [object.street, object.city, object.province, object.postal_code, object.country].compact.join(", "),
          street: object.street,
          city: object.city,
          state: object.province,
          zip: object.postal_code,
          country: object.country
        }
      end
    end
  end
end
