# frozen_string_literal: true

module Types
  module Jobber
    class JobType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON><PERSON>'s unique identifier for the job"
      field :title, String, null: true, description: "The title of the job"
      field :job_number, Integer, null: true, description: "The job number"
      field :job_status, String, null: false, description: "The status of the job"
      field :job_type, String, null: false, description: "The type of job"

      field :client, Types::Jobber::ClientType, null: false, description: "The client associated with the job"
      field :property, Types::Jobber::PropertyType, null: true, description: "The property associated with the job"

      field :total, Float, null: false, description: "The total chargeable amount of the job"
      field :line_items, [Types::Jobber::LineItemType], null: false, description: "Line items associated with the job"
      field :visits, [Types::Jobber::VisitType], null: false, description: "Visits associated with the job"
      field :timesheet_entries, [Types::Jobber::TimesheetEntryType], null: false, description: "Timesheet entries for this job"

      field :start_at, GraphQL::Types::ISO8601DateTime, null: true, description: "Start date of the job"
      field :end_at, GraphQL::Types::ISO8601DateTime, null: true, description: "End date of the job"
      field :created_at, GraphQL::Types::ISO8601DateTime, null: false, description: "When the job was created"
      field :updated_at, GraphQL::Types::ISO8601DateTime, null: false, description: "When the job was last updated"
      field :completed_at, GraphQL::Types::ISO8601DateTime, null: true, description: "When the job was completed"

      # Methods to map to Protiv Job model
      def to_protiv_job(organization_id)
        {
          remote_slug: object.id,
          organization_id: organization_id,
          name: object.title,
          job_number: object.job_number.to_s,
          status: map_job_status(object.job_status),
          job_type: map_job_type(object.job_type),
          contract_price_cents: (object.total * 100).to_i,
          created_date: object.start_at || object.created_at,
          last_activity_at: object.updated_at,
          client_name: object.client&.name&.full
        }
      end

      private

      def map_job_status(jobber_status)
        case jobber_status
        when "ACTIVE", "IN_PROGRESS"
          :in_progress
        when "COMPLETED"
          :completed
        when "CANCELED"
          :canceled
        else
          :pending
        end
      end

      def map_job_type(jobber_type)
        case jobber_type
        when "ONE_OFF"
          "fixed"
        when "RECURRING"
          "recurring"
        else
          "fixed"
        end
      end
    end
  end
end
