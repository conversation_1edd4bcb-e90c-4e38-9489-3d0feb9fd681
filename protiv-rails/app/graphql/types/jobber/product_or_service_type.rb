# frozen_string_literal: true

module Types
  module Jobber
    class ProductOrServiceType < Types::Jobber::BaseObject
      field :id, ID, null: false, description: "<PERSON>ber's unique identifier for the product/service"
      field :name, String, null: false, description: "The name of the product/service"
      field :description, String, null: true, description: "The description of the product/service"
      field :default_unit_cost, Float, null: false, description: "The default unit cost"
      field :category, String, null: false, description: "The category of the product/service"
      field :taxable, Boolean, null: true, description: "Whether the product/service is taxable"
      field :visible, Boolean, null: true, description: "Whether the product/service is visible"

      # Methods to map to Protiv Catalog Item model
      def to_protiv_catalog_item(organization_id)
        {
          remote_slug: object.id,
          organization_id: organization_id,
          name: object.name,
          description: object.description,
          item_type: map_item_type(object.category),
          purchase_unit: "each",
          allocation_unit: "each",
          default_unit_cost_cents: (object.default_unit_cost * 100).to_i
        }
      end

      private

      def map_item_type(jobber_category)
        case jobber_category
        when "LABOR", "SERVICE"
          "Labor"
        when "PRODUCT", "MATERIAL"
          "Material"
        else
          "Other"
        end
      end
    end
  end
end
