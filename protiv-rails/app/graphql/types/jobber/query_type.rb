# frozen_string_literal: true

module Types
  module Jobber
    class QueryType < Types::BaseObject
      field :jobs, Types::Jobber::JobConnection, null: false do
        argument :filter, Types::Jobber::JobFilterInput, required: false
        argument :first, Integer, required: false
        argument :after, String, required: false
      end

      field :job, Types::Jobber::JobType, null: true do
        argument :id, ID, required: true
      end

      field :visits, Types::Jobber::VisitConnection, null: false do
        argument :filter, Types::Jobber::VisitFilterInput, required: false
        argument :first, Integer, required: false
        argument :after, String, required: false
      end

      field :visit, Types::Jobber::VisitType, null: true do
        argument :id, ID, required: true
      end

      field :users, [Types::Jobber::UserType], null: false

      field :products_and_services, [Types::Jobber::ProductOrServiceType], null: false
    end
  end
end
