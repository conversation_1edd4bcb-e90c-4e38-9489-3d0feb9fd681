# frozen_string_literal: true

class ServiceAdapter < Graphiti::Adapters::Abstract
  # NOTE: the ServiceAdapter exists to bridge non-ActiveRecord service objects,
  # with Graphiti's JSONAPI.
  # Resources using the ServiceAdapter may declare relationships or other attributes
  # that are important for the invocation of the service.
  #
  # ActiveRecord-backed relationships may be resolved their respective Graphiti
  # resources & policies.
  #
  # Custom policies are recommended in order to have more granular action-based permissions.
  # See the ClockIn service as an example.
  #
  # This adapter expects the resource model to conform to the following properties:
  # - has a nested Builder constant that inherits from ApplicationService::Builder
  # - the Builder should have writer methods for each of the attributes declared
  #   on the resource. Relationships _should_ have an attr_writer or attr_accessor
  #   for the id method (e.g. identity_id), and the relationship/permissions should be
  #   validated by the builder.
  #
  def paginate(scope, ...)
    scope
  end

  def transaction(model_class)
    yield
  end

  def build(model_class)
    model_class.const_get(:Builder).new.tap do |builder|
      builder.current_user = resource.current_user
      builder.current_organization = resource.current_organization
    end
  end

  def save(model)
    model.build_and_execute!
  end

  def base_scope
    {}
  end
end
