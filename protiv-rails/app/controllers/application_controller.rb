# frozen_string_literal: true

class ApplicationController < ActionController::Base
  include AuthenticationHelpers
  include Graphiti::Rails::Responders

  # TODO: figure out what is a good policy for this for production
  #
  # This was disabled because we ran into issues in development. In Chrome,
  # when emulating certain mobile devices using the inspector, it will send UA
  # strings that were deemed too old by this check.
  #
  # There are many options there, we can disable this for development only, or
  # we can customize the web inspector, or customize this list below. However,
  # we haven't really given this that much thoughts; there are also concerns
  # about this blocking search engine bots (for better or worse).
  #
  # Disabling for now, but it's probably worth revisiting this when we have
  # some cycles to spare.
  #
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  # allow_browser versions: :modern

  allow_token_from_cookie!

  before_action :set_current_request_details
  before_action :authenticate

  private

  def authenticate
    unless current_user
      redirect_to sign_in_path(redirect: request.original_fullpath)
    end
  end

  def require_sudo
    unless Current.session.sudo?
      redirect_to new_sessions_sudo_path(proceed_to_url: request.original_url)
    end
  end
end
