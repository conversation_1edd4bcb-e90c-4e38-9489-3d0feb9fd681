# frozen_string_literal: true

class OnboardingController < ApplicationController
  # Skip authentication for onboarding steps
  skip_before_action :authenticate

  # Use the onboarding layout for all actions in this controller
  layout "onboarding"

  def step_2
    # Track the current onboarding step in the session
    session[:onboarding_step] = 2
    render :step_2
  end

  def step_3
    # Track the current onboarding step in the session
    session[:onboarding_step] = 3
    render :step_3
  end
end
