# frozen_string_literal: true

class Sessions::PasswordlessesController < ApplicationController
  skip_before_action :authenticate

  before_action :set_user, only: :edit

  include I18n

  def new
  end

  # FIXME: why is this `edit`?
  def edit
    sign_in_user(@user)

    revoke_tokens

    redirect_to(root_path, notice: t("sessions.sign_in_success"))
  end

  def create
    if @user = User.find_by(email: params[:email], verified: true)
      send_passwordless_email
      redirect_to root_path, notice: t("sessions.check_email")
    else
      redirect_to new_sessions_passwordless_path, alert: t("sessions.verification_alert")
    end
  end

  private

  def set_user
    token = SignInToken.find_signed!(params[:sid])
    @user = token.user
  rescue
    redirect_to new_sessions_passwordless_path, alert: t("sessions.invalid_link")
  end

  def send_passwordless_email
    UserMailer.with(user: @user).passwordless.deliver_later
  end

  def revoke_tokens
    @user.sign_in_tokens.delete_all
  end
end
