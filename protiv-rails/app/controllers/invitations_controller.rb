# frozen_string_literal: true

class InvitationsController < ApplicationController
  skip_before_action :authenticate, only: [:accept]

  def accept
    @invitation = Invitation.find_by_token_for!(:add_user_to_organization, params[:token])

    if current_user
      @invitation.claim!(current_user)
      redirect_to root_path, notice: "You've been added to #{@invitation.organization.name}"
    else
      store_token_in_session
      redirect_to sign_in_path
    end
  rescue Invitation::AlreadyClaimed
    redirect_to root_path, alert: "This invitation has already been claimed"
  rescue Invitation::Expired
    redirect_to root_path, alert: "This invitation has expired"
  rescue ActiveSupport::MessageVerifier::InvalidSignature
    redirect_to root_path, alert: "Invalid invitation link"
  end

  private

  def store_token_in_session
    session[:invitation_token] = params[:token]
  end
end
