# frozen_string_literal: true

class Api::ProPaysController < Api::BaseController
  def index
    respond_with ProPayResource.all(params)
  end

  def show
    respond_with ProPayResource.find(params)
  end

  def create
    job_id = params[:data][:attributes][:job_id]
    milestone_ids = params[:data][:attributes][:milestone_ids] # Optional array of milestone IDs

    job = Job.find(job_id)

    pro_pays = if milestone_ids.present?
      # Create pro_pay for specific milestones
      create_milestone_pro_pays(job, milestone_ids)
    elsif job.job_type == "recurring"
      # Create pro_pay for all milestones
      create_milestone_pro_pays(job, job.milestones.pluck(:id))
    else
      # Create single pro_pay for the job
      [create_job_pro_pay(job)]
    end

    # Return the first ProPay since the frontend expects a single response
    # In the future, we might want to return an array of ProPays
    render jsonapi: ProPayResource.find(id: pro_pays.first.id, include: "jobs,milestones,source_route")
  rescue ActiveRecord::RecordNotFound
    render jsonapi_errors: { title: "Job not found" }, status: :not_found
  rescue StandardError => e
    render jsonapi_errors: { title: e.message }, status: :unprocessable_entity
  end

  def approve
    proxy = ProPayResource.find(params)
    pro_pay = proxy.to_a

    if pro_pay.approve!
      render jsonapi: proxy
    else
      raise
    end
  rescue => e
    render_errors(e, proxy)
  end

  private


  def render_errors(exception, proxy)
    pro_pay = proxy.to_a

    unless exception.message.blank?
      pro_pay.errors.add(:base, exception.message)
    end

    case exception
    when ActiveRecord::RecordNotFound
      render jsonapi_errors: proxy, status: :not_found
    when ActiveRecord::RecordInvalid
      render jsonapi_errors: proxy, status: :unprocessable_entity
    when AASM::InvalidTransition
      render jsonapi_errors: proxy, status: :unprocessable_entity
    else
      render jsonapi_errors: proxy, status: :internal_server_error
    end
  end

  def create_milestone_pro_pays(job, milestone_ids)
    milestones = Milestone.where(id: milestone_ids)

    milestones.map do |milestone|
      # Skip if milestone already has a ProPay
      next if milestone.resolved_pro_pay.present? or milestone.seconds_budget.zero?

      # Create the ProPay
      pro_pay = ProPay.create!(
        payout_type: "milestone",
        source_type: "manual",
        organization_id: job.organization_id,
        reference_bonus_pool: job.organization.default_bonus_pool,
        status: milestone.status == "completed" ? "completed" : "active"
      )

      # Create the SubProPay
      # TODO: use organization configuration when available for budget_type and distribution_type
      sub_pro_pay = SubProPay.create!(pro_pay: pro_pay,
                                      budget_type: "hours",
                                      distribution_type: "equal_rate",
                                      budget_minutes: milestone.seconds_budget / 60,
                                      reference_bonus_pool: job.organization.default_bonus_pool)

      sub_pro_pay_pro_payable = SubProPayProPayable.create!(sub_pro_pay: sub_pro_pay, pro_payable: milestone)

      # This is needed to save bonus_pool_values
      pro_pay.save!
      sub_pro_pay.save!
      sub_pro_pay_pro_payable.save!

      pro_pay
    end.compact # Remove nil values from skipped milestones
  end

  def create_job_pro_pay(job)
    # Don't create a ProPay if one already exists
    return if job.sub_pro_pay_pro_payable.present? or job.seconds_budget.zero?

    pro_pay = ProPay.create!(
      payout_type: "job",
      source_type: "manual",
      organization_id: job.organization_id,
      reference_bonus_pool: job.organization.default_bonus_pool,
      status: job.status == "completed" ? "completed" : "active"
    )

    # Create the SubProPay
    # TODO: use organization configuration when available for budget_type and distribution_type
    sub_pro_pay = SubProPay.create!(pro_pay: pro_pay,
                                    budget_type: "hours",
                                    distribution_type: "equal_rate",
                                    budget_minutes: job.seconds_budget / 60,
                                    reference_bonus_pool: job.organization.default_bonus_pool)


    # Create the SubProPayPayable for the job
    sub_pro_pay_pro_payable = SubProPayProPayable.create!(
      sub_pro_pay: sub_pro_pay,
      pro_payable: job
    )

    # This is needed to save bonus_pool_values
    pro_pay.save!
    sub_pro_pay.save!
    sub_pro_pay_pro_payable.save!

    pro_pay
  end
end
