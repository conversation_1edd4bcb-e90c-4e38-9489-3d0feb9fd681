# frozen_string_literal: true

class Api::StatementsController < Api::BaseController
  before_action :set_statement, only: [:show, :update, :destroy, :pay, :void, :open]

  def index
    respond_with StatementResource.all(params), meta: { available_statuses: %w[open paid void] }
  end

  def show
    respond_with StatementResource.find(params)
  end

  def create
    statement = StatementResource.build(params)

    if statement.save
      respond_with statement, status: :created
    else
      render_with_errors statement
    end
  end

  def update
    statement = StatementResource.find(params)

    if statement.update_attributes
      respond_with StatementResource.find(params)
    else
      render jsonapi_errors: @statement.errors, status: :unprocessable_entity
    end
  end

  def destroy
    @statement.destroy
    head :no_content
  end

  def pay
    proxy = StatementResource.find(params)
    statement = proxy.to_a

    if statement.pay!
      render jsonapi: proxy
    else
      raise
    end
  rescue => e
    render_errors(e, proxy)
  end

  def void
    proxy = StatementResource.find(params)
    statement = proxy.to_a

    if statement.void!
      render jsonapi: proxy
    else
      p statement.errors
      raise
    end
  rescue => e
    render_errors(e, proxy)
  end

  def open
    proxy = StatementResource.find(params)
    statement = proxy.to_a

    if statement.open!(params[:voided])
      render jsonapi: proxy
    else
      raise
    end
  rescue => e
    render_errors(e, proxy)
  end

  private

  def render_errors(exception, proxy)
    statement = proxy.to_a

    unless exception.message.blank?
      statement.errors.add(:base, exception.message)
    end

    case exception
    when ActiveRecord::RecordNotFound
      render jsonapi_errors: proxy, status: :not_found
    when ActiveRecord::RecordInvalid
      render jsonapi_errors: proxy, status: :unprocessable_entity
    when AASM::InvalidTransition
      render jsonapi_errors: proxy, status: :unprocessable_entity
    else
      render jsonapi_errors: proxy, status: :internal_server_error
    end
  end

  def set_statement
    @statement = Statement.find(params[:id])
  end

  def statement_params
    params.require(:statement).permit(:pay_period_id, :payable_id, :payable_type, :status, :net_amount_cents, :bonuses_cents, :deductions_cents, :balance_cents, :overtime_cents)
  end
end
