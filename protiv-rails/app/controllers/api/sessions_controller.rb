# frozen_string_literal: true

class Api::SessionsController < Api::BaseController
  include PermittedRedirectHelper

  skip_before_action :authenticate, only: %i[create destroy]

  def create
    if user = User.authenticate_by(email: params[:email], password: params[:password])
      params[:organization_id] ||= user.organization_ids.sort.first
      create_or_update(user)
    else
      render jsonapi: { errors: [jsonapi_error] }, status: :unauthorized
    end
  end

  def destroy
    # Sessions#destroy is essentially meaningless via the API,
    # but we want to clear cookies on the html/www subdomain
    redirect_path = sign_out_url(**permitted_redirect)

    render jsonapi: {
      data: {
        type: "next-action",
        id: SecureRandom.uuid,
        attributes: {
          redirect: redirect_path
        }
      }
    }, status: :accepted
  end

  def update
    create_or_update(current_user)
  end

  private

  def create_or_update(user)
    token_opts = { expires_in: 5.minutes }
    org = nil

    if organization_id
      if user.organizations.where(id: organization_id).exists?
        token_opts[:organization_id] = organization_id
        org = user.organizations.find(organization_id)
      else
        return render jsonapi: { errors: [jsonapi_error] }, status: :unauthorized
      end
    elsif user.organization_ids.any?
      org = user.organizations.first
    end

    if params[:target] == "token"
      session = Session.new(user, org)
      api_token = session.generate_token_for(:api)

      render jsonapi: {
        data: {
          type: "token",
          id: session.id,
          attributes: {
            token: api_token,
            user_id: user.id,
            user_email: user.email,
            organization_id: org&.id,
            organization_name: org&.name
          }
        }
      }
    else
      signed_id = user.sign_in_token(**token_opts)

      # this path will set the cookie and inject a token into the front end app.
      # As these redirects may indicate the app should do a full-page refresh, we should
      # forward any necessary state here, e.g. redirect url
      redirect_path = passwordless_url(sid: signed_id, **permitted_redirect)
      render jsonapi: {
        data: {
          type: "next-action",
          id: SecureRandom.uuid,
          attributes: {
            redirect: redirect_path
          }
        }
      }
    end
  end


  def jsonapi_error
    {
      id: SecureRandom.uuid,
      status: "401",
      code: "sessions.invalid-credentials",
      title: I18n.t("sessions.invalid_credentials"),
      detail: I18n.t("sessions.invalid_credentials_detail")
    }
  end

  def organization_id
    params[:organization_id]
  end
end
