# frozen_string_literal: true

class Api::ServicesController < Api::BaseController
  def index
    processed_params = params.permit(:page, :sort, :include, :format, filter: {}).to_h
    processed_params[:filter] ||= {}
    respond_with ServiceResource.all(ActionController::Parameters.new(processed_params))
  end

  def show
    respond_with ServiceResource.find(params)
  end

  def update
    service = ServiceResource.find(params)

    if service.update_attributes
      render jsonapi: service
    else
      render jsonapi_errors: service
    end
  rescue ArgumentError => e
    # Handle enum validation errors (e.g., invalid progress_type)
    if e.message.include?("is not a valid progress_type")
      render jsonapi: {
        errors: [{
                   code: "invalid_progress_type",
                   status: "422",
                   title: "Invalid progress type",
                   detail: "progress_type must be one of: percentage, units"
                 }]
      }, status: :unprocessable_entity
    else
      raise
    end
  end

  # Bulk update progress types for multiple services
  def bulk_update_progress_types
    # Check if service_updates parameter exists
    unless params.key?(:service_updates)
      return render jsonapi: {
        errors: [{
                   code: "missing_parameter",
                   status: "400",
                   title: "Missing required parameter",
                   detail: "param is missing or the value is empty: service_updates"
                 }]
      }, status: :bad_request
    end

    service_updates = params[:service_updates]

    # Handle empty array case
    if service_updates.empty?
      return render jsonapi: {
        data: [],
        meta: {
          updated_count: 0,
          operation: "bulk_progress_type_update"
        }
      }
    end

    updated_services = ServiceResource.bulk_update_progress_types(service_updates, graphiti_context)

    # Use standard JSON:API response format
    render jsonapi: {
      data: updated_services.map { |service|
        {
          type: "services",
          id: service.id.to_s,
          attributes: {
            name: service.name,
            progress_type: service.progress_type
          }
        }
      },
      meta: {
        updated_count: updated_services.length,
        operation: "bulk_progress_type_update"
      }
    }
  rescue ActiveRecord::RecordInvalid => e
    render jsonapi: {
      errors: [{
                 code: "validation_failed",
                 status: "422",
                 title: "Bulk update failed",
                 detail: e.message
               }]
    }, status: :unprocessable_entity
  rescue ActiveRecord::RecordNotFound => e
    render jsonapi: {
      errors: [{
                 code: "not_found",
                 status: "404",
                 title: "Service not found",
                 detail: e.message
               }]
    }, status: :not_found
  rescue ArgumentError => e
    # Handle enum validation errors (e.g., invalid progress_type)
    render jsonapi: {
      errors: [{
                 code: "invalid_progress_type",
                 status: "422",
                 title: "Invalid progress type",
                 detail: e.message
               }]
    }, status: :unprocessable_entity
  rescue ActionController::ParameterMissing => e
    render jsonapi: {
      errors: [{
                 code: "missing_parameter",
                 status: "400",
                 title: "Missing required parameter",
                 detail: e.message
               }]
    }, status: :bad_request
  end

  # Custom action for managing tracking materials
  def tracking_materials
    service = Service.find(params[:id])
    material_ids = params.require(:material_ids)

    # Find materials that belong to the same organization
    new_materials = CatalogItem.where(
      id: material_ids,
      organization: service.organization,
      item_type: "Material"
    )

    # Handle different HTTP methods
    case request.method
    when "POST"
      # Add materials to existing ones (avoid duplicates)
      existing_ids = service.tracking_materials.pluck(:id)
      all_material_ids = (existing_ids + new_materials.pluck(:id)).uniq
      all_materials = CatalogItem.where(id: all_material_ids, organization: service.organization, item_type: "Material")
      service.tracking_materials = all_materials
    when "PATCH"
      # Replace all materials with new ones
      service.tracking_materials = new_materials
    end

    # Simple response without including materials (client can refetch if needed)
    head :no_content
  rescue ActiveRecord::RecordNotFound
    render jsonapi: {
      errors: [{
                 code: "not_found",
                 status: "404",
                 title: "Service not found"
               }]
    }, status: :not_found
  rescue ActionController::ParameterMissing => e
    render jsonapi: {
      errors: [{
                 code: "missing_parameter",
                 status: "400",
                 title: "Missing required parameter",
                 detail: e.message
               }]
    }, status: :bad_request
  end
end
