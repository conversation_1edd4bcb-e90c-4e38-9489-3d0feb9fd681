# frozen_string_literal: true

class Api::UsersController < Api::BaseController
  skip_before_action :authenticate, only: [:verify_password_change]

  def index
    respond_with UserResource.all(params)
  end

  def show
    # Authorization happens at the resource level through UserPolicy which ensures a user can only view their own data
    respond_with UserResource.find(params)
  end

  # Email verification flag reset is handled by a `before_validation` callback in the User model.
  def update
    record = User.find(params[:id])
    raise Api::BaseController::NotAuthorized unless record == current_user

    user = UserResource.find(params)

    if user.update_attributes
      render jsonapi: user
    else
      render jsonapi_errors: user
    end
  end

  def change_password
    @user = User.find(params[:id])

    # Authorization check - users can only change their own password
    raise Api::BaseController::NotAuthorized unless @user == current_user

    service = ChangePasswordService.new(
      @user,
      password_params[:current_password],
      password_params[:new_password]
    )

    result = service.call

    if result.success?
      # Return a JSON:API compliant response with the message in a meta object
      render json: { meta: { message: "Password change initiated. Please check your email to verify." } }, status: :ok
    else
      # Return JSON:API compliant errors (assuming result.errors is structured appropriately)
      render json: { errors: result.errors }, status: :unprocessable_entity
    end
  end

  def verify_password_change
    @user = User.find(params[:id])
    token = params[:token]

    service = VerifyPasswordChangeService.new(@user, token)
    result = service.call

    if result.success?
      render json: { message: "Password change verified successfully." }, status: :ok
    else
      render json: { errors: service.errors.map { |error| { detail: error } } }, status: :unprocessable_entity
    end
  end

  private

  def password_params
    # Require attributes hash and permit password fields within it
    params.require(:data).require(:attributes).permit(:current_password, :new_password)
  end
end
