# frozen_string_literal: true

class Api::PasswordResetsController < Api::BaseController
  include PermittedRedirectHelper

  before_action :set_user, only: %i[ show update ]
  skip_before_action :authenticate

  rescue_from ActiveRecord::RecordNotFound do |exception|
    render jsonapi: {
      errors: [{
        id: SecureRandom.uuid,
        status: "401",
        code: "password-resets.token-expired",
        title: I18n.t("password_resets.token_expired"),
        detail: I18n.t("password_resets.token_expired_detail")
      }]
    }, status: 401
  end

  def create
    if @user = User.find_by(email: email_param, verified: true)
      send_password_reset_email
    end

    render jsonapi: {
      data: {
        type: "next-action",
        id: SecureRandom.uuid,
        attributes: {
          message: {
            title: I18n.t("password_resets.email_sent"),
            detail: I18n.t("password_resets.email_sent_detail")
          }
        }
      }
    }, status: :accepted
  end

  def show
    render jsonapi: {
      data: {
        type: "password-reset",
        id: params[:id],
        attributes: {
          email: @user.email
        }
      }
    }
  end

  def update
    # `permitted_redirect` may throw
    ApplicationRecord.transaction do
      @user.update!(password: password_param)

      # FIXME copied from Api::SessionsController#create_or_update
      token_opts = { expires_in: 5.minutes }

      signed_id = @user.sign_in_token(**token_opts)

      # this path will set the cookie and inject a token into the front end app.
      # As these redirects may indicate the app should do a full-page refresh, we should
      # forward any necessary state here, e.g. redirect url
      redirect_path = passwordless_url(sid: signed_id, **permitted_redirect)
      render jsonapi: {
        data: {
          type: "next-action",
          id: SecureRandom.uuid,
          attributes: {
            redirect: redirect_path
          }
        }
      }
    end
  end

  private

  def set_user
    @user = User.find_by_token_for!(:password_reset, params[:id])
  rescue StandardError
    raise ActiveRecord::RecordNotFound
  end

  def email_param
    params.require(:email)
  end

  def password_param
    params.require(:password)
  end

  def send_password_reset_email
    if Rails.env.development?
      Rails.logger.debug(
        "Password reset link for #{@user.name}: " +
        password_reset_url(@user.generate_token_for(:password_reset))
      )
    end

    UserMailer.with(user: @user).password_reset.deliver_later
  end
end
