# frozen_string_literal: true

class Api::BaseController < ActionController::API
  include ActionController::HttpAuthentication::Token::ControllerMethods
  include AuthenticationHelpers
  include Graphiti::Rails

  class NotAuthorized < StandardError
  end

  respond_to :jsonapi

  before_action :set_current_request_details
  before_action :authenticate, except: %i[healthcheck]

  rescue_from \
    "Api::BaseController::NotAuthorized",
    "Pundit::NotAuthorizedError",
    "ActiveRecord::ReadonlyAttributeError" do |exception|
    render jsonapi: {
      errors: { request: "Not authorized." }
    }, status: 401
  end

  rescue_from "Graphiti::Errors::RecordNotFound" do |exception|
    render jsonapi: {
      errors: { request: "record not found" }
    }, status: 404
  end

  rescue_from "TimeTracking::Error" do |exception|
    render jsonapi: {
      errors: { request: exception.message }
    }, status: 401
  end

  def healthcheck
    render json: { status: "ok" }
  end

  private

  def authenticate
    return if current_user
    raise NotAuthorized
  end

  def require_admin_role
    unless current_roles.include?(Role::ADMIN)
      raise NotAuthorized
    end
  end

  def require_manager_role
    unless current_roles.include?(Role::MANAGER)
      raise NotAuthorized
    end
  end

  def require_employee_role
    unless current_roles.include?(Role::EMPLOYEE)
      raise NotAuthorized
    end
  end
end
