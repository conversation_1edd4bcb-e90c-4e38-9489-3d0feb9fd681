# frozen_string_literal: true

class Api::StatementLineItemsController < Api::BaseController
  before_action :set_line_item, only: [:show, :update, :extend, :release, :void]

  def index
    respond_with StatementLineItemResource.all(params), meta: { available_statuses: %w[in_process held paid void] }
  end

  def show
    line_item = StatementLineItemResource.find(params)
    respond_with line_item
  end

  def create
    line_item = StatementLineItemResource.build(params)

    if line_item.save
      render jsonapi: line_item, status: :created
    else
      raise
    end
  rescue => e
    render_errors(e, line_item)
  end

  def update
    line_item = StatementLineItemResource.find(params)

    if line_item.update_attributes
      render jsonapi: line_item
    else
      render_with_errors line_item
    end
  end

  # Custom action: Extend
  def extend
    proxy = StatementLineItemResource.find(params)
    line_item = proxy.to_a

    if line_item.extend!(params[:days], params[:indefinitely])
      render jsonapi: proxy
    else
      raise
    end
  rescue => e
    render_errors(e, proxy)
  end

  # Custom action: Release
  def release
    proxy = StatementLineItemResource.find(params)
    line_item = proxy.to_a

    line_item.release!
    render jsonapi: proxy
  rescue => e
    render_errors(e, proxy)
  end

  # Custom action: Void
  def void
    proxy = StatementLineItemResource.find(params)
    line_item = proxy.to_a

    line_item.void!
    render jsonapi: proxy
  rescue => e
    render_errors(e, proxy)
  end

  private

  def render_errors(exception, proxy)
    line_item = proxy.to_a

    unless exception.message.blank?
      line_item.errors.add(:base, exception.message)
    end

    case exception
    when ActiveRecord::RecordNotFound
      render jsonapi_errors: proxy, status: :not_found
    when ActiveRecord::RecordInvalid
      render jsonapi_errors: proxy, status: :unprocessable_entity
    when AASM::InvalidTransition
      render jsonapi_errors: proxy, status: :unprocessable_entity
    else
      render jsonapi_errors: proxy, status: :internal_server_error
    end
  end

  def set_line_item
    @line_item = StatementLineItem.find(params[:id])
  end
end
