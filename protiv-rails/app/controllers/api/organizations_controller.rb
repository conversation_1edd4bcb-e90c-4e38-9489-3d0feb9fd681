# frozen_string_literal: true

class Api::OrganizationsController < Api::BaseController
  def index
    respond_with OrganizationResource.all(params)
  end

  def show
    respond_with OrganizationResource.find(params)
  end

  def create
    organization = OrganizationResource.build(params)

    if organization.save
      render jsonapi: organization, status: 201
    else
      render jsonapi_errors: organization
    end
  end

  def update
    organization = OrganizationResource.find(params)

    if organization.update_attributes
      render jsonapi: organization
    else
      render jsonapi_errors: organization
    end
  end
end
