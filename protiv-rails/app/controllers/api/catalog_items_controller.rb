# frozen_string_literal: true

class Api::CatalogItemsController < Api::BaseController
  def index
    # Filter by item_type if specified
    catalog_items = current_organization.catalog_items

    if params[:filter] && params[:filter][:item_type]
      catalog_items = catalog_items.where(item_type: params[:filter][:item_type])
    end

    # Manually build JSON:API response for catalog items
    response_data = catalog_items.map do |item|
      {
        id: item.id.to_s,
        type: "catalog-items",
        attributes: {
          name: item.name,
          item_type: item.item_type,
          description: item.description,
          purchase_unit: item.purchase_unit,
          allocation_unit: item.allocation_unit
        }
      }
    end

    render jsonapi: { data: response_data }
  end

  def fetch_for_signup
    # Get days_back parameter, default to 7
    days_back = params[:days_back]&.to_i || 7

    # Validate days_back is reasonable (1-90 days)
    days_back = 7 unless days_back.between?(1, 90)

    integration = current_organization.integrations.aspire.first

    if integration.nil?
      render json: { error: "No Aspire integration found" }, status: :unprocessable_entity
      return
    end

    # Trigger the background job with service filtering enabled
    # Note: Sidekiq requires all arguments to be JSON-serializable, so we pass as positional args
    Sync::FetchCatalogItemsForSignupJob.perform_async(
      integration.id,
      days_back,
      true # filter_by_selected_services
    )

    # Return current materials immediately (might be empty initially)
    materials = current_organization.catalog_items.material.order(:name)

    response_data = materials.map do |item|
      {
        id: item.id.to_s,
        type: "catalog-items",
        attributes: {
          name: item.name,
          item_type: item.item_type,
          description: item.description,
          purchase_unit: item.purchase_unit,
          allocation_unit: item.allocation_unit
        }
      }
    end

    render jsonapi: {
      data: response_data,
      meta: {
        days_back: days_back,
        job_queued: true,
        message: "Materials fetch job queued. Refresh in a few seconds to see results."
      }
    }
  end

  def show
    catalog_item = current_organization.catalog_items.find(params[:id])

    response_data = {
      id: catalog_item.id.to_s,
      type: "catalog-items",
      attributes: {
        name: catalog_item.name,
        item_type: catalog_item.item_type,
        description: catalog_item.description,
        purchase_unit: catalog_item.purchase_unit,
        allocation_unit: catalog_item.allocation_unit
      }
    }

    render jsonapi: { data: response_data }
  rescue ActiveRecord::RecordNotFound
    head :not_found
  end
end
