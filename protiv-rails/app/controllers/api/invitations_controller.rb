# frozen_string_literal: true

class Api::InvitationsController < Api::BaseController
  before_action :authenticate
  before_action :authorize_invitation, only: [:create]

  def create
    Rails.logger.info "Creating invitation with params: #{invitation_params.inspect}"
    Rails.logger.info "Email from params: #{invitation_params[:email]}"

    target_user = User.find_by(email: invitation_params[:email])
    unless target_user
      Rails.logger.info "User not found for email: #{invitation_params[:email]}"
      render_error(
        code: "not_found",
        status: :unprocessable_entity,
        title: "User not found",
        detail: "No registered user found with this email address"
      )
      return
    end

    if target_user.roles.exists?(organization: current_organization)
      Rails.logger.info "User #{target_user.email} is already a member of organization #{current_organization.id}"
      render_error(
        code: "already_member",
        status: :unprocessable_entity,
        title: "Already a member",
        detail: "User is already a member of this organization"
      )
      return
    end

    invitation = Invitation.new(
      organization: current_organization,
      invited_by: current_user,
      role_type: invitation_params[:role_type],
      email: invitation_params[:email]
    )

    if invitation.save
      Rails.logger.info "Invitation saved successfully with ID: #{invitation.id}"

      render json: {
        data: {
          id: invitation.id.to_s,
          type: "invitation",
          attributes: {
            role_type: invitation.role_type,
            email: invitation.email
          },
          relationships: {
            organization: { data: { id: invitation.organization.id.to_s, type: "organization" } },
            invited_by: { data: { id: invitation.invited_by.id.to_s, type: "user" } }
          }
        }
      }, status: :created
    else
      Rails.logger.error "Invitation failed to save: #{invitation.errors.full_messages}"
      render jsonapi_errors: invitation.errors, status: :unprocessable_entity
    end
  rescue ArgumentError => e
    Rails.logger.error "ArgumentError: #{e.message}"
    render_error(
      code: "invalid_role_type",
      status: :unprocessable_entity,
      title: "Invalid role type",
      detail: "role_type must be one of: admin, manager, employee"
    )
  rescue => e
    Rails.logger.error "Unexpected error: #{e.class} - #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise
  end

  private

  def invitation_params
    params.require(:data)
          .require(:attributes)
          .permit(:email, :role_type)
          .to_h
          .with_indifferent_access
  end

  def authorize_invitation
    return true if current_user.admin? ||
                  (current_organization && current_user.roles.where(organization: current_organization, role_type: Role::ADMIN).exists?)

    render_error(
      code: "unauthorized",
      status: :forbidden,
      title: "Unauthorized",
      detail: "You don't have permission to invite users to this organization"
    )
  end

  def render_error(code:, status:, title:, detail:)
    errors = [{
      code: code,
      status: Rack::Utils.status_code(status).to_s,
      title: title,
      detail: detail
    }]

    render json: { errors: errors }, status: status
  end
end
