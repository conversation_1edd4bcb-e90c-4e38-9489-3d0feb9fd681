# frozen_string_literal: true

class Api::IntegrationsController < Api::BaseController
  before_action :require_admin_role, only: [:create, :update]

  def index
    respond_with IntegrationResource.all(params)
  end

  def show
    respond_with IntegrationResource.find(params)
  end

  def create
    integration = IntegrationResource.build(params)

    if integration.save
      render jsonapi: integration, status: 201
    else
      render jsonapi_errors: integration
    end
  end

  def update
    integration = IntegrationResource.find(params)

    if integration.update_attributes
      render jsonapi: integration
    else
      render jsonapi_errors: integration
    end
  end
end
