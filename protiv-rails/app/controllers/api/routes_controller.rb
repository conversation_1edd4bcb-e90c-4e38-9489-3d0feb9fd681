# frozen_string_literal: true

class Api::RoutesController < Api::BaseController
  before_action :require_manager_role, only: [:update]

  def index
    respond_with RouteResource.all(params)
  end

  def show
    respond_with RouteResource.find(params)
  end

  def update
    route = RouteResource.find(params)

    if route.update_attributes
      render jsonapi: route
    else
      render jsonapi_errors: route
    end
  end
end
