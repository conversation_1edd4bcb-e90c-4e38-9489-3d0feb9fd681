# frozen_string_literal: true

module Api
  module Integrations
    module Jobber
      module Oauth
        class JobberOauthController < Api::BaseController
          # Skip authentication for OAuth flow endpoints
          skip_before_action :authenticate, only: [:authorize, :callback, :debug_credentials]

          # Step 1: Initiate OAuth flow
          def authorize
            state = JobberOauthService.generate_state
            store_oauth_session_data(state)

            oauth_service = JobberOauthService.new
            authorization_url = oauth_service.authorization_url(state: state)

            Rails.logger.info("Initiating Jobber OAuth flow with state: #{state}")
            redirect_to authorization_url, allow_other_host: true
          end

          # Step 3: Handle OAuth callback
          def callback
            Rails.logger.info("Jobber OAuth: Processing callback")

            authorization_code = params[:code]
            received_state = params[:state]
            session_data = extract_oauth_session_data

            # Handle authorization denial
            return render_authorization_denied if authorization_code.blank?

            # Validate organization and get current organization
            organization = resolve_organization(session_data)
            return render_organization_error unless organization

            # Handle OAuth callback through service
            oauth_service = JobberOauthService.new
            result = oauth_service.handle_oauth_callback(
              authorization_code: authorization_code,
              received_state: received_state,
              stored_state: session_data[:stored_state],
              organization: organization
            )

            if result[:success]
              render_callback_success(result[:integration], result[:account_info])
            else
              render_callback_error(result)
            end
          end

          # Refresh token endpoint
          def refresh_token
            integration = current_organization.integrations.find_by(source: :jobber)

            unless integration
              return render_integration_not_found
            end

            oauth_service = JobberOauthService.new
            result = oauth_service.refresh_integration_token(integration)

            if result[:success]
              render_token_refresh_success(integration, result[:credential])
            else
              render_token_refresh_error(result)
            end
          end

          # Temporary debug endpoint
          def debug_credentials
            render jsonapi: {
              data: {
                type: "debug-info",
                id: SecureRandom.uuid,
                attributes: {
                  client_id: jobber_client_id,
                  redirect_uri: jobber_redirect_uri,
                  client_secret_first_chars: jobber_client_secret[0..5] + "...",
                  env_client_id: ENV["JOBBER_CLIENT_ID"],
                  env_redirect_uri: ENV["JOBBER_REDIRECT_URI"]
                }
              }
            }
          end

          private

          # Session management methods
          def store_oauth_session_data(state)
            session[:jobber_oauth_state] = state

            if current_user && current_organization
              session[:jobber_oauth_organization_id] = current_organization.id
            else
              # For testing purposes, set test mode
              session[:jobber_oauth_test_mode] = true
            end
          end

          def extract_oauth_session_data
            stored_state = session[:jobber_oauth_state]
            organization_id = session[:jobber_oauth_organization_id]
            test_mode = session[:jobber_oauth_test_mode]

            # Clear session data
            session.delete(:jobber_oauth_state)
            session.delete(:jobber_oauth_organization_id)
            session.delete(:jobber_oauth_test_mode)

            {
              stored_state: stored_state,
              organization_id: organization_id,
              test_mode: test_mode
            }
          end

          # Organization resolution
          def resolve_organization(session_data)
            # TODO: use current_organization
            Organization.first
          end

          # Render methods for different response types
          def render_authorization_denied
            render jsonapi: {
              errors: [{
                title: "Authorization Denied",
                detail: "User denied authorization or authorization failed"
              }]
            }, status: :unauthorized
          end

          def render_organization_error
            render jsonapi: {
              errors: [{
                title: "Invalid Organization",
                detail: "Organization validation failed"
              }]
            }, status: :forbidden
          end

          def render_callback_success(integration, account_info)
            Rails.logger.info("Jobber OAuth: Integration ##{integration.id} for organization ##{integration.organization_id} created successfully")

            render jsonapi: {
              data: {
                type: "integration",
                id: integration.id.to_s,
                attributes: {
                  source: integration.source,
                  organization_id: integration.organization_id,
                  account_info: account_info,
                  message: "Jobber integration connected successfully"
                }
              }
            }
          end

          def render_callback_error(result)
            error_title = case result[:error_type]
            when :invalid_state
                                         "Invalid State"
            when :token_exchange_failed
                                         "Token Exchange Failed"
            when :save_failed
                                         "Integration Save Failed"
            else
                                         "OAuth Callback Failed"
            end

            status = case result[:error_type]
            when :invalid_state
                      :bad_request
            when :token_exchange_failed, :save_failed
                      :unprocessable_entity
            else
                      :internal_server_error
            end

            render jsonapi: {
              errors: [{
                title: error_title,
                detail: result[:error]
              }]
            }, status: status
          end

          def render_integration_not_found
            render jsonapi: {
              errors: [{
                title: "No Refresh Token",
                detail: "No refresh token available for this integration"
              }]
            }, status: :not_found
          end

          def render_token_refresh_success(integration, credential)
            render jsonapi: {
              data: {
                type: "token-refresh",
                id: integration.id.to_s,
                attributes: {
                  message: "Token refreshed successfully",
                  expires_at: credential.expires_at
                }
              }
            }
          end

          def render_token_refresh_error(result)
            render jsonapi: {
              errors: [{
                title: "Token Refresh Failed",
                detail: result[:error] || "Failed to refresh access token"
              }]
            }, status: :unprocessable_entity
          end

          # Debug credential methods (temporary)
          # FIXME: remove this and use only current organization.integration.find_by(source: :jobber) client and secret
          def jobber_client_id
            Rails.application.credentials.dig(:jobber, :client_id) ||
              ENV["JOBBER_CLIENT_ID"]
          end

          def jobber_client_secret
            Rails.application.credentials.dig(:jobber, :client_secret) ||
              ENV["JOBBER_CLIENT_SECRET"]
          end

          def jobber_redirect_uri
            Rails.application.credentials.dig(:jobber, :redirect_uri) ||
              ENV["JOBBER_REDIRECT_URI"] ||
              "#{request.base_url}/api/v2/integrations/jobber/oauth/callback"
          end
        end
      end
    end
  end
end
