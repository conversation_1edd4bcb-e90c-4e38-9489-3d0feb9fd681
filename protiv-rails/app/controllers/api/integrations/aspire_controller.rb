# frozen_string_literal: true

module Api
  module Integrations
    class AspireController < Api::BaseController
      before_action :require_admin_role

      def create
        # Authorization is handled by the require_admin_role before_action

        integration = Integration.find_or_initialize_by(
          organization: current_organization,
          source: :aspire
        )

        integration.client_id = params[:client_id]
        integration.secret = params[:secret_key]
        integration.status = :pending if integration.respond_to?(:status=)

        if integration.save && integration.respond_to?(:sync_all)
          # Log the successful save and sync initiation
          Rails.logger.info("AspireController: Integration ##{integration.id} for organization ##{integration.organization_id} saved successfully")
          Rails.logger.info("AspireController: Initiating sync_all for integration ##{integration.id}")

          # Trigger background sync
          integration.sync_all(async: true)

          Rails.logger.info("AspireController: Sync job queued for integration ##{integration.id}")

          render json: {
            message: "Aspire credentials saved.",
            organization_id: integration.organization_id
          }, status: :ok
        elsif integration.save
          # Integration saved but sync_all method not available
          Rails.logger.info("AspireController: Integration ##{integration.id} for organization ##{integration.organization_id} saved successfully")
          Rails.logger.warn("AspireController: sync_all method not available for integration ##{integration.id}")

          render json: {
            message: "Aspire credentials saved, but sync not available.",
            organization_id: integration.organization_id
          }, status: :ok
        else
          # Log the validation errors
          Rails.logger.error("AspireController: Failed to save integration for organization ##{current_organization.id}")
          Rails.logger.error("AspireController: Validation errors: #{integration.errors.full_messages.join(', ')}")

          render json: { errors: integration.errors.full_messages }, status: :unprocessable_entity
        end
      end
    end
  end
end
