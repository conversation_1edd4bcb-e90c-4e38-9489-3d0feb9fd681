# frozen_string_literal: true

class Api::MilestonesController < Api::BaseController
  def show
    respond_with MilestoneResource.find(params)
  end

  def index
    respond_with MilestoneResource.all(params)
  end

  def update
    milestone = MilestoneResource.find(params)

    if milestone.update_attributes
      # After successful update, check if we should auto-assign material
      assignment_service = MilestoneProgressAssignmentService.new(milestone.data)
      assignment_service.auto_assign_material_if_possible

      render jsonapi: milestone
    else
      render jsonapi_errors: milestone
    end
  end

  private

  # Legacy method - keeping for backward compatibility but using service class
  def auto_assign_material_if_needed(milestone)
    assignment_service = MilestoneProgressAssignmentService.new(milestone)
    assignment_service.auto_assign_material_if_possible
  end
end
