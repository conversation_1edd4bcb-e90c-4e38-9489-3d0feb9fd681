# frozen_string_literal: true

class Api::LineItemCategoriesController < Api::BaseController
  before_action :set_category, only: [:show]

  def index
    respond_with LineItemCategoryResource.all(params)
  end

  def show
    line_item = LineItemCategoryResource.find(params)
    respond_with line_item
  end

  private

  def render_errors(exception, proxy)
    category = proxy.to_a

    unless exception.message.blank?
      category.errors.add(:base, exception.message)
    end

    case exception
    when ActiveRecord::RecordNotFound
      render jsonapi_errors: proxy, status: :not_found
    when ActiveRecord::RecordInvalid
      render jsonapi_errors: proxy, status: :unprocessable_entity
    when AASM::InvalidTransition
      render jsonapi_errors: proxy, status: :unprocessable_entity
    else
      render jsonapi_errors: proxy, status: :internal_server_error
    end
  end

  def set_category
    @category = LineItemCategory.find(params[:id])
  end
end
