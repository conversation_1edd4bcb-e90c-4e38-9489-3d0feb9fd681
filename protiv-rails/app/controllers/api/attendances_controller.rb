# frozen_string_literal: true

class Api::AttendancesController < Api::BaseController
  before_action :require_manager_identity

  def index
    respond_with AttendanceResource.all(params)
  end

  def show
    respond_with AttendanceResource.find(params)
  end

  def create
    attendance = AttendanceResource.build(params)

    if attendance.save
      render jsonapi: attendance
    else
      render jsonapi_errors: attendance
    end
  end

  def update
    attendance = AttendanceResource.find(params)

    if attendance.update_attributes
      render jsonapi: attendance
    else
      render jsonapi_errors: attendance
    end
  end

  private

  def require_manager_identity
    return true if Current.manager_identity.present?
    raise Api::BaseController::NotAuthorized
  end
end
