# frozen_string_literal: true

module Api
  class LaborEfficiencyBenchmarksController < Api::BaseController
    def index
      benchmarks = LaborEfficiencyBenchmarkResource.all(params)
      respond_with benchmarks
    end

    def show
      benchmark = LaborEfficiencyBenchmarkResource.find(params)
      respond_with benchmark
    end

    def update
      benchmark = LaborEfficiencyBenchmarkResource.find(params)

      if benchmark.update_attributes
        render jsonapi: benchmark
      else
        render jsonapi_errors: benchmark
      end
    end

    def reset
      # Find the organization's benchmark or create a new one
      organization_id = params[:organization_id] || Current.organization.id

      # Use the service to set the benchmark
      benchmark_manager = Metrics::BenchmarkManager.new(Organization.find(organization_id))

      # Use custom percentage if provided, otherwise reset to default
      if params[:target_percentage].present?
        target_percentage = params[:target_percentage].to_f
        benchmark = benchmark_manager.set_custom_percentage(target_percentage)
      else
        benchmark = benchmark_manager.reset_to_default
      end

      # Return a simple JSON response with the updated benchmark
      render json: {
        data: {
          id: benchmark.id,
          type: "labor-efficiency-benchmarks",
          attributes: {
            target_percentage: benchmark.target_percentage,
            level: benchmark.level,
            active: benchmark.active
          },
          relationships: {
            organization: {
              data: { id: benchmark.organization_id, type: "organizations" }
            }
          }
        }
      }
    end
  end
end
