# frozen_string_literal: true

class Api::AccountsController < Api::BaseController
  skip_before_action :authenticate, only: %i[create]

  def show
    begin
      # Check if the requested account belongs to the current user
      requested_id = params[:id]

      # Handle both numeric IDs (account-X) and email usernames
      if requested_id.start_with?("account-")
        requested_id = requested_id.split("-").last
      elsif requested_id.include?("@")
        username = requested_id.split("@").first
        requested_id = "#{username}@appleseed.com"
      end

      unless current_user && (current_user.id.to_s == requested_id.to_s || current_user.email == requested_id)
        render json: {
          errors: {
            request: "record not found"
          }
        }, status: :not_found, content_type: "application/vnd.api+json"
        return
      end

      respond_with AccountResource.find(params.merge(include: ["user", "organizations"]))
    rescue Graphiti::Errors::RecordNotFound
      render json: {
        errors: {
          request: "record not found"
        }
      }, status: :not_found, content_type: "application/vnd.api+json"
    end
  end

  def create
    account = AccountResource.build(params.merge(include: ["user", "organizations"]))

    if account.save
      render jsonapi: account, include: ["user", "organizations"], status: 201
    else
      render jsonapi_errors: account
    end
  end
end
