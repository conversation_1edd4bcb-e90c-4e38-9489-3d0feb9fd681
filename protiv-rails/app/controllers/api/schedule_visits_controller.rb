# frozen_string_literal: true

class Api::ScheduleVisitsController < Api::BaseController
  def index
    respond_with ScheduleVisitResource.all(params), meta: {
      available_filters: {
        date_range: %w[7d 14d 30d all],
        upcoming: true,
        route_id: "integer",
        milestone_id: "integer",
        job_id: "integer",
        crew_lead_id: "integer"
      }
    }
  end

  def show
    respond_with ScheduleVisitResource.find(params)
  end
end
