# frozen_string_literal: true

class Api::EmployeeMilestoneTimesController < Api::BaseController
  def index
    employee_milestone_times = EmployeeMilestoneTimeResource.all(params)

    # Initialize meta hash with pagination
    employee_milestone_times.meta[:pagination] = {}

    # Calculate pagination metadata
    page_number = params.dig(:page, :number)&.to_i || 1
    page_size = params.dig(:page, :size)&.to_i || 10

    # Execute a direct SQL count query to get the total number of records
    begin
      # Build a query that applies the same filters as the main query
      query = EmployeeMilestoneTime.joins(:milestone)
                .joins("INNER JOIN jobs ON milestones.job_id = jobs.id")
                .joins("INNER JOIN organizations ON jobs.organization_id = organizations.id")
                .joins("INNER JOIN roles ON organizations.id = roles.organization_id")
                .where("roles.active = ? AND roles.user_id = ? AND jobs.organization_id = ?",
                       true, current_user.id, current_organization.id)

      # Apply the job_id filter if present
      if params.dig(:filter, :job_id).present?
        job_id = params.dig(:filter, :job_id)
        query = query.joins(:milestone).where(milestones: { job_id: job_id })
      end

      # Apply the identity_id filter if present
      if params.dig(:filter, :identity_id).present?
        identity_id = params.dig(:filter, :identity_id)
        query = query.where(identity_id: identity_id)
      end

      # Apply the milestone_id filter if present
      if params.dig(:filter, :milestone_id).present?
        milestone_id = params.dig(:filter, :milestone_id)
        query = query.where(milestone_id: milestone_id)
      end

      # Count the total records
      Rails.logger.info("Executing count query: #{query.to_sql}")
      total_count = query.count
      Rails.logger.info("Count query result: #{total_count}")

      # Add the total_count to the meta data
      employee_milestone_times.meta[:pagination][:total_count] = total_count
    rescue => e
      # Log the error and fall back to the current page size
      Rails.logger.error("Error getting total count: #{e.message}")
      total_count = employee_milestone_times.data.size

      # Add the total_count to the meta data even in case of error
      employee_milestone_times.meta[:pagination][:total_count] = total_count
    end

    # Calculate total pages
    total_pages = (total_count.to_f / page_size).ceil

    # Update the pagination metadata
    employee_milestone_times.meta[:pagination][:total_pages] = total_pages
    employee_milestone_times.meta[:pagination][:current_page] = page_number
    total_pages = 1 if total_pages < 1

    # We don't need to add the meta data here since we've already added it to the employee_milestone_times object
    respond_with employee_milestone_times
  end
end
