# frozen_string_literal: true

class Api::JobDetailsController < Api::BaseController
  before_action :require_organization

  def show
    respond_with JobDetailsResource.find(params)
  end

  private

  def current_organization
    ActiveSupport::Deprecation.new("remove this override when the org-in-session branch is merged")
    current_user&.organizations&.first
  end

  def require_organization
    raise Api::BaseController::NotAuthorized unless current_organization
  end
end
