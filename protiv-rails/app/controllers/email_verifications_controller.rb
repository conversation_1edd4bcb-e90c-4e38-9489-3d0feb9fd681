# frozen_string_literal: true

class EmailVerificationsController < ApplicationController
  before_action :set_user, only: :show
  before_action :authenticate, only: :create

  def show
    @user.update! verified: true
    redirect_to root_path, notice: I18n.t("email_verifications.success")
  end

  def create
    send_email_verification
    redirect_to root_path, notice: I18n.t("email_verifications.notice_sent")
  end

  private

  def set_user
    @user = User.find_by_token_for!(:email_verification, params[:sid])
  rescue StandardError
    redirect_to edit_email_path, alert: I18n.t("email_verifications.invalid_token")
  end

  def send_email_verification
    UserMailer.with(user: Current.user).email_verification.deliver_later
  end
end
