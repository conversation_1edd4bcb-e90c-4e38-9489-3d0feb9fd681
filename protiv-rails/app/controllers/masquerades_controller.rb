# frozen_string_literal: true

class MasqueradesController < ApplicationController
  before_action :authorize
  before_action :set_user

  def create
    # FIXME
    session_record = @user.sessions.create!
    cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

    redirect_to root_path, notice: "Signed in successfully"
  end

  private

  def set_user
    @user = User.find(params[:user_id])
  end

  def authorize
    unless Rails.env.development? || current_user_admin?
      redirect_to(root_path, alert: "You must be in development")
    end
  end
end
