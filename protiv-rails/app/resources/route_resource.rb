# frozen_string_literal: true

class RouteResource < ApplicationResource
  belongs_to :organization, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :branch, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :manager, readable: true, writable: false, always_include_resource_ids: true, resource: IdentityResource
  belongs_to :crew_lead, readable: true, writable: false, always_include_resource_ids: true, resource: IdentityResource

  # Add has_many relationship to schedule visits
  has_many :schedule_visits

  attribute :name, :string, readable: true, writable: false
  attribute :create_grouped_pro_pays, :boolean, readable: true, writable: true

  filter :pro_pay_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(:pro_pays).where(pro_pays: { id: value })
    end
  end

  def base_scope
    super.includes(:organization, :branch, :manager, :crew_lead, :schedule_visits)
  end
end
