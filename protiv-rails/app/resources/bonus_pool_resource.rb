# frozen_string_literal: true

class BonusPoolResource < ApplicationResource
  # Attributes
  attribute :name, :string

  attribute :created_at, :datetime, readable: true, writable: false
  attribute :updated_at, :datetime, readable: true, writable: false

  attribute :company_percent_hundredths, :integer
  attribute :manager_percent_hundredths, :integer
  attribute :crew_percent_hundredths, :integer
  attribute :crew_lead_percent_hundredths, :integer
  attribute :other_percent_hundredths, :integer
  attribute :crew_retention_percent_hundredths, :integer

  belongs_to :organization, readable: true, writable: false, always_include_resource_ids: true

  def base_scope
    super
  end
end
