# frozen_string_literal: true

class SubProPayResource < ApplicationResource
  belongs_to :pro_pay
  has_many :sub_pro_pay_pro_payables, resource: SubProPayPayableResource

  attribute :pro_pay_id, :integer, only: [:filterable]
  attribute :total_hours_worked, :float

  filter :pro_pay_id, :integer

  filter :milestone_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.eager_load(:sub_pro_pay_pro_payables)
           .merge(SubProPayProPayable.where(pro_payable_id: value, pro_payable_type: "Milestone"))
    end
  end

  filter :job_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(:sub_pro_pay_pro_payables)
           .merge(SubProPayProPayable.where(pro_payable: value, pro_payable_type: "Job"))
    end
  end
end
