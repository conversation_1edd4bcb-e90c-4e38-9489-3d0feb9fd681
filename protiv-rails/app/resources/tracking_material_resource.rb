# frozen_string_literal: true

class TrackingMaterialResource < ApplicationResource
  self.adapter = Graphiti::Adapters::Null
  has_one :catalog_item, resource: CatalogItemResource, always_include_resource_ids: true

  attribute :milestone_id, :integer
  attribute :material_name, :string
  attribute :unit_of_measure, :string
  attribute :budget_quantity, :float # editable
  attribute :actual_quantity, :float
  attribute :remaining_quantity, :float
  attribute :percent_complete, :float
  attribute :install_rate, :money # labor budget / budget qty
  attribute :install_rate_per_hour, :float # labor hours / budget qty

  def base_scope
    []
  end
end
