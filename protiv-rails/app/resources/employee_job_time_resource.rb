# frozen_string_literal: true

class EmployeeJobTimeResource < ApplicationResource
  self.type = :"employee-job-time"

  belongs_to :job
  belongs_to :identity

  attribute :job_id, :integer, only: [:filterable]
  attribute :identity_id, :integer, only: [:filterable]
  attribute :duration_seconds, :integer
  attribute :start_time, :datetime
  attribute :end_time, :datetime
  attribute :base_hourly_rate, :money
  attribute :labor_cost, :money

  # Default scope
  def base_scope
    EmployeeJobTime.all
  end

  # Support filtering by job_id
  filter :job_id, :integer do
    eq do |scope, value|
      scope.where(job_id: value)
    end
  end

  # Support filtering by identity_id
  filter :identity_id, :integer do
    eq do |scope, value|
      scope.where(identity_id: value)
    end
  end
end
