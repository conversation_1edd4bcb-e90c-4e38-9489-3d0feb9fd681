# frozen_string_literal: true

class AttendanceResource < ApplicationResource
  belongs_to :identity, writable: :true
  belongs_to :milestone, writable: :true
  belongs_to :manager, resource: IdentityResource, writable: false
  belongs_to :route, resource: RouteResource, writable: true

  attribute :started_at, :datetime, writable: true
  attribute :ended_at, :datetime, writable: true
  attribute :break_time_seconds, :integer, writable: true

  filter :open, :boolean do
    eq do |scope, value|
      if value
        scope.open
      else
        scope.closed
      end
    end
  end

  def build(model, meta = nil)
    super.tap do |instance|
      instance.created_by_user = current_user
      instance.manager = Current.manager_identity
      instance.organization = current_organization
    end
  end
end
