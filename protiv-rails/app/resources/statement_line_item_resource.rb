# frozen_string_literal: true

class StatementLineItemResource < ApplicationResource
  # Attributes
  attribute :amount, :money, readable: true, writable: true
  attribute :status, :string_enum, allow: StatementLineItem.statuses.values, filterable: true

  attribute :code, :string

  attribute :held_days, :integer
  attribute :held_indefinitely, :boolean

  attribute :identity_id, :integer, writable: true
  attribute :statement_id, :integer
  attribute :pro_pay_id, :integer
  attribute :line_item_category_id, :integer, writable: true

  attribute :source_note, :string, writable: true
  attribute :category_note, :string, writable: true

  attribute :created_at, :datetime, readable: true, writable: false
  attribute :updated_at, :datetime, readable: true, writable: false

  # Relationships
  belongs_to :identity, always_include_resource_ids: true, resource: IdentityResource, writable: true
  belongs_to :statement, always_include_resource_ids: true, resource: StatementResource, writable: true
  belongs_to :pro_pay, always_include_resource_ids: true, resource: ProPayResource
  belongs_to :job, always_include_resource_ids: true, resource: JobResource, writable: true
  belongs_to :category, as: :line_item_category, always_include_resource_ids: true, resource: LineItemCategoryResource, writable: true

  # Filters
  filter :status, only: :eq, allow: StatementLineItem.statuses.values
  # filter :hold_until, :datetime do
  #   eq do |scope, value|
  #     scope.where(hold_until: value)
  #   end
  # end

  def base_scope
    super.includes(:statement, :line_item_category, :identity)
    super.order(id: :asc)
  end
end
