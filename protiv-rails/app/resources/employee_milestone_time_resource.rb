# frozen_string_literal: true

class EmployeeMilestoneTimeResource < ApplicationResource
  belongs_to :milestone, always_include_resource_ids: true
  belongs_to :job, foreign_key: :job_id, primary_key: :id, resource: JobResource

  attribute :milestone_id, :integer, only: [:filterable]
  attribute :job_id, :integer, only: [:filterable]
  attribute :duration_seconds, :integer
  attribute :start_time, :datetime
  attribute :end_time, :datetime
  attribute :base_hourly_rate, :money
  attribute :labor_cost, :money

  belongs_to :identity, always_include_resource_ids: true
  attribute :identity_id, :integer, only: [:filterable]

  def base_scope
    super.includes(:milestone, :identity, :job)
  end

  # Custom filter for job_id that goes through milestone's job relationship
  filter :job_id, :integer do
    eq do |scope, value|
      scope.joins(:milestone).where(milestones: { job_id: value })
    end
  end
end
