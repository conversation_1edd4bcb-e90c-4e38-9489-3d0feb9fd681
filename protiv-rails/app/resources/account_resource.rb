# frozen_string_literal: true

class AccountResource < ApplicationResource
  class Adapter < Graphiti::Adapters::Null
    def filter_integer_eq(scope, attribute, value)
      raise "unsupported attribute #{attribute}" unless attribute == :id

      # If the ID matches the current user's ID, return the account
      if value.to_s == Current.user&.id.to_s
        scope
      else
        # Return empty array to trigger RecordNotFound
        []
      end
    end

    def base_scope(model)
      [Account.current]
    end
  end

  self.adapter = Adapter

  attribute :id, :string do
    if context.respond_to?(:api_recorder) && context.api_recorder
      # Use the mapped user ID for the account ID since they're the same entity
      mapped_id = context.api_recorder.instance_variable_get(:@object_ids).dig("user", @object.id.to_s)
      mapped_id || "account-#{@object.id}"
    else
      @object.id.to_s
    end
  end

  attribute :name, :string, writable: true, readable: true
  attribute :email, :string, writable: true, readable: true
  attribute :password, :string, writable: true, readable: false
  attribute :invitation_token, :string, writable: true, readable: false
  attribute :company, :string, writable: true, readable: true
  attribute :phone, :string, writable: true, readable: true
  attribute :phone_country_code, :string, writable: true, readable: true
  # attribute :current_organization, writable: false

  has_one :user, readable: true, writable: false, always_include_resource_ids: true do
    params do |hash, accounts|
      hash[:filter][:id] = hash[:filter].delete(:account_id)
    end

    assign do |accounts, users|
      users[0]
    end
  end

  # Remove the user_id attribute since it's redundant with the user relationship

  belongs_to :organization, readable: true, writable: false, always_include_resource_ids: true

  has_many :organizations, readable: true, writable: false, always_include_resource_ids: true do
    params do |hash, accounts|
      # FIXME: this is silly
      hash[:filter].delete(:account_id)
      hash[:filter][:id] = accounts[0].organizations.reload.pluck(:id)  # Add reload here
    end

    assign do |accounts, organizations|
      organizations.sort_by(&:name)
    end
  end

  paginate do |scope, *args|
    scope
  end

  filter :id, only: :eq, single: true

  def save(account)
    account.build_and_validate_user

    if account.errors.none?
      ApplicationRecord.transaction do
        account.create_user!

        # Create organization if company name is provided
        if account.company.present?
          organization = Organization.create!(name: account.company)

          # Add user as admin to the organization
          AddUserToOrganization.new(
            user: account.user,
            organization: organization,
            role_type: :admin
          ).execute!

          # Add default PayrollSchedule to the organization
          AddPayrollScheduleToOrganization.new(
            organization: organization,
          ).execute!
        end
      end
    end

    account
  end

  def base_scope
    scope = [Account.current]
    # Ensure users can only access their own account
    scope = scope.select { |account| account.user == context.current_user }
    raise Graphiti::Errors::RecordNotFound if scope.empty?
    scope
  end

  # Remove or modify the resolve method since we're handling not found in base_scope
  def resolve(scope)
    super
  end

  # Add default includes
  def self.default_includes
    [:user, :organizations]
  end

  def organizations
    # During account creation with invitation, we want the organization to appear
    # in the organization relationship but not in organizations array yet
    if @model.instance_variable_get(:@pending_invitation)
      Organization.none
    else
      @model.organizations
    end
  end

  def organization
    # During account creation with invitation, we want to show the pending organization
    if @model.instance_variable_get(:@pending_invitation)
      @model.instance_variable_get(:@pending_invitation).organization
    else
      @model.organization
    end
  end
end
