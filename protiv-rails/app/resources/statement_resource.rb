# frozen_string_literal: true

class StatementResource < ApplicationResource
  # Attributes
  attribute :net_amount, :money, readable: true, writable: true
  attribute :bonuses, :money, readable: true, writable: true
  attribute :deductions, :money, readable: true, writable: true
  attribute :previous_balance, :money, readable: true, writable: true
  attribute :overtime, :money, readable: true, writable: true

  attribute :code, :string

  attribute :status, :string_enum, allow: Statement.statuses.values, filterable: true
  attribute :created_at, :datetime, readable: true, writable: false
  attribute :updated_at, :datetime, readable: true, writable: false

  attribute :payment_date, :datetime, readable: true, writable: false

  # Relationships
  has_many :line_items, as: :statement_line_items, resource: StatementLineItemResource
  belongs_to :pay_period, always_include_resource_ids: true, resource: PayPeriodResource
  belongs_to :identity, always_include_resource_ids: true, resource: IdentityResource

  # Filters
  filter :status, only: :eq, allow: Statement.statuses.values
  filter :payable_id, :integer

  # Additional stuff
  attribute :line_items_count, :integer do
    @object.statement_line_items.count
  end

  attribute :previous_statement_id, :string, writable: false do
    @object.previous_statement&.id
  end

  def base_scope
    super.includes(:line_items, :pay_period, :payable)
    super.order(id: :asc)
  end
end
