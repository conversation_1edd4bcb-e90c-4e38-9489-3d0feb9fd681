# frozen_string_literal: true

class JobResource < ApplicationResource
  # Override pagination settings from ApplicationResource
  self.default_page_size = 10
  self.max_page_size = 100
  FILTER_INTERVALS = {
    "7d" => 7.days,
    "14d" => 14.days,
    "30d" => 30.days,
    "60d" => 60.days,
    "90d" => 90.days
  }.freeze

  belongs_to :organization
  attribute :reference_name, :string, readable: true, writable: false
  attribute :name, :string, readable: true, writable: true
  attribute :client_name, :string
  attribute :created_date, :datetime
  attribute :last_activity_at, :datetime
  attribute :job_number, :string

  # polymorphic_has_one :sub_pro_pay_pro_payable, as: :pro_payable, always_include_resource_ids: true

  filter :pro_pay_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(sub_pro_pay: :sub_pro_pay_pro_payables).
        merge(SubProPayProPayable.where(sub_pro_pay: { pro_pay_id: value }))
    end
  end

  belongs_to :manager, always_include_resource_ids: true, resource: IdentityResource

  belongs_to :branch, always_include_resource_ids: true

  attribute :status, :string_enum, allow: Job.statuses.values, filterable: true
  attribute :progress, :hash
  attribute :budget, :hash

  attribute :total_budget, :money
  attribute :total_costs, :money
  attribute :seconds_budget, :integer
  attribute :seconds_cost, :integer
  attribute :job_type, :string

  attribute :milestone_count, :integer do
    @object.milestones.count
  end

  attribute :has_pro_pay, :boolean do
    if @object.job_type == Job.job_types["non_recurring"]
      @object.sub_pro_pay_pro_payable.present?
    else
      @object.milestones.with_sub_pro_pay.exists?
    end
  end

  # Return the next 15 days of schedule visits
  attribute :upcoming_schedule_visits, :array do
    upcoming_visits = ScheduleVisit.joins(:milestone, :route)
                                  .where(milestone: { job_id: @object.id })
                                  .where(scheduled_date: Date.today..Date.today + 15.days)
                                  .order(:scheduled_date)
                                  .includes(:route, route: :crew_lead)

    result = upcoming_visits.map do |visit|
      {
        id: visit.id,
        scheduled_date: visit.scheduled_date,
        route_name: visit.route&.name
      }
    end

    result.present? ? result : []
  end


  has_one :details, resource: JobDetailsResource do
    link do |job|
      Rails.application.routes.url_helpers.api_job_detail_path(job.to_param,
        include: "milestones.resolved_pro_pay,milestones.milestone_times.identity,property,branch")
    end
  end

  has_one :first_milestone, resource: MilestoneResource

  # alternatively, `recent` can be modelled as a custom filter operation on
  # `last_activity_at`, but modelling it this way gives you the `single` and
  # `allow` options which seems useful
  filter :recent, :string, single: true, only: [:eq], allow: FILTER_INTERVALS.keys do
    eq do |scope, value|
      interval = FILTER_INTERVALS.fetch(value)
      scope.where(last_activity_at: interval.ago..)
    end
  end

  filter :manager_id, :integer, only: [:eq] do |scope, values|
    scope.where(manager_id: values)
  end

  filter :is_recurring, :boolean, single: true do |scope, value|
    scope.where(job_type: Job.job_types["recurring"])
  end

  filter :name, :string, only: [:eq] do
    eq do |scope, value|
      # Handle different types of input values
      search_term = case value
      when String
                      value
      when Array
                      value.first.to_s
      else
                      value.to_s
      end

      Rails.logger.info("JobResource name filter - original value: #{value.inspect}, class: #{value.class}, processed search_term: #{search_term}")

      # Escape the search term to prevent SQL injection
      escaped_term = ActiveRecord::Base.connection.quote_string(search_term)

      # Search in jobs.name, properties.name, and jobs.remote_reference (job_number)
      query = scope.joins("LEFT JOIN properties ON jobs.property_id = properties.id")
                  .where("jobs.name ILIKE :term OR properties.name ILIKE :term OR jobs.remote_reference ILIKE :term", term: "%#{escaped_term}%")

      # Log the SQL query for debugging
      Rails.logger.info("JobResource search query: #{query.to_sql}")

      query
    end
  end

  # Define standardized sort methods for all sortable fields

  # Helper method to safely handle sort direction
  def self.safe_direction(direction)
    # Only allow 'asc' or 'desc' as valid values
    direction.to_s.downcase == "desc" ? "desc" : "asc"
  end

  # Make class method available to instances
  def safe_direction(direction)
    self.class.safe_direction(direction)
  end

  # Helper method to create a safe order clause with NULLS LAST
  def self.safe_order_nulls_last(scope, column, direction)
    # Validate direction to only allow 'asc' or 'desc'
    dir = safe_direction(direction)

    # Whitelist of allowed columns to prevent SQL injection
    allowed_columns = {
      "jobs.name" => "jobs.name",
      "jobs.last_activity_at" => "jobs.last_activity_at",
      "jobs.status" => "jobs.status",
      "jobs.job_number" => "jobs.job_number",
      "jobs.created_at" => "jobs.created_at",
      "properties.name" => "properties.name",
      "identities.name" => "identities.name",
      "job_budget_sums.total_seconds_cost" => "job_budget_sums.total_seconds_cost",
      "milestone_progress.progress_pct" => "milestone_progress.progress_pct"
    }

    # Ensure the column is in our whitelist
    safe_column = allowed_columns[column]
    raise ArgumentError, "Invalid column: #{column}" unless safe_column

    # Use a case statement which is safe from SQL injection
    # This approach avoids string interpolation entirely
    if dir == "asc"
      scope.order(Arel.sql("#{safe_column} ASC NULLS LAST"))
    else
      scope.order(Arel.sql("#{safe_column} DESC NULLS LAST"))
    end
  end

  # Make class method available to instances
  def safe_order_nulls_last(scope, column, direction)
    self.class.safe_order_nulls_last(scope, column, direction)
  end

  # Simple column sorts
  sort :name, :string do |scope, direction|
    safe_order_nulls_last(scope, "jobs.name", direction)
  end

  sort :last_activity_at, :datetime do |scope, direction|
    safe_order_nulls_last(scope, "jobs.last_activity_at", direction)
  end

  sort :status, :string do |scope, direction|
    safe_order_nulls_last(scope, "jobs.status", direction)
  end

  sort :job_number, :string do |scope, direction|
    safe_order_nulls_last(scope, "jobs.job_number", direction)
  end

  sort :created_date, :datetime do |scope, direction|
    safe_order_nulls_last(scope, "jobs.created_at", direction)
  end

  # Relationship-based sorts
  sort :client_name, :string do |scope, direction|
    # client_name is a method that returns property.name
    scope.left_joins(:property)
         .then { |s| safe_order_nulls_last(s, "properties.name", direction) }
  end

  sort :property_name, :string do |scope, direction|
    scope.left_joins(:property)
         .then { |s| safe_order_nulls_last(s, "properties.name", direction) }
  end

  sort :manager_name, :string do |scope, direction|
    scope.left_joins(:manager)
         .then { |s| safe_order_nulls_last(s, "identities.name", direction) }
  end

  # Calculated field sorts using subqueries
  sort :seconds_cost, :integer do |scope, direction|
    # Use a simpler approach with heredoc SQL
    sql = <<~SQL
      SELECT job_id, SUM(seconds_cost) as total_seconds_cost
      FROM job_budgets
      GROUP BY job_id
    SQL

    scope.joins("LEFT JOIN (#{sql}) job_budget_sums ON jobs.id = job_budget_sums.job_id")
         .then { |s| safe_order_nulls_last(s, "job_budget_sums.total_seconds_cost", direction) }
  end

  sort :progress_percentage, :float do |scope, direction|
    # Use a simpler approach without multi-line strings
    sql = <<~SQL
      SELECT job_id,
             COUNT(CASE WHEN status = 'completed' THEN 1 ELSE NULL END)::float /
             NULLIF(COUNT(id), 0) as progress_pct
      FROM milestones
      GROUP BY job_id
    SQL

    scope.joins("LEFT JOIN (#{sql}) milestone_progress ON jobs.id = milestone_progress.job_id")
         .then { |s| safe_order_nulls_last(s, "milestone_progress.progress_pct", direction) }
  end

  def base_scope
    super.includes(:branch, :job_budgets, :manager, :milestones)
  end
end
