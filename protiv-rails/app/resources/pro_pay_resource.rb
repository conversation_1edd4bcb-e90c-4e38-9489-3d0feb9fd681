# frozen_string_literal: true

class ProPayResource < ApplicationResource
  has_many :sub_pro_pays

  has_many :crew_bonuses, foreign_key: :pro_pay_id, resource: SubProPayLineItemResource do
    assign_each do |pro_pay, sub_pro_pay_line_items|
      # Filter line items that belong to this specific pro_pay
      sub_pro_pay_line_items.select { |spli| spli.sub_pro_pay.pro_pay_id == pro_pay.id }
    end
  end

  has_many :milestones do
    assign_each do |pro_pay, milestones|
      selected_milestones = milestones.select do |milestone|
        pro_pay.sub_pro_pays.any? do |spp|
          spp.sub_pro_pay_pro_payables.any? do |sppp|
            sppp.pro_payable == milestone
          end
        end
      end
      # Sort milestones alphabetically by name for consistent ordering
      selected_milestones.sort_by(&:name)
    end
  end

  # Normally it's one job, this is just in case to support grouped pro_pays
  has_many :jobs do
    assign_each do |pro_pay, jobs|
      jobs.select do |job|
        pro_pay.sub_pro_pays.any? do |spp|
          spp.sub_pro_pay_pro_payables.any? do |sppp|
            sppp.pro_payable == job
          end
        end
      end
    end
  end

  filter :milestone_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(sub_pro_pays: :sub_pro_pay_pro_payables).
        merge(SubProPayProPayable.where(pro_payable_id: value, pro_payable_type: "Milestone"))
    end
  end

  filter :job_id, :integer, only: [:eq] do
    eq do |scope, job_id|
      direct_job_condition = SubProPayProPayable.where(pro_payable_id: job_id, pro_payable_type: "Job")
      milestones_of_job_condition = SubProPayProPayable.where(
        pro_payable_type: "Milestone",
        pro_payable_id: Milestone.where(job_id: job_id).select(:id)
      )

      scope.joins(sub_pro_pays: :sub_pro_pay_pro_payables).
        merge(direct_job_condition.or(milestones_of_job_condition))
    end
  end

  attribute :code, :string
  attribute :status, :string_enum, allow: ProPay.statuses.values, filterable: true
  attribute :progress, :hash
  attribute :worker_count, :integer

  attribute :budget_type, :string_enum, allow: SubProPay.budget_types.values, filterable: true
  attribute :total_budget, :money

  attribute :seconds_budget, :integer
  attribute :seconds_cost, :integer

  attribute :total_costs, :money
  attribute :total_budget, :money
  attribute :budget, :hash

  attribute :total_bonus, :money
  attribute :available_bonus, :money
  attribute :approved_bonus, :money

  attribute :first_check_in, :datetime
  attribute :last_check_out, :datetime

  attribute :source_type, :string_enum, allow: ProPay.source_types.values, filterable: true
  attribute :payout_type, :string_enum, allow: ProPay.payout_types.values, filterable: true

  attribute :scheduled_date, :datetime
  attribute :partial_bonus_paid, :boolean
  attribute :distribution_type, :string_enum, allow: SubProPay.distribution_types.values, filterable: true

  belongs_to :source_route, resource: RouteResource, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :source_job, resource: JobResource, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :reference_bonus_pool, resource: BonusPoolResource, readable: true, writable: false, always_include_resource_ids: true

  filter :deleted, :boolean do
    eq do |scope, value|
      if value
        scope.merge(ProPay.where(status: "deleted"))
      else
        scope.active
      end
    end
  end

  stat deleted: [:count] do
    count do |scope, attr|
      scope.merge(ProPay.where(status: "deleted")).count
    end
  end

  def base_scope
    super.active
  end
end
