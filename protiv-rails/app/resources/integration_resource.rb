# frozen_string_literal: true

class IntegrationResource < ApplicationResource
  attribute :source, :string, readable: true, writable: :new_record?
  attribute :last_synced_at, :datetime, readable: true, writable: false
  # FIXME: the writable guard does not seem to apply to updates on relationships.
  # For now this has been declared as attr_readonly in the model.
  belongs_to :organization, writable: false

  attribute :client_id, :string, readable: true, writable: true
  attribute :secret, :string, readable: false, writable: true

  def new_record?
    Graphiti.context[:namespace] == :create
  end

  def create(attributes)
    super(attributes.merge(organization: current_organization)).tap do |result|
      if result.persisted?
        result.sync_all(async: true)
      end
    end
  end
end
