# frozen_string_literal: true

class SubProPayLineItemResource < ApplicationResource
  # Attributes
  # attribute :duration_seconds, :integer
  attribute :crew_lead, :boolean
  # attribute :labor_cost, :money
  # attribute :labor_cost_entered, :money
  # attribute :labor_cost_adjustment, :money
  # attribute :duration_hours, :float

  # Calculated bonus attributes
  attribute :available_bonus, :money
  attribute :approved_bonus, :money
  attribute :deductions, :money

  # Virtual attribute for pro_pay_id (accessed through sub_pro_pay relationship)
  attribute :pro_pay_id, :integer do
    @object.sub_pro_pay.pro_pay_id
  end

  # Relationships
  belongs_to :identity, resource: IdentityResource
  belongs_to :sub_pro_pay, resource: SubProPayResource

  has_many :statement_line_items, foreign_key: :identity_id, resource: StatementLineItemResource do
    assign_each do |sub_pro_pay_line_item, statement_line_items|
      statement_line_items.select do |sli|
        sli.identity_id == sub_pro_pay_line_item.identity_id && sli.pro_pay_id == sub_pro_pay_line_item.sub_pro_pay.pro_pay_id
      end
    end
  end

  # Virtual attributes for role
  attribute :role, :string do
    @object.crew_lead? ? "Crew Lead" : "Worker"
  end

  # Filters
  filter :pro_pay_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(:sub_pro_pay).where(sub_pro_pays: { pro_pay_id: value })
    end
  end
end
