# frozen_string_literal: true

class ServiceMaterialResource < ApplicationResource
  # Override the type to match the endpoint
  self.type = :'service-materials'

  # Attributes
  attribute :service_id, :integer
  attribute :catalog_item_id, :integer

  # Relationships
  belongs_to :service, resource: ServiceResource
  belongs_to :catalog_item, resource: CatalogItemResource

  # Writable attributes for creation
  extra_attribute :service_id, :integer, writable: true
  extra_attribute :catalog_item_id, :integer, writable: true

  # Filters for querying
  filter :service_id, :integer do
    eq do |scope, value|
      scope.where(service_id: value)
    end
  end

  def base_scope
    # Ensure we only return service materials for the current organization
    ServiceMaterial.joins(:service).where(services: { organization_id: context.current_organization.id })
  end
end
