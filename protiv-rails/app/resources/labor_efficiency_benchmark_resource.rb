# frozen_string_literal: true

class LaborEfficiencyBenchmarkResource < ApplicationResource
  # Override the type to match the endpoint
  self.type = :"labor-efficiency-benchmarks"

  # Attributes
  attribute :target_percentage, :float, writable: true
  attribute :level, :string, writable: false
  attribute :active, :boolean, writable: false
  attribute :created_at, :datetime, writable: false
  attribute :updated_at, :datetime, writable: false

  # Relationships
  belongs_to :organization, writable: false, always_include_resource_ids: true

  # Filters
  filter :organization_id, :integer do
    eq do |scope, value|
      scope.where(organization_id: value)
    end
  end

  filter :level, :string do
    eq do |scope, value|
      scope.where(level: value)
    end
  end

  filter :active, :boolean do
    eq do |scope, value|
      scope.where(active: value)
    end
  end

  # Base scope
  def base_scope
    LaborEfficiencyBenchmark.all
  end

  # Custom action to reset to default
  def reset
    benchmark_manager = Metrics::BenchmarkManager.new(context.organization)
    benchmark_manager.reset_to_default
  end
end
