# frozen_string_literal: true

class PayPeriodResource < ApplicationResource
  # Attributes
  attribute :period_type, :string_enum, allow: PayPeriod.period_types.values, filterable: true
  attribute :start_time, :datetime, readable: true, writable: true
  attribute :end_time, :datetime, readable: true, writable: true

  attribute :created_at, :datetime, readable: true, writable: false
  attribute :updated_at, :datetime, readable: true, writable: false

  # Relationships
  belongs_to :branch, always_include_resource_ids: true

  # Filters
  filter :period_type, :string, allow: PayPeriod.period_types.values
end
