# frozen_string_literal: true

class ScheduleVisitResource < ApplicationResource
  belongs_to :organization
  belongs_to :route, always_include_resource_ids: true
  belongs_to :milestone, always_include_resource_ids: true

  attribute :scheduled_date, :date, filterable: true
  attribute :hours, :float, filterable: false
  attribute :created_at, :datetime, filterable: false
  attribute :updated_at, :datetime, filterable: false

  # Filter by route
  filter :route_id, :integer do
    eq do |scope, value|
      scope.where(route_id: value)
    end
  end

  # Filter by milestone
  filter :milestone_id, :integer do
    eq do |scope, value|
      scope.where(milestone_id: value)
    end
  end

  # Filter by job (through milestone)
  filter :job_id, :integer do
    eq do |scope, value|
      scope.joins(milestone: :job).where(milestones: { job_id: value })
    end
  end

  # Filter by crew lead
  filter :crew_lead_id, :integer do
    eq do |scope, value|
      scope.joins(:route).where(routes: { crew_lead_id: value })
    end
  end

  # Filter for upcoming visits
  filter :upcoming, :boolean, single: true do
    eq do |scope, value|
      scope.where("scheduled_date >= ?", Date.today) if value
    end
  end

  # Date range filter
  filter :date_range, :string, single: true do
    eq do |scope, value|
      if value.is_a?(String) && value.match?(/^\d+d$/)
        days = value.to_i # Extracts the integer part, e.g., "10d" -> 10
        if days > 0
          scope.where(scheduled_date: Date.today..(Date.today + days.days))
        else
          # Raise an error for "0d" or negative days
          raise Graphiti::Errors::InvalidFilterValue.new(:date_range, value, "Number of days must be positive.")
        end
      elsif value == "all"
        scope
      else
        # Raise an error for any other format
        raise Graphiti::Errors::InvalidFilterValue.new(:date_range, value, "Allowed values are 'all' or a string like '7d', '15d', etc.")
      end
    end
  end

  def base_scope
    super.includes(:route, :milestone, route: :crew_lead, milestone: :job)
  end
end
