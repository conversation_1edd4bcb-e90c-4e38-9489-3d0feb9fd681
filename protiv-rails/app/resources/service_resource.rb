# frozen_string_literal: true

class ServiceResource < ApplicationResource
  self.type = :services

  # Attributes
  attribute :name, :string, writable: true
  attribute :progress_type, :string, writable: true

  # Flag to skip expensive callbacks during bulk operations
  extra_attribute :skip_milestone_cache_updates, :boolean, writable: true do
    # Custom assignment to set the flag on the model before saving
    assign do |services, value|
      services.each { |service| service.skip_milestone_cache_updates = value }
    end
  end

  # Read-only many-to-many relationship (writable: false to avoid Graphiti assignment issues)
  has_many :tracking_materials, resource: CatalogItemResource, writable: false do
    scope do |services|
      # Handle both Service objects and ID arrays
      service_ids = if services.first.is_a?(Integer)
                      services
      else
                      Array(services).map(&:id)
      end

      # Return CatalogItems that are associated with these services through ServiceMaterials
      CatalogItem.joins(:service_materials)
                 .where(service_materials: { service_id: service_ids })
                 .where(item_type: "Material")
    end

    assign do |services, catalog_items|
      # Custom assignment for many-to-many relationship for sideloading
      # Group catalog_items by the services they belong to
      service_material_map = ServiceMaterial.where(
        service_id: services.map(&:id),
        catalog_item_id: catalog_items.map(&:id)
      ).group_by(&:service_id)

      services.each do |service|
        service_materials = service_material_map[service.id] || []
        related_catalog_items = catalog_items.select do |ci|
          service_materials.any? { |sm| sm.catalog_item_id == ci.id }
        end
        service.association(:tracking_materials).target = related_catalog_items
      end
    end
  end

  # Bulk update progress types for multiple services
  def self.bulk_update_progress_types(service_updates, context)
    current_organization = context.current_organization

    Service.transaction do
      service_updates.map do |update_data|
        service_id = update_data[:id] || update_data["id"]
        progress_type = update_data[:progress_type] || update_data["progress_type"]

        # Find service with organization scoping
        service = current_organization.services.find(service_id)

        # Manual Pundit authorization for each service update
        #
        # We use manual authorization here instead of Graphiti's automatic authorization because:
        # 1. This is a custom class method outside the normal Graphiti resource flow
        # 2. We're updating multiple services in a single atomic transaction
        # 3. We need to authorize each service individually before updating
        # 4. We want to fail fast if any service update is unauthorized
        #
        # The ServicePolicy.update? method checks if the user is a manager or admin
        # of the service's organization, ensuring proper role-based access control.
        policy = ServicePolicy.new(context.current_user, service)
        raise Pundit::NotAuthorizedError unless policy.update?

        # Update the progress type with validation
        begin
          service.update!(progress_type: progress_type)
        rescue ArgumentError => e
          # Convert ArgumentError (invalid enum) to RecordInvalid for consistent error handling
          service.errors.add(:progress_type, e.message)
          raise ActiveRecord::RecordInvalid.new(service)
        end

        service
      end
    end
  end

  private

  def current_organization
    context.current_organization
  end
end
