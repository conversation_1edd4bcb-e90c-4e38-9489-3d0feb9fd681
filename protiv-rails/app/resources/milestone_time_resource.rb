# frozen_string_literal: true

class MilestoneTimeResource < ApplicationResource
  # Override pagination settings from ApplicationResource
  self.default_page_size = 10
  self.max_page_size = 100

  belongs_to :milestone, always_include_resource_ids: true
  attribute :milestone_id, :integer, only: [:filterable]
  attribute :start_time, :datetime
  attribute :end_time, :datetime
  attribute :duration_seconds, :integer
  attribute :base_hourly_rate, :money
  attribute :labor_cost, :money

  belongs_to :identity, always_include_resource_ids: true
  attribute :identity_id, :integer, only: [:filterable]
  filter :job_id, :integer do
    eq do |scope, value|
      scope.joins(milestone: :job).where(milestones: { job_id: value })
    end
  end

  # Define a class method for default sort to make it easier to mock in tests
  def self.default_sort
    [{ start_time: :asc }, { id: :asc }]
  end

  def base_scope
    scope = super.includes(:identity)

    # Apply default sorting
    self.class.default_sort.reduce(scope) do |s, sort_hash|
      sort_hash.each do |field, direction|
        s = s.order(field => direction)
      end
      s
    end
  end
end
