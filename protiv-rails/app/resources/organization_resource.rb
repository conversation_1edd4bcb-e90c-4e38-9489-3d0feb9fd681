# frozen_string_literal: true

class OrganizationResource < ApplicationResource
  attribute :name, :string, readable: true, writable: true

  attribute :user_role, :string, readable: true do
    @object.roles.find_by(user: context.current_user)&.role_type
  end

  attribute :benchmark_id, :integer, readable: true do
    @object.labor_efficiency_benchmark&.id
  end

  attribute :default_progress_tracking_type, :string, readable: true, writable: true
  attribute :default_tracking_material, :string, readable: true, writable: true

  # Define base scope for authorization
  def base_scope
    policy_scope(Organization.all)
  end

  # Create default schedules and add the current user as admin
  before_save do |model|
    if model.new_record?
      # Create default payroll schedule
      payroll_schedule = PayrollSchedule.create!(
        organization: model,
        period: "biweekly",
        week_day: 1,
        name: "Payroll"
      )
      model.default_payroll_schedule = payroll_schedule

      # Create default bonus schedule
      bonus_schedule = PayrollSchedule.create!(
        organization: model,
        period: "weekly",
        week_day: 1,
        name: "Bonus"
      )
      model.default_bonus_schedule = bonus_schedule

      # Create default route schedule
      route_schedule = PayrollSchedule.create!(
        organization: model,
        period: "weekly",
        week_day: 1,
        name: "Route"
      )
      model.default_route_schedule = route_schedule

      # Add the current user as an admin
      if context.current_user
        AddUserToOrganization.new(
          user: context.current_user,
          organization: model,
          role_type: :admin
        ).execute!
      end
    end
  end
end
