# frozen_string_literal: true

# NOTE: this resource may not be necessary past the bootstrapping stage
class SessionResource < ApplicationResource
  self.adapter = Graphiti::Adapters::Null
  self.attributes_writable_by_default = false

  has_one :user, always_include_resource_ids: true do
    params do |hash, sessions|
      hash[:filter][:id] = hash[:filter].delete(:session_id)
    end

    assign do |sessions, users|
      users[0]
    end
  end

  attribute :email, :string
  attribute :admin, :boolean
  attribute :verified, :boolean

  primary_endpoint "/session"

  def base_scope
    {}
  end

  def resolve(scope)
    if current_user
      [Session.new(current_user)]
    else
      []
    end
  end
end
