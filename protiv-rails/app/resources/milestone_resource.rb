# frozen_string_literal: true

class MilestoneResource < ApplicationResource
  belongs_to :job, filter: true, always_include_resource_ids: true
  attribute :job_id, :integer, filter: true, readable: false
  polymorphic_has_one :sub_pro_pay_pro_payable, as: :pro_payable, resource: SubProPayPayableResource

  # Add service relationship for material selection logic
  belongs_to :service, always_include_resource_ids: true

  has_many :schedule_visits

  filter :pro_pay_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(sub_pro_pay: :sub_pro_pay_pro_payables).
        merge(SubProPayProPayable.where(sub_pro_pay: { pro_pay_id: value }))
    end
  end

  has_one :resolved_sub_pro_pay, foreign_key: :milestone_id, resource: SubProPayResource do
    pre_load do |proxy, _milestones|
      proxy.scope.object = proxy.scope.object.eager_load(:sub_pro_pay_pro_payables)
    end

    assign_each do |milestone, sub_pro_pays|
      selected = []
      sub_pro_pays.each do |spp|
        spp.sub_pro_pay_pro_payables.each do |sppp|
          selected << spp if sppp.pro_payable == milestone
        end
      end
      selected
    end
  end

  has_one :resolved_pro_pay, foreign_key: :milestone_id, resource: ProPayResource do
    pre_load do |proxy, _milestones|
      proxy.scope.object = proxy.scope.object.eager_load(sub_pro_pays: :sub_pro_pay_pro_payables)
    end

    assign_each do |milestone, pro_pays|
      selected = []
      pro_pays.each do |pp|
        pp.sub_pro_pays.each do |spp|
          spp.sub_pro_pay_pro_payables.each do |sppp|
            selected << pp if sppp.pro_payable == milestone
          end
        end
      end
      selected
    end
  end

  has_many :milestone_times
  has_many :employee_milestone_times

  attribute :name, :string do
    if @object.remote_reference.present?
      "#{@object.remote_reference} - #{@object.name}"
    else
      @object.name
    end
  end
  attribute :description, :string, writable: true
  attribute :status, :string
  attribute :contract_price, :money
  attribute :seconds_budget, :integer
  attribute :seconds_cost, :integer

  # Add tracked_milestone_item_id as writable attribute for material selection
  attribute :tracked_milestone_item_id, :integer, writable: true

  # Add computed attribute to indicate if material selection is needed
  attribute :needs_material_selection, :boolean do
    service = @object.service
    next false unless service&.progress_type == "units"           # Must be units tracking
    next false if service.tracking_materials.count <= 1          # Must have multiple options
    next false if @object.tracked_milestone_item.present?        # Must not already be selected
    true
  end

  ApplicationRecord::BUDGET_CATEGORIES.each do |category|
    attribute :"#{category}_budget", :money
    attribute :"#{category}_cost", :money
  end
  attribute :first_check_in, :datetime
  attribute :last_check_out, :datetime
  attribute :total_costs, :money
  attribute :total_budget, :money
  attribute :budget, :hash
  attribute :employee_count, :integer
  attribute :percent_complete, :float

  attribute :is_t_and_m, :boolean do
    @object.invoice_type&.time_and_materials? || false
  end

  attribute :labor_metrics_summary, :hash do
    if @object.invoice_type&.time_and_materials?
      # Get aggregated metrics
      metrics = Metrics::MilestoneLaborAggregator.new(
        milestone_id: @object.id
      ).call

      # Simply return the labor_metrics_summary that's already built
      metrics[:labor_metrics_summary]
    end
  end

  attribute :next_schedule_visit, :hash do
    next_visit = @object.schedule_visits
                        .where("scheduled_date >= ?", Date.today)
                        .order(:scheduled_date)
                        .includes(:route, route: :crew_lead)
                        .first

    if next_visit
      {
        id: next_visit.id,
        scheduled_date: next_visit.scheduled_date,
        route_id: next_visit.route_id
      }
    else
      nil
    end
  end

  has_one :tracking_material, resource: TrackingMaterialResource

  def base_scope
    policy_scope(model).includes(:milestone_times, :job, :pro_pay, :schedule_visits, :service)
  end
end
