<p style="color: red"><%= alert %></p>

<h1>Enter your password to continue</h1>

<%= form_with(url: sessions_sudo_path) do |form| %>

  <%= form.hidden_field :proceed_to_url, value: params[:proceed_to_url] %>

  <div>
    <%= form.password_field :password, required: true, autofocus: true, autocomplete: "current-password" %>
  </div>

  <div>
    <%= form.submit "Continue" %>
  </div>
<% end %>

<br>

<p>
  <strong>Why are you asking me to do this?</strong><br>
  To better protect your account, we'll occasionally ask you to confirm your password before performing sensitive actions.
</p>

<p>
  <strong>Forgot your password?</strong><br>
  We'll help you <%= link_to "reset it", password_reset_path %> so you can continue.
</p>
