# frozen_string_literal: true

class UserMailer < ApplicationMailer
  def password_reset
    @user = params[:user]
    @signed_id = @user.generate_token_for(:password_reset)
    @email = @user.email

    mail(
      to: @email,
      subject: "Reset your password"
    ) do |format|
      format.html
    end
    send_mail
  end

  def email_verification
    @user = params[:user]
    @signed_id = @user.generate_token_for(:email_verification)
    @email = @user.email

    mail(
      to: @email,
      subject: "Verify your email"
    ) do |format|
      format.html
    end
    send_mail
  end

  def passwordless
    @user = params[:user]
    @signed_id = @user.sign_in_tokens.create.signed_id(expires_in: 1.day)
    @email = @user.email

    mail(
      to: @email,
      subject: "Your sign in link"
    ) do |format|
      format.html
    end
    send_mail
  end

  def invitation_instructions
    @invitation = params[:invitation]
    raise ArgumentError, "Invitation not found" unless @invitation

    @token = @invitation.generate_token_for(:add_user_to_organization)
    @email = @invitation.email

    mail(
      to: @email,
      subject: "You've been invited to join #{@invitation.organization.name}"
    ) do |format|
      format.html { render "invitation_instructions" }
    end
    send_mail
  end

  def password_change_verification_email
    @user = params[:user]
    @token = params[:token] || @user.generate_token_for(:password_change_verification)

    mail to: @user.email, subject: "Verify your password change"
  end
end
