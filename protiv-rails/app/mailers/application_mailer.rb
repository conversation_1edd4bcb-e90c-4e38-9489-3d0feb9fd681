# frozen_string_literal: true

class ApplicationMailer < ActionMailer::Base
  default from: ENV.fetch("MAILER_FROM", "<EMAIL>")
  layout "mailer"

  # Use Mandrill if configured, otherwise fall back to standard SMTP
  # Only send emails if SEND_EMAILS is set to "true"
  # In development, respect the configured delivery method (letter_opener_web)
  def send_mail
    html_content = render_to_string(layout: "mailer")
    to_address = @email || message.to.first

    # In development, use the configured delivery method (letter_opener_web)
    if Rails.env.development? && ActionMailer::Base.delivery_method == :letter_opener_web
      # Still respect the SEND_EMAILS flag
      if ENV["SEND_EMAILS"] != "true"
        Rails.logger.info "Email sending is disabled. Would have shown in letter_opener: #{to_address}"
        return true
      end

      # Use the standard Rails mailer which will use letter_opener_web in development
      mail(
        to: to_address,
        subject: message.subject
      ).deliver_now

      return true
    end

    # For other environments, check if email sending is enabled
    if ENV["SEND_EMAILS"] != "true"
      Rails.logger.info "Email sending is disabled. Would have sent to: #{to_address}"
      return true
    end

    if ENV["MANDRILL_API_KEY"].present?
      Rails.logger.info "Using Mandrill for email delivery"
      MandrillMailer.send_mail(
        to: to_address,
        subject: message.subject,
        html_content: html_content
      )
    else
      Rails.logger.info "Using standard mailer for email delivery"
      StandardMailer.send_mail(
        to: to_address,
        subject: message.subject,
        html_content: html_content
      )
    end
  end
end
