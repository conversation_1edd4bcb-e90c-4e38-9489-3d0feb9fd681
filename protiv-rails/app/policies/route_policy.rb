# frozen_string_literal: true

class RoutePolicy < ApplicationPolicy
  def index?
    user.employee_organizations.include?(record.organization)
  end

  def show?
    user.employee_organizations.include?(record.organization)
  end

  def update?
    user.manager_organizations.include?(record.organization)
  end

  class Scope < Scope
    def resolve
      if user
        user.routes.where(organization: organization)
      else
        Route.none
      end
    end
  end
end
