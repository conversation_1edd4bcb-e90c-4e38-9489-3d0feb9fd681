# frozen_string_literal: true

class MilestonePolicy < ApplicationPolicy
  def update?
    return false unless user

    # Allow update if the milestone belongs to a job in the user's organization
    return false unless record.job
    JobPolicy.new(user, record.job).show?
  end

  class Scope < Scope
    def resolve
      return scope.none unless user

      job_scope = JobPolicy::Scope.new(user, user.jobs)
      scope.joins(:job).merge(job_scope.resolve)
    end
  end
end
