# frozen_string_literal: true

class OrganizationPolicy < ApplicationPolicy
  def show?
    true # FIXME:
  end

  def index?
    true # FIXME
  end

  def create?
    user.present?
  end

  def update?
    return false unless user
    # Allow update if the user is a member of the organization
    user.organizations.include?(record)
  end

  class Scope < Scope
    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      if user
        user.organizations
      else
        Organization.none
      end
    end
  end
end
