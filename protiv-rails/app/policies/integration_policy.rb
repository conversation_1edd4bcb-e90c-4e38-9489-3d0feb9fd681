# frozen_string_literal: true

class IntegrationPolicy < ApplicationPolicy
  def index?
    user.present?# handled via Scope
  end

  def show?
    index?
  end

  def create?
    user.admin_organizations.include?(record.organization)
  end

  def update?
    create?
  end

  class Scope < Scope
    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      if user
        user.integrations
      else
        Integration.none
      end
    end
  end
end
