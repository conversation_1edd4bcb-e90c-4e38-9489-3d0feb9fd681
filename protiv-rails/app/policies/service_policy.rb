# frozen_string_literal: true

class ServicePolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      return scope.none unless user && organization
      return scope.none unless user.manager_organizations.include?(organization) || user.admin_organizations.include?(organization)

      scope.merge(organization.services)
    end
  end

  def allowed?
    user && (user.manager_organizations.include?(organization) || user.admin_organizations.include?(organization))
  end

  alias_method :show?, :allowed?
  alias_method :index?, :allowed?
  alias_method :create?, :allowed?
  alias_method :update?, :allowed?
end
