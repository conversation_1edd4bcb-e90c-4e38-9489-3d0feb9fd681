# frozen_string_literal: true

class LaborEfficiencyBenchmarkPolicy < ApplicationPolicy
  def index?
    user_is_manager_or_admin?
  end

  def show?
    user_is_manager_or_admin?
  end

  def update?
    user_is_manager_or_admin?
  end

  def reset?
    user_is_manager_or_admin?
  end

  private

  def user_is_manager_or_admin?
    return false unless user && organization
    user.manager_organizations.include?(organization) ||
      user.admin_organizations.include?(organization)
  end

  class Scope < Scope
    def resolve
      return scope.none unless user && organization
      return scope.none unless user.manager_organizations.include?(organization) ||
                              user.admin_organizations.include?(organization)

      scope.where(organization: organization)
    end
  end
end
