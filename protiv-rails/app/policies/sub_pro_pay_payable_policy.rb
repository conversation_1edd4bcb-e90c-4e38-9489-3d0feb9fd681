# frozen_string_literal: true

class SubProPayPayablePolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      return scope.none unless user
      scope.all

      # FIXME: Scope to admin
      # scope.joins(sub_pro_pay: { pro_pay: :organization }).where(organizations: { id: user.organizations })
      # if user.admin?
      #   scope.joins(:pro_payable).merge(user.sub_pro_pay_pro_payables)
      # else
      #   scope.none
      # end
    end
  end
end
