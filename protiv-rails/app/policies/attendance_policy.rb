# frozen_string_literal: true

class AttendancePolicy < ApplicationPolicy
  def index?
    true
  end

  def show?
    true
  end

  def create?
    true
  end

  def update?
    true
  end

  class Scope < Scope
    def resolve
      if user && organization && manager_identity
        scope.where(organization:)
      else
        scope.none
      end
    end

    def manager_identity
      Current.manager_identity
    end
  end
end
