# frozen_string_literal: true

class StatementLineItemPolicy < ApplicationPolicy
  def index?
    true # FIXME by role
  end

  def show?
    true # FIXME by role
  end

  def create?
    true # FIXME by role
  end

  def update?
    true # FIXME by role
  end

  class Scope < Scope
    def resolve
      if user && organization
        scope.merge(organization.statement_line_items)
      else
        scope.none
      end
    end
  end
end
